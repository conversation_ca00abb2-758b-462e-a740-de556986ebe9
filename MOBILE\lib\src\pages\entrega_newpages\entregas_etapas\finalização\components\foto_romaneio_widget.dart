import 'dart:io';

import 'package:camera_camera/camera_camera.dart';
import 'package:flutter/material.dart';
import 'package:octalog/src/components/flavor_image/flavor_image.dart';

import '../../../../../helpers/web_connector.dart';
import '../../../controller/entrega_new_store.dart';
import 'preview_fotos.dart';

class FotoRomaneioWidget extends StatefulWidget {
  final EntregaNewStore store;
  final List<int>? bytesRomaneio;

  const FotoRomaneioWidget({super.key, required this.store, required this.bytesRomaneio});

  @override
  State<FotoRomaneioWidget> createState() => _FotoRomaneioWidgetState();
}

class _FotoRomaneioWidgetState extends State<FotoRomaneioWidget> {
  late XFile? fotopath;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              GestureDetector(
                onTap: () {
                  widget.store.clearFotoRomaneio();
                  fotopath = null;
                },
                child: const Icon(Icons.delete, color: Colors.red, size: 25),
              ),
            ],
          ),
          const SizedBox(height: 10),
          SizedBox(
            height: 75,
            child: GestureDetector(
              onTap: () async {
                final foto = await WebConnector().tirarFoto(context);
                if (foto == null) return;
                fotopath = foto;

                bool? liberarFoto = await Navigator.push(context, MaterialPageRoute(builder: (context) => PreviewImageSemRosto(imageFile: foto)));
                if (liberarFoto == null || !liberarFoto) return;

                widget.store.uploaFotoRomaneio(foto);
              },
              child: Builder(
                builder: (context) {
                  final foto = widget.bytesRomaneio;
                  if (foto == null) {
                    return Stack(children: [SizedBox(height: 75, child: FlavorImage(assetName: 'foto_canhoto.gif', fit: BoxFit.fill))]);
                  }
                  return SizedBox(height: 200, width: double.infinity, child: Image.file(File(fotopath!.path), fit: BoxFit.cover));
                },
              ),
            ),
            // ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}
