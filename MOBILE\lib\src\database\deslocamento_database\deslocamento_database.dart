// OfflineRequestDatabase._() {
//   _init();
//   _enviar();
// }
// static final OfflineRequestDatabase instance = OfflineRequestDatabase._();

// late Box<String> _box;
// bool _initialized = false;
// bool _waiting = false;

// Future<void> _init() async {
//   while (_waiting) {
//     await Future.delayed(const Duration(milliseconds: 300));
//   }
//   if (!_initialized) {
//     _waiting = true;
//     try {
//       _box = await Hive.openBox<String>('offline_request_map');
//       _initialized = true;
//     } catch (_) {}
//     _waiting = false;
//   }
// }

import 'dart:convert';

import 'package:asuka/asuka.dart' as asuka;
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hive/hive.dart';

import '../../components/fcm_alert_dailog/models/fcm_deslocamento_get.dart';
import '../../components/fcm_alert_dailog/sub_widgets/fcm_show_dialog.dart';
import '../offline_request/offline_request_database.dart';

class DeslocamentoDatabase {
  DeslocamentoDatabase._() {
    _init();
  }

  static final DeslocamentoDatabase instance = DeslocamentoDatabase._();

  late Box<String> _box;
  bool _initialized = false;
  bool _waiting = false;

  Future<void> _init() async {
    while (_waiting) {
      await Future.delayed(const Duration(milliseconds: 300));
    }
    if (!_initialized) {
      _waiting = true;
      try {
        _box = await Hive.openBox<String>('deslocamento_map');
        _initialized = true;
      } catch (_) {}
      _waiting = false;
    }
  }

  Future<List<FcmDeslocamentoGet>> getDeslocamentos() async {
    await _init();
    final valuesString = _box.values.toList();
    final values =
        await compute(OfflineRequestDatabase.convertStringToMap, valuesString);
    return values
        .map((e) {
          return FcmDeslocamentoGet.fromMap(e);
        })
        .toList()
        .where((e) => e.dataHoraFinalizada == null)
        .toList();
  }

  Future<void> save(FcmDeslocamentoGet deslocamento) async {
    await _init();
    final value = _box.get(deslocamento.id.toString());
    if (value == null) {
      if (!deslocamento.uberizado) {
        await asuka.Asuka.showDialog(
          barrierColor: Colors.black.withOpacity(0.8),
          barrierDismissible: false,
          builder: (ctx) {
            return FcmShowDailog(
              message: RemoteMessage(data: deslocamento.toMapKlev()),
            );
          },
        );
      }
    } else {
      final dOld = FcmDeslocamentoGet.fromMap(jsonDecode(value));
      final bool trocouReverso = dOld.reverso != deslocamento.reverso;
      deslocamento = dOld.copyWithDeslocamento(deslocamento);
      if (trocouReverso) {
        deslocamento = deslocamento.semDatas();
      }
    }
    await _box.put(
      deslocamento.id.toString(),
      json.encode(deslocamento.toMap()),
    );
  }

  Future<void> deleteWhereNotIn(
    List<FcmDeslocamentoGet> deslocamentos,
  ) async {
    await _init();
    final idsDeslocamentos = deslocamentos.map((d) => d.id.toString()).toList();
    final keys = _box.keys.toList();
    for (final key in keys) {
      if (!idsDeslocamentos.contains(key)) {
        await _box.delete(key);
      }
    }
  }

  Future<void> deleteById(int idDeslocamento) async {
    await _init();
    await _box.delete(idDeslocamento.toString());
  }

  Future<void> deleteAll() async {
    await _init();
    await _box.clear();
  }
}
