{"inputs": ["C:\\projetos\\octa.log\\MOBILE\\.dart_tool\\flutter_build\\c21f6eee132be27eecfd4a7bbe040f39\\app.dill", "C:\\src\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\icon_tree_shaker.dart", "C:\\src\\flutter\\bin\\internal\\engine.version", "C:\\src\\flutter\\bin\\internal\\engine.version", "C:\\src\\flutter\\bin\\internal\\engine.version", "C:\\src\\flutter\\bin\\internal\\engine.version", "C:\\projetos\\octa.log\\MOBILE\\pubspec.yaml", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\alert.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\cnh.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\deteccao-de-rosto.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\doc_veiculo.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\expedicao.jpg", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\fake_gps.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\foto_acareacao.gif", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\foto_canhoto.gif", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\foto_fachada.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\foto_receita.gif", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\google.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\google_maps.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\google_search.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\image_fmc.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\keyboard_close.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\localization.gif", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\localization.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\locations.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\logo200.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\logo512.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\mapa2.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\mapa_circular.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\maps1.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\map_marker.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\menu_circular.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\message.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\packageLs.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\perfil.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\problem.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\receita.dart.webp", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\transferir.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\user.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\waze.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\waze_2.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\octalog\\whats.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\alert.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\cnh.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\deteccao-de-rosto.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\doc_veiculo.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\expedicao.jpg", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\fake_gps.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\foto_acareacao.gif", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\foto_canhoto.gif", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\foto_fachada.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\foto_receita.gif", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\google.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\google_maps.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\google_search.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\image_fmc.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\keyboard_close.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\localization.gif", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\localization.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\locations.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\logo200.pdn", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\logo512.pdn", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\mapa2.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\mapa_circular.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\maps1.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\map_marker.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\menu_circular.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\message.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\packageLs.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\perfil.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\problem.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\receita.dart.webp", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\transferir.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\user.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\waze.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\waze_2.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\arcargo\\whats.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\alert.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\cnh.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\deteccao-de-rosto.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\doc_veiculo.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\expedicao.jpg", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\fake_gps.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\foto_acareacao.gif", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\foto_canhoto.gif", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\foto_fachada.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\foto_receita.gif", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\google.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\google_maps.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\google_search.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\image_fmc.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\keyboard_close.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\localization.gif", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\localization.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\locations.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\logo200.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\logo512.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\mapa2.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\mapa_circular.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\maps1.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\map_marker.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\menu_circular.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\message.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\packageLs.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\perfil.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\problem.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\receita.dart.webp", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\transferir.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\user.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\waze.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\waze_2.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\connect\\whats.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\alert.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\cnh.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\deteccao-de-rosto.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\doc_veiculo.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\expedicao.jpg", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\fake_gps.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\foto_acareacao.gif", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\foto_canhoto.gif", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\foto_fachada.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\foto_receita.gif", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\google.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\google_maps.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\google_search.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\image_fmc.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\keyboard_close.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\localization.gif", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\localization.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\locations.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\logo200.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\logo512.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\mapa2.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\mapa_circular.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\maps1.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\map_marker.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\menu_circular.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\message.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\packageLs.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\perfil.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\problem.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\receita.dart.webp", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\transferir.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\user.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\waze.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\waze_2.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\images\\rondolog\\whats.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\sac\\acabou-tempo-espera.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\sac\\alert.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\sac\\atendete.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\sac\\horario-nao-atendido.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\sac\\sem-atendentes-disponiveis.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\sac\\sucesso.png", "C:\\projetos\\octa.log\\MOBILE\\assets\\animations\\loading.riv", "C:\\projetos\\octa.log\\MOBILE\\assets\\notification\\music.mp3", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_map-4.0.0\\lib\\assets\\flutter_map_logo.png", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\font_awesome_flutter-10.8.0\\lib\\fonts\\fa-brands-400.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\font_awesome_flutter-10.8.0\\lib\\fonts\\fa-regular-400.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\font_awesome_flutter-10.8.0\\lib\\fonts\\fa-solid-900.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\iconsax-0.0.8\\lib\\assets\\fonts\\iconsax.ttf", "C:\\src\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "C:\\projetos\\octa.log\\MOBILE\\.dart_tool\\flutter_build\\c21f6eee132be27eecfd4a7bbe040f39\\native_assets.json", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\_fe_analyzer_shared-80.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\_flutterfire_internals-1.3.54\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\airplane_mode_checker-3.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\analyzer-7.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\android_id-0.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\archive-4.0.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\args-2.7.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\asuka-2.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\azstore-1.0.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\boolean_selector-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build-2.4.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_config-1.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_daemon-4.0.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_resolvers-2.4.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_runner-2.4.14\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_runner_core-8.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\built_collection-5.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\built_value-8.9.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cached_network_image-3.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cached_network_image_platform_interface-4.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cached_network_image_web-1.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\calendar_date_picker2-2.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera-0.10.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android-0.10.10+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_avfoundation-0.9.18+13\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_camera-3.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_platform_interface-2.10.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_web-0.3.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\change_app_package_name-1.5.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\charcode-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\checked_yaml-2.0.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cli_util-0.4.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\code_builder-4.10.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\connectivity_plus-6.1.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\connectivity_plus_platform_interface-2.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cross_file-0.3.4+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dart_style-3.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dart_webrtc-1.5.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus-11.3.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus_platform_interface-7.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dio-5.8.0+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dio_web_adapter-2.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dotted_decoration-2.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.26.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift_dev-2.26.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\equatable-2.0.7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fake_async-1.3.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-9.2.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_linux-0.9.3+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_macos-0.9.4+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_windows-0.9.3+4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_core-3.13.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_core_platform_interface-5.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_core_web-2.22.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_messaging-15.2.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_messaging_platform_interface-4.6.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_messaging_web-3.10.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_remote_config-5.4.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_remote_config_platform_interface-1.5.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_remote_config_web-1.8.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fixnum-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_activity_recognition-4.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_keyboard_visibility-6.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_keyboard_visibility_linux-1.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_keyboard_visibility_macos-1.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_keyboard_visibility_platform_interface-2.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_keyboard_visibility_web-2.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_keyboard_visibility_windows-1.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_launcher_icons-0.14.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_linkify-6.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_lints-5.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_map-4.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_pdfview-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_plugin_android_lifecycle-2.0.27\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_ringtone_player-4.0.0+4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_svg-2.0.9\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.12.12+hotfix.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\font_awesome_flutter-10.8.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\frontend_server_client-4.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator-13.0.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_android-4.6.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_apple-2.3.13\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_platform_interface-4.2.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_web-4.1.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_windows-0.2.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\glob-2.1.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-4.0.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_mlkit_commons-0.11.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_mlkit_face_detection-0.13.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\graphs-2.3.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive_flutter-1.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-0.13.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_multi_server-3.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\iconsax-0.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image-4.5.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker-1.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_android-0.8.12+22\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_for_web-3.0.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_ios-0.8.12+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_linux-0.2.1+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_macos-0.2.1+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_windows-0.2.1+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\in_app_review-2.0.10\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\in_app_review_platform_interface-2.0.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.19.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\io-1.0.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\js-0.7.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\latlong2-0.8.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker-10.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.9\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\linkify-5.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lints-5.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lists-1.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\logger-2.5.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\logging-1.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mailer-6.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\map_fields-0.0.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\matcher-0.12.17\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.16.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mgrs_dart-2.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-2.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-6.0.7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\nm-0.5.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ntp-2.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\octo_image-2.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\onesignal_flutter-5.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_config-2.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_info_plus-8.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_info_plus_platform_interface-3.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_parsing-1.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider-2.1.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.16\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pedantic-1.11.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler-11.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_android-12.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_apple-9.4.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_html-0.1.3+5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_windows-0.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\polylabel-1.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pool-1.5.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\posix-6.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\proj4dart-2.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pub_semver-2.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pubspec_parse-1.5.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\recase-4.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rive-0.13.20\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rive_common-0.4.15\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.27.7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\screenshot-3.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_web-2.4.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shelf-1.4.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shelf_web_socket-2.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shorebird_code_push-2.0.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\signalr_core-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\signature-5.5.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_gen-2.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\speech_to_text-7.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\speech_to_text_platform_interface-2.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sprintf-7.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_android-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_darwin-2.4.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_platform_interface-2.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3_flutter_libs-0.5.32\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlparser-0.41.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sse_client-0.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_transform-2.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\system_info_plus-0.0.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\test_api-0.7.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\timing-1.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\tuple-2.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\unicode-0.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\universal_io-2.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_android-6.3.15\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_ios-6.3.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_macos-3.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_web-2.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_windows-3.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_graphics-1.1.10+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_graphics_codec-1.1.10+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_graphics_compiler-1.1.10+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vm_service-14.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\watcher-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket_channel-2.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webrtc_interface-1.2.2+hotfix.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter-4.10.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_android-4.3.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_platform_interface-2.10.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\webview_flutter_wkwebview-3.18.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.12.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32_registry-2.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\wkt_parser-2.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xdg_directories-1.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\yaml-3.1.3\\LICENSE", "C:\\src\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "C:\\src\\flutter\\packages\\flutter\\LICENSE", "C:\\projetos\\octa.log\\MOBILE\\DOES_NOT_EXIST_RERUN_FOR_WILDCARD1001749204"], "outputs": ["C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\vm_snapshot_data", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\isolate_snapshot_data", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\kernel_blob.bin", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/alert.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/cnh.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/deteccao-de-rosto.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/doc_veiculo.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/expedicao.jpg", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/fake_gps.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/foto_acareacao.gif", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/foto_canhoto.gif", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/foto_fachada.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/foto_receita.gif", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/google.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/google_maps.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/google_search.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/image_fmc.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/keyboard_close.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/localization.gif", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/localization.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/locations.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/logo200.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/logo512.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/mapa2.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/mapa_circular.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/maps1.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/map_marker.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/menu_circular.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/message.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/packageLs.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/perfil.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/problem.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/receita.dart.webp", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/transferir.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/user.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/waze.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/waze_2.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/octalog/whats.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/alert.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/cnh.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/deteccao-de-rosto.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/doc_veiculo.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/expedicao.jpg", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/fake_gps.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/foto_acareacao.gif", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/foto_canhoto.gif", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/foto_fachada.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/foto_receita.gif", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/google.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/google_maps.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/google_search.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/image_fmc.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/keyboard_close.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/localization.gif", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/localization.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/locations.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/logo200.pdn", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/logo512.pdn", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/mapa2.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/mapa_circular.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/maps1.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/map_marker.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/menu_circular.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/message.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/packageLs.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/perfil.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/problem.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/receita.dart.webp", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/transferir.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/user.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/waze.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/waze_2.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/arcargo/whats.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/alert.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/cnh.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/deteccao-de-rosto.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/doc_veiculo.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/expedicao.jpg", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/fake_gps.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/foto_acareacao.gif", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/foto_canhoto.gif", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/foto_fachada.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/foto_receita.gif", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/google.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/google_maps.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/google_search.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/image_fmc.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/keyboard_close.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/localization.gif", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/localization.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/locations.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/logo200.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/logo512.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/mapa2.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/mapa_circular.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/maps1.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/map_marker.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/menu_circular.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/message.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/packageLs.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/perfil.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/problem.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/receita.dart.webp", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/transferir.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/user.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/waze.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/waze_2.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/connect/whats.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/alert.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/cnh.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/deteccao-de-rosto.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/doc_veiculo.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/expedicao.jpg", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/fake_gps.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/foto_acareacao.gif", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/foto_canhoto.gif", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/foto_fachada.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/foto_receita.gif", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/google.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/google_maps.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/google_search.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/image_fmc.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/keyboard_close.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/localization.gif", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/localization.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/locations.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/logo200.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/logo512.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/mapa2.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/mapa_circular.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/maps1.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/map_marker.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/menu_circular.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/message.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/packageLs.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/perfil.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/problem.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/receita.dart.webp", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/transferir.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/user.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/waze.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/waze_2.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/images/rondolog/whats.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/sac/acabou-tempo-espera.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/sac/alert.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/sac/atendete.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/sac/horario-nao-atendido.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/sac/sem-atendentes-disponiveis.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/sac/sucesso.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/animations/loading.riv", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\assets/notification/music.mp3", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\packages/flutter_map/lib/assets/flutter_map_logo.png", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\packages/iconsax/lib/assets/fonts/iconsax.ttf", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\shaders/ink_sparkle.frag", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\AssetManifest.json", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\AssetManifest.bin", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\FontManifest.json", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\NOTICES.Z", "C:\\projetos\\octa.log\\MOBILE\\build\\app\\intermediates\\flutter\\arcargoDebug\\flutter_assets\\NativeAssetsManifest.json"]}