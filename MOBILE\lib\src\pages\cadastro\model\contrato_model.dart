import 'package:map_fields/map_fields.dart';
import 'package:octalog/src/utils/extesion.dart';

class ContratoModel {
  final int? idTipoAgente;
  final String? tipoAgente;
  final String? prestador;
  final bool prestadorAtivo;
  final String? contrato;
  final String nomeAgente;
  final String cpf;
  final String telefone;
  final String email;
  final String cep;
  final String endereco;
  final String bairro;
  final String cidade;
  final String numero;
  final String uf;
  final String? fotoUsuario;
  final String? fotoCNH;
  final String? fotoDocumentoVeiculo;
  final String? fotoComprovanteEndereco;
  final int iDTipoVeiculo;
  final DateTime? dataValidadeCNH;
  final DateTime? dataConfirmouCadastro;
  final bool agentePrincipal;
  final String banco;
  final String agencia;
  final String conta;
  final String titularConta;
  final String pix;
  final String titularPix;
  final List<int> transportadoras;

  ContratoModel({
    required this.idTipoAgente,
    required this.tipoAgente,
    required this.prestador,
    required this.contrato,
    required this.prestadorAtivo,
    required this.nomeAgente,
    required this.cpf,
    required this.telefone,
    required this.email,
    required this.cep,
    required this.endereco,
    required this.bairro,
    required this.cidade,
    required this.numero,
    required this.uf,
    this.fotoUsuario,
    this.fotoCNH,
    this.fotoDocumentoVeiculo,
    this.fotoComprovanteEndereco,
    required this.iDTipoVeiculo,
    required this.dataValidadeCNH,
    required this.dataConfirmouCadastro,
    required this.agentePrincipal,
    required this.banco,
    required this.agencia,
    required this.conta,
    required this.titularConta,
    required this.pix,
    required this.titularPix,
    required this.transportadoras,
  });

  ContratoModel setTransportadoras(List<int> transportadoras) {
    return _copyWith(transportadoras: transportadoras);
  }

  ContratoModel _copyWith({
    int? idTipoAgente,
    String? tipoAgente,
    String? prestador,
    String? contrato,
    bool? prestadorAtivo,
    String? nomeAgente,
    String? cpf,
    String? telefone,
    String? email,
    String? cep,
    String? endereco,
    String? bairro,
    String? cidade,
    String? numero,
    String? uf,
    String? fotoUsuario,
    String? fotoCNH,
    String? fotoDocumentoVeiculo,
    String? fotoComprovanteEndereco,
    int? iDTipoVeiculo,
    DateTime? dataValidadeCNH,
    DateTime? dataConfirmouCadastro,
    bool? agentePrincipal,
    String? banco,
    String? agencia,
    String? conta,
    String? titularConta,
    String? pix,
    String? titularPix,
    List<int>? transportadoras,
  }) {
    return ContratoModel(
      idTipoAgente: idTipoAgente ?? this.idTipoAgente,
      tipoAgente: tipoAgente ?? this.tipoAgente,
      prestador: prestador ?? this.prestador,
      contrato: contrato ?? this.contrato,
      prestadorAtivo: prestadorAtivo ?? this.prestadorAtivo,
      nomeAgente: nomeAgente ?? this.nomeAgente,
      cpf: cpf ?? this.cpf,
      telefone: telefone ?? this.telefone,
      email: email ?? this.email,
      cep: cep ?? this.cep,
      endereco: endereco ?? this.endereco,
      bairro: bairro ?? this.bairro,
      cidade: cidade ?? this.cidade,
      numero: numero ?? this.numero,
      uf: uf ?? this.uf,
      fotoUsuario: fotoUsuario ?? this.fotoUsuario,
      fotoCNH: fotoCNH ?? this.fotoCNH,
      fotoDocumentoVeiculo: fotoDocumentoVeiculo ?? this.fotoDocumentoVeiculo,
      fotoComprovanteEndereco:
          fotoComprovanteEndereco ?? this.fotoComprovanteEndereco,
      iDTipoVeiculo: iDTipoVeiculo ?? this.iDTipoVeiculo,
      dataValidadeCNH: dataValidadeCNH ?? this.dataValidadeCNH,
      dataConfirmouCadastro:
          dataConfirmouCadastro ?? this.dataConfirmouCadastro,
      agentePrincipal: agentePrincipal ?? this.agentePrincipal,
      banco: banco ?? this.banco,
      agencia: agencia ?? this.agencia,
      conta: conta ?? this.conta,
      titularConta: titularConta ?? this.titularConta,
      pix: pix ?? this.pix,
      titularPix: titularPix ?? this.titularPix,
      transportadoras: transportadoras ?? this.transportadoras,
    );
  }

  factory ContratoModel.fromJson(Map<String, dynamic> json) {
    final MapFields contrato = MapFields.load(json);
    return ContratoModel(
      idTipoAgente: contrato.getInt('IDTipoAgente', 0),
      tipoAgente: contrato.getString('TipoAgente', ''),
      prestador: contrato.getString('Prestador', ''),
      contrato: contrato.getString('Contrato', ''),
      prestadorAtivo: contrato.getBool('PrestadorAtivo', false),
      nomeAgente: contrato.getString('NomeAgente', ''),
      cpf: contrato.getString('CPF', ''),
      telefone: contrato.getString('Telefone', ''),
      email: contrato.getString('Email', ''),
      cep: contrato.getString('Cep', ''),
      endereco: contrato.getString('Endereco', ''),
      bairro: contrato.getString('Bairro', ''),
      cidade: contrato.getString('Cidade', ''),
      numero: contrato.getString('Numero', ''),
      uf: contrato.getString('UF', 'SP'),
      fotoUsuario: contrato.getStringNullable('FotoUsuario'),
      fotoCNH: contrato.getStringNullable('FotoCNH'),
      fotoDocumentoVeiculo: contrato.getStringNullable('FotoDocumentoVeiculo'),
      fotoComprovanteEndereco: contrato.getStringNullable(
        'FotoComprovanteEndereco',
      ),
      iDTipoVeiculo: contrato.getInt('IDTipoVeiculo', 0),
      dataValidadeCNH: contrato.getDateTimeNullable('DataValidadeCNH'),
      dataConfirmouCadastro: contrato.getDateTimeNullable(
        'DataConfirmouCadastro',
      ),
      agentePrincipal: contrato.getBool('IsAgentePrincipal', false),
      banco: contrato.getString('Banco', ''),
      agencia: contrato.getString('Agencia', ''),
      conta: contrato.getString('Conta', ''),
      titularConta: contrato.getString('TitularConta', ''),
      pix: contrato.getString('Pix', ''),
      titularPix: contrato.getString('TitularPix', ''),
      transportadoras: contrato.getList('Transportadoras', []).cast<int>(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'IDTipoAgente': idTipoAgente,
      'TipoAgente': tipoAgente,
      'Prestador': prestador,
      'Contrato': contrato,
      'PrestadorAtivo': prestadorAtivo,
      'NomeAgente': nomeAgente,
      'CPF': cpf,
      'Telefone': telefone,
      'Email': email,
      'Cep': cep,
      'Endereco': endereco,
      'Bairro': bairro,
      'Cidade': cidade,
      'Numero': numero,
      'UF': uf,
      'FotoUsuario': fotoUsuario,
      'FotoCNH': fotoCNH,
      'FotoDocumentoVeiculo': fotoDocumentoVeiculo,
      'fotoComprovanteEndereco': fotoComprovanteEndereco,
      'IDTipoVeiculo': iDTipoVeiculo,
      'DataValidadeCNH':
          dataValidadeCNH?.dataHoraServidorFomart.toIso8601String(),
      'DataConfirmouCadastro':
          dataConfirmouCadastro?.dataHoraServidorFomart.toIso8601String(),
      'IsAgentePrincipal': agentePrincipal,
      'Banco': banco,
      'Agencia': agencia,
      'Conta': conta,
      'TitularConta': titularConta,
      'Pix': pix,
      'TitularPix': titularPix,
      'Transportadoras': transportadoras,
    };
  }
}
