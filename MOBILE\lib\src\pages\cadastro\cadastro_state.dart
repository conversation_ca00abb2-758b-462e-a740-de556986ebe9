import 'package:flutter/material.dart';
import 'package:octalog/src/pages/cadastro/enum/enum_page_fotos.dart';
import 'package:octalog/src/pages/cadastro/model/contrato_model.dart';
import 'package:octalog/src/pages/cadastro/model/model_veiculos.dart';

import 'enum/enum_page_cadastro.dart';

class CadastroState {
  final EnumCadastroPages titlesPage;
  final EnumCadastroFoto fotoPage;
  final PageController pageController;
  ContratoModel? contratoModel;
  final bool isLoadingcarregartela;
  final List<Veiculo> veiculos;
  final bool buttonAceiteContratoLoading;
  final bool isLoadingBuscaCep;
  final bool contaCorrente;
  CadastroState({
    required this.fotoPage,
    required this.titlesPage,
    required this.pageController,
    this.contratoModel,
    required this.isLoadingcarregartela,
    required this.veiculos,
    required this.buttonAceiteContratoLoading,
    required this.isLoadingBuscaCep,
    required this.contaCorrente,
  });

  factory CadastroState.initial() {
    return CadastroState(
      fotoPage: EnumCadastroFoto.fotoCnh,
      titlesPage: EnumCadastroPages.notificacaoTransportadora,
      pageController: PageController(initialPage: 0),
      isLoadingcarregartela: false,
      veiculos: [Veiculo(id: 0, nome: '')],
      contratoModel: null,
      buttonAceiteContratoLoading: false,
      isLoadingBuscaCep: false,
      contaCorrente: true,
    );
  }

  CadastroState _copyWith({
    EnumCadastroPages? titlesPage,
    EnumCadastroFoto? fotoPage,
    PageController? pageController,
    ContratoModel? contratoModel,
    bool? isLoadingcarregartela,
    bool? isAceitecontrato,
    List<Veiculo>? veiculos,
    bool? buttonAceiteContratoLoading,
    bool? isLoadingBuscaCep,
    bool? contaCorrente,
  }) {
    return CadastroState(
      titlesPage: titlesPage ?? this.titlesPage,
      fotoPage: fotoPage ?? this.fotoPage,
      pageController: pageController ?? this.pageController,
      contratoModel: contratoModel ?? this.contratoModel,
      isLoadingcarregartela: isLoadingcarregartela ?? this.isLoadingcarregartela,
      veiculos: veiculos ?? this.veiculos,
      buttonAceiteContratoLoading: buttonAceiteContratoLoading ?? this.buttonAceiteContratoLoading,
      isLoadingBuscaCep: isLoadingBuscaCep ?? this.isLoadingBuscaCep,
      contaCorrente: contaCorrente ?? this.contaCorrente,
    );
  }

  setFotoPage(EnumCadastroFoto value) => _copyWith(fotoPage: value);

  setTitlesPage(EnumCadastroPages value) => _copyWith(titlesPage: value);
  setContratoModel(ContratoModel value) => _copyWith(contratoModel: value);
  setisLoadingcarregartela(bool value) => _copyWith(isLoadingcarregartela: value);
  setisAceitecontrato(bool value) => _copyWith(isAceitecontrato: value);
  setPageController(PageController value) => _copyWith(pageController: value);
  setVeiculos(List<Veiculo> value) => _copyWith(veiculos: value);
  setContratoModelParte(ContratoModel? value) => _copyWith(contratoModel: value);
  setButtonAceiteContratoLoading(bool value) => _copyWith(buttonAceiteContratoLoading: value);
  setisLoadingBuscaCep(bool value) => _copyWith(isLoadingBuscaCep: value);
  setContaCorrente(bool value) => _copyWith(contaCorrente: value);
}
