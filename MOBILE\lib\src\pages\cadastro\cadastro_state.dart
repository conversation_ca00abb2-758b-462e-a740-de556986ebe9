import 'package:flutter/material.dart';
import 'package:octalog/src/pages/cadastro/enum/enum_page_fotos.dart';
import 'package:octalog/src/pages/cadastro/model/contrato_model.dart';
import 'package:octalog/src/pages/cadastro/model/contrato_novo.dart';
import 'package:octalog/src/pages/cadastro/model/model_veiculos.dart';

import 'enum/enum_page_cadastro.dart';

class CadastroState {
  final EnumCadastroPages titlesPage;
  final EnumCadastroFoto fotoPage;
  final PageController pageController;
  ContratoModel? contratoModel;
  final bool isLoadingcarregartela;
  final List<Veiculo> veiculos;
  final bool buttonAceiteContratoLoading;
  final bool isLoadingBuscaCep;
  final bool contaCorrente;
  final List<ContratoNovo> contratos;
  final List<int> transportadoras;
  CadastroState({
    required this.fotoPage,
    required this.titlesPage,
    required this.pageController,
    this.contratoModel,
    required this.isLoadingcarregartela,
    required this.veiculos,
    required this.buttonAceiteContratoLoading,
    required this.isLoadingBuscaCep,
    required this.contaCorrente,
    required this.contratos,
    required this.transportadoras,
  });

  factory CadastroState.initial() {
    return CadastroState(
      fotoPage: EnumCadastroFoto.fotoCnh,
      titlesPage: EnumCadastroPages.notificacaoTransportadora,
      pageController: PageController(initialPage: 0),
      isLoadingcarregartela: false,
      veiculos: [Veiculo(id: 0, nome: '')],
      contratoModel: null,
      buttonAceiteContratoLoading: false,
      isLoadingBuscaCep: false,
      contaCorrente: true,
      contratos: [],
      transportadoras: [],
    );
  }

  CadastroState aceitarContrato(bool value, int idTransportadora) {
    final transportadoras = [...this.transportadoras];
    if (value) {
      transportadoras.add(idTransportadora);
    } else {
      transportadoras.remove(idTransportadora);
    }
    final contrato = contratoModel?.setTransportadoras(transportadoras);
    return _copyWith(transportadoras: transportadoras, contratoModel: contrato);
  }

  bool get todasTransportadorasForamAceitas {
    return contratos.every((c) => transportadoras.contains(c.idTransportadora));
  }

  CadastroState _copyWith({
    EnumCadastroPages? titlesPage,
    EnumCadastroFoto? fotoPage,
    PageController? pageController,
    ContratoModel? contratoModel,
    bool? isLoadingcarregartela,
    bool? isAceitecontrato,
    List<Veiculo>? veiculos,
    bool? buttonAceiteContratoLoading,
    bool? isLoadingBuscaCep,
    bool? contaCorrente,
    List<ContratoNovo>? contratos,
    List<int>? transportadoras,
  }) {
    return CadastroState(
      titlesPage: titlesPage ?? this.titlesPage,
      fotoPage: fotoPage ?? this.fotoPage,
      pageController: pageController ?? this.pageController,
      contratoModel: contratoModel ?? this.contratoModel,
      isLoadingcarregartela:
          isLoadingcarregartela ?? this.isLoadingcarregartela,
      veiculos: veiculos ?? this.veiculos,
      buttonAceiteContratoLoading:
          buttonAceiteContratoLoading ?? this.buttonAceiteContratoLoading,
      isLoadingBuscaCep: isLoadingBuscaCep ?? this.isLoadingBuscaCep,
      contaCorrente: contaCorrente ?? this.contaCorrente,
      contratos: contratos ?? this.contratos,
      transportadoras: transportadoras ?? this.transportadoras,
    );
  }

  setFotoPage(EnumCadastroFoto value) => _copyWith(fotoPage: value);

  setTitlesPage(EnumCadastroPages value) => _copyWith(titlesPage: value);
  setContratoModel(ContratoModel value) => _copyWith(contratoModel: value);
  setisLoadingcarregartela(bool value) =>
      _copyWith(isLoadingcarregartela: value);
  setisAceitecontrato(bool value) => _copyWith(isAceitecontrato: value);
  setPageController(PageController value) => _copyWith(pageController: value);
  setVeiculos(List<Veiculo> value) => _copyWith(veiculos: value);
  setContratoModelParte(ContratoModel? value) =>
      _copyWith(contratoModel: value);
  setButtonAceiteContratoLoading(bool value) =>
      _copyWith(buttonAceiteContratoLoading: value);
  setisLoadingBuscaCep(bool value) => _copyWith(isLoadingBuscaCep: value);
  setContaCorrente(bool value) => _copyWith(contaCorrente: value);
  setContratos(List<ContratoNovo> value) {
    final transportadoras =
        value
            .where((e) => e.dataConfirmouCadastro != null)
            .map((e) => e.idTransportadora)
            .toList();
    return _copyWith(contratos: value, transportadoras: transportadoras);
  }
}
