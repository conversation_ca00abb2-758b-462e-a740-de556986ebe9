import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../models_new/tag_new.dart';

class ReentregaWidget extends StatelessWidget {
  final double scale;
  final List<TagNew> tags;
  const ReentregaWidget({
    super.key,
    this.scale = 1,
    required this.tags,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: tags.map((e) {
          final color = e.getColor();
          final title = e.descricao;
          return Container(
            //alignment: Alignment.center,
            //margin: const EdgeInsets.only(right: 10),
            padding: const EdgeInsets.only(right: 15),
            // decoration: BoxDecoration(
            //   //color: color,
            //   borderRadius: BorderRadius.circular(3),
            // ),
            child: RichText(
              overflow: TextOverflow.clip,
              //textAlign: TextAlign.center,
              text: TextSpan(
                style: GoogleFonts.roboto(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: color,
                ),
                text: title,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
