<variant
    name="octalogRelease"
    package="com.octalog"
    minSdkVersion="21"
    targetSdkVersion="35"
    shrinking="true"
    mergedManifest="C:\projetos\octa.log\MOBILE\build\app\intermediates\merged_manifest\octalogRelease\processOctalogReleaseMainManifest\AndroidManifest.xml"
    manifestMergeReport="C:\projetos\octa.log\MOBILE\build\app\outputs\logs\manifest-merger-octalog-release-report.txt"
    proguardFiles="C:\projetos\octa.log\MOBILE\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.0;C:\src\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro;proguard-rules.pro"
    partialResultsDir="C:\projetos\octa.log\MOBILE\build\app\intermediates\lint_vital_partial_results\octalogRelease\lintVitalAnalyzeOctalogRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\octalog\java;src\release\java;src\octalogRelease\java;src\main\kotlin;src\octalog\kotlin;src\release\kotlin;src\octalogRelease\kotlin"
        resDirectories="src\main\res;src\octalog\res;src\release\res;src\octalogRelease\res"
        assetsDirectories="src\main\assets;src\octalog\assets;src\release\assets;src\octalogRelease\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <resValues>
    <resValue
        type="string"
        name="app_name"
        value="Octalog" />
  </resValues>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <artifact
      classOutputs="C:\projetos\octa.log\MOBILE\build\app\intermediates\javac\octalogRelease\compileOctalogReleaseJavaWithJavac\classes;C:\projetos\octa.log\MOBILE\build\app\tmp\kotlin-classes\octalogRelease;C:\projetos\octa.log\MOBILE\build\app\kotlinToolingMetadata;C:\projetos\octa.log\MOBILE\build\app\intermediates\compile_and_runtime_not_namespaced_r_class_jar\octalogRelease\processOctalogReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.octalog"
      generatedSourceFolders="C:\projetos\octa.log\MOBILE\build\app\generated\ap_generated_sources\octalogRelease\out;C:\projetos\octa.log\MOBILE\build\app\generated\source\buildConfig\octalog\release"
      generatedResourceFolders="C:\projetos\octa.log\MOBILE\build\app\generated\res\resValues\octalog\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6acd1316a909a3b9467814415d8b5421\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
