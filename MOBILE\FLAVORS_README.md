# Configuração de Flavors - Octalog

Este documento descreve como usar o sistema de flavors implementado no projeto Octalog para suportar múltiplos clientes com personalização whitelabel.

## Flavors Disponíveis

- **octalog** - <PERSON><PERSON><PERSON> (azul/laranja)
- **arcargo** - <PERSON><PERSON><PERSON> ArCargo (verde)
- **connect** - <PERSON><PERSON><PERSON> Connect (azul)
- **rondolog** - Cliente RondoLog (laranja)

## Estrutura de Arquivos

### Configuração Principal
- `lib/src/config/flavor_config.dart` - Configuração central dos flavors
- `lib/src/config/flavor_helper.dart` - Helper para detectar flavor atual
- `lib/src/utils/theme_colors.dart` - Sistema de cores baseado em tema
- `lib/src/components/flavor_image/flavor_image.dart` - Widget para imagens por flavor

### Assets por Flavor
```
assets/images/
├── octalog/
├── arcargo/
├── connect/
└── rondolog/
```

### Scripts de Build
- `scripts/build_flavors.sh` - Script para Linux/Mac
- `scripts/build_flavors.bat` - Script para Windows

## Como Usar

### 1. Build para Android

#### APK
```bash
# Linux/Mac
./scripts/build_flavors.sh octalog apk

# Windows
scripts\build_flavors.bat octalog apk

# Comando direto
flutter build apk --release --flavor octalog --dart-define=FLAVOR=octalog
```

#### AAB (App Bundle)
```bash
# Linux/Mac
./scripts/build_flavors.sh arcargo appbundle

# Windows
scripts\build_flavors.bat arcargo appbundle

# Comando direto
flutter build appbundle --release --flavor arcargo --dart-define=FLAVOR=arcargo
```

### 2. Build para iOS
```bash
flutter build ios --release --flavor connect --dart-define=FLAVOR=connect
```

### 3. Executar em Debug
```bash
flutter run --flavor rondolog --dart-define=FLAVOR=rondolog
```

## Personalização por Flavor

### Cores
Cada flavor tem seu próprio tema definido em `FlavorConfig`:
- **octalog**: Azul/laranja (cores originais)
- **arcargo**: Verde
- **connect**: Azul
- **rondolog**: Laranja

### Assets
Cada flavor carrega assets de sua pasta específica:
- Logos: `logo200.png`, `logo512.png`
- Imagens de interface: `foto_acareacao.gif`, `foto_receita.gif`, etc.

### Nomes de App
- **octalog**: "Octalog"
- **arcargo**: "ArCargo"
- **connect**: "Connect"
- **rondolog**: "RondoLog"

### Application IDs
- **octalog**: `com.octalog`
- **arcargo**: `com.arcargo`
- **connect**: `com.connect`
- **rondolog**: `com.rondolog`

## Migração de Código

### Cores
**Antes:**
```dart
import 'package:octalog/src/utils/colors.dart';

Container(
  color: ThemeColors.customOrange(context),
  child: Text(
    'Texto',
    style: TextStyle(color: ThemeColors.customWhite(context)),
  ),
)
```

**Depois:**
```dart
import 'package:octalog/src/utils/theme_colors.dart';

Container(
  color: context.customOrange, // ou Theme.of(context).colorScheme.primary
  child: Text(
    'Texto',
    style: TextStyle(color: context.customWhite),
  ),
)
```

### Imagens
**Antes:**
```dart
Image.asset('assets/images/logo_ls.png')
```

**Depois:**
```dart
import 'package:octalog/src/components/flavor_image/flavor_image.dart';

FlavorImage(assetName: 'logo200.png')
// ou
FlavorLogo(width: 200, height: 200)
```

## Estrutura de Build

### Android
Os flavors são configurados em `android/app/build.gradle.kts`:
- Cada flavor tem seu próprio `applicationId`
- Nome do app é definido via `resValue`
- BuildConfig inclui `FLAVOR_NAME`

### iOS
Para iOS, os flavors são detectados via `--dart-define=FLAVOR=nome`

## Adicionando Novo Flavor

1. **Adicionar enum em `FlavorConfig`:**
```dart
enum FlavorType {
  octalog,
  arcargo,
  connect,
  rondolog,
  novoCliente, // Adicionar aqui
}
```

2. **Criar configuração no `_createFlavor`:**
```dart
case FlavorType.novoCliente:
  return FlavorConfig._(
    flavor: flavor,
    name: 'Novo Cliente',
    applicationId: 'com.novocliente',
    theme: _createNovoClienteTheme(),
    assetPath: 'assets/images/novocliente/',
    assetOverrides: _getNovoClienteAssets(),
  );
```

3. **Adicionar flavor no Android (`build.gradle.kts`):**
```kotlin
create("novocliente") {
    dimension = "client"
    applicationId = "com.novocliente"
    resValue("string", "app_name", "Novo Cliente")
    buildConfigField("String", "FLAVOR_NAME", "\"novocliente\"")
}
```

4. **Criar pasta de assets:**
```
assets/images/novocliente/
├── logo200.png
├── logo512.png
├── foto_acareacao.gif
└── ...
```

5. **Atualizar pubspec.yaml:**
```yaml
assets:
  - assets/images/novocliente/
```

## Ícones Personalizados por Flavor

### Configuração
Cada flavor possui seu próprio arquivo de configuração de ícones:
- `flutter_launcher_icons-octalog.yaml`
- `flutter_launcher_icons-arcargo.yaml`
- `flutter_launcher_icons-connect.yaml`
- `flutter_launcher_icons-rondolog.yaml`

### Geração de Ícones

#### Método Automático (Recomendado)
```bash
# Linux/Mac
./scripts/generate_icons.sh [flavor]

# Windows
scripts\generate_icons.bat [flavor]

# Exemplos
./scripts/generate_icons.sh octalog
./scripts/generate_icons.sh all  # Gera para todos os flavors
```

#### Método Manual
```bash
# Para um flavor específico
flutter pub run flutter_launcher_icons:main -f flutter_launcher_icons-octalog.yaml

# Para todos os flavors
flutter pub run flutter_launcher_icons:main -f flutter_launcher_icons-octalog.yaml
flutter pub run flutter_launcher_icons:main -f flutter_launcher_icons-arcargo.yaml
flutter pub run flutter_launcher_icons:main -f flutter_launcher_icons-connect.yaml
flutter pub run flutter_launcher_icons:main -f flutter_launcher_icons-rondolog.yaml
```

### Workflow Recomendado
1. **Antes de buildar um flavor**, gere os ícones correspondentes:
   ```bash
   ./scripts/generate_icons.sh octalog
   flutter build apk --release --flavor octalog --dart-define=FLAVOR=octalog
   ```

2. **Para desenvolvimento**, gere os ícones do flavor que está testando:
   ```bash
   ./scripts/generate_icons.sh connect
   flutter run --flavor connect --dart-define=FLAVOR=connect
   ```

### Estrutura de Ícones
- **Android**: Ícones são gerados em `android/app/src/main/res/mipmap-*/`
- **iOS**: Ícones são gerados em `ios/Runner/Assets.xcassets/AppIcon.appiconset/`
- **Web**: Ícones são gerados em `web/icons/` (opcional)

### Personalização de Ícones
Para alterar o ícone de um flavor:
1. Substitua o arquivo `assets/images/[flavor]/logo512.png`
2. Execute: `./scripts/generate_icons.sh [flavor]`
3. Faça o build do flavor

## Troubleshooting

### Erro de Asset não encontrado
- Verificar se a pasta do flavor existe em `assets/images/`
- Verificar se o asset está listado no `pubspec.yaml`
- Verificar se o nome do arquivo está correto no `FlavorConfig`

### Erro de Build
- Limpar build: `flutter clean && flutter pub get`
- Verificar se o flavor está definido corretamente no `build.gradle.kts`
- Verificar se o `--dart-define=FLAVOR=nome` está sendo passado

### Cores não aplicadas
- Verificar se o widget está usando `context.customColor` ou `Theme.of(context)`
- Verificar se o `FlavorHelper.initializeFlavor()` está sendo chamado no `main()`

### Erro na geração de ícones
- Verificar se o arquivo de logo existe: `assets/images/[flavor]/logo512.png`
- Verificar se o `flutter_launcher_icons` está instalado: `flutter pub get`
- Verificar se a extensão do arquivo está correta no YAML
- Para arcargo, usar `.pdn` em vez de `.png`
