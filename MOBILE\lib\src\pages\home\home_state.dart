// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:latlong2/latlong.dart';
import 'package:octalog/src/components/fcm_alert_dailog/models/fcm_deslocamento_get.dart';
import 'package:octalog/src/models/id_status_atividade_enum.dart';
import 'package:octalog/src/models_new/endereco_new.dart';
import 'package:octalog/src/pages/home/<USER>/custom_enum_navigation.dart';
import 'package:octalog/src/pages/home/<USER>/transferencia/lojas_model.dart';

class HomeState {
  final bool isOnline;
  final bool isOnlineLoading;
  final DateTime? dataForceUltimaSincronizacao;
  final bool listColetaAdd;
  final String search;
  final bool isBarcode;
  final bool loading;
  final List<EnderecoNew> atividadesCompletas;
  final IdStatusAtividadeEnum statusAtividade;
  final HomeNavigationEnum pageSelected;
  final bool havenegativa;
  final bool carregouInicio;
  final List<FcmDeslocamentoGet> deslocamentosColeta;
  final LatLng? fimRota;
  final bool rodandoFetchAtividades;
  final bool isExpedicao;
  final TransferenciaModel? lojasTransferencia;
  final LojasTransferencia? lojaOrigem;
  final LojasTransferencia? lojaDestino;
  final bool? transferenciaComRetorno;
  final bool isLoadingTelaTransferencia;
  final bool loadingButtonTransferencia;
  HomeState({
    required this.isOnline,
    required this.isOnlineLoading,
    required this.dataForceUltimaSincronizacao,
    required this.listColetaAdd,
    required this.search,
    required this.isBarcode,
    required this.loading,
    required this.atividadesCompletas,
    required this.statusAtividade,
    required this.pageSelected,
    required this.havenegativa,
    required this.carregouInicio,
    required this.deslocamentosColeta,
    required this.fimRota,
    required this.rodandoFetchAtividades,
    required this.isExpedicao,
    required this.isLoadingTelaTransferencia,
    this.transferenciaComRetorno,
    this.lojasTransferencia,
    this.lojaOrigem,
    this.lojaDestino,
    required this.loadingButtonTransferencia,
  });

  List<EnderecoNew> get atividadesFiltradasSomenteTexto {
    List<EnderecoNew> atividades = atividadesCompletas;
    if (search.isNotEmpty) {
      atividades =
          atividades.where((atividade) => atividade.find(search)).toList();
    }
    return atividades;
  }

  Map<int, int> get countStatusAtividade {
    Map<int, int> count = {
      0: 0,
      1: 0,
      2: 0,
      3: 0,
    };
    for (var key in count.keys) {
      final ats = atividadesFiltradasSomenteTexto
          .where((at) => at.status.map((e) => e.index).contains(key))
          .toList();
      final count2 = ats.map((e) {
        return e.whereStatus(IdStatusAtividadeEnum.values[key]);
      }).toList();

      count[key] = count2.fold<int>(0, (p, e) => p + e.volumesLength);
    }

    return count;
  }

  bool get reallyEmpty {
    final list = countStatusAtividade.values.toList();
    list.removeWhere((e) => e == 0);
    return list.isEmpty;
  }

  List<String> get statusComAtividades {
    final map = countStatusAtividade;
    map.removeWhere((k, v) => v == 0);
    final list = map.keys.toList();
    return list.map((e) => getIdStatusAtividadeEnumName(e)).toList();
  }

  List<EnderecoNew> get atividadesFiltradas {
    List<EnderecoNew> atividades = atividadesFiltradasSomenteTexto
        .where(
          (atividade) => atividade.status.contains(statusAtividade),
        )
        .toList();
    return atividades;
  }

  List<LojasTransferencia> get lojasOrigem {
    return lojasTransferencia?.origens ?? [];
  }

  List<LojasTransferencia> get lojasDestino {
    if (lojaOrigem != null) {
      final lojas = lojasTransferencia?.destinos
          .where((element) => element.iDLocalGrupo == lojaOrigem?.iDLocalGrupo)
          .toList();
      lojas?.removeWhere((element) => element.idLoja == lojaOrigem?.idLoja);
      return lojas ?? [];
    }

    return lojasTransferencia?.destinos ?? [];
  }

  factory HomeState.init() {
    return HomeState(
      isOnline: false,
      isOnlineLoading: false,
      dataForceUltimaSincronizacao: null,
      listColetaAdd: false,
      search: '',
      isBarcode: false,
      loading: false,
      atividadesCompletas: [],
      statusAtividade: IdStatusAtividadeEnum.entrega,
      pageSelected: HomeNavigationEnum.home,
      havenegativa: false,
      carregouInicio: false,
      deslocamentosColeta: [],
      fimRota: null,
      rodandoFetchAtividades: false,
      isExpedicao: false,
      isLoadingTelaTransferencia: false,
      loadingButtonTransferencia: false,
    );
  }

  HomeState copyWith({
    bool? isOnline,
    bool? isOnlineLoading,
    DateTime? dataForceUltimaSincronizacao,
    bool? listColetaAdd,
    String? search,
    bool? isBarcode,
    bool? loading,
    List<EnderecoNew>? atividadesCompletas,
    IdStatusAtividadeEnum? statusAtividade,
    HomeNavigationEnum? pageSelected,
    bool? havenegativa,
    bool? carregouInicio,
    List<FcmDeslocamentoGet>? deslocamentosColeta,
    LatLng? fimRota,
    bool? rodandoFetchAtividades,
    bool? isExpedicao,
    TransferenciaModel? lojasTransferencia,
    LojasTransferencia? lojaOrigem,
    LojasTransferencia? lojaDestino,
    bool? isLoadingTelaTransferencia,
    bool? transferenciaComRetorno,
    bool? loadingButtonTransferencia,
  }) {
    return HomeState(
      isOnline: isOnline ?? this.isOnline,
      isOnlineLoading: isOnlineLoading ?? this.isOnlineLoading,
      dataForceUltimaSincronizacao:
          dataForceUltimaSincronizacao ?? this.dataForceUltimaSincronizacao,
      listColetaAdd: listColetaAdd ?? this.listColetaAdd,
      search: search ?? this.search,
      isBarcode: isBarcode ?? this.isBarcode,
      loading: loading ?? this.loading,
      atividadesCompletas: atividadesCompletas ?? this.atividadesCompletas,
      statusAtividade: statusAtividade ?? this.statusAtividade,
      pageSelected: pageSelected ?? this.pageSelected,
      havenegativa: havenegativa ?? this.havenegativa,
      carregouInicio: carregouInicio ?? this.carregouInicio,
      deslocamentosColeta: deslocamentosColeta ?? this.deslocamentosColeta,
      fimRota: fimRota ?? this.fimRota,
      rodandoFetchAtividades:
          rodandoFetchAtividades ?? this.rodandoFetchAtividades,
      isExpedicao: isExpedicao ?? this.isExpedicao,
      lojasTransferencia: lojasTransferencia ?? this.lojasTransferencia,
      lojaOrigem: lojaOrigem ?? this.lojaOrigem,
      lojaDestino: lojaDestino ?? this.lojaDestino,
      isLoadingTelaTransferencia:
          isLoadingTelaTransferencia ?? this.isLoadingTelaTransferencia,
      transferenciaComRetorno:
          transferenciaComRetorno ?? this.transferenciaComRetorno,
      loadingButtonTransferencia:
          loadingButtonTransferencia ?? this.loadingButtonTransferencia,
    );
  }

  HomeState copyWithNull({
    bool isLojaOrigem = false,
    bool isLojaDestino = false,
    bool isResto = false,
  }) {
    return HomeState(
      isOnline: isOnline,
      isOnlineLoading: isOnlineLoading,
      dataForceUltimaSincronizacao: dataForceUltimaSincronizacao,
      listColetaAdd: listColetaAdd,
      search: search,
      isBarcode: isBarcode,
      loading: loading,
      atividadesCompletas: atividadesCompletas,
      statusAtividade: statusAtividade,
      pageSelected: pageSelected,
      havenegativa: havenegativa,
      carregouInicio: carregouInicio,
      deslocamentosColeta: deslocamentosColeta,
      fimRota: fimRota,
      rodandoFetchAtividades: rodandoFetchAtividades,
      isExpedicao: isExpedicao,
      lojasTransferencia: lojasTransferencia,
      lojaOrigem: isLojaOrigem ? null : lojaOrigem,
      lojaDestino: isLojaDestino ? null : lojaDestino,
      isLoadingTelaTransferencia: isResto ? false : isLoadingTelaTransferencia,
      transferenciaComRetorno: transferenciaComRetorno,
      loadingButtonTransferencia: isResto ? false : loadingButtonTransferencia,
    );
  }
}
