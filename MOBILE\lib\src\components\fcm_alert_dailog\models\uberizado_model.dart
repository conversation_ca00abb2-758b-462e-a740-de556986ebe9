import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:map_fields/map_fields.dart';

class UberizadoModel {
  final int idDeslocamento;
  final int totalPedidos;
  final String mensagem;
  final DateTime dataInclusao;
  final DateTime dataHoraAgendada;
  final DateTime? dataHoraDeslocamento;
  final DateTime? dataHoraChegada;
  final DateTime? dataHoraFinalizado;
  final bool reverso;
  final bool uberizado;
  final LocalUberizado local;
  final int tipoModalidadeColeta;
  final bool receita;
  UberizadoModel({
    required this.idDeslocamento,
    required this.totalPedidos,
    required this.mensagem,
    required this.dataInclusao,
    required this.dataHoraAgendada,
    required this.dataHoraDeslocamento,
    required this.dataHoraChegada,
    required this.dataHoraFinalizado,
    required this.reverso,
    required this.uberizado,
    required this.local,
    required this.tipoModalidadeColeta,
    required this.receita,
  });

  factory UberizadoModel.fromMap(Map<String, dynamic> map) {
    final f = MapFields.load(map);
    return UberizadoModel(
      idDeslocamento: f.getInt('IDDeslocamento', -1),
      totalPedidos: f.getInt('TotalPedidos', -1),
      mensagem: f.getString('Mensagem', ''),
      dataInclusao: f.getDateTime('DataInclusao', DateTime.now()),
      dataHoraAgendada: f.getDateTime('DataHoraAgendada', DateTime.now()),
      dataHoraDeslocamento: f.getDateTimeNullable('DataHoraDeslocamento'),
      dataHoraChegada: f.getDateTimeNullable('DataHoraChegada'),
      dataHoraFinalizado: f.getDateTimeNullable('DataHoraFinalizado'),
      reverso: f.getBool('Reverso', false),
      uberizado: f.getBool('Uberizado', false),
      local: LocalUberizado.fromMap(f.getMap('Local', {})),
      tipoModalidadeColeta: f.getInt('TipoModalidadeColeta', 0),
      receita: f.getBool('Receita', false),
    );
  }

  RemoteMessage toRemoteMessage() {
    final data = {
      "id": idDeslocamento,
      "titulo": local.nome,
      "latitude": local.latitude,
      "longitude": local.longitude,
      "endereco": local.endereco,
      "horario": dataHoraAgendada.toIso8601String(),
      "ativo": true,
      "uberizado": true,
      "tipoModalidadeColeta": tipoModalidadeColeta,
      "receita": receita,
    };
    return RemoteMessage(
      data: data,
    );
  }
}

class LocalUberizado {
  final int idLocal;
  final String nome;
  final String logo;
  final String endereco;
  final String latitude;
  final String longitude;

  LocalUberizado({
    required this.idLocal,
    required this.nome,
    required this.logo,
    required this.endereco,
    required this.latitude,
    required this.longitude,
  });

  factory LocalUberizado.fromMap(Map<String, dynamic> map) {
    final f = MapFields.load(map);
    return LocalUberizado(
      idLocal: f.getInt('IDLocal', -1),
      nome: f.getString('Nome', ''),
      logo: f.getString('Logo', ''),
      endereco: f.getString('Endereco', ''),
      latitude: f.getString('Latitude', ''),
      longitude: f.getString('Longitude', ''),
    );
  }
}
