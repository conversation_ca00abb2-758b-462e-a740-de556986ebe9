import 'dart:io';
import 'dart:math';

import 'package:camera_camera/camera_camera.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:octalog/src/components/buttom_ls/button_ls_custom.dart';
import 'package:octalog/src/pages/entrega_newpages/controller/entrega_new_store.dart';
import 'package:octalog/src/utils/extesion.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:signature/signature.dart';

class AssinaturaRecebimentoPage extends StatefulWidget {
  final String os;
  final String endereco;
  final String cliente;
  final String nomeRecebedor;
  final EntregaNewStore store;
  const AssinaturaRecebimentoPage(
      {super.key,
      required this.os,
      required this.endereco,
      required this.cliente,
      required this.nomeRecebedor,
      required this.store});

  @override
  _AssinaturaRecebimentoPageState createState() =>
      _AssinaturaRecebimentoPageState();
}

class _AssinaturaRecebimentoPageState extends State<AssinaturaRecebimentoPage> {
  final DateTime _now = DateTime.now();
  final SignatureController _controller = SignatureController(
    penStrokeWidth: 2,
    penColor: Colors.black,
    exportBackgroundColor: Colors.white,
  );
  bool loading = false;
  final ScreenshotController _screenshotController = ScreenshotController();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _clearSignature() {
    setState(() {
      _controller.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Assinatura'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: const Color(0xFFD3D3D3),
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.orange,
          ),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      backgroundColor: const Color(0xFFD3D3D3),
      body: Screenshot(
        controller: _screenshotController,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    RichText(
                      text: TextSpan(
                        style: DefaultTextStyle.of(context).style,
                        children: <TextSpan>[
                          TextSpan(
                            text: 'Pedido: ',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                              color: Colors.grey.shade700,
                            ),
                          ),
                          TextSpan(
                            text: widget.os,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: Colors.black,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    RichText(
                      text: TextSpan(
                        style: DefaultTextStyle.of(context).style,
                        children: <TextSpan>[
                          TextSpan(
                            text: 'Endereço: ',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                              color: Colors.grey.shade700,
                            ),
                          ),
                          TextSpan(
                            text: widget.endereco,
                            style: const TextStyle(
                                fontWeight: FontWeight.bold, fontSize: 16),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    RichText(
                      text: TextSpan(
                        style: DefaultTextStyle.of(context).style,
                        children: <TextSpan>[
                          TextSpan(
                            text: 'Cliente: ',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                              color: Colors.grey.shade700,
                            ),
                          ),
                          TextSpan(
                            text: widget.cliente,
                            style: const TextStyle(
                                fontWeight: FontWeight.bold, fontSize: 16),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    RichText(
                      text: TextSpan(
                        style: DefaultTextStyle.of(context).style,
                        children: <TextSpan>[
                          TextSpan(
                            text: 'Data: ',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                              color: Colors.grey.shade700,
                            ),
                          ),
                          TextSpan(
                            text: _now.dataHoraPtBr,
                            style: const TextStyle(
                                fontWeight: FontWeight.bold, fontSize: 16),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const Padding(
                padding: EdgeInsets.symmetric(
                  vertical: 16.0,
                ),
                child: Row(
                  children: [
                    Text(
                      'Confirmo ter recebido o pedido sem avaria ou defeito',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 15,
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(
                  bottom: 16.0,
                ),
                child: Container(
                  padding: const EdgeInsets.all(11.0),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  child: Column(
                    children: [
                      Stack(
                        children: [
                          Signature(
                            controller: _controller,
                            backgroundColor: Colors.white,
                            height: 250,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const SizedBox(
                                width: 70,
                              ),
                              const Text(
                                'ASSINATURA',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.orange,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              GestureDetector(
                                onTap: _clearSignature,
                                child: const Text(
                                  'LIMPAR',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.black,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Positioned(
                            bottom: 0,
                            child: Column(
                              children: [
                                SizedBox(
                                    width:
                                        MediaQuery.of(context).size.width * 0.9,
                                    child: const Divider(
                                        thickness: 1, color: Colors.black)),
                                Row(
                                  children: [
                                    Text(
                                      widget.nomeRecebedor,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: ButtonLsCustom(
          text: 'CONFIRMAR',
          isLoading: loading,
          onPressed: () async {
            setState(() => loading = true);
            final image = await _screenshotController.capture();
            if (image != null) {
              final tempDir = await getTemporaryDirectory();
              final String random =
                  DateTime.now().millisecondsSinceEpoch.toString() +
                      Random().nextInt(1000).toString();
              final file = File('${tempDir.path}/${random}_assinatura.png');
              await file.writeAsBytes(image);
              final xFile = XFile(file.path);
              await widget.store.upLoadFotoAssinatura(xFile);
              Navigator.pop(context, true);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Erro ao salvar Tentar novamente'),
                  backgroundColor: Colors.red,
                ),
              );
            }
            setState(() => loading = false);
          },
        ),
      ),
    );
  }
}
