import 'package:octalog/src/utils/extesion.dart';
import 'package:map_fields/map_fields.dart';

class FcmPedido {
  final String title;
  final String cliente;
  final String status;
  final String os;
  final int idOs;

  FcmPedido(
      {required this.title,
      required this.cliente,
      required this.status,
      required this.os,
      required this.idOs});

  bool get isNotValid => title.isEmpty;

  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'cliente': cliente,
      'status': status,
      'os': os,
      'idOs': idOs,
    };
  }

  Map<String, dynamic> toMapKlev() {
    return {
      'Cliente': cliente,
      'OS': os,
      'IDOS': idOs,
      'EnderecoCompleto': title,
    };
  }

  Map<String, dynamic> toMapKlevSend() {
    return {
      'OS': os,
      'IDOS': idOs == 0 ? null : idOs,
      'DataColeta': DateTime.now().dataHoraServidorFomart.toIso8601String(),
    };
  }

  factory FcmPedido.fromMapKlev(Map<String, dynamic> map) {
    final f = MapFields.load(map);
    return FcmPedido(
      title: f.getString('EnderecoCompleto', 'Endereço na etiqueta'),
      cliente: f.getString('Cliente', 'Consumidor final'),
      status: f.getString('StatusAtividade', ''),
      os: f.getString('OS', ''),
      idOs: f.getInt('IDOS', 0),
    );
  }

  factory FcmPedido.fromMap(Map<String, dynamic> e) {
    final f = MapFields.load(e);
    return FcmPedido(
      title: f.getString('title', ''),
      cliente: f.getString('cliente', ''),
      status: f.getString('status', ''),
      os: f.getString('os', ''),
      idOs: f.getInt('idOs', 0),
    );
  }
}
