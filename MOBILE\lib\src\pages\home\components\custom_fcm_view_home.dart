import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/components/fcm_alert_dailog/models/fcm_deslocamento_get.dart';
import 'package:octalog/src/utils/extesion.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../../components/fcm_alert_dailog/fcm_alert_dialog_widget.dart';
import '../../../components/image_perfil/image_perfil.dart';
import '../../../utils/colors.dart';

class CustomFcmViewHome extends StatelessWidget {
  final FcmDeslocamentoGet deslocamento;
  const CustomFcmViewHome({
    super.key,
    required this.deslocamento,
  });

  @override
  Widget build(context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => FcmAlertDialogWidget(
                    dados: deslocamento,
                  ),
                ),
              );
            },
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    if (deslocamento.reverso) ...[
                      Text(
                        'Devolução',
                        style: GoogleFonts.roboto(
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                          color: ColorsCustom.customBlack,
                        ),
                      ),
                      const SizedBox(
                        width: 10,
                      )
                    ],
                    Expanded(
                      child: Text(
                        deslocamento.titulo,
                        overflow: TextOverflow.ellipsis,
                        softWrap: true,
                        textWidthBasis: TextWidthBasis.longestLine,
                        style: GoogleFonts.roboto(
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                          color: ThemeColors.customOrange(context),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 5),
                Row(
                  children: [
                    const SizedBox(width: 2),
                    SizedBox(
                      height: 46,
                      width: 46,
                      child: ImagePerfil(
                        url: deslocamento.logo ?? '',
                        iniciais: deslocamento.titulo.iniciais,
                        fontSize: 25,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        deslocamento.endereco,
                        style: GoogleFonts.roboto(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 3,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 5),
                if (!deslocamento.reverso)
                  Row(
                    children: [
                       Icon(
                        Icons.access_time_outlined,
                        size: 18,
                        color: ThemeColors.customOrange(context),
                      ),
                      const SizedBox(width: 5),
                      Text(
                        'Seu tempo termina às ',
                        style: GoogleFonts.roboto(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: ColorsCustom.customBlack,
                        ),
                      ),
                      Text(
                        deslocamento.time,
                        style: GoogleFonts.roboto(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: ColorsCustom.customBlack,
                        ),
                      ),
                    ],
                  ),
                if (deslocamento.reverso)
                  Text(
                    deslocamento.mensagem,
                    style: GoogleFonts.roboto(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: ColorsCustom.customBlack,
                    ),
                  ),
              ],
            ),
          ),
        ),
        const Divider(
          color: ColorsCustom.customGrey,
          thickness: 1,
        ),
      ],
    );
  }
}
