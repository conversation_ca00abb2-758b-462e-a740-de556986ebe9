import 'package:flutter/material.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../pages/home/<USER>';
import './../utils/colors.dart';

class IconPadrao extends StatelessWidget {
  final bool? isBack;
  const IconPadrao({
    super.key,
    this.isBack,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,
      width: 40,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: ThemeColors.customOrange(context).withOpacity(0.1),
      ),
      child: IconButton(
        icon:  Icon(
          Icons.arrow_back,
          color: ThemeColors.customOrange(context),
        ),
        onPressed: isBack != null
            ? () {
                if (isBack ?? true) {
                  if (Navigator.canPop(context)) {
                    Navigator.pop(context);
                    return;
                  }
                } else {
                  Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(
                      builder: (_) => const Home(
                        enterAtividade: false,
                      ),
                    ),
                    (route) => false,
                  );
                }
              }
            : () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content:  Text(
                      'Você não pode voltar!',
                      style: TextStyle(
                        color: ThemeColors.customOrange(context),
                      ),
                    ),
                    action: SnackBarAction(
                      label: 'OK',
                      textColor: ThemeColors.customOrange(context),
                      onPressed: () =>
                          ScaffoldMessenger.of(context).hideCurrentSnackBar(),
                    ),
                  ),
                );
              },
      ),
    );
  }
}
