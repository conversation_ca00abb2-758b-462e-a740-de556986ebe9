import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:octalog/src/components/loading_ls/loading_ls.dart';
import 'package:octalog/src/pages/home/<USER>/custom_enum_navigation.dart';
import 'package:octalog/src/pages/home/<USER>/transferencia/card_transferencia.dart';
import 'package:octalog/src/pages/home/<USER>';
import 'package:octalog/src/pages/home/<USER>';
import 'package:octalog/src/utils/colors.dart';
import 'package:octalog/src/utils/theme_colors.dart';

class TransferenciaWidget extends StatefulWidget {
  final HomeController controller;
  const TransferenciaWidget({super.key, required this.controller});

  @override
  State<TransferenciaWidget> createState() => _TransferenciaWidgetState();
}

class _TransferenciaWidgetState extends State<TransferenciaWidget> {
  @override
  void initState() {
    init();
    super.initState();
  }

  init() async {
    await widget.controller.buscarLojasParaTransferencia().then((value) {
      if (value) {
        dialog();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: ValueListenableBuilder<HomeState>(
        valueListenable: widget.controller.state,
        builder: (BuildContext context, value, Widget? child) {
          if (!value.isLoadingTelaTransferencia) {
            return const Center(
              child: LoadingLs(),
            );
          }
          return KeyboardVisibilityBuilder(
            builder: (context, isKeyboardVisible) {
              return Scaffold(
                backgroundColor: Colors.transparent,
                body: SizedBox(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  child: SingleChildScrollView(
                    physics: const BouncingScrollPhysics(),
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 5, vertical: 20),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: <Widget>[
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Container(
                                    height: 40,
                                    width: 40,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: ThemeColors.customOrange(context)
                                          .withOpacity(0.1),
                                    ),
                                    child: IconButton(
                                      icon:  Icon(
                                        Icons.arrow_back,
                                        color: ThemeColors.customOrange(context),
                                      ),
                                      onPressed: () {
                                        widget.controller
                                            .setNullTransferencia();
                                        widget.controller.setPageSelected(
                                            HomeNavigationEnum.home);
                                      },
                                    ),
                                  ),
                                  const Text(
                                    'Transferências entre lojas',
                                    style: TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.w700,
                                      color: ColorsCustom.customBlack,
                                    ),
                                  ),
                                  const SizedBox(
                                    width: 40,
                                  )
                                ],
                              ),
                            ),
                            const SizedBox(
                              height: 5,
                            ),
                            Visibility(
                              visible: value.transferenciaComRetorno != null,
                              child: GestureDetector(
                                onTap: () {
                                  widget.controller.setTransferenciaComRetorno(
                                      !value.transferenciaComRetorno!);
                                },
                                child: Card(
                                  elevation: 1,
                                  child: Padding(
                                    padding: const EdgeInsets.all(15.0),
                                    child: Row(
                                      children: [
                                        Container(
                                          width: 30,
                                          height: 30,
                                          decoration: BoxDecoration(
                                            color:
                                                value.transferenciaComRetorno ??
                                                        false
                                                    ? ThemeColors.customOrange(context)
                                                    : ColorsCustom.customBlue,
                                            shape: BoxShape.circle,
                                          ),
                                          child: const Icon(
                                            Icons.loop_rounded,
                                            color: Colors.white,
                                          ),
                                        ),
                                        const SizedBox(
                                          width: 10,
                                        ),
                                        MediaQuery(
                                          data: MediaQuery.of(context).copyWith(
                                              textScaler:
                                                  const TextScaler.linear(1.0)),
                                          child: Text(
                                            value.transferenciaComRetorno ??
                                                    false
                                                ? 'Retirar pedido ou produto de outra loja'
                                                : 'Levar pedido ou produto para outra loja',
                                            style: TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.w800,
                                              color:
                                                  value.transferenciaComRetorno ??
                                                          false
                                                      ? ColorsCustom
                                                          .customOrange
                                                      : ColorsCustom.customBlue,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(
                              height: 5,
                            ),
                            CardLojasTransferencia(
                              destino: false,
                              lojas: value.lojasOrigem,
                              controller: widget.controller,
                              loja_selecionada: value.lojaOrigem,
                            ),
                            CardLojasTransferencia(
                              destino: true,
                              lojas: value.lojasDestino,
                              controller: widget.controller,
                              loja_selecionada: value.lojaDestino,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  dialog() async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Selec(
          controller: widget.controller,
        );
      },
    );
  }
}

class Selec extends StatefulWidget {
  final HomeController controller;
  const Selec({super.key, required this.controller});

  @override
  State<Selec> createState() => _SelecState();
}

class _SelecState extends State<Selec> {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.transparent,
      child: AlertDialog(
        title: const Text('Qual o tipo de transferência'),
        content: ValueListenableBuilder<HomeState>(
          valueListenable: widget.controller.state,
          builder: (BuildContext context, value, Widget? child) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                GestureDetector(
                  onTap: () {
                    widget.controller.setTransferenciaComRetorno(false);
                    Navigator.pop(context);
                  },
                  child: Row(
                    children: [
                      Checkbox(
                        checkColor: ThemeColors.customOrange(context),
                        fillColor:
                            WidgetStateProperty.resolveWith<Color>((states) {
                          if (states.contains(WidgetState.selected)) {
                            return ThemeColors.customOrange(context);
                          }
                          return Colors.white;
                        }),
                        value: value.transferenciaComRetorno == false,
                        shape: const CircleBorder(),
                        onChanged: (_) {
                          widget.controller.setTransferenciaComRetorno(false);
                          Navigator.pop(context);
                        },
                      ),
                      MediaQuery(
                        data: MediaQuery.of(context).copyWith(
                          textScaler: const TextScaler.linear(1.0),
                        ),
                        child: const Expanded(
                          child:
                              Text('Levar pedido ou produto para outra loja'),
                        ),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    widget.controller.setTransferenciaComRetorno(true);
                    Navigator.pop(context);
                  },
                  child: Row(
                    children: [
                      Checkbox(
                        checkColor: ThemeColors.customOrange(context),
                        fillColor:
                            WidgetStateProperty.resolveWith<Color>((states) {
                          if (states.contains(WidgetState.selected)) {
                            return ThemeColors.customOrange(context);
                          }
                          return Colors.white;
                        }),
                        value: value.transferenciaComRetorno == true,
                        shape: const CircleBorder(),
                        onChanged: (_) {
                          widget.controller.setTransferenciaComRetorno(true);
                          Navigator.pop(context);
                        },
                      ),
                      MediaQuery(
                        data: MediaQuery.of(context)
                            .copyWith(textScaler: const TextScaler.linear(1.0)),
                        child: const Text(
                            'Retirar pedido ou produto de outra loja'),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextButton(
                      onPressed: () {
                        widget.controller
                            .setPageSelected(HomeNavigationEnum.home);
                        Navigator.pop(context);
                      },
                      child: const Text(
                        'CANCELAR',
                        style: TextStyle(
                            fontSize: 16, color: ColorsCustom.customBlack),
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
