#!/bin/bash

# Script para configurar flavors no iOS
# Uso: ./configure_ios_flavor.sh [flavor] [build_mode]
# Exemplo: ./configure_ios_flavor.sh up360 debug

set -e

FLAVOR=${1:-octalog}
BUILD_MODE=${2:-debug}

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}🍎 Configurando iOS para flavor: ${YELLOW}$FLAVOR${NC}"
echo -e "${BLUE}📱 Modo de build: ${YELLOW}$BUILD_MODE${NC}"

# Validar flavor
case $FLAVOR in
    "octalog"|"up360"|"connect"|"rondolog"|"spotlog"|"boyviny")
        echo -e "${GREEN}✅ Flavor válido: $FLAVOR${NC}"
        ;;
    *)
        echo -e "${RED}❌ Flavor inválido: $FLAVOR${NC}"
        echo -e "${YELLOW}Flavors disponíveis: octalog, up360, connect, rondolog, spotlog, boyviny${NC}"
        exit 1
        ;;
esac

# Definir configurações por flavor
case $FLAVOR in
    "octalog")
        BUNDLE_ID="com.octalog"
        DISPLAY_NAME="Octalog"
        ;;
    "up360")
        BUNDLE_ID="com.octalog.up360"
        DISPLAY_NAME="UP360"
        ;;
    "connect")
        BUNDLE_ID="com.octalog.connect"
        DISPLAY_NAME="Connect"
        ;;
    "rondolog")
        BUNDLE_ID="com.octalog.rondolog"
        DISPLAY_NAME="RondoLog"
        ;;
    "spotlog")
        BUNDLE_ID="com.octalog.spotlog"
        DISPLAY_NAME="SpotLog"
        ;;
    "boyviny")
        BUNDLE_ID="com.octalog.boyviny"
        DISPLAY_NAME="Boy Viny"
        ;;
esac

# Definir arquivo de configuração baseado no modo de build
case $BUILD_MODE in
    "debug")
        CONFIG_FILE="ios/Flutter/Flavor-Debug.xcconfig"
        ;;
    "release")
        CONFIG_FILE="ios/Flutter/Flavor-Release.xcconfig"
        ;;
    "profile")
        CONFIG_FILE="ios/Flutter/Flavor-Profile.xcconfig"
        ;;
    *)
        echo -e "${RED}❌ Modo de build inválido: $BUILD_MODE${NC}"
        echo -e "${YELLOW}Modos disponíveis: debug, release, profile${NC}"
        exit 1
        ;;
esac

echo -e "${BLUE}📝 Atualizando arquivo de configuração: ${YELLOW}$CONFIG_FILE${NC}"
echo -e "${BLUE}🆔 Bundle ID: ${YELLOW}$BUNDLE_ID${NC}"
echo -e "${BLUE}📱 Nome do App: ${YELLOW}$DISPLAY_NAME${NC}"

# Criar conteúdo do arquivo de configuração
cat > "$CONFIG_FILE" << EOF
#include "$(echo $BUILD_MODE | sed 's/.*/\u&/').xcconfig"

// Flavor configuration for iOS - $FLAVOR
// Generated automatically by configure_ios_flavor.sh

FLAVOR_BUNDLE_IDENTIFIER = $BUNDLE_ID
FLAVOR_DISPLAY_NAME = $DISPLAY_NAME
EOF

echo -e "${GREEN}✅ Configuração iOS atualizada com sucesso!${NC}"
echo -e "${BLUE}📄 Arquivo gerado: ${YELLOW}$CONFIG_FILE${NC}"
echo ""
echo -e "${BLUE}🚀 Para buildar o iOS agora, use:${NC}"
echo -e "${YELLOW}flutter build ios --release --flavor $FLAVOR --dart-define=FLAVOR=$FLAVOR${NC}"
