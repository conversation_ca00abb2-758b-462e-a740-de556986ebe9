import 'package:flutter/material.dart';

import '../../../../../models/historico_sac.dart';

import '../../../../../utils/extesion.dart' as extesion;

class HistoricoItem extends StatefulWidget {
  final HistoricoSacModel historico;
  final Function() onTap;
  const HistoricoItem(
      {super.key, required this.historico, required this.onTap});

  @override
  _HistoricoItemState createState() => _HistoricoItemState();
}

class _HistoricoItemState extends State<HistoricoItem> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: Card(
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5),
        ),
        child: Container(
          decoration: const BoxDecoration(
            color: Color.fromARGB(197, 243, 243, 243),
          ),
          padding: const EdgeInsets.all(10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildRichText('OS:', widget.historico.os),
              _buildRichText('Status:', widget.historico.statusAtividade),
              _buildRichText(
                  'Data:', widget.historico.dataContatoAgente.dataHoraPtBr),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRichText(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            text: '$label ',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeColors.customBlack(context)White,
            ),
            children: [
              TextSpan(
                text: value,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.normal,
                  color: ThemeColors.customBlack(context)White,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 2,
        ),
      ],
    );
  }
}
