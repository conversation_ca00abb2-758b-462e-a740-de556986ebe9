import 'package:octalog/src/components/fcm_alert_dailog/models/fcm_deslocamento_model.dart';
import 'package:map_fields/map_fields.dart';

class FcmDeslocamentoInfo {
  final int idDeslocamento;
  final double? latitude;
  final double? longitude;
  final DateTime dataHora;
  final String status;

  FcmDeslocamentoInfo({
    required this.idDeslocamento,
    required this.latitude,
    required this.longitude,
    required this.dataHora,
    required this.status,
  });

  factory FcmDeslocamentoInfo.fromMap(Map<String, dynamic> map) {
    final f = MapFields.load(map);
    return FcmDeslocamentoInfo(
      idDeslocamento: f.getInt('idDeslocamento', 0),
      latitude: f.getDouble('latitude', 0),
      longitude: f.getDouble('longitude', 0),
      dataHora: f.getDateTime('dataHora', DateTime.now()),
      status: f.getString('status', ''),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      "idDeslocamento": idDeslocamento,
      "latitude": latitude,
      "longitude": longitude,
      "dataHora": dataHora.toIso8601String(),
      "status": status,
    };
  }

  FcmDeslocamentoModel toFcmDeslocamentoModel() {
    return FcmDeslocamentoModel(
      idDeslocamento: idDeslocamento,
      dataHora: dataHora,
      latitude: latitude,
      longitude: longitude,
    );
  }
}
