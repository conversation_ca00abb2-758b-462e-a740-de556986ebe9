import 'package:asuka/asuka.dart' as asuka;
// ignore: depend_on_referenced_packages, implementation_imports
import 'package:firebase_messaging_platform_interface/src/remote_message.dart';
import 'package:flutter/material.dart';
// import 'package:octalog/src/components/loading_ls/loading_ls.dart';
import 'package:octalog/src/helpers/web_connector.dart';
import 'package:octalog/src/utils/extesion.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../../database/log_database/log_database.dart';
import '../../../helpers/gps/gps_contract.dart';
import '../../../models_new/position_data_location.dart';
import '../../../pages/home/<USER>';
import '../../../utils/colors.dart';
import '../../buttom_ls/button_ls_custom.dart';
import '../components/fcm_appbar_custom.dart';
import '../components/fcm_deslocamento_widget.dart';
// import '../components/fcm_distance_data.dart';
import '../components/fcm_porcent.dart';
import '../fcm_alert_dialog_store.dart';
import '../models/fcm_deslocamento_get.dart';

class FcmShowDailog extends StatefulWidget {
  final RemoteMessage message;

  const FcmShowDailog({
    super.key,
    required this.message,
  });

  @override
  State<FcmShowDailog> createState() => _FcmShowDailogState();
}

class _FcmShowDailogState extends State<FcmShowDailog> {
  FcmDeslocamentoGet get deslocamentoInfo => FcmDeslocamentoGet.fromMapKlev(
        widget.message.data,
      );
  late final FcmAlertDialogStore store;
  PositionDataLocation? posicao;
  bool isclinked = false;

  Future<void> init() async {
    final posicaoAtual =
        await GpsHelperContract.instance.updateAndGetLastPosition();
    posicao = posicaoAtual;
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    init();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: SingleChildScrollView(
          reverse: true,
          child: Padding(
            padding: const EdgeInsets.only(
              left: 20,
              right: 20,
              top: 10,
            ),
            child: Column(
              children: [
                const FcmAppBarCustom(
                  title: 'Alerta de Pedido',
                  isBack: false,
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 18),
                  child: Image.asset(
                    'assets/images/image_fmc.png',
                    width: MediaQuery.of(context).size.width * 0.9,
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.only(top: 10, left: 10, right: 10),
                  child: FcmPorcent(),
                ),
                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: deslocamentoInfo.reverso
                      ?  Text(
                          'Você tem devolução para fazer a loja',
                          style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                              color: ThemeColors.customOrange(context)),
                        )
                      :  Text(
                          'Você tem uma coleta para fazer na loja',
                          style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                              color: ThemeColors.customOrange(context)),
                        ),
                ),
                const SizedBox(
                  height: 10,
                ),
                FcmDeslocamentoWidget(
                  local: deslocamentoInfo.titulo,
                  endereco: deslocamentoInfo.endereco,
                ),
                // const SizedBox(
                //   height: 20,
                // ),
                // posicao == null
                //     ? SizedBox(
                //         width: MediaQuery.of(context).size.width * 0.2,
                //         child: const LoadingLs())
                //     : FcmDistanceData(
                //         horaFinal: deslocamentoInfo.reverso
                //             ? null
                //             : TimeOfDay.fromDateTime(
                //                     deslocamentoInfo.dataHoraChegada ??
                //                         DateTime.now())
                //                 .format(context),
                //         destino: deslocamentoInfo.destino,
                //       ),
                // const SizedBox(
                //   height: 80,
                // )
              ],
            ),
          ),
        ),
        floatingActionButton: Padding(
          padding: const EdgeInsets.only(
            left: 40,
            right: 10,
          ),
          child: ButtonLsCustom(
            isLoading: isclinked,
            text: 'CONFIRMAR',
            colorBackground: ThemeColors.customOrange(context),
            onPressed: () async {
              setState(() => isclinked = true);
              try {
                final conn = WebConnector();
                await conn.put(
                  '/deslocamento/aceite',
                  body: {
                    "IDDeslocamento": deslocamentoInfo.id,
                    "DataHora":
                        DateTime.now().dataHoraServidorFomart.toIso8601String(),
                    "Latitude": posicao?.latitude.toString(),
                    "Longitude": posicao?.longitude.toString(),
                    "Aceitou": true,
                  },
                );
                if (Navigator.of(context).canPop()) {
                  HomeController.instance.fetchAtividades();
                  Navigator.of(context).pop();
                }
              } catch (e) {
                setState(() => isclinked = false);
                await LogDatabase.instance.logError(
                  '',
                  '/deslocamento/aceite',
                  'Aceitar deslocamento',
                  {
                    'error': e.toString(),
                    'error_type': e.runtimeType.toString(),
                    'message': e.toString(),
                  },
                );
                asuka.Asuka.showSnackBar(
                  const SnackBar(
                    content: Text('Erro ao confirmar deslocamento '),
                  ),
                );
              }
            },
          ),
        ),
      ),
    );
  }
}
