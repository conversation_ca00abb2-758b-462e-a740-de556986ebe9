  Manifest android  
permission android.Manifest  ACTIVITY_RECOGNITION android.Manifest.permission  SuppressLint android.annotation  Activity android.app  
PendingIntent android.app  equals android.app.Activity  getGETPrevPermissionStatus android.app.Activity  getGetPrevPermissionStatus android.app.Activity  getISPermissionGranted android.app.Activity  getIsPermissionGranted android.app.Activity  getPrevPermissionStatus android.app.Activity  getSETPrevPermissionStatus android.app.Activity  getSetPrevPermissionStatus android.app.Activity  isPermissionGranted android.app.Activity  setPrevPermissionStatus android.app.Activity  $shouldShowRequestPermissionRationale android.app.Activity  FLAG_MUTABLE android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  getBroadcast android.app.PendingIntent  ActivityData android.app.Service   ActivityRecognitionIntentService android.app.Service  ActivityRecognitionResult android.app.Service  ActivityRecognitionUtils android.app.Service  Context android.app.Service  
ErrorCodes android.app.Service  	Exception android.app.Service  Gson android.app.Service  Intent android.app.Service  PreferencesKey android.app.Service  String android.app.Service  enqueueWork android.app.Service  getSharedPreferences android.app.Service  java android.app.Service  
jsonConverter android.app.Service  maxByOrNull android.app.Service  with android.app.Service  BroadcastReceiver android.content  Context android.content  Intent android.content  SharedPreferences android.content   ActivityRecognitionIntentService !android.content.BroadcastReceiver  ActivityRecognitionResult !android.content.BroadcastReceiver  Context !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  ActivityData android.content.Context  ActivityPermission android.content.Context   ActivityRecognitionIntentService android.content.Context  ActivityRecognitionResult android.content.Context  ActivityRecognitionUtils android.content.Context  Context android.content.Context  
ContextCompat android.content.Context  
ErrorCodes android.content.Context  	Exception android.content.Context  Gson android.content.Context  Intent android.content.Context  MODE_PRIVATE android.content.Context  PackageManager android.content.Context  PreferencesKey android.content.Context  String android.content.Context  enqueueWork android.content.Context  
getPERMISSION android.content.Context  
getPermission android.content.Context  getPrevPermissionStatus android.content.Context  getSharedPreferences android.content.Context  getWITH android.content.Context  getWith android.content.Context  isPermissionGranted android.content.Context  java android.content.Context  
jsonConverter android.content.Context  maxByOrNull android.content.Context  
permission android.content.Context  setPrevPermissionStatus android.content.Context  $shouldShowRequestPermissionRationale android.content.Context  with android.content.Context  ActivityData android.content.ContextWrapper   ActivityRecognitionIntentService android.content.ContextWrapper  ActivityRecognitionResult android.content.ContextWrapper  ActivityRecognitionUtils android.content.ContextWrapper  Context android.content.ContextWrapper  
ErrorCodes android.content.ContextWrapper  	Exception android.content.ContextWrapper  Gson android.content.ContextWrapper  Intent android.content.ContextWrapper  PreferencesKey android.content.ContextWrapper  String android.content.ContextWrapper  enqueueWork android.content.ContextWrapper  getPrevPermissionStatus android.content.ContextWrapper  getSharedPreferences android.content.ContextWrapper  isPermissionGranted android.content.ContextWrapper  java android.content.ContextWrapper  
jsonConverter android.content.ContextWrapper  maxByOrNull android.content.ContextWrapper  setPrevPermissionStatus android.content.ContextWrapper  $shouldShowRequestPermissionRationale android.content.ContextWrapper  with android.content.ContextWrapper  Editor !android.content.SharedPreferences   OnSharedPreferenceChangeListener !android.content.SharedPreferences  edit !android.content.SharedPreferences  	getString !android.content.SharedPreferences  (registerOnSharedPreferenceChangeListener !android.content.SharedPreferences  *unregisterOnSharedPreferenceChangeListener !android.content.SharedPreferences  PreferencesKey (android.content.SharedPreferences.Editor  clear (android.content.SharedPreferences.Editor  commit (android.content.SharedPreferences.Editor  
getPERMISSION (android.content.SharedPreferences.Editor  
getPermission (android.content.SharedPreferences.Editor  
permission (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  remove (android.content.SharedPreferences.Editor  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  Build 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  M android.os.Build.VERSION_CODES  Q android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  getPrevPermissionStatus  android.view.ContextThemeWrapper  isPermissionGranted  android.view.ContextThemeWrapper  setPrevPermissionStatus  android.view.ContextThemeWrapper  $shouldShowRequestPermissionRationale  android.view.ContextThemeWrapper  ActivityCompat androidx.core.app  JobIntentService androidx.core.app  requestPermissions  androidx.core.app.ActivityCompat  ActivityData "androidx.core.app.JobIntentService   ActivityRecognitionIntentService "androidx.core.app.JobIntentService  ActivityRecognitionResult "androidx.core.app.JobIntentService  ActivityRecognitionUtils "androidx.core.app.JobIntentService  Context "androidx.core.app.JobIntentService  
ErrorCodes "androidx.core.app.JobIntentService  	Exception "androidx.core.app.JobIntentService  Gson "androidx.core.app.JobIntentService  Intent "androidx.core.app.JobIntentService  PreferencesKey "androidx.core.app.JobIntentService  String "androidx.core.app.JobIntentService  enqueueWork "androidx.core.app.JobIntentService  getSharedPreferences "androidx.core.app.JobIntentService  java "androidx.core.app.JobIntentService  
jsonConverter "androidx.core.app.JobIntentService  maxByOrNull "androidx.core.app.JobIntentService  with "androidx.core.app.JobIntentService  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  ActivityRecognition com.google.android.gms.location  ActivityRecognitionClient com.google.android.gms.location  !ActivityRecognitionIntentReceiver com.google.android.gms.location  ActivityRecognitionManager com.google.android.gms.location  ActivityRecognitionResult com.google.android.gms.location  Build com.google.android.gms.location  Context com.google.android.gms.location  DetectedActivity com.google.android.gms.location  
ErrorCodes com.google.android.gms.location  Intent com.google.android.gms.location  Log com.google.android.gms.location  
PendingIntent com.google.android.gms.location  PreferencesKey com.google.android.gms.location  RequestCode com.google.android.gms.location  TAG com.google.android.gms.location  java com.google.android.gms.location  with com.google.android.gms.location  	getClient 3com.google.android.gms.location.ActivityRecognition  equals 9com.google.android.gms.location.ActivityRecognitionClient  removeActivityUpdates 9com.google.android.gms.location.ActivityRecognitionClient  requestActivityUpdates 9com.google.android.gms.location.ActivityRecognitionClient  
extractResult 9com.google.android.gms.location.ActivityRecognitionResult  getPROBABLEActivities 9com.google.android.gms.location.ActivityRecognitionResult  getProbableActivities 9com.google.android.gms.location.ActivityRecognitionResult  	hasResult 9com.google.android.gms.location.ActivityRecognitionResult  probableActivities 9com.google.android.gms.location.ActivityRecognitionResult  setProbableActivities 9com.google.android.gms.location.ActivityRecognitionResult  
IN_VEHICLE 0com.google.android.gms.location.DetectedActivity  
ON_BICYCLE 0com.google.android.gms.location.DetectedActivity  ON_FOOT 0com.google.android.gms.location.DetectedActivity  RUNNING 0com.google.android.gms.location.DetectedActivity  STILL 0com.google.android.gms.location.DetectedActivity  TILTING 0com.google.android.gms.location.DetectedActivity  WALKING 0com.google.android.gms.location.DetectedActivity  
confidence 0com.google.android.gms.location.DetectedActivity  
getCONFIDENCE 0com.google.android.gms.location.DetectedActivity  
getConfidence 0com.google.android.gms.location.DetectedActivity  getTYPE 0com.google.android.gms.location.DetectedActivity  getType 0com.google.android.gms.location.DetectedActivity  
setConfidence 0com.google.android.gms.location.DetectedActivity  setType 0com.google.android.gms.location.DetectedActivity  type 0com.google.android.gms.location.DetectedActivity  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  Gson com.google.gson  toJson com.google.gson.Gson  SerializedName com.google.gson.annotations  ActivityPermissionManager (com.pravera.flutter_activity_recognition  ActivityRecognitionManager (com.pravera.flutter_activity_recognition  Any (com.pravera.flutter_activity_recognition  
ErrorCodes (com.pravera.flutter_activity_recognition  ErrorHandleUtils (com.pravera.flutter_activity_recognition  EventChannel (com.pravera.flutter_activity_recognition   FlutterActivityRecognitionPlugin (com.pravera.flutter_activity_recognition  MethodCallHandlerImpl (com.pravera.flutter_activity_recognition  
MethodChannel (com.pravera.flutter_activity_recognition  PreferencesKey (com.pravera.flutter_activity_recognition  RequestCode (com.pravera.flutter_activity_recognition  StreamHandlerImpl (com.pravera.flutter_activity_recognition  String (com.pravera.flutter_activity_recognition  
isInitialized (com.pravera.flutter_activity_recognition  ActivityPluginBinding Icom.pravera.flutter_activity_recognition.FlutterActivityRecognitionPlugin  
FlutterPlugin Icom.pravera.flutter_activity_recognition.FlutterActivityRecognitionPlugin  MethodCallHandlerImpl Icom.pravera.flutter_activity_recognition.FlutterActivityRecognitionPlugin  StreamHandlerImpl Icom.pravera.flutter_activity_recognition.FlutterActivityRecognitionPlugin  
isInitialized Icom.pravera.flutter_activity_recognition.FlutterActivityRecognitionPlugin  methodCallHandler Icom.pravera.flutter_activity_recognition.FlutterActivityRecognitionPlugin  onAttachedToActivity Icom.pravera.flutter_activity_recognition.FlutterActivityRecognitionPlugin  onDetachedFromActivity Icom.pravera.flutter_activity_recognition.FlutterActivityRecognitionPlugin  
streamHandler Icom.pravera.flutter_activity_recognition.FlutterActivityRecognitionPlugin  ActivityPermission >com.pravera.flutter_activity_recognition.MethodCallHandlerImpl  ActivityPermissionCallback >com.pravera.flutter_activity_recognition.MethodCallHandlerImpl  ActivityPermissionManager >com.pravera.flutter_activity_recognition.MethodCallHandlerImpl  ActivityPluginBinding >com.pravera.flutter_activity_recognition.MethodCallHandlerImpl  BinaryMessenger >com.pravera.flutter_activity_recognition.MethodCallHandlerImpl  Context >com.pravera.flutter_activity_recognition.MethodCallHandlerImpl  
ErrorCodes >com.pravera.flutter_activity_recognition.MethodCallHandlerImpl  ErrorHandleUtils >com.pravera.flutter_activity_recognition.MethodCallHandlerImpl  
MethodCall >com.pravera.flutter_activity_recognition.MethodCallHandlerImpl  
MethodChannel >com.pravera.flutter_activity_recognition.MethodCallHandlerImpl  activityPermissionManager >com.pravera.flutter_activity_recognition.MethodCallHandlerImpl  binding >com.pravera.flutter_activity_recognition.MethodCallHandlerImpl  dispose >com.pravera.flutter_activity_recognition.MethodCallHandlerImpl  init >com.pravera.flutter_activity_recognition.MethodCallHandlerImpl  invoke >com.pravera.flutter_activity_recognition.MethodCallHandlerImpl  
isInitialized >com.pravera.flutter_activity_recognition.MethodCallHandlerImpl  
methodChannel >com.pravera.flutter_activity_recognition.MethodCallHandlerImpl  onAttachedToActivity >com.pravera.flutter_activity_recognition.MethodCallHandlerImpl  onDetachedFromActivity >com.pravera.flutter_activity_recognition.MethodCallHandlerImpl  
ACTIVITY_DATA 7com.pravera.flutter_activity_recognition.PreferencesKey  ACTIVITY_ERROR 7com.pravera.flutter_activity_recognition.PreferencesKey   ACTIVITY_PERMISSION_STATUS_PREFS 7com.pravera.flutter_activity_recognition.PreferencesKey  !ACTIVITY_RECOGNITION_RESULT_PREFS 7com.pravera.flutter_activity_recognition.PreferencesKey  prefix 7com.pravera.flutter_activity_recognition.PreferencesKey  ACTIVITY_DETECTED 4com.pravera.flutter_activity_recognition.RequestCode  REQUEST_ACTIVITY_PERMISSION 4com.pravera.flutter_activity_recognition.RequestCode  ActivityDataCallback :com.pravera.flutter_activity_recognition.StreamHandlerImpl  ActivityPluginBinding :com.pravera.flutter_activity_recognition.StreamHandlerImpl  ActivityRecognitionManager :com.pravera.flutter_activity_recognition.StreamHandlerImpl  Any :com.pravera.flutter_activity_recognition.StreamHandlerImpl  BinaryMessenger :com.pravera.flutter_activity_recognition.StreamHandlerImpl  Context :com.pravera.flutter_activity_recognition.StreamHandlerImpl  
ErrorCodes :com.pravera.flutter_activity_recognition.StreamHandlerImpl  ErrorHandleUtils :com.pravera.flutter_activity_recognition.StreamHandlerImpl  EventChannel :com.pravera.flutter_activity_recognition.StreamHandlerImpl  String :com.pravera.flutter_activity_recognition.StreamHandlerImpl  activityRecognitionManager :com.pravera.flutter_activity_recognition.StreamHandlerImpl  binding :com.pravera.flutter_activity_recognition.StreamHandlerImpl  context :com.pravera.flutter_activity_recognition.StreamHandlerImpl  dispose :com.pravera.flutter_activity_recognition.StreamHandlerImpl  eventChannel :com.pravera.flutter_activity_recognition.StreamHandlerImpl  init :com.pravera.flutter_activity_recognition.StreamHandlerImpl  invoke :com.pravera.flutter_activity_recognition.StreamHandlerImpl  
isInitialized :com.pravera.flutter_activity_recognition.StreamHandlerImpl  onAttachedToActivity :com.pravera.flutter_activity_recognition.StreamHandlerImpl  onDetachedFromActivity :com.pravera.flutter_activity_recognition.StreamHandlerImpl  ACTIVITY_DATA_ENCODING_FAILED /com.pravera.flutter_activity_recognition.errors  ACTIVITY_NOT_ATTACHED /com.pravera.flutter_activity_recognition.errors  %ACTIVITY_PERMISSION_REQUEST_CANCELLED /com.pravera.flutter_activity_recognition.errors  ACTIVITY_UPDATES_REMOVE_FAILED /com.pravera.flutter_activity_recognition.errors  ACTIVITY_UPDATES_REQUEST_FAILED /com.pravera.flutter_activity_recognition.errors  
ErrorCodes /com.pravera.flutter_activity_recognition.errors  String /com.pravera.flutter_activity_recognition.errors  ACTIVITY_DATA_ENCODING_FAILED :com.pravera.flutter_activity_recognition.errors.ErrorCodes  ACTIVITY_NOT_ATTACHED :com.pravera.flutter_activity_recognition.errors.ErrorCodes  %ACTIVITY_PERMISSION_REQUEST_CANCELLED :com.pravera.flutter_activity_recognition.errors.ErrorCodes  ACTIVITY_UPDATES_REMOVE_FAILED :com.pravera.flutter_activity_recognition.errors.ErrorCodes  ACTIVITY_UPDATES_REQUEST_FAILED :com.pravera.flutter_activity_recognition.errors.ErrorCodes  String :com.pravera.flutter_activity_recognition.errors.ErrorCodes  message :com.pravera.flutter_activity_recognition.errors.ErrorCodes  toString :com.pravera.flutter_activity_recognition.errors.ErrorCodes  valueOf :com.pravera.flutter_activity_recognition.errors.ErrorCodes  ActivityData /com.pravera.flutter_activity_recognition.models  ActivityPermission /com.pravera.flutter_activity_recognition.models  String /com.pravera.flutter_activity_recognition.models  SerializedName <com.pravera.flutter_activity_recognition.models.ActivityData  String <com.pravera.flutter_activity_recognition.models.ActivityData  DENIED Bcom.pravera.flutter_activity_recognition.models.ActivityPermission  GRANTED Bcom.pravera.flutter_activity_recognition.models.ActivityPermission  PERMANENTLY_DENIED Bcom.pravera.flutter_activity_recognition.models.ActivityPermission  equals Bcom.pravera.flutter_activity_recognition.models.ActivityPermission  toString Bcom.pravera.flutter_activity_recognition.models.ActivityPermission  valueOf Bcom.pravera.flutter_activity_recognition.models.ActivityPermission  ActivityCompat 0com.pravera.flutter_activity_recognition.service  ActivityData 0com.pravera.flutter_activity_recognition.service  ActivityDataCallback 0com.pravera.flutter_activity_recognition.service  ActivityPermission 0com.pravera.flutter_activity_recognition.service  ActivityPermissionCallback 0com.pravera.flutter_activity_recognition.service  ActivityPermissionManager 0com.pravera.flutter_activity_recognition.service  ActivityRecognition 0com.pravera.flutter_activity_recognition.service  ActivityRecognitionClient 0com.pravera.flutter_activity_recognition.service  !ActivityRecognitionIntentReceiver 0com.pravera.flutter_activity_recognition.service   ActivityRecognitionIntentService 0com.pravera.flutter_activity_recognition.service  ActivityRecognitionManager 0com.pravera.flutter_activity_recognition.service  ActivityRecognitionResult 0com.pravera.flutter_activity_recognition.service  ActivityRecognitionUtils 0com.pravera.flutter_activity_recognition.service  Array 0com.pravera.flutter_activity_recognition.service  Boolean 0com.pravera.flutter_activity_recognition.service  Build 0com.pravera.flutter_activity_recognition.service  Context 0com.pravera.flutter_activity_recognition.service  
ContextCompat 0com.pravera.flutter_activity_recognition.service  
ErrorCodes 0com.pravera.flutter_activity_recognition.service  	Exception 0com.pravera.flutter_activity_recognition.service  Gson 0com.pravera.flutter_activity_recognition.service  Int 0com.pravera.flutter_activity_recognition.service  IntArray 0com.pravera.flutter_activity_recognition.service  Intent 0com.pravera.flutter_activity_recognition.service  Log 0com.pravera.flutter_activity_recognition.service  Manifest 0com.pravera.flutter_activity_recognition.service  PackageManager 0com.pravera.flutter_activity_recognition.service  
PendingIntent 0com.pravera.flutter_activity_recognition.service  PreferencesKey 0com.pravera.flutter_activity_recognition.service  RequestCode 0com.pravera.flutter_activity_recognition.service  String 0com.pravera.flutter_activity_recognition.service  TAG 0com.pravera.flutter_activity_recognition.service  arrayOf 0com.pravera.flutter_activity_recognition.service  enqueueWork 0com.pravera.flutter_activity_recognition.service  indexOf 0com.pravera.flutter_activity_recognition.service  isEmpty 0com.pravera.flutter_activity_recognition.service  java 0com.pravera.flutter_activity_recognition.service  
jsonConverter 0com.pravera.flutter_activity_recognition.service  maxByOrNull 0com.pravera.flutter_activity_recognition.service  
permission 0com.pravera.flutter_activity_recognition.service  with 0com.pravera.flutter_activity_recognition.service  
ErrorCodes Ecom.pravera.flutter_activity_recognition.service.ActivityDataCallback  String Ecom.pravera.flutter_activity_recognition.service.ActivityDataCallback  onError Ecom.pravera.flutter_activity_recognition.service.ActivityDataCallback  onUpdate Ecom.pravera.flutter_activity_recognition.service.ActivityDataCallback  ActivityPermission Kcom.pravera.flutter_activity_recognition.service.ActivityPermissionCallback  
ErrorCodes Kcom.pravera.flutter_activity_recognition.service.ActivityPermissionCallback  onError Kcom.pravera.flutter_activity_recognition.service.ActivityPermissionCallback  onResult Kcom.pravera.flutter_activity_recognition.service.ActivityPermissionCallback  Activity Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  ActivityCompat Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  ActivityPermission Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  ActivityPermissionCallback Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  Array Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  Boolean Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  Build Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  Context Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  
ContextCompat Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  
ErrorCodes Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  Int Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  IntArray Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  Manifest Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  PackageManager Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  PreferencesKey Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  RequestCode Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  String Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  SuppressLint Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  activity Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  arrayOf Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  callback Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  checkPermission Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  disposeReference Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  
getARRAYOf Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  
getArrayOf Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  
getINDEXOf Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  
getISEmpty Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  
getIndexOf Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  
getIsEmpty Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  
getPERMISSION Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  
getPermission Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  getPrevPermissionStatus Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  getWITH Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  getWith Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  indexOf Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  isEmpty Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  isPermissionGranted Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  
permission Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  requestPermission Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  setPrevPermissionStatus Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  with Jcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager  Activity Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  ActivityCompat Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  ActivityPermission Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  ActivityPermissionCallback Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  Array Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  Boolean Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  Build Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  Context Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  
ContextCompat Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  
ErrorCodes Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  Int Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  IntArray Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  Manifest Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  PackageManager Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  PreferencesKey Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  RequestCode Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  String Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  SuppressLint Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  arrayOf Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  
getARRAYOf Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  
getArrayOf Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  
getINDEXOf Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  
getISEmpty Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  
getIndexOf Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  
getIsEmpty Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  getWITH Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  getWith Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  indexOf Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  invoke Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  isEmpty Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  
permission Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion  with Tcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.Companion   ActivityRecognitionIntentService Rcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentReceiver  ActivityRecognitionResult Rcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentReceiver  Context Rcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentReceiver  Intent Rcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentReceiver  ActivityData Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService   ActivityRecognitionIntentService Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  ActivityRecognitionResult Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  ActivityRecognitionUtils Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  	Companion Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  Context Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  
ErrorCodes Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  	Exception Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  Gson Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  Intent Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  PreferencesKey Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  String Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  enqueueWork Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  getJSONConverter Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  getJsonConverter Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  getMAXByOrNull Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  getMaxByOrNull Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  getSharedPreferences Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  getWITH Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  getWith Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  java Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  
jsonConverter Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  maxByOrNull Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  with Qcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService  ActivityData [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion   ActivityRecognitionIntentService [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion  ActivityRecognitionResult [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion  ActivityRecognitionUtils [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion  Context [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion  
ErrorCodes [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion  	Exception [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion  Gson [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion  Intent [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion  PreferencesKey [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion  String [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion  enqueueWork [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion  getENQUEUEWork [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion  getEnqueueWork [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion  getMAXByOrNull [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion  getMaxByOrNull [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion  getWITH [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion  getWith [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion  java [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion  
jsonConverter [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion  maxByOrNull [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion  with [com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.Companion  ActivityDataCallback Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  ActivityRecognition Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  ActivityRecognitionClient Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  !ActivityRecognitionIntentReceiver Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  ActivityRecognitionManager Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  Build Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  	Companion Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  Context Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  
ErrorCodes Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  Intent Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  Log Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  
PendingIntent Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  PreferencesKey Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  RequestCode Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  SharedPreferences Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  String Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  SuppressLint Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  TAG Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  callback Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  getPendingIntent Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  getWITH Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  getWith Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  java Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  
pendingIntent Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  &registerSharedPreferenceChangeListener Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  removeActivityUpdates Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  requestActivityUpdates Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  
serviceClient Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  startService Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  stopService Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  (unregisterSharedPreferenceChangeListener Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  with Kcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager  ActivityDataCallback Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  ActivityRecognition Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  ActivityRecognitionClient Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  !ActivityRecognitionIntentReceiver Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  ActivityRecognitionManager Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  Build Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  Context Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  
ErrorCodes Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  Intent Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  Log Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  
PendingIntent Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  PreferencesKey Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  RequestCode Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  SharedPreferences Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  String Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  SuppressLint Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  TAG Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  getWITH Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  getWith Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  invoke Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  java Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  with Ucom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.Companion  ActivityRecognitionUtils .com.pravera.flutter_activity_recognition.utils  DetectedActivity .com.pravera.flutter_activity_recognition.utils  ErrorHandleUtils .com.pravera.flutter_activity_recognition.utils  Int .com.pravera.flutter_activity_recognition.utils  String .com.pravera.flutter_activity_recognition.utils  DetectedActivity Gcom.pravera.flutter_activity_recognition.utils.ActivityRecognitionUtils  Int Gcom.pravera.flutter_activity_recognition.utils.ActivityRecognitionUtils  String Gcom.pravera.flutter_activity_recognition.utils.ActivityRecognitionUtils  getActivityConfidenceFromValue Gcom.pravera.flutter_activity_recognition.utils.ActivityRecognitionUtils  getActivityTypeFromValue Gcom.pravera.flutter_activity_recognition.utils.ActivityRecognitionUtils  DetectedActivity Qcom.pravera.flutter_activity_recognition.utils.ActivityRecognitionUtils.Companion  Int Qcom.pravera.flutter_activity_recognition.utils.ActivityRecognitionUtils.Companion  String Qcom.pravera.flutter_activity_recognition.utils.ActivityRecognitionUtils.Companion  getActivityConfidenceFromValue Qcom.pravera.flutter_activity_recognition.utils.ActivityRecognitionUtils.Companion  getActivityTypeFromValue Qcom.pravera.flutter_activity_recognition.utils.ActivityRecognitionUtils.Companion  
ErrorCodes ?com.pravera.flutter_activity_recognition.utils.ErrorHandleUtils  EventChannel ?com.pravera.flutter_activity_recognition.utils.ErrorHandleUtils  
MethodChannel ?com.pravera.flutter_activity_recognition.utils.ErrorHandleUtils  handleMethodCallError ?com.pravera.flutter_activity_recognition.utils.ErrorHandleUtils  
ErrorCodes Icom.pravera.flutter_activity_recognition.utils.ErrorHandleUtils.Companion  EventChannel Icom.pravera.flutter_activity_recognition.utils.ErrorHandleUtils.Companion  
MethodChannel Icom.pravera.flutter_activity_recognition.utils.ErrorHandleUtils.Companion  handleMethodCallError Icom.pravera.flutter_activity_recognition.utils.ErrorHandleUtils.Companion  Log 
io.flutter  d io.flutter.Log  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getAPPLICATIONContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBINARYMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  #addRequestPermissionsResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  getACTIVITY Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  getActivity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  &removeRequestPermissionsResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  setActivity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BinaryMessenger io.flutter.plugin.common  EventChannel io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  PluginRegistry io.flutter.plugin.common  	EventSink %io.flutter.plugin.common.EventChannel  
StreamHandler %io.flutter.plugin.common.EventChannel  setStreamHandler %io.flutter.plugin.common.EventChannel  error /io.flutter.plugin.common.EventChannel.EventSink  success /io.flutter.plugin.common.EventChannel.EventSink  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result   RequestPermissionsResultListener 'io.flutter.plugin.common.PluginRegistry  ACTIVITY_DATA_ENCODING_FAILED 	java.lang  ACTIVITY_NOT_ATTACHED 	java.lang  %ACTIVITY_PERMISSION_REQUEST_CANCELLED 	java.lang  ACTIVITY_UPDATES_REMOVE_FAILED 	java.lang  ACTIVITY_UPDATES_REQUEST_FAILED 	java.lang  ActivityCompat 	java.lang  ActivityData 	java.lang  ActivityPermission 	java.lang  ActivityPermissionManager 	java.lang  ActivityRecognition 	java.lang  !ActivityRecognitionIntentReceiver 	java.lang   ActivityRecognitionIntentService 	java.lang  ActivityRecognitionManager 	java.lang  ActivityRecognitionResult 	java.lang  ActivityRecognitionUtils 	java.lang  Build 	java.lang  Class 	java.lang  Context 	java.lang  
ContextCompat 	java.lang  DetectedActivity 	java.lang  
ErrorCodes 	java.lang  ErrorHandleUtils 	java.lang  EventChannel 	java.lang  	Exception 	java.lang  Gson 	java.lang  Intent 	java.lang  Log 	java.lang  Manifest 	java.lang  MethodCallHandlerImpl 	java.lang  
MethodChannel 	java.lang  PackageManager 	java.lang  
PendingIntent 	java.lang  PreferencesKey 	java.lang  RequestCode 	java.lang  StreamHandlerImpl 	java.lang  TAG 	java.lang  Void 	java.lang  arrayOf 	java.lang  enqueueWork 	java.lang  indexOf 	java.lang  isEmpty 	java.lang  
isInitialized 	java.lang  java 	java.lang  
jsonConverter 	java.lang  maxByOrNull 	java.lang  
permission 	java.lang  with 	java.lang  
getSIMPLEName java.lang.Class  
getSimpleName java.lang.Class  
setSimpleName java.lang.Class  
simpleName java.lang.Class  ACTIVITY_DATA_ENCODING_FAILED kotlin  ACTIVITY_NOT_ATTACHED kotlin  %ACTIVITY_PERMISSION_REQUEST_CANCELLED kotlin  ACTIVITY_UPDATES_REMOVE_FAILED kotlin  ACTIVITY_UPDATES_REQUEST_FAILED kotlin  ActivityCompat kotlin  ActivityData kotlin  ActivityPermission kotlin  ActivityPermissionManager kotlin  ActivityRecognition kotlin  !ActivityRecognitionIntentReceiver kotlin   ActivityRecognitionIntentService kotlin  ActivityRecognitionManager kotlin  ActivityRecognitionResult kotlin  ActivityRecognitionUtils kotlin  Any kotlin  Array kotlin  Boolean kotlin  Build kotlin  Context kotlin  
ContextCompat kotlin  DetectedActivity kotlin  
ErrorCodes kotlin  ErrorHandleUtils kotlin  EventChannel kotlin  	Exception kotlin  	Function1 kotlin  Gson kotlin  Int kotlin  IntArray kotlin  Intent kotlin  Log kotlin  Manifest kotlin  MethodCallHandlerImpl kotlin  
MethodChannel kotlin  Nothing kotlin  PackageManager kotlin  
PendingIntent kotlin  PreferencesKey kotlin  RequestCode kotlin  StreamHandlerImpl kotlin  String kotlin  TAG kotlin  Unit kotlin  arrayOf kotlin  enqueueWork kotlin  indexOf kotlin  isEmpty kotlin  
isInitialized kotlin  java kotlin  
jsonConverter kotlin  maxByOrNull kotlin  
permission kotlin  with kotlin  
getINDEXOf kotlin.Array  
getIndexOf kotlin.Array  
getISEmpty kotlin.IntArray  
getIsEmpty kotlin.IntArray  isEmpty kotlin.IntArray  ACTIVITY_DATA_ENCODING_FAILED kotlin.annotation  ACTIVITY_NOT_ATTACHED kotlin.annotation  %ACTIVITY_PERMISSION_REQUEST_CANCELLED kotlin.annotation  ACTIVITY_UPDATES_REMOVE_FAILED kotlin.annotation  ACTIVITY_UPDATES_REQUEST_FAILED kotlin.annotation  ActivityCompat kotlin.annotation  ActivityData kotlin.annotation  ActivityPermission kotlin.annotation  ActivityPermissionManager kotlin.annotation  ActivityRecognition kotlin.annotation  !ActivityRecognitionIntentReceiver kotlin.annotation   ActivityRecognitionIntentService kotlin.annotation  ActivityRecognitionManager kotlin.annotation  ActivityRecognitionResult kotlin.annotation  ActivityRecognitionUtils kotlin.annotation  Build kotlin.annotation  Context kotlin.annotation  
ContextCompat kotlin.annotation  DetectedActivity kotlin.annotation  
ErrorCodes kotlin.annotation  ErrorHandleUtils kotlin.annotation  EventChannel kotlin.annotation  	Exception kotlin.annotation  Gson kotlin.annotation  Intent kotlin.annotation  Log kotlin.annotation  Manifest kotlin.annotation  MethodCallHandlerImpl kotlin.annotation  
MethodChannel kotlin.annotation  PackageManager kotlin.annotation  
PendingIntent kotlin.annotation  PreferencesKey kotlin.annotation  RequestCode kotlin.annotation  StreamHandlerImpl kotlin.annotation  TAG kotlin.annotation  arrayOf kotlin.annotation  enqueueWork kotlin.annotation  indexOf kotlin.annotation  isEmpty kotlin.annotation  
isInitialized kotlin.annotation  java kotlin.annotation  
jsonConverter kotlin.annotation  maxByOrNull kotlin.annotation  
permission kotlin.annotation  with kotlin.annotation  ACTIVITY_DATA_ENCODING_FAILED kotlin.collections  ACTIVITY_NOT_ATTACHED kotlin.collections  %ACTIVITY_PERMISSION_REQUEST_CANCELLED kotlin.collections  ACTIVITY_UPDATES_REMOVE_FAILED kotlin.collections  ACTIVITY_UPDATES_REQUEST_FAILED kotlin.collections  ActivityCompat kotlin.collections  ActivityData kotlin.collections  ActivityPermission kotlin.collections  ActivityPermissionManager kotlin.collections  ActivityRecognition kotlin.collections  !ActivityRecognitionIntentReceiver kotlin.collections   ActivityRecognitionIntentService kotlin.collections  ActivityRecognitionManager kotlin.collections  ActivityRecognitionResult kotlin.collections  ActivityRecognitionUtils kotlin.collections  Build kotlin.collections  Context kotlin.collections  
ContextCompat kotlin.collections  DetectedActivity kotlin.collections  
ErrorCodes kotlin.collections  ErrorHandleUtils kotlin.collections  EventChannel kotlin.collections  	Exception kotlin.collections  Gson kotlin.collections  Intent kotlin.collections  Log kotlin.collections  Manifest kotlin.collections  MethodCallHandlerImpl kotlin.collections  
MethodChannel kotlin.collections  MutableList kotlin.collections  PackageManager kotlin.collections  
PendingIntent kotlin.collections  PreferencesKey kotlin.collections  RequestCode kotlin.collections  StreamHandlerImpl kotlin.collections  TAG kotlin.collections  arrayOf kotlin.collections  enqueueWork kotlin.collections  indexOf kotlin.collections  isEmpty kotlin.collections  
isInitialized kotlin.collections  java kotlin.collections  
jsonConverter kotlin.collections  maxByOrNull kotlin.collections  
permission kotlin.collections  with kotlin.collections  getMAXByOrNull kotlin.collections.MutableList  getMaxByOrNull kotlin.collections.MutableList  ACTIVITY_DATA_ENCODING_FAILED kotlin.comparisons  ACTIVITY_NOT_ATTACHED kotlin.comparisons  %ACTIVITY_PERMISSION_REQUEST_CANCELLED kotlin.comparisons  ACTIVITY_UPDATES_REMOVE_FAILED kotlin.comparisons  ACTIVITY_UPDATES_REQUEST_FAILED kotlin.comparisons  ActivityCompat kotlin.comparisons  ActivityData kotlin.comparisons  ActivityPermission kotlin.comparisons  ActivityPermissionManager kotlin.comparisons  ActivityRecognition kotlin.comparisons  !ActivityRecognitionIntentReceiver kotlin.comparisons   ActivityRecognitionIntentService kotlin.comparisons  ActivityRecognitionManager kotlin.comparisons  ActivityRecognitionResult kotlin.comparisons  ActivityRecognitionUtils kotlin.comparisons  Build kotlin.comparisons  Context kotlin.comparisons  
ContextCompat kotlin.comparisons  DetectedActivity kotlin.comparisons  
ErrorCodes kotlin.comparisons  ErrorHandleUtils kotlin.comparisons  EventChannel kotlin.comparisons  	Exception kotlin.comparisons  Gson kotlin.comparisons  Intent kotlin.comparisons  Log kotlin.comparisons  Manifest kotlin.comparisons  MethodCallHandlerImpl kotlin.comparisons  
MethodChannel kotlin.comparisons  PackageManager kotlin.comparisons  
PendingIntent kotlin.comparisons  PreferencesKey kotlin.comparisons  RequestCode kotlin.comparisons  StreamHandlerImpl kotlin.comparisons  TAG kotlin.comparisons  arrayOf kotlin.comparisons  enqueueWork kotlin.comparisons  indexOf kotlin.comparisons  isEmpty kotlin.comparisons  
isInitialized kotlin.comparisons  java kotlin.comparisons  
jsonConverter kotlin.comparisons  maxByOrNull kotlin.comparisons  
permission kotlin.comparisons  with kotlin.comparisons  ACTIVITY_DATA_ENCODING_FAILED 	kotlin.io  ACTIVITY_NOT_ATTACHED 	kotlin.io  %ACTIVITY_PERMISSION_REQUEST_CANCELLED 	kotlin.io  ACTIVITY_UPDATES_REMOVE_FAILED 	kotlin.io  ACTIVITY_UPDATES_REQUEST_FAILED 	kotlin.io  ActivityCompat 	kotlin.io  ActivityData 	kotlin.io  ActivityPermission 	kotlin.io  ActivityPermissionManager 	kotlin.io  ActivityRecognition 	kotlin.io  !ActivityRecognitionIntentReceiver 	kotlin.io   ActivityRecognitionIntentService 	kotlin.io  ActivityRecognitionManager 	kotlin.io  ActivityRecognitionResult 	kotlin.io  ActivityRecognitionUtils 	kotlin.io  Build 	kotlin.io  Context 	kotlin.io  
ContextCompat 	kotlin.io  DetectedActivity 	kotlin.io  
ErrorCodes 	kotlin.io  ErrorHandleUtils 	kotlin.io  EventChannel 	kotlin.io  	Exception 	kotlin.io  Gson 	kotlin.io  Intent 	kotlin.io  Log 	kotlin.io  Manifest 	kotlin.io  MethodCallHandlerImpl 	kotlin.io  
MethodChannel 	kotlin.io  PackageManager 	kotlin.io  
PendingIntent 	kotlin.io  PreferencesKey 	kotlin.io  RequestCode 	kotlin.io  StreamHandlerImpl 	kotlin.io  TAG 	kotlin.io  arrayOf 	kotlin.io  enqueueWork 	kotlin.io  indexOf 	kotlin.io  isEmpty 	kotlin.io  
isInitialized 	kotlin.io  java 	kotlin.io  
jsonConverter 	kotlin.io  maxByOrNull 	kotlin.io  
permission 	kotlin.io  with 	kotlin.io  ACTIVITY_DATA_ENCODING_FAILED 
kotlin.jvm  ACTIVITY_NOT_ATTACHED 
kotlin.jvm  %ACTIVITY_PERMISSION_REQUEST_CANCELLED 
kotlin.jvm  ACTIVITY_UPDATES_REMOVE_FAILED 
kotlin.jvm  ACTIVITY_UPDATES_REQUEST_FAILED 
kotlin.jvm  ActivityCompat 
kotlin.jvm  ActivityData 
kotlin.jvm  ActivityPermission 
kotlin.jvm  ActivityPermissionManager 
kotlin.jvm  ActivityRecognition 
kotlin.jvm  !ActivityRecognitionIntentReceiver 
kotlin.jvm   ActivityRecognitionIntentService 
kotlin.jvm  ActivityRecognitionManager 
kotlin.jvm  ActivityRecognitionResult 
kotlin.jvm  ActivityRecognitionUtils 
kotlin.jvm  Build 
kotlin.jvm  Context 
kotlin.jvm  
ContextCompat 
kotlin.jvm  DetectedActivity 
kotlin.jvm  
ErrorCodes 
kotlin.jvm  ErrorHandleUtils 
kotlin.jvm  EventChannel 
kotlin.jvm  	Exception 
kotlin.jvm  Gson 
kotlin.jvm  Intent 
kotlin.jvm  Log 
kotlin.jvm  Manifest 
kotlin.jvm  MethodCallHandlerImpl 
kotlin.jvm  
MethodChannel 
kotlin.jvm  PackageManager 
kotlin.jvm  
PendingIntent 
kotlin.jvm  PreferencesKey 
kotlin.jvm  RequestCode 
kotlin.jvm  StreamHandlerImpl 
kotlin.jvm  TAG 
kotlin.jvm  arrayOf 
kotlin.jvm  enqueueWork 
kotlin.jvm  indexOf 
kotlin.jvm  isEmpty 
kotlin.jvm  
isInitialized 
kotlin.jvm  java 
kotlin.jvm  
jsonConverter 
kotlin.jvm  maxByOrNull 
kotlin.jvm  
permission 
kotlin.jvm  with 
kotlin.jvm  ACTIVITY_DATA_ENCODING_FAILED 
kotlin.ranges  ACTIVITY_NOT_ATTACHED 
kotlin.ranges  %ACTIVITY_PERMISSION_REQUEST_CANCELLED 
kotlin.ranges  ACTIVITY_UPDATES_REMOVE_FAILED 
kotlin.ranges  ACTIVITY_UPDATES_REQUEST_FAILED 
kotlin.ranges  ActivityCompat 
kotlin.ranges  ActivityData 
kotlin.ranges  ActivityPermission 
kotlin.ranges  ActivityPermissionManager 
kotlin.ranges  ActivityRecognition 
kotlin.ranges  !ActivityRecognitionIntentReceiver 
kotlin.ranges   ActivityRecognitionIntentService 
kotlin.ranges  ActivityRecognitionManager 
kotlin.ranges  ActivityRecognitionResult 
kotlin.ranges  ActivityRecognitionUtils 
kotlin.ranges  Build 
kotlin.ranges  Context 
kotlin.ranges  
ContextCompat 
kotlin.ranges  DetectedActivity 
kotlin.ranges  
ErrorCodes 
kotlin.ranges  ErrorHandleUtils 
kotlin.ranges  EventChannel 
kotlin.ranges  	Exception 
kotlin.ranges  Gson 
kotlin.ranges  IntRange 
kotlin.ranges  Intent 
kotlin.ranges  Log 
kotlin.ranges  Manifest 
kotlin.ranges  MethodCallHandlerImpl 
kotlin.ranges  
MethodChannel 
kotlin.ranges  PackageManager 
kotlin.ranges  
PendingIntent 
kotlin.ranges  PreferencesKey 
kotlin.ranges  RequestCode 
kotlin.ranges  StreamHandlerImpl 
kotlin.ranges  TAG 
kotlin.ranges  arrayOf 
kotlin.ranges  enqueueWork 
kotlin.ranges  indexOf 
kotlin.ranges  isEmpty 
kotlin.ranges  
isInitialized 
kotlin.ranges  java 
kotlin.ranges  
jsonConverter 
kotlin.ranges  maxByOrNull 
kotlin.ranges  
permission 
kotlin.ranges  with 
kotlin.ranges  contains kotlin.ranges.IntProgression  contains kotlin.ranges.IntRange  KClass kotlin.reflect  KMutableProperty0 kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  getISInitialized  kotlin.reflect.KMutableProperty0  getIsInitialized  kotlin.reflect.KMutableProperty0  
isInitialized  kotlin.reflect.KMutableProperty0  ACTIVITY_DATA_ENCODING_FAILED kotlin.sequences  ACTIVITY_NOT_ATTACHED kotlin.sequences  %ACTIVITY_PERMISSION_REQUEST_CANCELLED kotlin.sequences  ACTIVITY_UPDATES_REMOVE_FAILED kotlin.sequences  ACTIVITY_UPDATES_REQUEST_FAILED kotlin.sequences  ActivityCompat kotlin.sequences  ActivityData kotlin.sequences  ActivityPermission kotlin.sequences  ActivityPermissionManager kotlin.sequences  ActivityRecognition kotlin.sequences  !ActivityRecognitionIntentReceiver kotlin.sequences   ActivityRecognitionIntentService kotlin.sequences  ActivityRecognitionManager kotlin.sequences  ActivityRecognitionResult kotlin.sequences  ActivityRecognitionUtils kotlin.sequences  Build kotlin.sequences  Context kotlin.sequences  
ContextCompat kotlin.sequences  DetectedActivity kotlin.sequences  
ErrorCodes kotlin.sequences  ErrorHandleUtils kotlin.sequences  EventChannel kotlin.sequences  	Exception kotlin.sequences  Gson kotlin.sequences  Intent kotlin.sequences  Log kotlin.sequences  Manifest kotlin.sequences  MethodCallHandlerImpl kotlin.sequences  
MethodChannel kotlin.sequences  PackageManager kotlin.sequences  
PendingIntent kotlin.sequences  PreferencesKey kotlin.sequences  RequestCode kotlin.sequences  StreamHandlerImpl kotlin.sequences  TAG kotlin.sequences  arrayOf kotlin.sequences  enqueueWork kotlin.sequences  indexOf kotlin.sequences  isEmpty kotlin.sequences  
isInitialized kotlin.sequences  java kotlin.sequences  
jsonConverter kotlin.sequences  maxByOrNull kotlin.sequences  
permission kotlin.sequences  with kotlin.sequences  ACTIVITY_DATA_ENCODING_FAILED kotlin.text  ACTIVITY_NOT_ATTACHED kotlin.text  %ACTIVITY_PERMISSION_REQUEST_CANCELLED kotlin.text  ACTIVITY_UPDATES_REMOVE_FAILED kotlin.text  ACTIVITY_UPDATES_REQUEST_FAILED kotlin.text  ActivityCompat kotlin.text  ActivityData kotlin.text  ActivityPermission kotlin.text  ActivityPermissionManager kotlin.text  ActivityRecognition kotlin.text  !ActivityRecognitionIntentReceiver kotlin.text   ActivityRecognitionIntentService kotlin.text  ActivityRecognitionManager kotlin.text  ActivityRecognitionResult kotlin.text  ActivityRecognitionUtils kotlin.text  Build kotlin.text  Context kotlin.text  
ContextCompat kotlin.text  DetectedActivity kotlin.text  
ErrorCodes kotlin.text  ErrorHandleUtils kotlin.text  EventChannel kotlin.text  	Exception kotlin.text  Gson kotlin.text  Intent kotlin.text  Log kotlin.text  Manifest kotlin.text  MethodCallHandlerImpl kotlin.text  
MethodChannel kotlin.text  PackageManager kotlin.text  
PendingIntent kotlin.text  PreferencesKey kotlin.text  RequestCode kotlin.text  StreamHandlerImpl kotlin.text  TAG kotlin.text  arrayOf kotlin.text  enqueueWork kotlin.text  indexOf kotlin.text  isEmpty kotlin.text  
isInitialized kotlin.text  java kotlin.text  
jsonConverter kotlin.text  maxByOrNull kotlin.text  
permission kotlin.text  with kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            