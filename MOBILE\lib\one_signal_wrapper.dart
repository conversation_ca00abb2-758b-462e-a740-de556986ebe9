import 'package:asuka/asuka.dart' as asuka;
import 'package:flutter/foundation.dart';
// import 'package:octalog/src/utils/colors.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
// import 'package:asuka/asuka.dart';
import 'package:flutter/material.dart';
//import 'package:custom_colors/custom_colors.dart';

class OneSignalService {
  static final OneSignalService _instance = OneSignalService._internal();

  factory OneSignalService() => _instance;

  static String appId = "116185c8-33eb-4da9-b042-1d82d8c4482e";

  OneSignalService._internal();

  Future<void> init() async {
    if (kDebugMode) {
      OneSignal.Debug.setLogLevel(OSLogLevel.error);
    } else {
      OneSignal.Debug.setLogLevel(OSLogLevel.none);
    }

    OneSignal.initialize(appId);

    await OneSignal.Notifications.requestPermission(true);

    OneSignal.Notifications.addForegroundWillDisplayListener((event) {
      final data = event.notification.additionalData;

      if (data != null && data.containsKey('trigger_in_app')) {
        asuka.Asuka.showDialog(
          barrierColor: Colors.black.withOpacity(0.5),
          builder: (context) {
            final media = MediaQuery.of(context).size;

            return Dialog(
              backgroundColor: Theme.of(context).dialogBackgroundColor,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              child: SizedBox(
                width: media.width * 0.8, // opcional, pode ajustar
                height: media.height * 0.5, // 50% da altura da tela
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(event.notification.title ?? 'Notificação', style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold), textAlign: TextAlign.center),
                      const SizedBox(height: 20),
                      Text(event.notification.body ?? '', textAlign: TextAlign.center, style: const TextStyle(fontSize: 20)),const SizedBox(height: 20),
                      const SizedBox(height: 20),
                      Text(data['message'] ?? '', textAlign: TextAlign.center, style: const TextStyle(fontSize: 25, fontStyle: FontStyle.italic)),
                      const Spacer(),
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text(
                          'Fechar',
                          textAlign: TextAlign.right,
                          style: TextStyle(color: Colors.orange), // ou ThemeColors.customOrange(context)
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );

        event.preventDefault();
      }
    });
  }

  void salveTags(Map<String, dynamic> tags) {
    final userid = userId ?? '';

    if (userid.isNotEmpty) {
      OneSignal.User.addTags({"user_id": userid});
    }

    final convertedTags = tags.map((key, value) => MapEntry(key, value.toString()));
    OneSignal.User.addTags(convertedTags);
  }

  void deleteTags() async {
    OneSignal.User.removeTag("user_id");

    final allTags = await OneSignal.User.getTags();

    for (var key in allTags.keys.toList()) {
      OneSignal.User.removeTag(key);
    }
  }

  String? get userId => OneSignal.User.pushSubscription.id;
}
