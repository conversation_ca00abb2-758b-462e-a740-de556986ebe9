// PreviewPdf
//
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:octalog/src/components/buttom_ls/button_ls_custom.dart';

import '../../../utils/colors-dart';

class PreviewPdf extends StatelessWidget {
  final PlatformFile file;
  const PreviewPdf({super.key, required this.file});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Builder(
          builder: (context) {
            String stringFile = file.extension!.toLowerCase();
            if (stringFile == 'jpg' ||
                stringFile == 'jpeg' ||
                stringFile == 'png' ||
                stringFile == 'heic') {
              return Column(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 30),
                      child: Image.file(
                        File(file.path!),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Expanded(
                          flex: 2,
                          child: ButtonLsCustom(
                            onPressed: () {
                              Navigator.pop(context, false);
                            },
                            colorBackground: ThemeColors.customGrey(context),
                            text: "CANCELAR",
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          flex: 3,
                          child: ButtonLsCustom(
                            onPressed: () {
                              Navigator.pop(context, true);
                            },
                            text: "CONFIRMAR",
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 10)
                ],
              );
            }
            if (stringFile == 'pdf') {
              return Column(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: PDFView(
                        filePath: file.path,
                        enableSwipe: true,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Expanded(
                          flex: 2,
                          child: ButtonLsCustom(
                            onPressed: () {
                              Navigator.pop(context, false);
                            },
                            colorBackground: ThemeColors.customGrey(context),
                            text: "CANCELAR",
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          flex: 3,
                          child: ButtonLsCustom(
                            onPressed: () {
                              Navigator.pop(context, true);
                            },
                            text: "CONFIRMAR",
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 10)
                ],
              );
            }
            return const Center(
              child: Text("Arquivo não suportado"),
            );
          },
        ),
      ),
    );
  }
}
