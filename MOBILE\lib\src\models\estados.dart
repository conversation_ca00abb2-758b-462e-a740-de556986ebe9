class Estado {
  final String uf;
  final String nome;
  Estado({
    required this.uf,
    required this.nome,
  });
}

List<Estado> estados = <Estado>[
  <PERSON><PERSON><PERSON>(uf: 'AC', nome: 'Acre'),
  <PERSON><PERSON><PERSON>(uf: 'AL', nome: 'Alagoas'),
  <PERSON><PERSON><PERSON>(uf: 'AP', nome: 'Amap<PERSON>'),
  <PERSON><PERSON><PERSON>(uf: 'AM', nome: 'Amazonas'),
  Estado(uf: 'BA', nome: 'Bahia'),
  <PERSON><PERSON>o(uf: 'CE', nome: 'Ceará'),
  Estado(uf: 'DF', nome: 'Distrito Federal'),
  Estado(uf: 'ES', nome: 'Espírito Santo'),
  Estado(uf: 'GO', nome: 'Goi<PERSON>'),
  Estado(uf: 'MA', nome: 'Maranhão'),
  Estado(uf: 'MT', nome: 'Mato Grosso'),
  Estado(uf: 'MS', nome: 'Mato Grosso do Sul'),
  Estado(uf: 'MG', nome: 'Minas Gerais'),
  <PERSON><PERSON>o(uf: 'PA', nome: '<PERSON><PERSON><PERSON>'),
  <PERSON><PERSON><PERSON>(uf: 'PB', nome: '<PERSON>í<PERSON>'),
  Estad<PERSON>(uf: 'PR', nome: 'Paraná'),
  Estado(uf: 'PE', nome: 'Pernambuco'),
  Estado(uf: 'PI', nome: 'Piauí'),
  Estado(uf: 'RJ', nome: 'Rio de Janeiro'),
  Estado(uf: 'RN', nome: 'Rio Grande do Norte'),
  Estado(uf: 'RS', nome: 'Rio Grande do Sul'),
  Estado(uf: 'RO', nome: 'Rondônia'),
  Estado(uf: 'RR', nome: 'Roraima'),
  Estado(uf: 'SC', nome: 'Santa Catarina'),
  Estado(uf: 'SP', nome: 'São Paulo'),
  Estado(uf: 'SE', nome: 'Sergipe'),
  Estado(uf: 'TO', nome: 'Tocantins'),
];
