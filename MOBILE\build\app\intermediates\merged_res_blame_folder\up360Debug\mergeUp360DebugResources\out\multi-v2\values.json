{"logs": [{"outputFile": "com.octalog.app-mergeUp360DebugResources-68:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e22c70eb4700b3262896da0db5fa32c7\\transformed\\jetified-in-app-messages-5.1.29\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,231", "endColumns": "175,84", "endOffsets": "226,311"}, "to": {"startLines": "444,445", "startColumns": "4,4", "startOffsets": "28196,28372", "endColumns": "175,84", "endOffsets": "28367,28452"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\504a3b9bc759ca6028567aaea36c5498\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "330,346,374,3136,3141", "startColumns": "4,4,4,4,4", "startOffsets": "20079,20833,22308,177877,178047", "endLines": "330,346,374,3140,3144", "endColumns": "56,64,63,24,24", "endOffsets": "20131,20893,22367,178042,178191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\23aabf00f3a34e0b600bc8ec6a919265\\transformed\\work-runtime-2.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "64,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "2266,2331,2401,2465", "endColumns": "64,69,63,60", "endOffsets": "2326,2396,2460,2521"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\87eca1956ddb86a2a7193da8df59556f\\transformed\\jetified-activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "343,371", "startColumns": "4,4", "startOffsets": "20689,22144", "endColumns": "41,59", "endOffsets": "20726,22199"}}, {"source": "C:\\projetos\\octa.log\\MOBILE\\build\\app\\generated\\res\\resValues\\up360\\debug\\values\\gradleResValues.xml", "from": {"startLines": "6", "startColumns": "4", "startOffsets": "165", "endColumns": "63", "endOffsets": "224"}, "to": {"startLines": "411", "startColumns": "4", "startOffsets": "24828", "endColumns": "63", "endOffsets": "24887"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\07660f349a2fe95c07a6bc7026689012\\transformed\\jetified-play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "99,100,101,102,103,104,105,106,419,420,421,422,423,424,425,426,428,429,430,431,432,433,434,435,436,3230,3646", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4776,4866,4946,5036,5126,5206,5287,5367,25403,25508,25689,25814,25921,26101,26224,26340,26610,26798,26903,27084,27209,27384,27532,27595,27657,180590,194128", "endLines": "99,100,101,102,103,104,105,106,419,420,421,422,423,424,425,426,428,429,430,431,432,433,434,435,436,3242,3664", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4861,4941,5031,5121,5201,5282,5362,5442,25503,25684,25809,25916,26096,26219,26335,26438,26793,26898,27079,27204,27379,27527,27590,27652,27731,180900,194540"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\52f643eeb6fba9363586e00d8d04b656\\transformed\\jetified-location-5.1.29\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,127", "endColumns": "71,93", "endOffsets": "122,216"}, "to": {"startLines": "446,447", "startColumns": "4,4", "startOffsets": "28457,28529", "endColumns": "71,93", "endOffsets": "28524,28618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cdc1fd5a4f8cb18094ee01b408d70e6e\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "5,28,29,60,61,62,68,69,70,71,72,73,74,77,78,79,80,81,82,83,84,85,86,91,92,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,245,246,250,251,252,253,254,255,256,282,283,284,285,286,287,288,289,325,326,327,328,333,341,342,347,369,375,376,377,378,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,457,462,463,464,465,466,467,475,476,480,484,495,500,506,513,517,521,526,530,534,538,542,546,550,556,560,566,570,576,580,585,589,592,596,602,606,612,616,622,625,629,633,637,641,645,646,647,648,651,654,657,660,664,665,666,667,668,671,673,675,677,682,683,687,693,697,698,700,712,713,717,723,727,728,729,733,760,764,765,769,797,969,995,1166,1192,1223,1231,1237,1253,1275,1280,1285,1295,1304,1313,1317,1324,1343,1350,1351,1360,1363,1366,1370,1374,1378,1381,1382,1387,1392,1402,1407,1414,1420,1421,1424,1428,1433,1435,1437,1440,1443,1445,1449,1452,1459,1462,1465,1469,1471,1475,1477,1479,1481,1485,1493,1501,1513,1519,1528,1531,1542,1545,1546,1551,1552,1589,1658,1728,1729,1739,1748,1900,1902,1906,1909,1912,1915,1918,1921,1924,1927,1931,1934,1937,1940,1944,1947,1951,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1977,1979,1980,1981,1982,1983,1984,1985,1986,1988,1989,1991,1992,1994,1996,1997,1999,2000,2001,2002,2003,2004,2006,2007,2008,2009,2010,2022,2024,2026,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2042,2043,2044,2045,2046,2047,2048,2050,2054,2066,2067,2068,2069,2070,2071,2075,2076,2077,2078,2080,2082,2084,2086,2088,2089,2090,2091,2093,2095,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2111,2112,2113,2114,2116,2118,2119,2121,2122,2124,2126,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2141,2142,2143,2144,2146,2147,2148,2149,2150,2152,2154,2156,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2178,2253,2256,2259,2262,2276,2293,2335,2338,2367,2394,2403,2467,2835,2884,2922,3060,3184,3208,3214,3243,3264,3388,3416,3422,3566,3598,3665,3736,3836,3856,3911,3923,3949", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "277,943,988,2035,2076,2131,2526,2590,2660,2721,2796,2872,2949,3187,3272,3354,3430,3506,3583,3661,3767,3873,3952,4281,4338,5447,5521,5596,5661,5727,5787,5848,5920,5993,6060,6128,6187,6246,6305,6364,6423,6477,6531,6584,6638,6692,6746,7001,7075,7154,7227,7301,7372,7444,7516,7589,7646,7704,7777,7851,7925,8000,8072,8145,8215,8286,8346,8407,8476,8545,8615,8689,8765,8829,8906,8982,9059,9124,9193,9270,9345,9414,9482,9559,9625,9686,9783,9848,9917,10016,10087,10146,10204,10261,10320,10384,10455,10527,10599,10671,10743,10810,10878,10946,11005,11068,11132,11222,11313,11373,11439,11506,11572,11642,11706,11759,11826,11887,11954,12067,12125,12188,12253,12318,12393,12466,12538,12582,12629,12675,12724,12785,12846,12907,12969,13033,13097,13161,13226,13289,13349,13410,13476,13535,13595,13657,13728,13788,14657,14743,14993,15083,15170,15258,15340,15423,15513,17238,17290,17348,17393,17459,17523,17580,17637,19814,19871,19919,19968,20223,20593,20640,20898,22069,22372,22436,22498,22558,22879,22953,23023,23101,23155,23225,23310,23358,23404,23465,23528,23594,23658,23729,23792,23857,23921,23982,24043,24095,24168,24242,24311,24386,24460,24534,24675,29255,29616,29694,29784,29872,29968,30058,30640,30729,30976,31257,31923,32208,32601,33078,33300,33522,33798,34025,34255,34485,34715,34945,35172,35591,35817,36242,36472,36900,37119,37402,37610,37741,37968,38394,38619,39046,39267,39692,39812,40088,40389,40713,41004,41318,41455,41586,41691,41933,42100,42304,42512,42783,42895,43007,43112,43229,43443,43589,43729,43815,44163,44251,44497,44915,45164,45246,45344,46001,46101,46353,46777,47032,47126,47215,47452,49476,49718,49820,50073,52229,62910,64426,75121,76649,78406,79032,79452,80713,81978,82234,82470,83017,83511,84116,84314,84894,86262,86637,86755,87293,87450,87646,87919,88175,88345,88486,88550,88915,89282,89958,90222,90560,90913,91007,91193,91499,91761,91886,92013,92252,92463,92582,92775,92952,93407,93588,93710,93969,94082,94269,94371,94478,94607,94882,95390,95886,96763,97057,97627,97776,98508,98680,98764,99100,99192,101576,106807,112178,112240,112818,113402,121349,121462,121691,121851,122003,122174,122340,122509,122676,122839,123082,123252,123425,123596,123870,124069,124274,124604,124688,124784,124880,124978,125078,125180,125282,125384,125486,125588,125688,125784,125896,126025,126148,126279,126410,126508,126622,126716,126856,126990,127086,127198,127298,127414,127510,127622,127722,127862,127998,128162,128292,128450,128600,128741,128885,129020,129132,129282,129410,129538,129674,129806,129936,130066,130178,131076,131222,131366,131504,131570,131660,131736,131840,131930,132032,132140,132248,132348,132428,132520,132618,132728,132780,132858,132964,133056,133160,133270,133392,133555,134122,134202,134302,134392,134502,134592,134833,134927,135033,135125,135225,135337,135451,135567,135683,135777,135891,136003,136105,136225,136347,136429,136533,136653,136779,136877,136971,137059,137171,137287,137409,137521,137696,137812,137898,137990,138102,138226,138293,138419,138487,138615,138759,138887,138956,139051,139166,139279,139378,139487,139598,139709,139810,139915,140015,140145,140236,140359,140453,140565,140651,140755,140851,140939,141057,141161,141265,141391,141479,141587,141687,141777,141887,141971,142073,142157,142211,142275,142381,142467,142577,142661,143065,145681,145799,145914,145994,146355,146941,148345,148423,149767,151128,151516,154359,164597,166260,167931,174744,179045,179796,180058,180905,181284,185562,186416,186645,191253,192593,194545,196945,201069,201813,203944,204284,205595", "endLines": "5,28,29,60,61,62,68,69,70,71,72,73,74,77,78,79,80,81,82,83,84,85,86,91,92,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,245,246,250,251,252,253,254,255,256,282,283,284,285,286,287,288,289,325,326,327,328,333,341,342,347,369,375,376,377,378,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,457,462,463,464,465,466,474,475,479,483,487,499,505,512,516,520,525,529,533,537,541,545,549,555,559,565,569,575,579,584,588,591,595,601,605,611,615,621,624,628,632,636,640,644,645,646,647,650,653,656,659,663,664,665,666,667,670,672,674,676,681,682,686,692,696,697,699,711,712,716,722,726,727,728,732,759,763,764,768,796,968,994,1165,1191,1222,1230,1236,1252,1274,1279,1284,1294,1303,1312,1316,1323,1342,1349,1350,1359,1362,1365,1369,1373,1377,1380,1381,1386,1391,1401,1406,1413,1419,1420,1423,1427,1432,1434,1436,1439,1442,1444,1448,1451,1458,1461,1464,1468,1470,1474,1476,1478,1480,1484,1492,1500,1512,1518,1527,1530,1541,1544,1545,1550,1551,1556,1657,1727,1728,1738,1747,1748,1901,1905,1908,1911,1914,1917,1920,1923,1926,1930,1933,1936,1939,1943,1946,1950,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1976,1978,1979,1980,1981,1982,1983,1984,1985,1987,1988,1990,1991,1993,1995,1996,1998,1999,2000,2001,2002,2003,2005,2006,2007,2008,2009,2010,2023,2025,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2041,2042,2043,2044,2045,2046,2047,2049,2053,2057,2066,2067,2068,2069,2070,2074,2075,2076,2077,2079,2081,2083,2085,2087,2088,2089,2090,2092,2094,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2110,2111,2112,2113,2115,2117,2118,2120,2121,2123,2125,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2140,2141,2142,2143,2145,2146,2147,2148,2149,2151,2153,2155,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2252,2255,2258,2261,2275,2281,2302,2337,2366,2393,2402,2466,2829,2838,2911,2949,3077,3207,3213,3219,3263,3387,3407,3421,3425,3571,3632,3676,3801,3855,3910,3922,3948,3955", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "327,983,1032,2071,2126,2188,2585,2655,2716,2791,2867,2944,3022,3267,3349,3425,3501,3578,3656,3762,3868,3947,4027,4333,4391,5516,5591,5656,5722,5782,5843,5915,5988,6055,6123,6182,6241,6300,6359,6418,6472,6526,6579,6633,6687,6741,6795,7070,7149,7222,7296,7367,7439,7511,7584,7641,7699,7772,7846,7920,7995,8067,8140,8210,8281,8341,8402,8471,8540,8610,8684,8760,8824,8901,8977,9054,9119,9188,9265,9340,9409,9477,9554,9620,9681,9778,9843,9912,10011,10082,10141,10199,10256,10315,10379,10450,10522,10594,10666,10738,10805,10873,10941,11000,11063,11127,11217,11308,11368,11434,11501,11567,11637,11701,11754,11821,11882,11949,12062,12120,12183,12248,12313,12388,12461,12533,12577,12624,12670,12719,12780,12841,12902,12964,13028,13092,13156,13221,13284,13344,13405,13471,13530,13590,13652,13723,13783,13851,14738,14825,15078,15165,15253,15335,15418,15508,15599,17285,17343,17388,17454,17518,17575,17632,17686,19866,19914,19963,20014,20252,20635,20684,20939,22096,22431,22493,22553,22610,22948,23018,23096,23150,23220,23305,23353,23399,23460,23523,23589,23653,23724,23787,23852,23916,23977,24038,24090,24163,24237,24306,24381,24455,24529,24670,24740,29303,29689,29779,29867,29963,30053,30635,30724,30971,31252,31504,32203,32596,33073,33295,33517,33793,34020,34250,34480,34710,34940,35167,35586,35812,36237,36467,36895,37114,37397,37605,37736,37963,38389,38614,39041,39262,39687,39807,40083,40384,40708,40999,41313,41450,41581,41686,41928,42095,42299,42507,42778,42890,43002,43107,43224,43438,43584,43724,43810,44158,44246,44492,44910,45159,45241,45339,45996,46096,46348,46772,47027,47121,47210,47447,49471,49713,49815,50068,52224,62905,64421,75116,76644,78401,79027,79447,80708,81973,82229,82465,83012,83506,84111,84309,84889,86257,86632,86750,87288,87445,87641,87914,88170,88340,88481,88545,88910,89277,89953,90217,90555,90908,91002,91188,91494,91756,91881,92008,92247,92458,92577,92770,92947,93402,93583,93705,93964,94077,94264,94366,94473,94602,94877,95385,95881,96758,97052,97622,97771,98503,98675,98759,99095,99187,99465,106802,112173,112235,112813,113397,113488,121457,121686,121846,121998,122169,122335,122504,122671,122834,123077,123247,123420,123591,123865,124064,124269,124599,124683,124779,124875,124973,125073,125175,125277,125379,125481,125583,125683,125779,125891,126020,126143,126274,126405,126503,126617,126711,126851,126985,127081,127193,127293,127409,127505,127617,127717,127857,127993,128157,128287,128445,128595,128736,128880,129015,129127,129277,129405,129533,129669,129801,129931,130061,130173,130313,131217,131361,131499,131565,131655,131731,131835,131925,132027,132135,132243,132343,132423,132515,132613,132723,132775,132853,132959,133051,133155,133265,133387,133550,133707,134197,134297,134387,134497,134587,134828,134922,135028,135120,135220,135332,135446,135562,135678,135772,135886,135998,136100,136220,136342,136424,136528,136648,136774,136872,136966,137054,137166,137282,137404,137516,137691,137807,137893,137985,138097,138221,138288,138414,138482,138610,138754,138882,138951,139046,139161,139274,139373,139482,139593,139704,139805,139910,140010,140140,140231,140354,140448,140560,140646,140750,140846,140934,141052,141156,141260,141386,141474,141582,141682,141772,141882,141966,142068,142152,142206,142270,142376,142462,142572,142656,142776,145676,145794,145909,145989,146350,146583,147453,148418,149762,151123,151511,154354,164407,164727,167625,169283,175311,179791,180053,180253,181279,185557,186163,186640,186791,191463,193671,194852,199966,201808,203939,204279,205590,205793"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1f37faca982b225960866422b14b0e48\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "372", "startColumns": "4", "startOffsets": "22204", "endColumns": "53", "endOffsets": "22253"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\765cbe31314e92111ddb6ec950f59a6b\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "4,2175,2950,2956", "startColumns": "4,4,4,4", "startOffsets": "216,142920,169288,169499", "endLines": "4,2177,2955,3039", "endColumns": "60,12,24,24", "endOffsets": "272,143060,169494,174010"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1a36ee1e87beff061ef9f2c053c4f171\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "87,88,89,90,233,234,438,440,441,442", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "4032,4090,4156,4219,13856,13927,27774,27899,27966,28045", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "4085,4151,4214,4276,13922,13994,27837,27961,28040,28109"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c4042ec9e8cc7d9cdf61197d11f01375\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "410", "startColumns": "4", "startOffsets": "24745", "endColumns": "82", "endOffsets": "24823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\73db24a2829d8fb9cf5663cfbb96cfeb\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,6,12,20,31,43,49,55,56,57,58,59,329,2282,2288,3677,3685,3700", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,332,505,724,1097,1411,1599,1786,1839,1899,1951,1996,20019,146588,146783,194857,195139,195753", "endLines": "2,11,19,27,42,48,54,55,56,57,58,59,329,2287,2292,3684,3699,3715", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,500,719,938,1406,1594,1781,1834,1894,1946,1991,2030,20074,146778,146936,195134,195748,196402"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\54b8830a716a856dc674cceb64efb5c6\\transformed\\jetified-android-pdf-viewer-3.2.0-beta.3\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "7", "endColumns": "24", "endOffsets": "380"}, "to": {"startLines": "3592", "startColumns": "4", "startOffsets": "192263", "endLines": "3597", "endColumns": "24", "endOffsets": "192588"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0df03071d3ea5a13ca8a701a85da690b\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "373", "startColumns": "4", "startOffsets": "22258", "endColumns": "49", "endOffsets": "22303"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fec79dfcef552889e3835cc7bab572a\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "63,131,275,276,277,278,279,280,281,338,339,340,380,381,437,439,448,454,459,460,461,1557,1749,1752,1758,1764,1767,1773,1777,1780,1787,1793,1796,1802,1807,1812,1819,1821,1827,1833,1841,1846,1853,1858,1864,1868,1875,1879,1885,1891,1894,1898,1899,2830,2873,3040,3078,3220,3408,3426,3490,3500,3510,3517,3523,3633,3802,3819", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2193,6932,16805,16869,16924,16992,17059,17124,17181,20436,20484,20532,22683,22746,27736,27842,28623,29115,29379,29518,29568,99470,113493,113598,113843,114181,114327,114667,114879,115042,115449,115787,115910,116249,116488,116745,117116,117176,117514,117800,118249,118541,118929,119234,119578,119823,120153,120360,120628,120901,121045,121246,121293,164412,165859,174015,175316,180258,186168,186796,188721,189003,189308,189570,189830,193676,199971,200501", "endLines": "63,131,275,276,277,278,279,280,281,338,339,340,380,381,437,439,448,456,459,460,461,1573,1751,1757,1763,1766,1772,1776,1779,1786,1792,1795,1801,1806,1811,1818,1820,1826,1832,1840,1845,1852,1857,1863,1867,1874,1878,1884,1890,1893,1897,1898,1899,2834,2883,3059,3081,3229,3415,3489,3499,3509,3516,3522,3565,3645,3818,3835", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2261,6996,16864,16919,16987,17054,17119,17176,17233,20479,20527,20588,22741,22804,27769,27894,28662,29250,29513,29563,29611,100903,113593,113838,114176,114322,114662,114874,115037,115444,115782,115905,116244,116483,116740,117111,117171,117509,117795,118244,118536,118924,119229,119573,119818,120148,120355,120623,120896,121040,121241,121288,121344,164592,166255,174739,175460,180585,186411,188716,188998,189303,189565,189825,191248,194123,200496,201064"}}, {"source": "C:\\projetos\\octa.log\\MOBILE\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1582,1586", "startColumns": "4,4", "startOffsets": "101226,101407", "endLines": "1585,1588", "endColumns": "12,12", "endOffsets": "101402,101571"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f46bbd13206153774356ca6103aa890\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "30,75,76,93,94,129,130,238,239,240,241,242,243,244,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,335,336,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,382,412,413,414,415,416,417,418,458,2011,2012,2016,2017,2021,2173,2174,2839,2912,3082,3115,3145,3178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1037,3027,3099,4396,4461,6800,6869,14169,14239,14307,14379,14449,14510,14584,15827,15888,15949,16011,16075,16137,16198,16266,16366,16426,16492,16565,16634,16691,16743,17691,17763,17839,17904,17963,18022,18082,18142,18202,18262,18322,18382,18442,18502,18562,18622,18681,18741,18801,18861,18921,18981,19041,19101,19161,19221,19281,19340,19400,19460,19519,19578,19637,19696,19755,20323,20358,20944,20999,21062,21117,21175,21233,21294,21357,21414,21465,21515,21576,21633,21699,21733,21768,22809,24892,24959,25031,25100,25169,25243,25315,29308,130318,130435,130636,130746,130947,142781,142853,164732,167630,175465,177196,178196,178878", "endLines": "30,75,76,93,94,129,130,238,239,240,241,242,243,244,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,335,336,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,382,412,413,414,415,416,417,418,458,2011,2015,2016,2020,2021,2173,2174,2844,2921,3114,3135,3177,3183", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1092,3094,3182,4456,4522,6864,6927,14234,14302,14374,14444,14505,14579,14652,15883,15944,16006,16070,16132,16193,16261,16361,16421,16487,16560,16629,16686,16738,16800,17758,17834,17899,17958,18017,18077,18137,18197,18257,18317,18377,18437,18497,18557,18617,18676,18736,18796,18856,18916,18976,19036,19096,19156,19216,19276,19335,19395,19455,19514,19573,19632,19691,19750,19809,20353,20388,20994,21057,21112,21170,21228,21289,21352,21409,21460,21510,21571,21628,21694,21728,21763,21798,22874,24954,25026,25095,25164,25238,25310,25398,29374,130430,130631,130741,130942,131071,142848,142915,164930,167926,177191,177872,178873,179040"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f80248970efbd3bafd1721bfcda96536\\transformed\\jetified-firebase-messaging-24.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "443", "startColumns": "4", "startOffsets": "28114", "endColumns": "81", "endOffsets": "28191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32b951ec29728b45cedd679ddbec605b\\transformed\\jetified-notifications-5.1.29\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,136", "endColumns": "80,82", "endOffsets": "131,214"}, "to": {"startLines": "449,450", "startColumns": "4,4", "startOffsets": "28667,28748", "endColumns": "80,82", "endOffsets": "28743,28826"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\31710a1d11dfb3018b4d584b1dcc524f\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "247,248,249,257,258,259,334,3572", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "14830,14889,14937,15604,15679,15755,20257,191468", "endLines": "247,248,249,257,258,259,334,3591", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "14884,14932,14988,15674,15750,15822,20318,192258"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2748b27b9a4960040fddad0a76748cc0\\transformed\\jetified-core-common-2.0.4\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "2058", "startColumns": "4", "startOffsets": "133712", "endLines": "2065", "endColumns": "8", "endOffsets": "134117"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2c52fddffb4a4858b65e09bc36907dcb\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,35,36,37,38,45,47,50,7", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,107,168,230,292,2179,2238,2295,2349,2763,2827,2953,356", "endLines": "2,3,4,5,6,35,36,37,44,46,49,52,34", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "102,163,225,287,351,2233,2290,2344,2758,2822,2948,3076,2174"}, "to": {"startLines": "3,95,96,97,98,235,236,237,488,1574,1576,1579,2845", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "164,4527,4588,4650,4712,13999,14058,14115,31509,100908,100972,101098,164935", "endLines": "3,95,96,97,98,235,236,237,494,1575,1578,1581,2872", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "211,4583,4645,4707,4771,14053,14110,14164,31918,100967,101093,101221,165854"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f6da13a9846494f8148c639e8691af2b\\transformed\\jetified-appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2303,2319,2325,3716,3732", "startColumns": "4,4,4,4,4", "startOffsets": "147458,147883,148061,196407,196818", "endLines": "2318,2324,2334,3731,3735", "endColumns": "24,24,24,24,24", "endOffsets": "147878,148056,148340,196813,196940"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\952d4bb05156a6bae798ebc3d4f6b56b\\transformed\\jetified-core-5.1.29\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,180,263", "endColumns": "124,82,75", "endOffsets": "175,258,334"}, "to": {"startLines": "451,452,453", "startColumns": "4,4,4", "startOffsets": "28831,28956,29039", "endColumns": "124,82,75", "endOffsets": "28951,29034,29110"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cf636ef2f0738047fe8129f320ef339e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "379,427", "startColumns": "4,4", "startOffsets": "22615,26443", "endColumns": "67,166", "endOffsets": "22678,26605"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\07d5ffcb9c6912ffd610dc84a8004380\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "370", "startColumns": "4", "startOffsets": "22101", "endColumns": "42", "endOffsets": "22139"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5c90255c19f9c683385e871d79ee1ace\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "331,332,337,344,345,364,365,366,367,368", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "20136,20176,20393,20731,20786,21803,21857,21909,21958,22019", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "20171,20218,20431,20781,20828,21852,21904,21953,22014,22064"}}]}]}