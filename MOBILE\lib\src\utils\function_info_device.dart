import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';

import '../models/info_device_static.dart';
import 'info_device_global.dart';

Future deviceInfoStatic() async {
  final deviceInfo = DeviceInfoPlugin();

  if (Platform.isAndroid) {
    final androidInfo = await deviceInfo.androidInfo;
    final deviceInfoStatic = DeviceInfoStatic(
      name: androidInfo.device,
      tela: androidInfo.display,
      modelo: androidInfo.model,
      systemName: androidInfo.version.release,
      systemVersion: androidInfo.version.sdkInt.toString(),
    );
    InfoDeviceData.instance.setDeviceInfoStatic(deviceInfoStatic);
  } else if (Platform.isIOS) {
    final iosInfo = await deviceInfo.iosInfo;
    final deviceInfoStatic = DeviceInfoStatic(
      name: iosInfo.name,
      tela: iosInfo.utsname.machine,
      modelo: iosInfo.model,
      systemName: iosInfo.systemName,
      systemVersion: iosInfo.systemVersion,
    );
    InfoDeviceData.instance.setDeviceInfoStatic(deviceInfoStatic);
  }
}
