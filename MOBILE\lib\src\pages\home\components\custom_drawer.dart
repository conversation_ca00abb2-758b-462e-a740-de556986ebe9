import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/one_signal_wrapper.dart';
import 'package:octalog/src/components/flavor_image/flavor_image.dart';
import 'package:octalog/src/helpers/login/login.dart';
import 'package:octalog/src/helpers/login/login_hive.dart';
import 'package:octalog/src/pages/login/login_page.dart';
// import 'package:octalog/src/utils/versao.dart';

// import '../../../components/fcm_alert_dailog/fcm_external/fcm_external_login.dart';
//import '../../../helpers/api_ls.dart';
// import '../../../helpers/web_connector.dart';

import '../../cadastro/cadastro_page.dart';
// import '../../cadastro/components/card_pestrador.dart';
import '../../cadastro/data/hive_contrato.dart';
import '../../cadastro/model/contrato_model.dart';
import '../../expedicao/expedicao_page.dart';
import '../../notificacoes_page/notificacoes_page.dart';
import '../home.dart';
// import '../home_controller.dart';
import 'aviso_scronimos_pendente.dart';
//import 'historico_sac/historico_sac_page.dart';
// import 'is_online_component.dart';
// import '../stores/home_store.dart';

class CustomDrawer extends StatefulWidget {
  const CustomDrawer({super.key});

  @override
  State<CustomDrawer> createState() => _CustomDrawerState();
}

class _CustomDrawerState extends State<CustomDrawer> {
  bool iscoletaL = false;
  ContratoModel? contratoModel;

  @override
  void initState() {
    carregarContrato();
    super.initState();
  }

  Future carregarContrato() async {
    try {
      contratoModel = await ContratoPrefs.instance.read();
    } catch (_) {
      contratoModel = null;
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: const Color(0xFFF9F9F9),
      child: SafeArea(
        child: Column(
          children: [
            // Cabeçalho com foto e informações do usuário
            _buildHeader(),

            // Lista de itens do menu
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: ListView(
                  children: [
                    const SizedBox(height: 20),
                    _buildMenuItem(icon: Icons.home_outlined, title: 'Home', subtitle: 'Seus pedidos do dia', onTap: () => _navigateToPage(const Home())),
                    _buildMenuItem(
                      icon: Icons.notifications_outlined,
                      title: 'Mensagens',
                      subtitle: 'Avisos e notificações',
                      onTap: () => _navigateToPage(const NotificacoesPage()),
                    ),
                    _buildMenuItem(
                      icon: Icons.person_outline,
                      title: 'Meu Cadastro',
                      subtitle: 'Seus dados e contratos',
                      onTap: () => _navigateToPage(const CadastroAgente()),
                    ),
                    _buildMenuItem(
                      icon: Icons.inventory_2_outlined,
                      title: 'Minhas Entregas',
                      subtitle: 'Acompanhe suas entregas',
                      onTap: () => _navigateToPage(const ExpedicaoPage()),
                    ),
                    _buildMenuItem(icon: Icons.logout_outlined, title: 'Sair', subtitle: 'Finalizar sessão', onTap: _showLogoutDialog, isLogout: true),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          // Foto do usuário com borda
          // Container(
          //   width: 80,
          //   height: 80,
          //   decoration: BoxDecoration(shape: BoxShape.circle, border: Border.all(color: const Color(0xFF3F81A0), width: 2.0)),
          //   child: ClipOval(child: Login.instance.fotoUsuarioLogado(80)),
          // ),
          Center(child: FlavorImage(assetName: 'logo200.png', width: 120, height: 120)),
          const SizedBox(height: 16),

          // Nome do usuário
          // Text(
          //   Login.instance.usuarioLogado?.nomeCompleto ?? 'Usuário',
          //   style: GoogleFonts.roboto(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.black),
          //   textAlign: TextAlign.center,
          // ),
          // const SizedBox(height: 4),

          // Subtítulo
          Text('O app que acelera o seu dia.', style: GoogleFonts.roboto(fontSize: 14, color: const Color(0xFF666666)), textAlign: TextAlign.center),
        ],
      ),
    );
  }

  Widget _buildMenuItem({required IconData icon, required String title, required String subtitle, required VoidCallback onTap, bool isLogout = false}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        elevation: 2,
        shadowColor: Colors.black.withValues(alpha: 0.05),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                // Ícone
                Icon(icon, size: 24, color: isLogout ? Colors.red : Colors.black),
                const SizedBox(width: 16),

                // Textos
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(title, style: GoogleFonts.roboto(fontSize: 16, fontWeight: FontWeight.w600, color: isLogout ? Colors.red : Colors.black)),
                      const SizedBox(height: 2),
                      Text(subtitle, style: GoogleFonts.roboto(fontSize: 12, color: const Color(0xFF666666))),
                    ],
                  ),
                ),

                // Seta
                Icon(Icons.arrow_forward_ios, size: 16, color: isLogout ? Colors.red : const Color(0xFF666666)),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToPage(Widget page) {
    Navigator.pop(context); // Fecha o drawer
    Navigator.push(context, MaterialPageRoute(builder: (context) => page));
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: Text('Confirmar Saída', style: GoogleFonts.roboto(fontWeight: FontWeight.bold)),
          content: Text('Tem certeza que deseja sair do aplicativo?', style: GoogleFonts.roboto()),
          actions: [
            TextButton(onPressed: () => Navigator.pop(context), child: Text('Cancelar', style: GoogleFonts.roboto(color: const Color(0xFF666666)))),
            ElevatedButton(
              onPressed: () async {
                final navigator = Navigator.of(context);
                navigator.pop(); // Fecha o dialog
                navigator.pop(); // Fecha o drawer

                bool continuar = await entrarTelaSicronismo(minimo: 0);
                if (!continuar) return;

                try {
                  await LoginHive.instance.clear();
                  await Login.instance.logout();
                  await ContratoPrefs.instance.clean();
                  OneSignalService().deleteTags();

                  navigator.pushAndRemoveUntil(MaterialPageRoute(builder: (context) => const LoginPage()), (route) => false);
                } catch (e) {
                  debugPrint(e.toString());
                }
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red, shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8))),
              child: Text('Sair', style: GoogleFonts.roboto(color: Colors.white, fontWeight: FontWeight.w600)),
            ),
          ],
        );
      },
    );
  }
}
