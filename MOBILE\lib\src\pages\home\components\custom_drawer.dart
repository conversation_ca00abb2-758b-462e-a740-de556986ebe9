import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/one_signal_wrapper.dart';
import 'package:octalog/src/components/custom_scaffold/custom_scaffold.dart';
// import 'package:octalog/src/components/loading_ls/loading_ls.dart';
//import 'package:octalog/src/components/tutorial/tutorial_page.dart';
import 'package:octalog/src/helpers/login/login.dart';
import 'package:octalog/src/helpers/login/login_hive.dart';
//import 'package:octalog/src/pages/log_page/log_page.dart';
import 'package:octalog/src/pages/login/login_page.dart';
import 'package:octalog/src/utils/theme_colors.dart';
// import 'package:octalog/src/utils/versao.dart';

// import '../../../components/fcm_alert_dailog/fcm_external/fcm_external_login.dart';
//import '../../../helpers/api_ls.dart';
// import '../../../helpers/web_connector.dart';

import '../../cadastro/cadastro_page.dart';
// import '../../cadastro/components/card_pestrador.dart';
import '../../cadastro/data/hive_contrato.dart';
import '../../cadastro/model/contrato_model.dart';
import '../../expedicao/expedicao_page.dart';
import '../../notificacoes_page/notificacoes_page.dart';
import '../home.dart';
// import '../home_controller.dart';
import 'aviso_scronimos_pendente.dart';
//import 'historico_sac/historico_sac_page.dart';
// import 'is_online_component.dart';
// import '../stores/home_store.dart';

class CustomDrawer extends StatefulWidget {
  const CustomDrawer({super.key});

  @override
  State<CustomDrawer> createState() => _CustomDrawerState();
}

class _CustomDrawerState extends State<CustomDrawer> {
  bool iscoletaL = false;
  ContratoModel? contratoModel;

  @override
  void initState() {
    carregarContrato();
    super.initState();
  }

  Future carregarContrato() async {
    try {
      contratoModel = await ContratoPrefs.instance.read();
    } catch (_) {
      contratoModel = null;
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: CustomScaffold(
        floatingActionButtonLocation: FloatingActionButtonLocation.endTop,
        floatingActionButton: null,
        onPop: () {
          Navigator.of(context).pop();
        },
        child: SingleChildScrollView(
          child: Column(
            children: [
              Row(
                children: [
                  const SizedBox(width: 30),
                  SizedBox(width: 60, height: 60, child: Login.instance.fotoUsuarioLogado(40)),
                  const SizedBox(width: 20),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        Login.instance.usuarioLogado?.nomeCompleto ?? '',
                        textAlign: TextAlign.left,
                        style: GoogleFonts.roboto(fontSize: 22, fontWeight: FontWeight.w500, color: ThemeColors.customBlack(context)),
                      ),
                      Text(
                        contratoModel?.prestador ?? '',
                        textAlign: TextAlign.left,
                        style: GoogleFonts.roboto(fontSize: 16, fontWeight: FontWeight.w600, color: ThemeColors.customGrey(context)),
                      ),
                    ],
                  ),
                ],
              ),

              Column(
                children: [
                  const SizedBox(height: 15),

                  Container(
                    height: 1.0, // Define a altura da linha
                    color: Colors.grey[300], // Cor cinza clara
                  ),
                  const SizedBox(height: 10),
                  ListView(
                    shrinkWrap: true,
                    physics: const BouncingScrollPhysics(),
                    children:
                        [
                          {'title': 'Home', 'icon': Icons.home, 'page': const Home(), 'remove': false, 'onClick': () async {}, 'color': Colors.grey},
                          {
                            'title': 'Mensagens',
                            'icon': Icons.notifications,
                            'page': const NotificacoesPage(),
                            'remove': false,
                            'onClick': () async {},
                            'color': Colors.grey,
                          },
                          {
                            'title': 'Meu Cadastro',
                            'icon': Icons.person_pin_rounded,
                            'page': const CadastroAgente(),
                            'remove': false,
                            'onClick': () async {},
                            'color': Colors.grey,
                          },
                          // {
                          //   'title': 'Histórico de Sacs',
                          //   'icon': Icons.history,
                          //   'page': HistoricoSac(),
                          //   'remove': false,
                          //   'onClick': () async {},
                          //   'color': Colors.blue,
                          // },
                          {
                            'title': 'Minhas Entregas',
                            'icon': Icons.list_alt_rounded,
                            'page': const ExpedicaoPage(),
                            'remove': false,
                            'onClick': () async {},
                            'color': Colors.grey,
                          },
                          // {
                          //   'title': 'Tutorial',
                          //   'icon': Icons.import_contacts_rounded,
                          //   'page': const TutorialPage(),
                          //   'remove': false,
                          //   'onClick': () async {},
                          //   'color': Colors.orange,
                          // },

                          // {
                          //   'title': 'Logs',
                          //   'icon': Icons.bug_report,
                          //   'page': const LogPage(),
                          //   'remove': false,
                          //   'onClick': () async {},
                          //   'color': ThemeColors.customOrange(context),
                          // },
                          {
                            'title': 'Sair',
                            'icon': Icons.close,
                            'remove': true,
                            'page': const LoginPage(),
                            'onClick': () async {
                              bool continuar = await entrarTelaSicronismo(minimo: 0);
                              if (!continuar) return;
                              try {
                                await LoginHive.instance.clear();
                                await Login.instance.logout();
                                await ContratoPrefs.instance.clean();
                                OneSignalService().deleteTags();
                              } catch (e) {
                                debugPrint(e.toString());
                              }
                            },
                            'color': Colors.grey,
                          },
                        ].map((e) {
                          final icon = Center(
                            child: Icon(
                              e['icon'] as IconData,
                              //  color: Colors.white,
                              size: 45,
                            ),
                          );
                          final title = Text(
                            e['title'] as String,
                            style: GoogleFonts.roboto(fontSize: 16, fontWeight: FontWeight.w500, color: ThemeColors.customBlack(context)),
                          );
                          final function = e['onClick'] as Function;
                          return GestureDetector(
                            onTap: () async {
                              await function();
                              if (e['remove'] as bool) {
                                final continuar = await entrarTelaSicronismo(minimo: 0);
                                if (!continuar) return;
                                Navigator.pushAndRemoveUntil(context, MaterialPageRoute(builder: (context) => e['page'] as Widget), (route) => false);
                              } else {
                                Navigator.push(context, MaterialPageRoute(builder: (context) => e['page'] as Widget));
                              }
                            },
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: Row(children: 
                              [const SizedBox(width: 30), icon, const SizedBox(width: 15), title]),
                            ),
                          );
                        }).toList(),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
