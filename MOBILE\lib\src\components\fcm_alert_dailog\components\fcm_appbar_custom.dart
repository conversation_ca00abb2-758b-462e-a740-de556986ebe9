import 'package:flutter/material.dart';

import '../../icon_return.dart';

class FcmAppBarCustom extends StatelessWidget {
  final String title;
  final bool isBack;
  final bool isLoadgin;
  final Widget? iconButton;
  final Function? onPressed;
  const FcmAppBarCustom({super.key, required this.title, this.isBack = true, this.isLoadgin = false, this.iconButton, this.onPressed});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        isBack ? IconPadrao(isBack: isBack) : const SizedBox(height: 40, width: 40),
        Expanded(
          child: Center(
            child: RichText(
              overflow: TextOverflow.clip,
              textAlign: TextAlign.center,
              textScaler: const TextScaler.linear(1.6),
              text: TextSpan(style: const TextStyle(color: Colors.black, fontWeight: FontWeight.w500, fontSize: 12), text: title),
            ),
          ),
        ),
        if (iconButton != null) iconButton!,
        if (isLoadgin) IconButton(onPressed: onPressed as void Function()?, icon: const Icon(Icons.replay_outlined, color: Colors.grey, size: 25)),
        if (onPressed == null && iconButton == null) const SizedBox(width: 40),
      ],
    );
  }
}
