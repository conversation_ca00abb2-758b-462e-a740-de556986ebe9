enum EnumCadastroPages {
  notificacaoTransportadora,
  contratos,
  informacoesPessoais,
  enderecoResidencial,
  validadeDocumentos,
  dadosBancarios,
  fotos,
}

extension EnumCadastroPagesExtension on EnumCadastroPages {
  String get name {
    switch (this) {
      case EnumCadastroPages.notificacaoTransportadora:
        return 'Sobre Octalog';
      case EnumCadastroPages.contratos:
        return 'Contratos';
      case EnumCadastroPages.informacoesPessoais:
        return 'Informações Pessoais';
      case EnumCadastroPages.enderecoResidencial:
        return 'Endereço Residencial';
      case EnumCadastroPages.validadeDocumentos:
        return 'Validade Documentos';
      case EnumCadastroPages.dadosBancarios:
        return 'Dados Bancários';
      case EnumCadastroPages.fotos:
        return 'Fotos';
    }
  }
}

// criar uma funçao para retornar os valores do EnumCadastroPages e passar um boleano para nao retornar o dados bancarios

List<EnumCadastroPages> getEnumCadastroPages(bool dadosBancarios) {
  final List<EnumCadastroPages> list = [
    EnumCadastroPages.notificacaoTransportadora,
    EnumCadastroPages.contratos,
    EnumCadastroPages.informacoesPessoais,
    EnumCadastroPages.enderecoResidencial,
    EnumCadastroPages.validadeDocumentos,
    EnumCadastroPages.fotos,
  ];
  if (dadosBancarios) {
    list.add(EnumCadastroPages.dadosBancarios);
  }
  return list;
}
