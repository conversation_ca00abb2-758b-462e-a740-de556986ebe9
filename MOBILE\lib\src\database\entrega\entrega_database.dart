import 'package:hive/hive.dart';

class EntregaDatabase {
  static final EntregaDatabase instance = EntregaDatabase();

  bool _boxIniciada = false;

  late Box _box;

  Future<void> _iniciarBanco() async {
    if (_boxIniciada) return;
    _box = await Hive.openBox('entrega_atividade_hive');
    // await Hive.box('entrega_atividade_hive').deleteFromDisk();
    _boxIniciada = true;
  }

  Future<Box> getBanco() async {
    await _iniciarBanco();
    return _box;
  }
}
