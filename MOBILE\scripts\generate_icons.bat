@echo off
setlocal enabledelayedexpansion

REM Script para gerar ícones personalizados por flavor (Windows)
REM Uso: scripts\generate_icons.bat [flavor]
REM Exemplo: scripts\generate_icons.bat octalog

REM Função para exibir ajuda
if "%1"=="" goto :show_help
if "%1"=="help" goto :show_help
if "%1"=="-h" goto :show_help
if "%1"=="--help" goto :show_help

REM Verificar se estamos no diretório correto
if not exist "pubspec.yaml" (
    echo [ERROR] Execute este script a partir do diretório raiz do projeto Flutter
    exit /b 1
)

REM Verificar se o flutter_launcher_icons está instalado
flutter pub deps | findstr "flutter_launcher_icons" >nul
if errorlevel 1 (
    echo [ERROR] flutter_launcher_icons não está instalado
    echo Execute: flutter pub get
    exit /b 1
)

set FLAVOR=%1

REM Processar o flavor solicitado
if "%FLAVOR%"=="octalog" goto :generate_single
if "%FLAVOR%"=="arcargo" goto :generate_single
if "%FLAVOR%"=="connect" goto :generate_single
if "%FLAVOR%"=="rondolog" goto :generate_single
if "%FLAVOR%"=="all" goto :generate_all

echo [ERROR] Flavor inválido: %FLAVOR%
echo.
goto :show_help

:generate_single
call :generate_flavor_icons %FLAVOR%
goto :end

:generate_all
echo [INFO] Gerando ícones para todos os flavors...
call :generate_flavor_icons octalog
call :generate_flavor_icons arcargo
call :generate_flavor_icons connect
call :generate_flavor_icons rondolog
echo [SUCCESS] Todos os ícones foram gerados!
goto :end

:generate_flavor_icons
set CURRENT_FLAVOR=%1
set CONFIG_FILE=flutter_launcher_icons-%CURRENT_FLAVOR%.yaml

echo [INFO] Gerando ícones para o flavor: %CURRENT_FLAVOR%

REM Verificar se o arquivo de configuração existe
if not exist "%CONFIG_FILE%" (
    echo [ERROR] Arquivo de configuração não encontrado: %CONFIG_FILE%
    exit /b 1
)

REM Extrair o caminho da imagem do arquivo YAML (método simplificado)
for /f "tokens=2 delims=:" %%a in ('findstr "image_path:" "%CONFIG_FILE%"') do (
    set IMAGE_PATH=%%a
    set IMAGE_PATH=!IMAGE_PATH: "=!
    set IMAGE_PATH=!IMAGE_PATH:"=!
    goto :found_image
)

:found_image
REM Verificar se a imagem existe
if not exist "!IMAGE_PATH!" (
    echo [ERROR] Imagem não encontrada: !IMAGE_PATH!
    echo Verifique se o arquivo existe e tem a extensão correta.
    exit /b 1
)

echo [INFO] Usando configuração: %CONFIG_FILE%
echo [INFO] Imagem do logo: !IMAGE_PATH!

REM Gerar os ícones
flutter pub run flutter_launcher_icons:main -f "%CONFIG_FILE%"

if errorlevel 1 (
    echo [ERROR] Erro ao gerar ícones para o flavor: %CURRENT_FLAVOR%
    exit /b 1
) else (
    echo [SUCCESS] Ícones gerados com sucesso para o flavor: %CURRENT_FLAVOR%
)

exit /b 0

:show_help
echo Gerador de Ícones por Flavor - Octalog
echo.
echo Uso: %0 [flavor]
echo.
echo Flavors disponíveis:
echo   octalog   - Cliente padrão (azul/laranja)
echo   arcargo   - Cliente ArCargo (verde)
echo   connect   - Cliente Connect (azul)
echo   rondolog  - Cliente RondoLog (laranja)
echo   all       - Gerar ícones para todos os flavors
echo.
echo Exemplos:
echo   %0 octalog
echo   %0 all
echo.
exit /b 0

:end
echo.
echo [INFO] Processo concluído!
