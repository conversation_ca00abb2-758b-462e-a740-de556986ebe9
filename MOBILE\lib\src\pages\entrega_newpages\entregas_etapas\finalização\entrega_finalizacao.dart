import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/components/custom_scaffold/custom_scaffold.dart';
import 'package:octalog/src/models_new/cliente_new.dart';
import 'package:octalog/src/pages/entrega_newpages/componentes/card_endereco.dart';
import 'package:octalog/src/pages/entrega_newpages/entregas_etapas/finaliza%C3%A7%C3%A3o/components/acareacao_widget.dart';
import 'package:octalog/src/pages/entrega_newpages/entregas_etapas/finaliza%C3%A7%C3%A3o/components/aviso_acareacao_distante.dart';
import 'package:octalog/src/pages/entrega_newpages/entregas_etapas/finaliza%C3%A7%C3%A3o/components/aviso_receber.dart';
import 'package:octalog/src/pages/entrega_newpages/entregas_etapas/finaliza%C3%A7%C3%A3o/components/valor_a_receber.dart';
import 'package:octalog/src/utils/chamada.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../../../components/buttom_ls/button_ls_custom.dart';
import '../../../../helpers/web_connector.dart';
import '../../../../utils/colors.dart';
import '../../../entregas/components/volume_card.dart';
import '../../../home/<USER>';
import '../../controller/entrega_new_state.dart';
import '../../controller/entrega_new_store.dart';
import '../../ocorrencias/entrega_negativa.dart';
import 'components/foto_acareacao_widget.dart';
import 'components/foto_receita_widget.dart';
import 'components/foto_romaneio_widget.dart';
import 'components/list_entrega_cards.dart';
import 'components/mensagem_cliente_escolhido_widget.dart';

class EntregaFinalizacao extends StatefulWidget {
  final EntregaNewStore store;
  final Position? position;

  const EntregaFinalizacao({super.key, required this.store, this.position});

  @override
  State<EntregaFinalizacao> createState() => _EntregaFinalizacaoState();
}

class _EntregaFinalizacaoState extends State<EntregaFinalizacao> {
  int? clienteIndex;
  int isEntregaCliente = 0;
  bool isColor = true;
  final entregueParaController = TextEditingController();

  bool isLoading = false;
  final textfoco = FocusNode();

  void setClienteIndex(int? value) {
    setState(() {
      widget.store.setClienteIndex(value);
      clienteIndex = value;
    });
  }

  ClienteNew? get clienteEscolhido {
    if (widget.store.state.value.restantes.length == 1 && widget.store.state.value.atividade.clientes.length == 1) {
      return widget.store.state.value.restantes.first;
    }
    if (widget.store.state.value.restantes.isEmpty) return null;
    return clienteIndex == null ? null : widget.store.state.value.restantes[clienteIndex!];
  }

  @override
  void initState() {
    super.initState();
    clienteIndex = widget.store.state.value.indexClienteEscolhido;
    entregueParaController.text = widget.store.state.value.entreguePara ?? '';

    if (entregueParaController.text.isNotEmpty) {
      isEntregaCliente = 0;
    }
  }

  @override
  void dispose() {
    super.dispose();
    entregueParaController.dispose();
  }

  @override
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: ValueListenableBuilder<EntregaNewState>(
        valueListenable: widget.store.state,
        builder: (BuildContext context, EntregaNewState value, Widget? child) {
          final atividade = value.atividade;

          return CustomScaffold(
            isColorIcon: isColor,
            cameraTela: voidCamera,
            canPop: false,
            onPopClose: onPopClose,
            onPop: onPop,
            title:
                atividade.clientes.last.acareacao
                    ? '${atividade.volumesLength} Acareação'
                    : '${atividade.volumesLength} ${atividade.volumesLength == 1 ? 'pedido' : 'pedidos'} para entregar',
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CardEndereco(store: widget.store, isColor: isColor),
                  SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            child: Visibility(
                              visible: clienteEscolhido != null && !clienteEscolhido!.acareacao,
                              child: Text(
                                clienteEscolhido?.nomeClienteTratado ?? 'Consumidor Final',
                                style: GoogleFonts.roboto(fontSize: 20, fontWeight: FontWeight.w500, color: ColorsCustom.customBlack),
                              ),
                            ),
                          ),
                        ),
                        Visibility(
                          visible: clienteEscolhido?.exibirNumeroClienteNaEntrega ?? false,
                          child: GestureDetector(
                            onTap: () {
                              showContactOptions(context, clienteEscolhido?.telefone ?? '');
                            },
                            child: Row(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    color: ThemeColors.customOrange(context),
                                    borderRadius: BorderRadius.circular(0),
                                    border: Border.all(color: ColorsCustom.customBlue, width: 2),
                                  ),
                                  padding: const EdgeInsets.symmetric(horizontal: 10),
                                  child: Row(
                                    children: [
                                      const Icon(Icons.call_end, size: 13, color: ColorsCustom.customWhite),
                                      Text(
                                        atividade.clientes.last.telefone,
                                        style: GoogleFonts.roboto(fontSize: 13, fontWeight: FontWeight.bold, color: ColorsCustom.customWhite),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Visibility(visible: clienteEscolhido?.acareacao ?? false, child: MensagemClienteEscolhidoWidget(mensagem: clienteEscolhido?.mensagem ?? '')),
                  Builder(
                    builder: (context) {
                      if (clienteEscolhido == null) {
                        return EntregaCards(
                          atividade: atividade,
                          etapa: value.etapa,
                          onTap: (int index) {
                            setClienteIndex(index);
                          },
                          clientesRestantes: value.restantes,
                        );
                      }
                      return Column(
                        children: [
                          Visibility(
                            visible: !clienteEscolhido!.acareacao,
                            replacement: AcareacaoEntragaWidget(
                              clienteEscolhido: clienteEscolhido!,
                              entregueParaController: entregueParaController,
                              store: widget.store,
                            ),
                            child: Column(
                              children: [
                                VolumeCard(
                                  cliente: clienteEscolhido!,
                                  isFinalizar: true,
                                  onReport: (os, idos) {
                                    showModalBottomSheet(
                                      shape: const RoundedRectangleBorder(
                                        borderRadius: BorderRadius.only(topLeft: Radius.circular(30), topRight: Radius.circular(30)),
                                      ),
                                      isScrollControlled: true,
                                      isDismissible: true,
                                      context: context,
                                      builder:
                                          (_) => EntregaNegativa(
                                            store: widget.store,
                                            isOcorrenciaGlobal: false,
                                            indexClienteRestante: widget.store.state.value.restantes.length == 1 ? 0 : clienteIndex,
                                            osSingle: os,
                                            idOsSingle: idos,
                                            removerAcareacao: !clienteEscolhido!.acareacao,
                                          ),
                                    );
                                  },
                                ),
                                const SizedBox(height: 10),
                                Visibility(
                                  visible: !clienteEscolhido!.receberValorObrigatorio && (clienteEscolhido?.receita ?? false),
                                  child: const WidgetAvisoReceber(),
                                ),
                                Visibility(
                                  visible: clienteEscolhido?.receberValor ?? false,
                                  child: WidgetValorReceber(
                                    store: widget.store,
                                    avisoReceber: !clienteEscolhido!.receberValorObrigatorio,
                                    receberValorObrigatorio: clienteEscolhido!.receberValorObrigatorio,
                                    clienteEscolhido: clienteEscolhido!,
                                  ),
                                ),
                                Visibility(
                                  visible: clienteEscolhido?.receita ?? false,
                                  child: FotoReceitaWidget(store: widget.store, bytesReceita: value.bytes),
                                ),
                                Visibility(
                                  visible: clienteEscolhido?.romaneioCanhoto ?? false,
                                  child: FotoRomaneioWidget(store: widget.store, bytesRomaneio: value.bytesRomaneio),
                                ),
                                Visibility(
                                  visible: clienteEscolhido?.acareacao ?? false,
                                  child: FotoAcareacaoWidget(store: widget.store, bytesAcareacao: value.bytesAcareacao),
                                ),
                                Visibility(
                                  visible: !clienteEscolhido!.preencherAutomaticoNomeClienteEntrega,
                                  replacement: SizedBox(
                                    child: SingleChildScrollView(
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          buildOptionRow(),
                                          buildOptionsList(1, 'Recebido de:', [2, 3, 4, 5]),
                                          buildDeliveryTextField(),
                                        ],
                                      ),
                                    ),
                                  ),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                                        child: Text(
                                          'Nome do recebedor:',
                                          style: GoogleFonts.roboto(fontSize: 16, color: ColorsCustom.customBlack, fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.only(left: 20, right: 20),
                                        child: TextField(
                                          focusNode: textfoco,
                                          controller: entregueParaController,
                                          onChanged: widget.store.setEntreguePara,
                                          decoration: const InputDecoration(isDense: true, border: OutlineInputBorder(), suffixText: '(Nome do Recebedor)'),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                  const SizedBox(height: 20),
                  if ((clienteEscolhido?.receberValor ?? false) && clienteEscolhido?.valorReceber != 0) const SizedBox(height: 20),
                  Visibility(
                    visible: clienteEscolhido != null,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Builder(
                        builder: (context) {
                          final cliente = clienteEscolhido;
                          final entreguePara = entregueParaController.text;
                          final valorRecebido = widget.store.valorRecebido;
                          final receberCliente = cliente?.receberValorObrigatorio ?? false;

                          String? message;

                          if (isEntregaCliente == 0 && (clienteEscolhido?.preencherAutomaticoNomeClienteEntrega ?? false) && !cliente!.acareacao) {
                            message = 'Selecione para quem está entregando.';
                          } else if ((cliente?.receita ?? false) && value.bytes == null) {
                            message = 'Por favor tirar uma foto da receita';
                          } else if ((cliente?.romaneioCanhoto ?? false) && value.bytesRomaneio == null) {
                            message = 'Por favor tirar uma foto do romaneio';
                          } else if (entreguePara.length < 6 && isEntregaCliente != 1) {
                            message = 'Preencha o nome do recebedor\nNome precisa ter mais de 6 letras';
                          } else if ((cliente?.acareacao ?? false) && value.bytesAcareacao == null) {
                            message = 'Por favor tirar uma foto da acareação';
                          }

                          if ((cliente?.preencherAutomaticoNomeClienteEntrega ?? false) && isEntregaCliente == 0 && !cliente!.acareacao) {
                            if (entreguePara.length < 6) {
                              message = 'Preencha o nome do recebedor\nNome precisa ter mais de 6 letras';
                              textfoco.requestFocus();
                            }
                          }
                          if ((cliente?.acareacao ?? false) && (cliente?.assinaturaObrigatoria ?? false) && !value.assinaturaPreenchida) {
                            message = 'E necessário que o cliente assine para prosseguir';
                          }
                          return ButtonLsCustom(
                            message: message,
                            text: 'CONCLUÍR ENTREGA',
                            isLoading: isLoading,
                            onPressed:
                                isLoading
                                    ? null
                                    : () async {
                                      if (cliente!.acareacao && !value.acareacaoRealizadaSucesso) {
                                        widget.store.seterroAcareacaoRealizadaSucesso(true);
                                        return;
                                      }
                                      setState(() => isLoading = true);
                                      bool? resultValorReceber = await valorReceberDiferente(receberCliente, valorRecebido, cliente);
                                      if (resultValorReceber != null && !resultValorReceber) {
                                        setState(() => isLoading = false);
                                        return;
                                      }
                                      bool resultDistancia = await checkDistancia(clienteEscolhido?.acareacao ?? false, clienteEscolhido!);
                                      if (resultDistancia) {
                                        setState(() => isLoading = false);
                                        return;
                                      }

                                      await widget.store.setEntreguePara(
                                        isEntregaCliente == 1 ? (clienteEscolhido?.nomeClienteTratado ?? 'Consumidor Final') : entregueParaController.text,
                                      );
                                      await widget.store
                                          .entregarCliente(
                                            (clienteEscolhido?.acareacao ?? false) ? 35 : 10,
                                            widget.store.state.value.restantes.indexOf(clienteEscolhido!),
                                            'Entregue realizado com sucesso',
                                          )
                                          .then((value) async {
                                            await widget.store.limparFotos();
                                            if (widget.store.state.value.restantes.isEmpty) {
                                              Navigator.of(context).pop();
                                              widget.store.limparBanco().then((value) {
                                                Navigator.of(
                                                  context,
                                                ).pushAndRemoveUntil(MaterialPageRoute(builder: (context) => const Home()), (route) => false);
                                              });
                                            } else {
                                              clienteIndex = null;
                                              setState(() {});
                                            }
                                          });
                                      setState(() => isLoading = false);
                                    },
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Future<bool?> valorReceberDiferente(bool receberCliente, double valorRecebido, ClienteNew? clienteEscolhido) async {
    if (receberCliente && valorRecebido < (clienteEscolhido?.valorReceber ?? 0)) {
      final valorRecebido = clienteEscolhido!.valorReceber;
      final valorDigitado = widget.store.valorRecebido;
      final showMessage =
          'Valor recebido é menor que o valor a receber.\n'
          'Valor a receber: R\$ ${valorRecebido!.toStringAsFixed(2).replaceAll('.', ',')}\n'
          'Valor recebido: R\$ ${valorDigitado.toStringAsFixed(2).replaceAll('.', ',')}';
      bool? continuar = await showDialog(
        context: context,
        barrierDismissible: false,
        barrierColor: Colors.black.withOpacity(0.5),
        builder:
            (ctx) => AlertDialog(
              title: const Text('Atenção'),
              content: Text(showMessage),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(ctx).pop(false);
                  },
                  child: const Text('CANCELAR', style: TextStyle(color: Colors.red)),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(ctx).pop(true);
                  },
                  child: Text('CONTINUAR', style: TextStyle(color: ThemeColors.customOrange(context))),
                ),
              ],
            ),
      );
      return continuar;
    }
    return null;
  }

  Widget buildOptionRow() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: GestureDetector(
        onTap: () async {
          if (clienteEscolhido?.nomeClienteTratado == 'Consumidor Final') {
            setState(() {
              isEntregaCliente = 6;
              widget.store.setEntreguePara(entregueParaController.text);
            });
            await Future.delayed(const Duration(milliseconds: 300));
            textfoco.requestFocus();
          } else {
            setState(() {
              isEntregaCliente = 1;
              widget.store.setEntreguePara('');
              entregueParaController.text = '';
            });
          }
        },
        child: Row(
          children: [
            Icon(
              isEntregaCliente == 1 || isEntregaCliente == 6 ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              color: isEntregaCliente == 1 || isEntregaCliente == 6 ? ColorsCustom.customGreen : ColorsCustom.customGreyLight,
              size: isEntregaCliente == 1 || isEntregaCliente == 6 ? 20 : 14,
            ),
            const SizedBox(width: 15),
            Text(
              clienteEscolhido?.nomeClienteTratado == 'Consumidor Final' ? 'Recebido de: ' : 'Entregue para: ',
              style: GoogleFonts.roboto(fontSize: 16, color: ColorsCustom.customBlack, fontWeight: FontWeight.w500),
            ),
            Expanded(
              child: Text(
                clienteEscolhido?.nomeClienteTratado == 'Consumidor Final' ? 'Cliente' : clienteEscolhido?.nomeClienteTratado ?? 'Cliente',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: GoogleFonts.roboto(
                  fontSize: 18,
                  color: clienteEscolhido?.nomeClienteTratado == 'Consumidor Final' ? ColorsCustom.customBlack : ThemeColors.customOrange(context),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildOptionsList(int currentValue, String labelText, List<int> options) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children:
          options.map((value) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
              child: GestureDetector(
                onTap: () async {
                  setState(() {
                    isEntregaCliente = value;
                    widget.store.setEntreguePara(entregueParaController.text);
                  });
                  await Future.delayed(const Duration(milliseconds: 300));
                  textfoco.requestFocus();
                },
                child: Row(
                  children: [
                    Icon(
                      isEntregaCliente == value ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                      color: isEntregaCliente == value ? ColorsCustom.customGreen : ColorsCustom.customGreyLight,
                      size: isEntregaCliente == value ? 20 : 14,
                    ),
                    const SizedBox(width: 15),
                    Text(_textChoiceEntrega(value), style: GoogleFonts.roboto(fontSize: 16, color: ColorsCustom.customBlack, fontWeight: FontWeight.bold)),
                  ],
                ),
              ),
            );
          }).toList(),
    );
  }

  Widget buildDeliveryTextField() {
    return Padding(
      padding: const EdgeInsets.only(top: 10),
      child: Container(
        height: 52,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: GestureDetector(
          onTap: () {
            if (isEntregaCliente == 2) {
              widget.store.setEntreguePara(entregueParaController.text);
            } else if (isEntregaCliente == 3) {
              widget.store.setEntreguePara(entregueParaController.text);
            } else if (isEntregaCliente == 4) {
              widget.store.setEntreguePara(entregueParaController.text);
            } else if (isEntregaCliente == 5) {
              widget.store.setEntreguePara(entregueParaController.text);
            }
          },
          child: Row(
            children: [
              Expanded(
                flex: 2,
                child: TextField(
                  focusNode: textfoco,
                  autofocus: true,
                  expands: false,
                  enabled: isEntregaCliente == 2 || isEntregaCliente == 3 || isEntregaCliente == 4 || isEntregaCliente == 5 || isEntregaCliente == 6,
                  controller: entregueParaController,
                  onChanged: widget.store.setEntreguePara,
                  decoration: InputDecoration(suffixText: _textEntrega(isEntregaCliente), isDense: true, border: const OutlineInputBorder()),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void onPop() {
    if (clienteIndex != null) {
      setClienteIndex(null);
    } else {
      Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const Home(enterAtividade: false)));
    }
  }

  Future<bool> onPopClose() async {
    if (clienteIndex != null) {
      setClienteIndex(null);
      return false;
    } else {
      Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const Home(enterAtividade: false)));
      return true;
    }
  }

  Future<void> voidCamera() async {
    final foto = await WebConnector().tirarFoto(context);
    if (foto == null) return;
    widget.store.upLoadFotoFinalizacao(foto, true);
    setState(() {
      isColor = false;
    });
  }

  String _textEntrega(int value) {
    final map = {2: ' - portaria', 3: ' - familiar', 4: ' - vizinho', 5: ' - outra pessoa', 6: ''};
    return map[value] ?? value.toString();
  }

  String _textChoiceEntrega(int value) {
    final map = {2: 'Na portaria, ADM. ou correspond.', 3: 'Para familiar ou funcionário', 4: 'Com vizinho', 5: 'Outra pessoal', 6: 'Próprio cliente'};
    return map[value] ?? value.toString();
  }
}
