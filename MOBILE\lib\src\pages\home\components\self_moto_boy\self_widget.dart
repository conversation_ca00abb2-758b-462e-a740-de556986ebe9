import 'dart:math';
import 'dart:typed_data';

import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/components/buttom_custom/button_custom.dart';
import 'package:octalog/src/components/loading_custom/loading_custom.dart';
import 'package:octalog/src/helpers/web_connector.dart';
import 'package:octalog/src/models/selfie_agente.dart';
import 'package:path/path.dart';

import '../../../../utils/theme_colors.dart';

late List<CameraDescription> _cameras;

class SelfWidget extends StatefulWidget {
  final SelfieAgente selfie;

  const SelfWidget({super.key, required this.selfie});

  @override
  State<SelfWidget> createState() => _SelfWidgetState();
}

class _SelfWidgetState extends State<SelfWidget> {
  late CameraController controller;
  bool isCameraReady = false;
  bool isLoading = false;
  bool error = false;
  XFile? file;

  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    _cameras = await availableCameras();
    controller = CameraController(
      _cameras[1],
      ResolutionPreset.max,
      imageFormatGroup: ImageFormatGroup.bgra8888,
      enableAudio: false,
    );
    controller.initialize().then((_) {
      if (!mounted) {
        return;
      }
      setState(() {
        isCameraReady = true;
      });
    }).catchError((Object e) {
      if (e is CameraException) {
        switch (e.code) {
          case 'CameraAccessDenied':
            break;
          default:
            break;
        }
      }
    });
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!isCameraReady || !controller.value.isInitialized) {
      return const LoadingLs();
    }
    return PopScope(
      canPop: false,
      child: Scaffold(
        body: file != null
            ? SizedBox(
                width: double.infinity,
                height: double.infinity,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        error
                            ? 'Erro ao enviar foto, tente novamente!'
                            : 'Foto tirada com sucesso!',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      const LoadingLs()
                    ],
                  ),
                ),
              )
            : SizedBox(
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height,
                child: Stack(
                  children: [
                    SizedBox(
                      width: MediaQuery.of(context).size.width,
                      height: MediaQuery.of(context).size.height,
                      child: CameraPreview(controller),
                    ),
                    IgnorePointer(
                      child: ClipPath(
                        clipper: InvertedCircleClipper(),
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          height: MediaQuery.of(context).size.height,
                          color: ThemeColors.customBlack(context).withOpacity(0.8),
                        ),
                      ),
                    ),
                    // icone para trocar de camera
                    Positioned(
                      top: 40,
                      right: 20,
                      child: IconButton(
                        onPressed: () {
                          if (controller.description.lensDirection ==
                              CameraLensDirection.front) {
                            controller = CameraController(
                              _cameras[0],
                              ResolutionPreset.medium,
                              imageFormatGroup: ImageFormatGroup.bgra8888,
                            );

                            controller.initialize().then((_) {
                              if (!mounted) {
                                return;
                              }
                              setState(() {
                                isCameraReady = true;
                              });
                            }).catchError((Object e) {
                              if (e is CameraException) {
                                switch (e.code) {
                                  case 'CameraAccessDenied':
                                    break;
                                  default:
                                    break;
                                }
                              }
                            });
                          } else {
                            controller = CameraController(
                                _cameras[1], ResolutionPreset.max);
                            controller.initialize().then((_) {
                              if (!mounted) {
                                return;
                              }
                              setState(() {
                                isCameraReady = true;
                              });
                            }).catchError((Object e) {
                              if (e is CameraException) {
                                switch (e.code) {
                                  case 'CameraAccessDenied':
                                    break;
                                  default:
                                    break;
                                }
                              }
                            });
                          }
                        },
                        icon:  Icon(
                          Icons.flip_camera_ios,
                          color: ThemeColors.customWhite(context),
                          size: 35,
                        ),
                      ),
                    ),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Container(
                          alignment: Alignment.center,
                          margin: const EdgeInsets.only(
                              bottom: 80, left: 20, right: 20),
                          child: Text(
                            'Posicione seu rosto precisamente no centro da tela. Vamos garantir a melhor apresentação possível.',
                            textAlign: TextAlign.center,
                            style: GoogleFonts.roboto(
                              fontSize: 16,
                              fontWeight: FontWeight.w700,
                              color: ThemeColors.customWhite(context),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
        floatingActionButton: Padding(
          padding: const EdgeInsets.only(left: 30),
          child: ButtonLsCustom(
            text: error ? "TENTAR NOVAMENTE" : 'TIRAR FOTO',
            isLoading: isLoading,
            onPressed: () async {
              error = false;
              file ??= await controller.takePicture();
              setState(() {
                isLoading = true;
              });
              try {
                Uint8List bytes = await file!.readAsBytes();
                final rnd = Random();
                final rand = rnd.nextInt(1000000);
                final fileNameBase = basename(file!.path);
                final fileName =
                    '${DateTime.now().millisecondsSinceEpoch}_$rand${extension(fileNameBase)}';

                final conn = WebConnector();
                final foto = await conn.postImageBlobStorage(
                  fileName: fileName,
                  contentType: 'jpeg',
                  content: bytes,
                );

                final response = await conn.post(
                  '/agente/Selfie-Agente',
                  body: {
                    'IDAgentesSelfInfo': widget.selfie.iDAgentesSelfInfo,
                    'Foto': foto,
                  },
                );
                if (response.statusCode == 200) {
                  Navigator.pop(context);
                }
              } catch (e) {
                setState(() {
                  isLoading = false;
                  error = true;
                });
                debugPrint(e.toString());
              }
            },
          ),
        ),
      ),
    );
  }
}

class InvertedCircleClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final Path path = Path()
      ..addRect(Rect.fromLTWH(0, 0, size.width, size.height))
      ..addRRect(RRect.fromRectAndRadius(
          Rect.fromCenter(
              center: Offset(size.width / 2, size.height / 2.27),
              width: size.width / 1.2,
              height: size.height / 1.4),
          const Radius.circular(0)))
      ..fillType = PathFillType.evenOdd;
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
