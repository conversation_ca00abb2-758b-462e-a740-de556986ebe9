import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/components/custom_scaffold/custom_scaffold.dart';
import 'package:octalog/src/pages/extrato/widget/widget_extrato_card.dart';
import 'package:octalog/src/pages/extrato/widget/widget_extrato_carosseu.dart';
import 'package:octalog/src/utils/extesion.dart';
import 'package:octalog/src/utils/theme_colors.dart';

//import '../../../src/utils/colors.dart';
import '../../helpers/login/login.dart';

class ExtratoEntregaPage extends StatefulWidget {
  const ExtratoEntregaPage({super.key});

  @override
  State<ExtratoEntregaPage> createState() => _ExtratoEntregaPageState();
}

class _ExtratoEntregaPageState extends State<ExtratoEntregaPage> {
  DateTime inicio = DateTime.now();
  DateTime fim = DateTime.now();

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      title: 'Extrato de Entrega',
      onPop: () => Navigator.pop(context),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            const SizedBox(height: 10),
            SizedBox(
              height: 70,
              width: MediaQuery.of(context).size.width,
              child: Row(
                children: [
                  const SizedBox(
                    width: 8,
                  ),
                   VerticalDivider(
                    width: 2,
                    thickness: 3,
                    color: ThemeColors.customOrange(context),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Nome: ${Login.instance.usuarioLogado?.nomeCompleto}',
                          style: GoogleFonts.roboto(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: ThemeColors.customOrange(context).withOpacity(0.9),
                          ),
                        ),
                        Row(
                          children: [
                            Text(
                              'Data: '
                              '${DateTime.now().dataPtBr} à ${DateTime.now().dataPtBr}',
                              style: GoogleFonts.roboto(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color:
                                    ThemeColors.customBlack(context).withOpacity(0.6),
                              ),
                            ),
                             Icon(
                              Icons.arrow_drop_down,
                              color: ThemeColors.customOrange(context),
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 5,
            ),
            SizedBox(
              height: 60,
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: const [
                  WidgetExtratoCarroseu(
                    title: 'Pedidos',
                    text: 10,
                  ),
                  WidgetExtratoCarroseu(
                    title: 'Entregas',
                    text: 10,
                  ),
                  WidgetExtratoCarroseu(
                    title: 'Entregas por dia',
                    text: 10,
                  ),
                  WidgetExtratoCarroseu(
                    title: 'Entregas por dia',
                    text: 10,
                  ),
                ],
              ),
            ),
            SizedBox(
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: Text(
                      "Entregas por dia",
                      textAlign: TextAlign.center,
                      style: GoogleFonts.roboto(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: ThemeColors.customBlack(context).withOpacity(0.4),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const WidgetExtratoCard(),
          ],
        ),
      ),
    );
  }
}
