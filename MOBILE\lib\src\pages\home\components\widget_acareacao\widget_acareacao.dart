import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:latlong2/latlong.dart';
import 'package:octalog/src/components/fcm_alert_dailog/components/fcm_appbar_custom.dart';
import 'package:octalog/src/components/loading_ls/loading_ls.dart';
import 'package:octalog/src/pages/home/<USER>/widget_acareacao/acareacao_map.dart';
import 'package:octalog/src/utils/extesion.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../../../models_new/cliente_new.dart';
import '../../../../utils/colors.dart';
import '../../../../utils/extesion.dart' as extesion;
import 'card_perfil_acareacao.dart';

class AcareacaoWidget extends StatefulWidget {
  final ClienteNew cliente;
  const AcareacaoWidget({super.key, required this.cliente});

  @override
  State<AcareacaoWidget> createState() => _AcareacaoWidgetState();
}

class _AcareacaoWidgetState extends State<AcareacaoWidget> {
  ClienteNew get cliente => widget.cliente;
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = 0;
    _pageController = PageController(initialPage: _currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext ctx) {
    return SafeArea(
      child: Scaffold(
        body: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Padding(
                padding: EdgeInsets.all(8.0),
                child: FcmAppBarCustom(title: 'Acareação'),
              ),
              const SizedBox(
                height: 5,
              ),
              Builder(builder: (context) {
                final List<Widget> listwidgets = [
                  IgnorePointer(
                    ignoring: true,
                    child: SizedBox(
                      width: MediaQuery.of(context).size.width,
                      child: MapWidgetAcareacao(
                        latLngEntrega: LatLng(
                          cliente.info?.latitudeentrega ?? 0,
                          cliente.info?.longitudeentrega ?? 0,
                        ),
                        latLngCliente: LatLng(
                          (cliente.info?.latitudecliente ?? 0) + 00.000996,
                          (cliente.info?.longitudecliente ?? 0) + 00.000996,
                        ),
                      ),
                    ),
                  ),
                  ...widget.cliente.info?.fotos
                          .map(
                            (e) => CachedNetworkImage(
                              imageUrl: e,
                              placeholder: (context, url) => const LoadingLs(),
                              errorWidget: (context, url, error) =>
                                  const Icon(Icons.error),
                            ),
                          )
                          .toList() ??
                      []
                ];
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.grey[200],
                        ),
                        height: 220.0,
                        child: PageView.builder(
                          controller: _pageController,
                          onPageChanged: (index) {
                            setState(() {
                              _currentIndex = index;
                            });
                          },
                          itemCount: listwidgets.length,
                          itemBuilder: (context, index) {
                            return listwidgets[index];
                          },
                        ),
                      ),
                      Stack(
                        children: [
                          if (listwidgets.length <= 1)
                            Padding(
                              padding: const EdgeInsets.only(
                                left: 10,
                              ),
                              child: Container(
                                margin: const EdgeInsets.only(
                                  top: 10,
                                ),
                                width: 100,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(4),
                                  color: ColorsCustom.customRed,
                                ),
                                alignment: Alignment.center,
                                child: const Text(
                                  'SEM FOTO',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 13,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                          Center(
                            child: Padding(
                              padding: const EdgeInsets.all(7.0),
                              child: SizedBox(
                                height: 25,
                                width: MediaQuery.of(context).size.width * 0.5,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: listwidgets
                                      .asMap()
                                      .entries
                                      .map((indicator) {
                                    return GestureDetector(
                                      onTap: () {
                                        _pageController.animateToPage(
                                          indicator.key,
                                          duration:
                                              const Duration(milliseconds: 300),
                                          curve: Curves.easeInOut,
                                        );
                                      },
                                      child: Container(
                                        width: 13,
                                        height: 13,
                                        margin: const EdgeInsets.symmetric(
                                          vertical: 8.0,
                                        ),
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: _currentIndex == indicator.key
                                              ? ThemeColors.customOrange(context)
                                              : ColorsCustom.customGrey,
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              }),
              Padding(
                padding: const EdgeInsets.only(
                  left: 15,
                  right: 10,
                  bottom: 10,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Visibility(
                      visible: cliente.info?.enderecoloja != null &&
                          cliente.info?.enderecoloja.isNotEmpty == true,
                      child: Padding(
                        padding: const EdgeInsets.only(
                          top: 10,
                        ),
                        child: Text(
                          cliente.info?.enderecocliente ?? '',
                          textAlign: TextAlign.start,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                          style: GoogleFonts.roboto(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 10,
                      ),
                      child: CardPerfilAcareacao(
                        referencia: cliente.info?.referencia ?? '',
                        complemento: cliente.info?.complemento ?? "",
                        urlFoto: cliente.info?.logoloja ?? '',
                        pedido: cliente.info?.pedidos.first ?? '',
                        iniciais: cliente.info?.complemento.iniciais ?? '',
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(15, 0, 10, 5),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          'Cliente:',
                          textAlign: TextAlign.start,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                          style: GoogleFonts.roboto(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          cliente.info?.nomecliente ?? 'Sem nome',
                          textAlign: TextAlign.start,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                          style: GoogleFonts.roboto(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.black.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        Text(
                          'Recebedor:',
                          textAlign: TextAlign.start,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                          style: GoogleFonts.roboto(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          cliente.info?.nomerecebedor ?? 'Sem nome',
                          textAlign: TextAlign.start,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                          style: GoogleFonts.roboto(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.black.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        Text(
                          'Data da entrega: ',
                          textAlign: TextAlign.start,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                          style: GoogleFonts.roboto(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ),
                        Text(
                          cliente.info?.dataentrega.dataPtBr ?? 'Sem data',
                          textAlign: TextAlign.start,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                          style: GoogleFonts.roboto(
                            fontSize: 15,
                            fontWeight: FontWeight.w500,
                            color: Colors.black.withOpacity(0.6),
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          cliente.info?.dataentrega.horaPtBrSemSegund ??
                              'Sem data',
                          textAlign: TextAlign.start,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                          style: GoogleFonts.roboto(
                            fontSize: 15,
                            fontWeight: FontWeight.w500,
                            color: Colors.black.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 15,
                  vertical: 2,
                ),
                child: Visibility(
                  visible: cliente.info?.mensagens != null &&
                      cliente.info?.mensagens.isNotEmpty == true,
                  child: Container(
                    constraints: const BoxConstraints(
                      minHeight: 60,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.grey[200],
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 10,
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ...List.generate(
                          cliente.info?.mensagens.length ?? 0,
                          (index) {
                            final mensagem = cliente.info?.mensagens[index];
                            return Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(
                                    top: 2,
                                    left: 2,
                                    right: 2,
                                  ),
                                  child: Text(
                                    '$mensagem',
                                    textAlign: TextAlign.start,
                                    style: GoogleFonts.roboto(
                                      fontSize: 13,
                                      fontWeight: FontWeight.w500,
                                      color: const Color.fromARGB(200, 0, 0, 0),
                                    ),
                                  ),
                                ),
                                if (index != cliente.info!.mensagens.length - 1)
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 2,
                                    ),
                                    child: Divider(
                                      height: 5,
                                      thickness: 1,
                                      color: Colors.black.withOpacity(0.3),
                                    ),
                                  ),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(
                height: 10,
              )
            ],
          ),
        ),
      ),
    );
  }
}
