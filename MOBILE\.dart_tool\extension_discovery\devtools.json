{"version": 2, "entries": [{"package": "drift", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/drift-2.26.0/", "packageUri": "lib/", "config": {"name": "drift", "issue_tracker": "https://github.com/simolus3/drift/issues", "version": "0.0.1", "material_icon_code_point": "0xf41e", "issueTracker": "https://github.com/simolus3/drift/issues", "materialIconCodePoint": "0xf41e"}}, {"package": "shared_preferences", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.5.3/", "packageUri": "lib/", "config": {"name": "shared_preferences", "issueTracker": "https://github.com/flutter/flutter/issues?q=is%3Aissue+is%3Aopen+label%3A%22p%3A+shared_preferences%22", "version": "1.0.0", "materialIconCodePoint": "0xe683"}}, {"package": "octalog", "rootUri": "../", "packageUri": "lib/"}]}