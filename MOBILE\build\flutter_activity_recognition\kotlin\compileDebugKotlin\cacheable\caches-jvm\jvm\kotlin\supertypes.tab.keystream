Icom.pravera.flutter_activity_recognition.FlutterActivityRecognitionPlugin>com.pravera.flutter_activity_recognition.MethodCallHandlerImpl:com.pravera.flutter_activity_recognition.StreamHandlerImpl:com.pravera.flutter_activity_recognition.errors.ErrorCodesBcom.pravera.flutter_activity_recognition.models.ActivityPermissionJcom.pravera.flutter_activity_recognition.service.ActivityPermissionManagerRcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentReceiverQcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentServiceKcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  