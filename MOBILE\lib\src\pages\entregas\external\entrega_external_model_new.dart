import 'package:latlong2/latlong.dart';

class EntregaExternalModelNew {
  final DateTime? inicio;
  final DateTime? chegada;
  final double? latitude;
  final double? longitude;
  final DateTime dataHora;
  final String? foto;
  final String? nomeRecebedor;
  final int? idStatusAtividade;
  final List<int> idosList;
  final double? valorRecebido;
  final LatLng? inicioLocalizacao;
  final LatLng? chegadaLocalizacao;
  final bool? foraDoLocal;

  EntregaExternalModelNew({
    this.inicio,
    this.chegada,
    required this.latitude,
    required this.longitude,
    required this.dataHora,
    this.foto,
    this.nomeRecebedor,
    this.idStatusAtividade,
    required this.idosList,
    this.valorRecebido,
    required this.inicioLocalizacao,
    required this.chegadaLocalizacao,
    this.foraDoLocal,
  });

  EntregaExternalModelNew copyWith({
    DateTime? inicio,
    DateTime? chegada,
    double? latitude,
    double? longitude,
    DateTime? dataHora,
    String? foto,
    String? nomeRecebedor,
    int? idStatusAtividade,
    List<int>? idosList,
    double? valorRecebido,
    LatLng? inicioLocalizacao,
    LatLng? fimLocalizacao,
    String? finalizar,
  }) {
    return EntregaExternalModelNew(
      inicio: inicio ?? this.inicio,
      chegada: chegada ?? this.chegada,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      dataHora: dataHora ?? this.dataHora,
      foto: foto ?? this.foto,
      nomeRecebedor: nomeRecebedor ?? this.nomeRecebedor,
      idStatusAtividade: idStatusAtividade ?? this.idStatusAtividade,
      idosList: idosList ?? this.idosList,
      valorRecebido: valorRecebido ?? this.valorRecebido,
      inicioLocalizacao: inicioLocalizacao ?? this.inicioLocalizacao,
      chegadaLocalizacao: fimLocalizacao ?? chegadaLocalizacao,
      foraDoLocal: foraDoLocal ?? foraDoLocal,
    );
  }

  Map<String, dynamic> toMap() {
    final resposta = {
      'Inicio': {
        'DataHora': inicio?.toIso8601String(),
        'IDOS': idosList,
        'Latitude': inicioLocalizacao?.latitude,
        'Longitude': inicioLocalizacao?.longitude,
      },
      'Chegada': {
        'DataHora': chegada?.toIso8601String(),
        'IDOS': idosList,
        'Latitude': chegadaLocalizacao?.latitude,
        'Longitude': chegadaLocalizacao?.longitude,
      },
      'Entrega': {
        'DataHora': dataHora.toIso8601String(),
        'IDOS': idosList,
        'Latitude': latitude,
        'Longitude': longitude,
      },
      'Foto': [foto],
      'IDOS': idosList,
      'NomeRecebedor': nomeRecebedor,
      'IDStatusAtividade': idStatusAtividade,
      'ValorRecebido': valorRecebido?.toString(),
    };
    return resposta;
  }
}
