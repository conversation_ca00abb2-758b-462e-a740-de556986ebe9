import 'package:asuka/asuka.dart' as asuka;
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/components/custom_scaffold/custom_scaffold.dart';
import 'package:octalog/src/pages/entrega_newpages/componentes/elevated_ls_button.dart';

import '../../../components/loading_ls/loading_ls.dart';
import '../../../database/config_blob/config_database.dart';
import '../../../database/offline_request/offline_request_database.dart';
import '../home_controller.dart';

Future<bool> entrarTelaSicronismo({int? minimo}) async {
  final responseConfig = await ConfigDatabase.instance.getConfig();

  final atividadesRestantes =
      await OfflineRequestDatabase.instance.getQtdEnventosRestantes();

  final m = minimo ?? responseConfig.qtdeForceSincronismoItens;

  if (atividadesRestantes >= m && atividadesRestantes != 0) {
    HomeController.instance.setDataForceUltimaSincronizacao();

    bool? resposta = await asuka.Asuka.showDialog<bool>(
        barrierDismissible: true,
        builder: (ctx) {
          return const AvisoScronimosPendente();
        });
    return resposta ?? false;
  }

  return true;
}

class AvisoScronimosPendente extends StatefulWidget {
  const AvisoScronimosPendente({
    super.key,
  });

  @override
  State<AvisoScronimosPendente> createState() => _AvisoScronimosPendenteState();
}

class _AvisoScronimosPendenteState extends State<AvisoScronimosPendente> {
  int qtd = 0;
  bool isloop = false;

  bool get isVisible => qtd > 0;

  void loadQtd() async {
    while (true) {
      if (!mounted) return;
      qtd = await OfflineRequestDatabase.instance.getQtdEnventosRestantes();
      if (!mounted) return;

      setState(() {});

      // final stateApp =
      //     WidgetsBinding.instance.lifecycleState == AppLifecycleState.resumed;
      // if (stateApp) {
      //   await FcmDataBase.instance.shownotification();
      // }
      await Future.delayed(const Duration(seconds: 1));
      if (qtd != 0) {
        isloop = true;
      }
      if (qtd == 0 && isloop) {
        Navigator.of(context).pop(true);
        return;
      }
    }
  }

  @override
  void initState() {
    super.initState();
    loadQtd();
  }

  @override
  void dispose() {
    super.dispose();
    loadQtd();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      canPop: false,
      onPopClose: () async {
        bool? resposta = await asuka.Asuka.showDialog(builder: (ctx) {
          return AlertDialog(
            title: const Text("Sincronização pendente"),
            content: const Text(
                "Por favor, mantenha o aplicativo aberto até a conclusão da sincronização."),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(false);
                },
                child: const Text("FECHAR"),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(true);
                },
                child: const Text("VOLTAR"),
              ),
            ],
          );
        });
        return resposta ?? false;
      },
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(right: 18),
        child: ElevatedLsButton(
          text: 'VOLTAR',
          isLoading: false,
          onPressed: () {
            Navigator.of(context).pop(false);
          },
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            const SizedBox(height: 20),
            const Text(
              "Sincronização pendente",
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            Column(
              children: [
                Text(
                  "$qtd Sincronizando",
                  textAlign: TextAlign.center,
                  style: GoogleFonts.roboto(
                      fontSize: 18, fontWeight: FontWeight.w800),
                ),
                const SizedBox(height: 10),
                Text(
                  "Por favor, mantenha o aplicativo aberto até a conclusão da sincronização. Fechar antes pode causar perda de informações importantes.",
                  textAlign: TextAlign.center,
                  style: GoogleFonts.roboto(
                      fontSize: 16, fontWeight: FontWeight.w400),
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      alignment: Alignment.topCenter,
                      height: 105,
                      child: Container(
                        height: 80,
                        alignment: Alignment.topCenter,
                        child: const LoadingLs(),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const Spacer(),
          ],
        ),
      ),
    );
  }
}
