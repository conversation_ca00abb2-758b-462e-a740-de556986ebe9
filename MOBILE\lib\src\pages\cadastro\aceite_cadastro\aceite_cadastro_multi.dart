import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdfrx/pdfrx.dart';
import 'package:permission_handler/permission_handler.dart';

import '../cadastro_store.dart';

class AceiteCadastroMulti extends StatefulWidget {
  final CadastroStore store;
  const AceiteCadastroMulti({super.key, required this.store});

  @override
  State<AceiteCadastroMulti> createState() => _AceiteCadastroMultiState();
}

class _AceiteCadastroMultiState extends State<AceiteCadastroMulti> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.store.buscarContratos(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: widget.store.state,
      builder: (context, state, child) {
        final contratos = state.contratos;
        final transportadoras = state.transportadoras;

        if (contratos.isEmpty) {
          return _LoadingScreen();
        }

        return ListView.builder(
          shrinkWrap: true,
          itemCount: contratos.length + 1,
          itemBuilder: (context, index) {
            if (index == contratos.length) {
              return const SizedBox(height: 100);
            }
            final contract = contratos[index];
            final isAlreadyAccepted = transportadoras.contains(
              contract.idTransportadora,
            );
            final url = contract.url ?? '';

            return Card(
              child: Column(
                children: [
                  Builder(
                    builder: (context) {
                      if (isAlreadyAccepted || url.isEmpty) {
                        return const SizedBox.shrink();
                      }
                      // visualiza o pdf do contrato (dentro da pagina)
                      return SizedBox(
                        height: 300,
                        child: GestureDetector(
                          onTap: () => _expandPdf(url),
                          child: PdfViewer.uri(Uri.parse(url)),
                        ),
                      );
                    },
                  ),
                  ListTile(
                    title: Text(contract.transportadora),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Builder(
                          builder: (_) {
                            final cnpj = contract.cnpj ?? '';
                            if (cnpj.isNotEmpty) {
                              return Text('CNPJ: $cnpj');
                            }
                            return const SizedBox.shrink();
                          },
                        ),
                        Builder(
                          builder: (_) {
                            if (!isAlreadyAccepted) {
                              return Text('Aguardando aceite');
                            }
                            final dataConfirmouCadastro =
                                contract.dataConfirmouCadastro;
                            if (dataConfirmouCadastro != null) {
                              return Text(
                                'Aceito em: ${_formatDate(dataConfirmouCadastro)}',
                              );
                            }
                            return Text('Aceito agora');
                          },
                        ),
                      ],
                    ),
                    trailing: Builder(
                      builder: (context) {
                        if (url.isNotEmpty) {
                          return IconButton(
                            icon: const Icon(Icons.download),
                            onPressed: () => _downloadPdf(url),
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                    leading: Icon(
                      isAlreadyAccepted ? Icons.check_circle : Icons.pending,
                      color: isAlreadyAccepted ? Colors.green : Colors.orange,
                    ),
                  ),
                  Visibility(
                    visible: !isAlreadyAccepted,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 16,
                      ),
                      child: Row(
                        children: [
                          Checkbox(
                            value: isAlreadyAccepted,
                            onChanged: (value) {
                              widget.store.aceitarContrato(
                                value!,
                                contract.idTransportadora,
                              );
                            },
                          ),
                          Expanded(child: Text(contract.textoAceite)),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  void _expandPdf(String url) {
    showDialog(
      context: context,
      builder:
          (context) => Dialog.fullscreen(
            child: Scaffold(
              appBar: AppBar(
                title: const Text('Contrato PDF'),
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
              body: PdfViewer.uri(Uri.parse(url)),
            ),
          ),
    );
  }

  Future<void> _downloadPdf(String url) async {
    try {
      // Solicita permissão para acessar o armazenamento
      if (Platform.isAndroid) {
        // Para Android 13+ (API 33+), usa as novas permissões de mídia
        final storageStatus = await Permission.storage.request();

        if (!storageStatus.isGranted) {
          // Tenta a permissão de gerenciamento externo
          final manageStatus = await Permission.manageExternalStorage.request();

          if (!manageStatus.isGranted) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text(
                    'Permissão necessária para salvar arquivo',
                  ),
                  action: SnackBarAction(
                    label: 'Configurações',
                    onPressed: () => openAppSettings(),
                  ),
                ),
              );
            }
            return;
          }
        }
      }

      // Mostra indicador de carregamento
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                CircularProgressIndicator(strokeWidth: 2),
                SizedBox(width: 16),
                Text('Baixando PDF...'),
              ],
            ),
            duration: Duration(seconds: 30),
          ),
        );
      }

      // Obter diretório de downloads
      Directory? downloadsDir;
      if (Platform.isAndroid) {
        downloadsDir = Directory('/storage/emulated/0/Download');
      } else if (Platform.isIOS) {
        downloadsDir = await getApplicationDocumentsDirectory();
      }

      if (downloadsDir == null || !downloadsDir.existsSync()) {
        downloadsDir = await getApplicationDocumentsDirectory();
      }

      // Nome do arquivo
      final fileName = 'contrato_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final filePath = '${downloadsDir.path}/$fileName';

      // Baixar o arquivo
      final dio = Dio();
      await dio.download(url, filePath);

      // Remover snackbar de carregamento e mostrar sucesso
      if (mounted) {
        ScaffoldMessenger.of(context).removeCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('PDF salvo em: $filePath'),
            action: SnackBarAction(
              label: 'Abrir',
              onPressed: () => _openFile(filePath),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).removeCurrentSnackBar();
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Erro ao baixar PDF: $e')));
      }
    }
  }

  Future<void> _openFile(String filePath) async {
    try {
      final result = await OpenFile.open(filePath);

      if (result.type != ResultType.done) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Erro ao abrir arquivo: ${result.message}')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Erro ao abrir arquivo: $e')));
      }
    }
  }
}

class _LoadingScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Contratos'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Carregando contratos...'),
          ],
        ),
      ),
    );
  }
}
