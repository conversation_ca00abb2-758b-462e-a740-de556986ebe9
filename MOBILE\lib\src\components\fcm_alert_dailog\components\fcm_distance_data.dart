import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import 'package:octalog/src/components/flavor_image/flavor_image.dart';
import 'package:octalog/src/components/loading_custom/loading_custom.dart';

import 'package:octalog/src/utils/theme_colors.dart';

import '../../../helpers/gps/gps_contract.dart';
import '../../../helpers/maps.dart';

class FcmDistanceData extends StatelessWidget {
  final String? horaFinal;
  final LatLng destino;
  final String? distanceText;
  const FcmDistanceData({super.key, required this.horaFinal, required this.destino, this.distanceText});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 20, right: 20),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              SizedBox(
                //  height: 50,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [FlavorImage(assetName: 'locations.png', color: ThemeColors.customOrange(context), width: 30)],
                ),
              ),
              const SizedBox(width: 10),
              FutureBuilder<String>(
                future: calculate(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const SizedBox(height: 50, width: 60, child: LoadingLs());
                  }
                  final value = snapshot.data;
                  return Text(value ?? 'Distancia não calculada');
                },
              ),
            ],
          ),
          const SizedBox(width: 40),
        ],
      ),
    );
  }

  Future<String> calculate() async {
    if (distanceText != null) return distanceText!;
    final position = GpsHelperContract.instance.currentPosition;
    if (position == null) return 'Distancia não calculada';
    final inicio = LatLng(position.latitude, position.longitude);
    try {
      final result = await LsMaps.instance.getRouteStartEnd(inicio, destino);
      if (result.distance == 0) throw 'error';
      //if (result.distance > 200) return 'Distancia não calculada';

      return 'Distância: ${(result.distance / 1000).toStringAsFixed(2)} KM';
    } catch (e) {
      final distancia = GeolocatorPlatform.instance.distanceBetween(inicio.latitude, inicio.longitude, destino.latitude, destino.longitude);

      //if (distancia > 200) return 'Distancia não calculada';
      return 'Distância: ${distancia.toStringAsFixed(2)} KM';
    }
  }
}
