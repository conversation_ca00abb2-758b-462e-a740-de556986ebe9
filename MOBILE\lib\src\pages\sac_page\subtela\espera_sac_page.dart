import 'package:flutter/material.dart';
import 'package:octalog/src/components/buttom_ls/button_ls_custom.dart';
import 'package:octalog/src/pages/sac_page/sac_page_store.dart';
import 'package:octalog/src/utils/colors-dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../../components/custom_scaffold/custom_scaffold.dart';
import '../components/circule_porcent.dart';
import '../sac_page.state.dart';
//import './../../../utils/colors.dart';

class SacEsperaPage extends StatefulWidget {
  final SacPageStore store;
  const SacEsperaPage({super.key, required this.store});

  @override
  State<SacEsperaPage> createState() => _SacEsperaPageState();
}

class _SacEsperaPageState extends State<SacEsperaPage> {
  int? filaInicial;

  @override
  void initState() {
    widget.store.initBuscarFila();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<SacPageState>(
      valueListenable: widget.store.state,
      builder: (_, SacPageState value, __) {
        filaInicial ??= value.sacModelFila?.fila ?? 0;
        return Visibility(
          child: CustomScaffold(
            floatingActionButton: Visibility(
              visible: value.sacModelFila?.chamado.nomeAtendente == null,
              child: Padding(
                padding: EdgeInsets.only(
                  left: MediaQuery.of(context).size.width * 0.08,
                ),
                child: SizedBox(
                  width: MediaQuery.of(context).size.width,
                  height: 50,
                  child: ButtonLsCustom(
                    text: 'CANCELAR ATENDIMENTO',
                    colorBackground: ColorsCustom.customRed.withOpacity(0.8),
                    isLoading: value.isBotaoCancelarSAC,
                    onPressed: () async {
                      if (value.sacModelFila?.chamado.nomeAtendente != null) {
                        return;
                      }
                      await widget.store.cancelarSac(
                        context,
                      );
                    },
                  ),
                ),
              ),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 15),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              const SizedBox(
                                width: 30,
                              ),
                              const Text(
                                "SAC",
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 28,
                                ),
                              ),
                              Builder(
                                builder: (context) {
                                  final busca = value.indicadorDeBuscaFila;
                                  if (busca == null) {
                                    return const SizedBox.shrink();
                                  }
                                  return Icon(
                                    busca ? Icons.wifi : Icons.wifi_outlined,
                                    color: busca
                                        ? ThemeColors.customGreen(context)
                                        : ThemeColors.customGrey(context),
                                    size: 30,
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Builder(
                      builder: (context) {
                        final fila = value.sacModelFila?.fila ?? 0;
                        final bsucando = value.indicadorDeBuscaFila;
                        return CirculePorcent(
                          fila: fila,
                          atendimentoIniciado:
                              value.sacModelFila?.chamado.nomeAtendente != null,
                          buscando: bsucando,
                        );
                      },
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 40, vertical: 40),
                      child: Column(
                        children: [
                          Visibility(
                            visible:
                                value.sacModelFila?.chamado.ocorrencia != null,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Text(
                                  "Ocorrência: ",
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  value.sacModelFila?.chamado.ocorrencia ?? "",
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Visibility(
                            visible:
                                value.sacModelFila?.chamado.nomeAtendente !=
                                    null,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Text(
                                  "Atendente: ",
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  value.sacModelFila?.chamado.nomeAtendente ??
                                      "",
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
