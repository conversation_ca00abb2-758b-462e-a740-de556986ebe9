import 'dart:convert';

import 'package:hive/hive.dart';
//import 'package:octalog/src/helpers/login/login.dart';
import 'package:octalog/src/helpers/web_connector.dart';
import 'package:octalog/src/utils/offline_helper.dart';

import 'config_blob_model.dart';

class ConfigDatabase {
  static final ConfigDatabase instance = ConfigDatabase();
  late Box<String> _base;
  bool _initialized = false;

  ConfigDatabase() {
    _init();
  }

  Future<void> _init() async {
    if (!_initialized) {
      _base = await Hive.openBox('config_map');
      _initialized = true;
    }
  }

  Future<ConfigBlobModelHive> getConfig() async {
    await _init();
    final data = _base.values.firstOrNull;

    if (data == null) {
      final config = await fetchConfig();
      if (config != null) {
        return config;
      }
    }
    return ConfigBlobModelHive.fromJson(jsonDecode(data.toString()));
  }

  Future<ConfigBlobModelHive?> fetchConfig() async {
    try {
      final responseConfig = await WebConnector().getSimples('/config/dados');
      final config = ConfigBlobModelHive.fromJson(
        responseConfig.data['blobStorage'],
      );
      await _init();
      await _base.clear();
      await _base.add(json.encode(config.toHiveMap()));
      return config;
    } catch (e) {
      offlineStore.setOffline(false);
      return null;
    }
  }

  Future<void> clear() async {
    await _init();
    await _base.clear();
  }
}
