import 'dart:io';

import 'package:asuka/asuka.dart' as asuka;
import 'package:flutter/material.dart';
import 'package:octalog/src/helpers/web_connector.dart';
import 'package:octalog/src/utils/offline_helper.dart';
import 'package:octalog/src/utils/theme_colors.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../models/versoes.dart';

abstract class VersoesHelper {
  static String localVersion = '0.0.0';

  static Future<bool> updateVerify(BuildContext context) async {
    final updateNecessary = await _getUpdateNecessary();
    final force = Platform.isIOS;
    if (updateNecessary) {
      await _updateDialog(context: context, force: !force);
      return force;
    }
    return false;
  }

  static Future<void> _updateDialog({required BuildContext context, bool force = false}) async {
    await showDialog(
      barrierDismissible: false,
      context: context,
      builder: (ctx) {
        return AlertDialog(
          title: const Row(mainAxisSize: MainAxisSize.max, mainAxisAlignment: MainAxisAlignment.center, children: [Text('Atualização disponível')]),
          insetPadding: const EdgeInsets.symmetric(horizontal: 30),
          content: const Text(
            'Uma nova atualização está disponível.\n\nessa atualização é necessária para o funcionamento correto ao aplicativo!',
            textAlign: TextAlign.center,
          ),
          actions: <Widget>[
            TextButton(
              child: Text('CONFIRMAR', style: TextStyle(color: ThemeColors.customOrange(context))),
              onPressed: () {
                _downloadApp();
              },
            ),
          ],
        );
      },
    );
  }

  static Future<bool> _getUpdateNecessary() async {
    try {
      localVersion = await _getLocalVersion();
      final storeVersionsData = await WebConnector()
          .getSimples('/config/versao')
          .timeout(
            const Duration(seconds: 20),
            onTimeout: () {
              throw false;
            },
          );
      final storeVersions = Versoes.fromMap(storeVersionsData.data);
      return storeVersions.updateNecessary(localVersion);
    } catch (e) {
      offlineStore.setOffline(false);
      return false;
    }
  }

  static Future<String> _getLocalVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    return packageInfo.version;
  }

  static String get _linkApp =>
      Platform.isIOS ? 'https://apps.apple.com/us/app/octalog-para-entregadores/id6444612656' : 'https://play.google.com/store/apps/details?id=com.octalog';

  static Future<void> _downloadApp() async {
    await launchUrlString(_linkApp, mode: LaunchMode.externalApplication);
  }

  static Future<void> updateDialogGlobal() async {
    // await showDialog(
    //   barrierDismissible: false,
    //   context: context,
    //   builder: (ctx) {
    //     return AlertDialog(
    //       title: const Row(
    //         mainAxisSize: MainAxisSize.max,
    //         mainAxisAlignment: MainAxisAlignment.center,
    //         children: [
    //           Text('Atualização disponível'),
    //         ],
    //       ),
    //       insetPadding: const EdgeInsets.symmetric(
    //         horizontal: 30,
    //       ),
    //       content: const Text(
    //         'Uma nova atualização está disponível.\n\nessa atualização é necessária para o funcionamento correto ao aplicativo!',
    //         textAlign: TextAlign.center,
    //       ),
    //       actions: <Widget>[
    //         TextButton(
    //           child: const Text('CONFIRMAR',
    //               style: TextStyle(color: ThemeColors.customOrange(context))),
    //           onPressed: () {
    //             _downloadApp(context);
    //           },
    //         ),
    //       ],
    //     );
    //   },
    // );

    await asuka.Asuka.showDialog(
      builder: (ctx) {
        return AlertDialog(
          title: const Row(mainAxisSize: MainAxisSize.max, mainAxisAlignment: MainAxisAlignment.center, children: [Text('Atualização disponível')]),
          insetPadding: const EdgeInsets.symmetric(horizontal: 30),
          content: const Text(
            'Uma nova atualização está disponível.\n\nessa atualização é necessária para o funcionamento correto ao aplicativo!',
            textAlign: TextAlign.center,
          ),
          actions: <Widget>[
            TextButton(
              child: Text('CONFIRMAR', style: TextStyle(color: ThemeColors.customOrange(ctx))),
              onPressed: () {
                _downloadApp();
              },
            ),
          ],
        );
      },
    );
  }
}
