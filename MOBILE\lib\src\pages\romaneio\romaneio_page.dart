import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/components/custom_scaffold/custom_scaffold.dart';
import 'package:octalog/src/pages/romaneio/romaneio_state.dart';
import 'package:octalog/src/pages/romaneio/romaneio_store.dart';
import 'package:octalog/src/pages/romaneio/widget/widget_romaneio_page.dart';
import 'package:octalog/src/utils/extesion.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../components/loading_ls/loading_ls.dart';
import '../../helpers/login/login.dart';
import '../../utils/colors.dart';

class RomaneioPage extends StatefulWidget {
  const RomaneioPage({super.key});

  @override
  State<RomaneioPage> createState() => _RomaneioPageState();
}

class _RomaneioPageState extends State<RomaneioPage> {
  final RomaneioStore store = RomaneioStore();

  @override
  void initState() {
    store.setRomaneios();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      title: 'Romaneio de Expedição',
      onPop: () => Navigator.pop(context),
      onPopClose: () async {
        Navigator.pop(context);
        return true;
      },
      child: ValueListenableBuilder<RomaneioState>(
        valueListenable: store.state,
        builder: (context, state, _) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Row(
                  children: [
                    const SizedBox(
                      width: 15,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 8,
                        right: 8,
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Nome: ${Login.instance.usuarioLogado?.nomeCompleto}',
                            style: GoogleFonts.roboto(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: ThemeColors.customOrange(context).withOpacity(0.9),
                            ),
                          ),
                          Text(
                            'Data: '
                            '${DateTime.now().dataPtBr} ${DateTime.now().horaPtBrSemSegund}',
                            style: GoogleFonts.roboto(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: ColorsCustom.customBlack.withOpacity(0.6),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              state.loading
                  ? const Expanded(
                      child: LoadingLs(),
                    )
                  : Expanded(
                      child: Builder(
                        builder: (context) {
                          if (state.romaneios.isEmpty) {
                            return const Center(
                              child: Text('Nenhum romaneio encontrado'),
                            );
                          }
                          return ListView.builder(
                            physics: const ScrollPhysics(),
                            shrinkWrap: true,
                            itemCount: state.romaneiosOrdenados.length,
                            itemBuilder: (context, index) {
                              final romaneio = state.romaneiosOrdenados[index];

                              return Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 10,
                                  vertical: 5,
                                ),
                                child: WidgetRomaneioCard(
                                  romaneio: romaneio,
                                ),
                              );
                            },
                          );
                        },
                      ),
                    ),
            ],
          );
        },
      ),
    );
  }
}
