import 'package:flutter/material.dart';
import 'package:octalog/src/components/buttom_ls/button_ls_custom.dart';
import 'package:octalog/src/components/loading_ls/loading_ls.dart';
//import 'package:octalog/src/components/tutorial/tutorial_page.dart';
import 'package:octalog/src/helpers/login/login.dart';
import 'package:octalog/src/helpers/login/login_hive.dart';
import 'package:octalog/src/pages/cadastro/aceiteCadastro/aceiteCadastro.dart';
import 'package:octalog/src/pages/cadastro/components/card_pestrador.dart';
import 'package:octalog/src/pages/cadastro/dados_bancarios/dados_bancario.dart';
import 'package:octalog/src/pages/cadastro/documentos/documentos_page.dart';
import 'package:octalog/src/pages/cadastro/enum/enum_page_fotos.dart';
import 'package:octalog/src/pages/cadastro/fotos/fotos_documentos_page.dart';
import 'package:octalog/src/pages/home/<USER>';
import 'package:octalog/src/pages/login/login_page.dart';
import 'package:octalog/src/utils/theme_colors.dart';

//import '../../components/tutorial/tutorial_page.dart';
import '../../utils/colors-dart';
import 'cadastro_state.dart';
import 'cadastro_store.dart';
import 'endereco/endereco_page.dart';
import 'enum/enum_page_cadastro.dart';
import 'first_access/first_access_page.dart';
//import 'aceiteCadastro/aceiteCadastro.dart';

class CadastroAgente extends StatefulWidget {
  const CadastroAgente({super.key});

  @override
  State<CadastroAgente> createState() => _CadastroAgenteState();
}

class _CadastroAgenteState extends State<CadastroAgente> {
  final store = CadastroStore();

  @override
  void initState() {
    store.buscarContatrato(context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Future showAlertReturn(bool dataConfirmouCadastro) async {
      await showAdaptiveDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Deseja sair do cadastro?'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('NÃO', style: TextStyle(color: Colors.black)),
              ),
              TextButton(
                onPressed: () async {
                  if (dataConfirmouCadastro) {
                    Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const Home()));
                  } else {
                    await LoginHive.instance.clear();
                    await Login.instance.logout();
                    Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const LoginPage()));
                  }
                },
                child: const Text('SIM', style: TextStyle(color: Colors.orange)),
              ),
            ],
          );
        },
      );
    }

    return ValueListenableBuilder<CadastroState>(
      valueListenable: store.state,
      builder: (context, state, _) {
        if (state.isLoadingcarregartela) {
          return const Center(child: LoadingLs());
        }
        final cadastroPages = store.getCadastroPages();

        return PopScope(
          canPop: false,
          onPopInvoked: (bool isPop) async {
            bool dataConfirmouCadastro = state.contratoModel!.dataConfirmouCadastro != null;
            await showAlertReturn(dataConfirmouCadastro);
          },
          child: SafeArea(
            child: Scaffold(
              body: Column(
                children: [
                  Center(
                    child: Container(
                      margin: const EdgeInsets.only(top: 20),
                      width: MediaQuery.of(context).size.width * 0.9,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        mainAxisSize: MainAxisSize.max,
                        children: List<Widget>.generate(
                          cadastroPages.length,
                          (index) => Expanded(
                            child: GestureDetector(
                              onTap: () {
                                store.setTitlesPage(
                                  state.contratoModel!.agentePrincipal
                                      ? cadastroPages[index]
                                      : EnumCadastroPages.dadosBancarios == cadastroPages[index]
                                      ? EnumCadastroPages.fotos
                                      : cadastroPages[index],
                                );
                              },
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Builder(
                                          builder: (context) {
                                            if (index == 0) {
                                              return const SizedBox();
                                            }
                                            bool isPrevPage = state.titlesPage.index >= index;
                                            return Divider(color: isPrevPage ? ThemeColors.customOrange(context) : Colors.grey, thickness: 2, height: 0);
                                          },
                                        ),
                                      ),
                                      Container(
                                        height: 20,
                                        width: 20,
                                        margin: const EdgeInsets.all(5),
                                        decoration: BoxDecoration(
                                          color: state.titlesPage.index >= index ? ThemeColors.customOrange(context) : ThemeColors.customGrey(context),
                                          borderRadius: BorderRadius.circular(20),
                                        ),
                                      ),
                                      Expanded(
                                        child: Builder(
                                          builder: (context) {
                                            if (index == cadastroPages.length - 1) {
                                              return const SizedBox();
                                            }
                                            bool isPrevPage = state.titlesPage.index >= index + 1;
                                            return Divider(color: isPrevPage ? ThemeColors.customOrange(context) : ThemeColors.customGrey(context), thickness: 2, height: 0);
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 40,
                                    width: 80,
                                    child: Text(
                                      cadastroPages[index].name,
                                      maxLines: 2,
                                      textAlign: TextAlign.center,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                        color: state.titlesPage.index >= index ? ThemeColors.customOrange(context) : ThemeColors.customGrey(context),
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(right: MediaQuery.of(context).size.width * .05, left: MediaQuery.of(context).size.width * .05),
                    child: CardPrestador(idTipoAgente: state.contratoModel?.idTipoAgente ?? 0, nomePrestador: state.contratoModel?.prestador ?? ''),
                  ),
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: MediaQuery.of(context).size.width * .05),
                      child: PageView.builder(
                        physics: const NeverScrollableScrollPhysics(),
                        controller: state.pageController,
                        onPageChanged: (index) {
                          store.setTitlesPage(state.contratoModel!.agentePrincipal ? cadastroPages[index] : EnumCadastroPages.values[index]);
                        },
                        itemCount: 4,
                        itemBuilder: (BuildContext context, int index) {
                          switch (state.titlesPage) {
                            case EnumCadastroPages.notificacaoTransportadora:
                              return RawScrollbar(
                                thumbColor: ThemeColors.customOrange(context),
                                thickness: 5,
                                radius: const Radius.circular(10),
                                padding: const EdgeInsets.all(5),
                                child: SingleChildScrollView(physics: const BouncingScrollPhysics(), child: AceiteCadastro(store: store)),
                              );
                            case EnumCadastroPages.informacoesPessoais:
                              return RawScrollbar(
                                thumbColor: ThemeColors.customOrange(context),
                                thickness: 5,
                                radius: const Radius.circular(10),
                                padding: const EdgeInsets.all(5),
                                child: SingleChildScrollView(physics: const BouncingScrollPhysics(), child: FirstAccess(store: store)),
                              );
                            case EnumCadastroPages.enderecoResidencial:
                              return RawScrollbar(
                                thumbColor: ThemeColors.customOrange(context),
                                thickness: 5,
                                radius: const Radius.circular(10),
                                child: SingleChildScrollView(child: EnderecoPage(store: store)),
                              );
                            case EnumCadastroPages.validadeDocumentos:
                              return RawScrollbar(
                                thumbColor: ThemeColors.customOrange(context),
                                thickness: 5,
                                radius: const Radius.circular(10),
                                child: SingleChildScrollView(child: DocumenoPage(store: store)),
                              );
                            case EnumCadastroPages.dadosBancarios:
                              return RawScrollbar(
                                thumbColor: ThemeColors.customOrange(context),
                                thickness: 5,
                                radius: const Radius.circular(10),
                                child: SingleChildScrollView(child: DadosBancariosPage(store: store)),
                              );
                            case EnumCadastroPages.fotos:
                              return RawScrollbar(
                                thumbColor: ThemeColors.customOrange(context),
                                thickness: 5,
                                radius: const Radius.circular(10),
                                child: SingleChildScrollView(child: FotosPage(store: store)),
                              );
                          }
                        },
                      ),
                    ),
                  ),
                ],
              ),
              floatingActionButton: Padding(
                padding: const EdgeInsets.only(left: 30),
                child: Builder(
                  builder: (context) {
                    String? text;
                    VoidCallback? onPressed;
                    String? message;
                    Color? colorBackground;
                    switch (state.titlesPage) {
                      case EnumCadastroPages.notificacaoTransportadora:
                        text = 'PRÓXIMO';
                        message = store.validarAceiteTransportadora();
                        if (message == null || message.isEmpty) {
                          onPressed = () {
                            store.setTitlesPage(EnumCadastroPages.informacoesPessoais);
                          };
                        }
                        break;
                      case EnumCadastroPages.informacoesPessoais:
                        text = 'PRÓXIMO';
                        message = store.validarDadosPessoais();
                        if (message == null || message.isEmpty) {
                          onPressed = () {
                            store.setTitlesPage(EnumCadastroPages.enderecoResidencial);
                          };
                        }
                        break;
                      case EnumCadastroPages.enderecoResidencial:
                        text = 'PRÓXIMO';
                        message = store.validarEndereco();
                        if (message == null || message.isEmpty) {
                          onPressed = () {
                            store.setTitlesPage(EnumCadastroPages.validadeDocumentos);
                          };
                        }
                        break;
                      case EnumCadastroPages.validadeDocumentos:
                        text = 'PRÓXIMO';
                        message = store.validarTipoDataVeiculo();
                        if (message == null || message.isEmpty) {
                          onPressed = () {
                            store.setTitlesPage(state.contratoModel!.agentePrincipal ? EnumCadastroPages.dadosBancarios : EnumCadastroPages.fotos);
                          };
                        }
                        break;
                      case EnumCadastroPages.dadosBancarios:
                        text = 'PRÓXIMO';
                        message = store.validarDadosBancarios();
                        if (message == null || message.isEmpty) {
                          onPressed = () {
                            store.setTitlesPage(EnumCadastroPages.fotos);
                          };
                        }
                        break;
                      case EnumCadastroPages.fotos:

                        if (state.fotoPage == EnumCadastroFoto.documentoVeiculo) {
                          text = 'PRÓXIMO';

                          message = store.validarDocumentoVeiculo();
                          onPressed = () async {
                            if (state.fotoPage == EnumCadastroFoto.documentoVeiculo) {
                              store.setFotoPage(EnumCadastroFoto.comprovanteEndereco);
                            }
                          };
                        }

                        if (state.fotoPage == EnumCadastroFoto.fotoCnh) {
                          text = 'PRÓXIMO';
                          message = store.validarFotoCNH();
                          onPressed = () async {
                            store.setFotoPage(EnumCadastroFoto.documentoVeiculo);
                          };
                        }

                        if (state.fotoPage == EnumCadastroFoto.comprovanteEndereco) {
                          text = 'PRÓXIMO';
                          message = store.validarComprovanteEndereco();
                          onPressed = () async {
                            if (state.fotoPage == EnumCadastroFoto.comprovanteEndereco) {
                              store.setFotoPage(EnumCadastroFoto.fotoPerfil);
                            }
                          };
                        }

                        if (state.fotoPage == EnumCadastroFoto.fotoPerfil) {
                          message = store.validarFotoPerfil();
                          text = 'CONCLUIR CADASTRO';
                          onPressed = () async {
                            String? mensagem;

                            mensagem = store.validarAll();

                            if (mensagem != null && mensagem.isNotEmpty) {
                              return await showAlertDialog(mensagem);
                            }
                            bool liberar = await store.confimarContrato();
                            if (liberar) {
                              Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const Home()));
                            }
                          };
                        }
                        break;
                    }

                    return ButtonLsCustom(
                      text: text ?? '',
                      message: message,
                      onPressed: onPressed,
                      colorBackground: colorBackground,
                      isLoading: state.buttonAceiteContratoLoading,

                      //disabled: true,
                    );
                  },
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  showAlertDialog(String mensagem) {
    return showDialog(
      context: context,
      builder:
          (ctx) => AlertDialog(
            title: const Text('Atenção'),
            content: Text(mensagem),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(ctx);
                },
                child:  Text('OK', style: TextStyle(color: ThemeColors.customOrange(context))),
              ),
            ],
          ),
    );
  }
}
