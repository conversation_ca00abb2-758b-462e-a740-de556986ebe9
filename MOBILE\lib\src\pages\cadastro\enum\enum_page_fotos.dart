enum EnumCadastroFoto {
  fotoCnh,
  documentoVeiculo,
  comprovanteEndereco,
  fotoPerfil,
}

extension EnumCadastroFotoExtension on EnumCadastroFoto {
  String get name {
    switch (this) {
      case EnumCadastroFoto.fotoCnh:
        return 'Documento CNH';
      case EnumCadastroFoto.documentoVeiculo:
        return 'Documento Veículo';
      case EnumCadastroFoto.comprovanteEndereco:
        return 'Comprovante Endereço';
      case EnumCadastroFoto.fotoPerfil:
        return 'Sua Foto';
    }
  }
}
