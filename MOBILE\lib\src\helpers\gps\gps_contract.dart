import '../../models_new/position_data_location.dart';
import 'gps_old.dart' as o;

abstract class GpsHelperContract {
  static final instance = o.GpsHelper.instance;
  // Platform.isIOS ? o.GpsHelper.instance : n.GpsHelper.instance;

  bool get iniciado;
  bool get serviceEnabled;
  bool get permissionGranted;
  PositionDataLocation? get currentPosition;

  bool last10Secs(PositionDataLocation? position);
  Future<PositionDataLocation?> updateAndGetLastPosition();
  Future<PositionDataLocation?> updateLoc({
    bool isInternet = false,
    int? timeoutSeconds,
  });
  Future<bool> enviarPosicaoAPI();
  Future<bool> checaGpsLigado();
  Future<PositionDataLocation> receberLocalizacao();
  Future infoDeviceDynamic();
}
