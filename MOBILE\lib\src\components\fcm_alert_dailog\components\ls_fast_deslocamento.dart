import 'package:flutter/material.dart';

import '../../../utils/theme_colors.dart' show ThemeColors;
import 'fcm_anim.dart';

class LsFastDeslocamento extends StatelessWidget {
  final String local;
  final String endereco;
  const LsFastDeslocamento(
      {super.key, required this.local, required this.endereco});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      child: SizedBox(
        height: 225,
        width: MediaQuery.of(context).size.width,
        child: Row(
          children: [
            icons(context),
            const SizedBox(
              width: 10,
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(top: 10, bottom: 13),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    const Text(
                      "Sua localização",
                      overflow: TextOverflow.visible,
                      maxLines: 2,
                      textAlign: TextAlign.start,
                      textScaler: TextScaler.linear(1),
                      style: TextStyle(
                        color: Colors.black,
                      ),
                    ),
                    Text(
                      endereco,
                      overflow: TextOverflow.visible,
                      maxLines: 2,
                      textAlign: TextAlign.start,
                      textScaler: const TextScaler.linear(1),
                      style: const TextStyle(
                        color: Colors.black,
                      ),
                    ),
                    Text(
                      local.toUpperCase(),
                      style: TextStyle(
                        fontSize: 20,
                        color: ThemeColors.customOrange(context),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget icons(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          height: 45,
          child: Image.asset(
            'assets/images/map_marker.png',
            height: 45,
            color: ThemeColors.customOrange(context),
          ),
        ),
        const FcmAnimTest(),
        SizedBox(
          height: 40,
          child: Image.asset(
            'assets/images/localization.png',
            height: 40,
          ),
        ),
        // ...diverVertical(),
        const FcmAnimTest(),
        Icon(
          Icons.circle_outlined,
          size: 40,
          color: ThemeColors.customOrange(context),
        )
      ],
    );
  }
}
