import 'package:flutter/material.dart';

import '../../../utils/theme_colors.dart';

class WidgetExtratoCarroseu extends StatelessWidget {
  final String title;
  final int text;
  const WidgetExtratoCarroseu(
      {super.key, required this.title, required this.text});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width * 0.33,
      child: Card(
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5.0),
        ),
        color: ThemeColors.customOrange(context),
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 10,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                text.toString(),
                style: const TextStyle(color: Colors.white, fontSize: 18.0),
              ),
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                ),
              ),
              const SizedBox(
                height: 5,
              )
            ],
          ),
        ),
      ),
    );
  }
}
