import 'package:latlong2/latlong.dart';
import 'package:octalog/src/helpers/web_connector.dart';
import 'package:map_fields/map_fields.dart';

abstract class LsMaps {
  static final LsMaps instance = LsMapsOpenStreet();

  Future<LsMapsRetorno> getRouteStartEnd(
    LatLng origin,
    LatLng destination,
  );
  Future<LsMapsRetorno> getRouteWaypoints(
    LatLng origin,
    LatLng? destination,
    List<LatLng> waypoints,
  );
}

class LsMapsOpenStreet implements LsMaps {
  @override
  Future<LsMapsRetorno> getRouteStartEnd(
    LatLng origin,
    LatLng destination,
  ) async {
    try {
      final body = {
        'start': {'lat': origin.latitude, 'lng': origin.longitude},
        'end': {'lat': destination.latitude, 'lng': destination.longitude},
      };
      final response = await WebConnector().post(
        '/maps/distance',
        body: body,
        setOfflineIfError: false,
      );
      return LsMapsRetorno.fromJson(response.data);
    } catch (e) {
      return LsMapsRetorno(distance: 0, duration: 0, geometry: '');
    }
  }

  @override
  Future<LsMapsRetorno> getRouteWaypoints(
    LatLng origin,
    LatLng? destination,
    List<LatLng> waypoints,
  ) async {
    try {
      final coords = [
        origin,
        ...waypoints,
        if (destination != null) destination,
      ].map((e) => {'lat': e.latitude, 'lng': e.longitude}).toList();
      final response = await WebConnector().post(
        '/maps/directions',
        body: coords,
        setOfflineIfError: false,
      );
      return LsMapsRetorno.fromJson(response.data);
    } catch (e) {
      return LsMapsRetorno(distance: 0, duration: 0, geometry: '');
    }
  }
}

class LsMapsRetorno {
  final double distance;
  final double duration;
  final String geometry;

  LsMapsRetorno({
    required this.distance,
    required this.duration,
    required this.geometry,
  });

  factory LsMapsRetorno.fromJson(
    Map<String, dynamic> map,
  ) {
    final f = MapFields.load(map);
    return LsMapsRetorno(
      distance: f.getDouble('distance', 0),
      duration: f.getDouble('duration', 0),
      geometry: f.getString('geometry', ''),
    );
  }
}
