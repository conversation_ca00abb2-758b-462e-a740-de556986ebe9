// ignore_for_file: unnecessary_null_comparison

import 'model/romaneio_model.dart';

class RomaneioState {
  final List<RomaneioModel> romaneios;
  final bool loading;
  final bool error;

  RomaneioState({
    required this.romaneios,
    required this.loading,
    required this.error,
  });

  List<RomaneioModel> get romaneiosOrdenados {
    return romaneios.where((element) => element.ordemExibicao != null).toList()
      ..sort((a, b) => a.ordemExibicao.compareTo(b.ordemExibicao));
  }

  RomaneioState copyWith({
    List<RomaneioModel>? romaneios,
    bool? loading,
    bool? error,
  }) {
    return RomaneioState(
      romaneios: romaneios ?? this.romaneios,
      loading: loading ?? this.loading,
      error: error ?? this.error,
    );
  }
}
