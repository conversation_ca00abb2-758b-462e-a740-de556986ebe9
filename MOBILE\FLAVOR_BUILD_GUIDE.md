# 🎯 Guia de Build com Flavors - Solução Completa

## ❌ **Problema Identificado**

O comando `flutter build appbundle --release --flavor connect --dart-define=FLAVOR=connect` não estava carregando os arquivos corretos do flavor em builds release.

## ✅ **Solução Implementada**

### 🔧 **1. Detecção Aprimorada de Flavor**

**Arquivo:** `lib/src/config/flavor_helper.dart`
- ✅ Detecção via `--dart-define=FLAVOR=connect`
- ✅ Fallback via Android BuildConfig
- ✅ Inicialização assíncrona para builds release

**Arquivo:** `android/app/src/main/kotlin/com/octalog/MainActivity.kt`
- ✅ MethodChannel para expor BuildConfig.FLAVOR_NAME
- ✅ Comunicação Flutter ↔ Android nativa

### 📱 **2. Scripts de Build Automatizados**

**Para Windows:** `scripts/build_aab.ps1`
```powershell
.\scripts\build_aab.ps1 connect release
```

**Para Linux/Mac:** `scripts/build_aab.sh`
```bash
./scripts/build_aab.sh connect release
```

---

## 🚀 **Como Usar**

### 📋 **Método 1: Scripts Automatizados (Recomendado)**

```bash
# Windows
.\scripts\build_aab.ps1 connect release

# Linux/Mac
chmod +x scripts/build_aab.sh
./scripts/build_aab.sh connect release
```

### 📋 **Método 2: Comando Manual**

```bash
flutter build appbundle \
    --release \
    --flavor connect \
    --dart-define=FLAVOR=connect \
    --target-platform android-arm,android-arm64,android-x64
```

### 📋 **Método 3: Comando Completo com Limpeza**

```bash
# Limpar build anterior
flutter clean

# Obter dependências
flutter pub get

# Build AAB
flutter build appbundle \
    --release \
    --flavor connect \
    --dart-define=FLAVOR=connect \
    --target-platform android-arm,android-arm64,android-x64
```

---

## 🎯 **Flavors Disponíveis**

| Flavor | Application ID | App Name | Assets Path |
|--------|---------------|----------|-------------|
| `octalog` | com.octalog | Octalog | assets/images/octalog/ |
| `arcargo` | com.octalog.arcargo | ArCargo | assets/images/arcargo/ |
| `connect` | com.octalog.connect | Connect | assets/images/connect/ |
| `rondolog` | com.octalog.rondolog | RondoLog | assets/images/rondolog/ |

---

## 🔍 **Verificação de Funcionamento**

### ✅ **1. Verificar Flavor Detectado**

Adicione este código temporário no `main.dart`:

```dart
void main() {
  FlavorHelper.initializeFlavor();
  print('🎯 Flavor atual: ${FlavorConfig.instance.flavor}');
  print('📱 App Name: ${FlavorConfig.instance.appName}');
  runApp(MyApp());
}
```

### ✅ **2. Verificar Assets Carregados**

Use o `FlavorImage` e verifique se as imagens corretas são carregadas:

```dart
FlavorImage(assetName: 'logo.png') // Deve carregar de assets/images/connect/
```

### ✅ **3. Verificar Application ID**

No build AAB, verifique se o `applicationId` está correto:
- Connect: `com.octalog.connect`
- ArCargo: `com.octalog.arcargo`
- etc.

---

## 🐛 **Troubleshooting**

### ❌ **Problema: Assets não carregam**

**Solução:**
1. Verifique se existe a pasta `assets/images/connect/`
2. Confirme que as imagens estão na pasta correta
3. Execute `flutter clean && flutter pub get`

### ❌ **Problema: Flavor não detectado**

**Solução:**
1. Use sempre `--dart-define=FLAVOR=connect`
2. Verifique se o flavor existe no `build.gradle.kts`
3. Confirme que o `FLAVOR_NAME` está configurado

### ❌ **Problema: Build falha**

**Solução:**
1. Execute `flutter clean`
2. Verifique se todas as dependências estão atualizadas
3. Use os scripts automatizados

---

## 📊 **Arquivos Modificados**

### 🔧 **Flutter (Dart)**
- ✅ `lib/src/config/flavor_helper.dart` - Detecção aprimorada
- ✅ `lib/src/components/flavor_image/flavor_image.dart` - Sistema de assets

### 🔧 **Android (Kotlin)**
- ✅ `android/app/src/main/kotlin/com/octalog/MainActivity.kt` - MethodChannel
- ✅ `android/app/build.gradle.kts` - Configuração de flavors

### 🔧 **Scripts**
- ✅ `scripts/build_aab.ps1` - Script Windows
- ✅ `scripts/build_aab.sh` - Script Linux/Mac

---

## 🎉 **Resultado Final**

✅ **Builds release funcionam corretamente**
✅ **Assets carregam do flavor correto**
✅ **Application ID específico por flavor**
✅ **Scripts automatizados para facilitar builds**
✅ **Detecção robusta de flavor**

### 📱 **Comando Final Recomendado:**

```bash
# Windows
.\scripts\build_aab.ps1 connect release

# Linux/Mac  
./scripts/build_aab.sh connect release
```

O AAB será gerado em:
`build/app/outputs/bundle/connectRelease/app-connect-release.aab`
