import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/components/loading_custom/loading_custom.dart';
import 'package:octalog/src/pages/cadastro/cadastro_store.dart';

import '../../../components/text_field/text_field_custom.dart';
import '../../../models/estados.dart';
import '../../../utils/theme_colors.dart';
import '../cadastro_state.dart';

class EnderecoPage extends StatefulWidget {
  final CadastroStore store;
  const EnderecoPage({super.key, required this.store});

  @override
  State<EnderecoPage> createState() => _EnderecoPageState();
}

class _EnderecoPageState extends State<EnderecoPage> {
  final TextEditingController cep = TextEditingController();
  final TextEditingController endereco = TextEditingController();
  final TextEditingController bairro = TextEditingController();
  final TextEditingController cidade = TextEditingController();
  final TextEditingController numero = TextEditingController();
  String estado = '';

  @override
  void initState() {
    init();
    super.initState();
  }

  init() {
    cep.text = widget.store.state.value.contratoModel?.cep ?? '';

    endereco.text = widget.store.state.value.contratoModel?.endereco ?? '';
    bairro.text = widget.store.state.value.contratoModel?.bairro ?? '';
    cidade.text = widget.store.state.value.contratoModel?.cidade ?? '';
    numero.text =
        widget.store.state.value.contratoModel?.numero.toString() ?? '';
    estado = widget.store.state.value.contratoModel?.uf ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<CadastroState>(
      valueListenable: widget.store.state,
      builder: (BuildContext context, state, _) {
        final store = widget.store;
        return state.isLoadingBuscaCep
            ? const Center(
                child: LoadingLs(),
              )
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextFieldLsCustom(
                    labelText: 'CEP',
                    keyboardType: TextInputType.number,
                    isError: false,
                    textInputAction: TextInputAction.next,
                    controller: cep,
                    maxCaracteres: 8,
                    onChanged: (value) async {
                      cep.text = cep.text.replaceAll('-', '');
                      store.setContratoModelParte(
                          cep: value.replaceAll('-', ''));
                      if (value.length == 8) {
                        await store.buscarCep();
                        await Future.delayed(
                          const Duration(milliseconds: 200),
                          () async {
                            init();
                          },
                        );
                      }
                    },
                  ),
                  _space(),
                  TextFieldLsCustom(
                    labelText: 'Endereço',
                    isError: false,
                    textInputAction: TextInputAction.next,
                    controller: endereco,
                    maxCaracteres: 255,
                    onChanged: (value) {
                      store.setContratoModelParte(
                        endereco: value,
                      );
                    },
                  ),
                  _space(),
                  TextFieldLsCustom(
                    labelText: 'Bairro',
                    isError: false,
                    textInputAction: TextInputAction.next,
                    controller: bairro,
                    maxCaracteres: 100,
                    onChanged: (value) {
                      store.setContratoModelParte(
                        bairro: value,
                      );
                    },
                  ),
                  _space(),
                  TextFieldLsCustom(
                    labelText: 'Cidade',
                    isError: false,
                    textInputAction: TextInputAction.next,
                    controller: cidade,
                    maxCaracteres: 50,
                    onChanged: (value) {
                      store.setContratoModelParte(
                        cidade: value,
                      );
                    },
                  ),
                  _space(),
                  SizedBox(
                    width: MediaQuery.of(context).size.width * 0.5,
                    child: TextFieldLsCustom(
                      labelText: 'Número',
                      isError: false,
                      textInputAction: TextInputAction.next,
                      controller: numero,
                      maxCaracteres: 50,
                      onChanged: (value) {
                        store.setContratoModelParte(
                          numero: value,
                        );
                      },
                    ),
                  ),
                  _space(),
                  SizedBox(
                    width: MediaQuery.of(context).size.width * 0.5,
                    child: DropdownButtonFormField<Estado>(
                      decoration: InputDecoration(
                        labelText: 'UF',
                        labelStyle: GoogleFonts.roboto(
                          fontSize: 16,
                          color: ThemeColors.customGrey(context),
                        ),
                      ),
                      icon: const Icon(
                        Icons.keyboard_arrow_down_outlined,
                        size: 20,
                      ),
                      value: estados.firstWhere(
                          (element) => element.uf == state.contratoModel!.uf,
                          orElse: () => estados[0]),
                      items: estados.map((Estado value) {
                        return DropdownMenuItem<Estado>(
                          value: value,
                          child: Text(
                            value.uf,
                            style: GoogleFonts.roboto(
                              fontSize: 16,
                              color: ThemeColors.customBlack(context),
                            ),
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        store.setContratoModelParte(
                          uf: value?.uf ?? '',
                        );
                      },
                    ),
                  ),
                  SizedBox(
                    height: MediaQuery.of(context).size.height * 0.3,
                  )
                ],
              );
      },
    );
  }

  Widget _space() {
    return const SizedBox(
      height: 10,
    );
  }
}
