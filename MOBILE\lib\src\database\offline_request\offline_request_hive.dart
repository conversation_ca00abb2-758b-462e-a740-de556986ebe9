import 'dart:convert';
import 'dart:typed_data';

import 'package:map_fields/map_fields.dart';
import 'package:uuid/uuid.dart';

import 'offline_request_drift_model.dart';

class OfflineRequest {
  final String uuid;
  final String endpoint;
  final String method;
  final Map<String, dynamic>? body;
  final Map<String, String>? headers;
  final String? fileName;
  final Uint8List? fileBytes;
  final int idAtividade;
  final bool enviado;
  final String? fileLink;
  final DateTime? dataHora;
  final bool isFcmDio;

  OfflineRequest({
    required this.uuid,
    required this.endpoint,
    required this.method,
    required this.body,
    required this.headers,
    required this.fileName,
    required this.fileBytes,
    required this.idAtividade,
    required this.enviado,
    required this.fileLink,
    this.dataHora,
    required this.isFcmDio,
  });

  factory OfflineRequest.novo({
    required String endpoint,
    required String method,
    required Map<String, dynamic>? body,
    required Map<String, String>? headers,
    required String? fileName,
    required Uint8List? fileBytes,
    required int idAtividade,
    required bool enviado,
    required String? fileLink,
    DateTime? dataHora,
    bool? isFcmDio,
  }) {
    final uuid = const Uuid().v4();
    return OfflineRequest(
      uuid: uuid,
      endpoint: endpoint,
      method: method,
      body: body,
      headers: headers,
      fileName: fileName,
      fileBytes: fileBytes,
      idAtividade: idAtividade,
      enviado: enviado,
      fileLink: fileLink ?? '',
      dataHora: dataHora ?? DateTime.now(),
      isFcmDio: isFcmDio ?? false,
    );
  }

  bool get isPhoto => fileBytes != null;

  OfflineRequest copyWith({
    bool? enviado,
    String? fileLink,
    Map<String, dynamic>? body,
  }) {
    return OfflineRequest(
      uuid: uuid,
      endpoint: endpoint,
      method: method,
      body: body ?? this.body,
      headers: headers,
      fileName: fileName,
      fileBytes: fileBytes,
      idAtividade: idAtividade,
      enviado: enviado ?? this.enviado,
      fileLink: fileLink ?? this.fileLink,
      dataHora: dataHora,
      isFcmDio: isFcmDio,
    );
  }

  OfflineRequestDriftData toDrift() {
    return OfflineRequestDriftData(
      uuid: uuid,
      endpoint: endpoint,
      method: method,
      body: json.encode(body ?? '{}'),
      headers: json.encode(headers ?? '{}'),
      fileName: fileName,
      fileBytes: fileBytes,
      idAtividade: idAtividade,
      enviado: enviado,
      fileLink: fileLink,
      dataHora: dataHora,
      isFcmDio: isFcmDio,
    );
  }

  factory OfflineRequest.fromDrift(OfflineRequestDriftData map) {
    return OfflineRequest(
      uuid: map.uuid,
      endpoint: map.endpoint,
      method: map.method,
      body: Map<String, dynamic>.from(json.decode(map.body ?? '{}')),
      headers: Map<String, String>.from(json.decode(map.headers ?? '{}')),
      fileName: map.fileName,
      fileBytes: map.fileBytes,
      idAtividade: map.idAtividade,
      enviado: map.enviado,
      fileLink: map.fileLink,
      dataHora: map.dataHora,
      isFcmDio: map.isFcmDio ?? false,
    );
  }

  factory OfflineRequest.fromHiveMap(Map<String, dynamic> map) {
    final m = MapFields.load(map);
    return OfflineRequest(
      uuid: m.getString('uuid', ''),
      endpoint: m.getString('endpoint', ''),
      method: m.getString('method', ''),
      body: Map<String, dynamic>.from(map['body'] ?? {}),
      headers: Map<String, String>.from(map['headers'] ?? {}),
      fileName: m.getStringNullable('fileName'),
      fileBytes: m.getListNullable<int>('fileBytes') == null
          ? null
          : Uint8List.fromList(m.getListNullable<int>('fileBytes')!),
      idAtividade: m.getInt('idAtividade', 0),
      enviado: m.getBool('enviado', false),
      fileLink: m.getStringNullable('fileLink'),
      dataHora: m.getDateTimeNullable('dataHora'),
      isFcmDio: m.getBool('isFcmDio', false),
    );
  }

  Map<String, dynamic> toHiveMap() {
    return {
      'uuid': uuid,
      'endpoint': endpoint,
      'method': method,
      'body': body,
      'headers': headers,
      'fileName': fileName,
      'fileBytes': fileBytes?.toList(),
      'idAtividade': idAtividade,
      'enviado': enviado,
      'fileLink': fileLink,
      'dataHora': dataHora?.toIso8601String(),
      'isFcmDio': isFcmDio,
    };
  }
}
