import 'dart:developer';

import 'package:asuka/asuka.dart' as asuka;
import 'package:octalog/src/helpers/login/login.dart';
import 'package:octalog/src/pages/sac_page/subtela/chamada/chamada_widget.dart';
import 'package:map_fields/map_fields.dart';
import 'package:signalr_core/signalr_core.dart';

class HubHelper {
  HubHelper._(this.url);
  static HubHelper? _instance;
  static HubHelper getInstance(String url) {
    _instance ??= HubHelper._(url);
    _instance!._start();
    return _instance!;
  }

  bool _isConnected = false;
  final String url;
  late final HubConnection _hub = HubConnectionBuilder()
      .withUrl("$url/hub-call")
      .withAutomaticReconnect([0, 2000, 10000, 30000, 30000]).build()
    ..onclose((error) {
      _isConnected = false;
      log("Conexão fechada: $error");
    })
    ..onreconnecting((error) {
      _isConnected = false;
      log("Reconectando: $error");
    })
    ..onreconnected((connectionId) {
      _isConnected = true;
      log("Reconectado: $connectionId");
      _conectar();
    });

  HubConnection get hub => _hub;

  Future<void> _start() async {
    try {
      await _hub.start();
      _isConnected = true;
      _conectar();
      _receberOfertaChamada();
    } catch (e) {
      log("Erro ao iniciar a conexão: $e");
      _scheduleReconnect();
    }
  }

  void _conectar() {
    try {
      if (_isConnected) {
        _hub.invoke('Conectar', args: [
          {
            'id': Login.instance.usuarioLogado?.usuario ?? '',
            'usuario': Login.instance.usuarioLogado?.nomeCompleto ?? '',
            'tipo': 'Agente',
          }
        ]);
      }
    } catch (e) {
      log("Erro ao conectar: $e");
    }
  }

  void _receberOfertaChamada() {
    _hub.on("ReceberOfertaChamada", (arguments) async {
      if (arguments == null || arguments.isEmpty) return;
      UsuarioHub usuarioHub = UsuarioHub.fromMap(arguments[0]);
      // if (globalNavigatorKey.currentContext != null) {
      //   await showDialog(
      //     context: globalNavigatorKey.currentContext!,
      //     builder: (context) {
      //       return ChamadaPage(
      //         remoteOffer: arguments[1],
      //         usuarioHub: usuarioHub,
      //       );
      //     },
      //   );
      // }

      await asuka.Asuka.showDialog(
        builder: (context) {
          return ChamadaPage(
            remoteOffer: arguments[1],
            usuarioHub: usuarioHub,
          );
        },
      );
    });
  }

  void on(String methodName, void Function(List<dynamic>?) callback) {
    _hub.on(methodName, callback);
  }

  Future<void> invoke(String methodName, {List<dynamic>? args}) async {
    try {
      await _hub.invoke(methodName, args: args);
    } catch (e) {
      log("Erro ao invocar $methodName: $e");
    }
  }

  void _scheduleReconnect() {
    Future.delayed(const Duration(seconds: 5), () {
      if (!_isConnected) {
        _start();
      }
    });
  }
}

class UsuarioHub {
  final String id;
  final String usuario;
  final String connectionId;
  final bool emChamada;
  UsuarioHub({
    required this.id,
    required this.usuario,
    required this.connectionId,
    required this.emChamada,
  });

  factory UsuarioHub.fromMap(Map<String, dynamic> map) {
    final mapFields = MapFields.load(map);
    return UsuarioHub(
      id: mapFields.getString('id', ''),
      usuario: mapFields.getString('usuario', ''),
      connectionId: mapFields.getString('connectionId', ''),
      emChamada: mapFields.getBool('emChamada', false),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'usuario': usuario,
      'connectionId': connectionId,
      'emChamada': emChamada,
    };
  }
}
