kotlin.Enum1io.flutter.embedding.engine.plugins.FlutterPlugin:io.flutter.embedding.engine.plugins.activity.ActivityAware8io.flutter.plugin.common.MethodChannel.MethodCallHandler3io.flutter.plugin.common.EventChannel.StreamHandlerHio.flutter.plugin.common.PluginRegistry.RequestPermissionsResultListener!android.content.BroadcastReceiver"androidx.core.app.JobIntentServiceBandroid.content.SharedPreferences.OnSharedPreferenceChangeListener                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         