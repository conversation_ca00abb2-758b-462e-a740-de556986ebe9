import 'dart:io';

import 'package:asuka/asuka.dart' as asuka;
// import 'package:battery_info/battery_info_plugin.dart';
// import 'package:battery_info/model/iso_battery_info.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:octalog/src/components/custom_scaffold/custom_scaffold.dart';
import 'package:octalog/src/database/config_blob/config_blob_model.dart';
import 'package:octalog/src/database/offline_request/offline_request_database.dart';
import 'package:octalog/src/helpers/web_connector.dart';
import 'package:octalog/src/pages/entrega_newpages/componentes/elevated_ls_button.dart';
import 'package:octalog/src/pages/home/<USER>/aviso_de_gps_fake.dart';
import 'package:octalog/src/utils/extesion.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:system_info_plus/system_info_plus.dart';

import '../../database/config_blob/config_database.dart';
import '../../database/log_database/log_database.dart';
import '../../models/info_device_dynmic.dart';
import '../../models_new/position_data_location.dart';
import '../../pages/home/<USER>';
import '../login/login.dart';
import 'gps_contract.dart';

class GpsHelper implements GpsHelperContract {
  GpsHelper._() {
    _init();
  }
  static final GpsHelper instance = GpsHelper._();
  @override
  bool iniciado = false;
  @override
  bool serviceEnabled = false;
  @override
  bool permissionGranted = false;

  PositionDataLocation? _currentPosition;

  @override
  PositionDataLocation? get currentPosition => _currentPosition;

  @override
  bool last10Secs(PositionDataLocation? position) {
    final timestamp = position?.timestamp ?? DateTime(1900);
    final difference = DateTime.now().difference(timestamp).inSeconds.abs();
    return difference < 10;
  }

  @override
  Future<PositionDataLocation?> updateAndGetLastPosition() async {
    String? token = Login.instance.usuarioLogado?.token;
    final config = await ConfigDatabase.instance.getConfig();
    final listAppsParaBloquear = config.appsFakeGps;
    bool isMock = _currentPosition?.isMock ?? false;

    if (token != null && isMock && listAppsParaBloquear.isNotEmpty) {
      List<AppsFakeGps>? appsInstalados = await getInstalledApps();

      final List<AppsFakeGps> listaDeAppsBloqueado = appsInstalados!
          .where((element) => listAppsParaBloquear
              .any((e) => e.package == element.package && e.bloquear))
          .toList();

      final jsonbody = appsInstalados.map((e) => e.toJson()).toList();

      try {
        await WebConnector().postSimples(
          '/atividades/fakegps-detectado',
          body: jsonbody,
        );
      } catch (_) {}

      await asuka.Asuka.showDialog(
        barrierDismissible: false,
        builder: (context) => AvisoDeGpsFake(
          appsFakeGps: listaDeAppsBloqueado,
        ),
      );
    }

    return updateLoc(timeoutSeconds: 8).then(
      (value) => last10Secs(value) ? value : null,
    );
  }

  @override
  Future<PositionDataLocation?> updateLoc({
    bool isInternet = false,
    int? timeoutSeconds,
  }) async {
    if (timeoutSeconds != null) {
      try {
        final position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.bestForNavigation,
          forceAndroidLocationManager: isInternet,
          timeLimit: Duration(seconds: timeoutSeconds),
        );
        final location = PositionDataLocation(
          latitude: position.latitude,
          longitude: position.longitude,
          timestamp: DateTime.now(),
          isMock: position.isMocked,
        );
        _currentPosition = location;
        return location;
      } catch (e) {
        return null;
      }
    }
    final position = await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.bestForNavigation,
      forceAndroidLocationManager: isInternet,
    );
    final location = PositionDataLocation(
      latitude: position.latitude,
      longitude: position.longitude,
      timestamp: DateTime.now(),
      isMock: position.isMocked,
    );
    _currentPosition = location;
    return location;
  }

  void _init() async {
    if (!iniciado) {
      iniciado = true;
    }

    serviceEnabled = await checaGpsLigado();
    if (!serviceEnabled) {
      asuka.Asuka.showDialog(
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: const Text('Ligar o GPS'),
          content: const Text(
              'É necessário para o funcionamento deste aplicativo permitir a sua localização!\n\nDepois que permitir clique em "Ok" e feche o aplicaito e tente novamente'),
          actions: [
            TextButton(
              onPressed: () async {
                await Geolocator.openLocationSettings().then((value) {
                  Navigator.of(context).pop();
                });
              },
              child: const Text(
                'Ok',
                style: TextStyle(color: Colors.orange),
              ),
            )
          ],
        ),
      );
    } else {
      permissionGranted = await checaPermissao();
      if (!permissionGranted) {
        if (Platform.isIOS) return;
        asuka.Asuka.showDialog(
          barrierDismissible: false,
          builder: (ctx) => CustomScaffold(
            child: Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 30),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      'Serviço de localização não autorizado! \nPor favor, reinicie o aplicativo e autorize a usar o serviço de localização.',
                    ),
                    const SizedBox(height: 20),
                    ElevatedLsButton(
                      text: 'Abrir configurações',
                      isLoading: false,
                      onPressed: () async {
                        openAppSettings();
                        exit(0);
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
        // asuka.AsukaSnackbar.info(
        //   'Serviço de localização não autorizado! Por favor, autorize o aplicativo a usar o serviço de localização.',
        // ).show();
      } else {
        while (true) {
          await enviarPosicaoAPI();
          await Future.delayed(const Duration(minutes: 1));
        }
      }
    }
  }

  @override
  Future<bool> enviarPosicaoAPI() async {
    try {
      String? token = Login.instance.usuarioLogado?.token;
      await updateLoc();
      DeviceInfoDynamics infos = await infoDeviceDynamic();
      bool error = false;

      if (_currentPosition != null) {
        try {
          if (token != null) {
            final disponivel = HomeController.instance.state.value.isOnline;
            final response = await WebConnector().put(
              '/agente/posicao',
              headers: {
                'token': token,
              },
              body: {
                // "pedidos": pedidos,
                "disponivel": disponivel,
                "latitude": _currentPosition!.latitude,
                "longitude": _currentPosition!.longitude,
                "dataEvento":
                    DateTime.now().dataHoraServidorFomart.toIso8601String(),
                "sincronismoRestante": await OfflineRequestDatabase.instance
                    .getQtdEnventosRestantes(),
                "conexao": infos.conexao,
                "bateriaStatus": infos.bateriaStatus,
                "bateriaSaude": infos.bateriaSaude,
                "memoriaTotal": infos.memoriaTotal,
              },
            );
            if (response.statusCode == 200) {
              debugPrint('Posição enviada com sucesso!');
            } else {
              error = true;
              debugPrint('Erro ao enviar posição!');
            }
          }
        } catch (e) {
          error = true;
          debugPrint(e.toString());
        }
      } else {
        error = true;
      }
      return error;
    } on DioException catch (e) {
      await LogDatabase.instance.logError(
        'Envio do GPS',
        'Erro ao enviar a posição do GPS',
        '',
        {
          'erro': e.toString(),
        },
      );

      return true;
    }
  }

  Future<List<AppsFakeGps>>? getInstalledApps() async {
    if (!Platform.isAndroid) return [];
    const MethodChannel channel = MethodChannel('com.example.dadosMoveis');
    final List<dynamic> result = await channel.invokeMethod('getAllAppsInfo');
    final List<Map<String, String>> apps = result.map((dynamic item) {
      return Map<String, String>.from(item);
    }).toList();

    final List<AppsFakeGps> appsFakeGps = apps.map((e) {
      return AppsFakeGps.fromJson(e);
    }).toList();

    return appsFakeGps;
  }

  Future<bool> checaPermissao([bool request = false]) async {
    var permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.unableToDetermine ||
        permission == LocationPermission.denied) {
      if (request) return false;
      await pedirPermissao();
      return await checaPermissao(true);
    } else if (permission == LocationPermission.deniedForever) {
      return false;
    } else if (permission == LocationPermission.unableToDetermine) {
      return false;
    } else {
      return true;
    }
  }

  @override
  Future<bool> checaGpsLigado() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  Future<void> pedirPermissao() async {
    if (Platform.isIOS) {
      await Geolocator.requestPermission();
    } else {
      await Permission.location.request();
    }
  }

  @override
  Future<PositionDataLocation> receberLocalizacao() async {
    while (_currentPosition == null) {
      await Future.delayed(const Duration(milliseconds: 100));
    }
    return _currentPosition!;
  }

  @override
  Future infoDeviceDynamic() async {
    int deviceMemory = -1;
    final connectivityResult = await Connectivity().checkConnectivity();

    try {
      deviceMemory = await SystemInfoPlus.physicalMemory ?? -1;
    } on PlatformException {
      deviceMemory = -1;
      deviceMemory = deviceMemory;
    }

    if (Platform.isAndroid) {
      //final bateria = await BatteryInfoPlugin().androidBatteryInfo;
      return DeviceInfoDynamics(
        conexao: connectivityResult.last.name,
        bateriaStatus: 0, //bateria?.batteryLevel ?? 0,
        bateriaSaude: '', //bateria?.health ?? '',
        memoriaTotal: deviceMemory,
      );
    }
    if (Platform.isIOS) {
 //     IosBatteryInfo? bateria;
      try {
   //     bateria = await BatteryInfoPlugin().iosBatteryInfo;
      } catch (_) {}
      return DeviceInfoDynamics(
        conexao: connectivityResult.last.name,
        bateriaStatus: 0,//bateria?.batteryLevel ?? 0,
        bateriaSaude: '', // bateria?.chargingStatus.toString() ?? '',
        memoriaTotal: deviceMemory,
      );
    }
  }
}
