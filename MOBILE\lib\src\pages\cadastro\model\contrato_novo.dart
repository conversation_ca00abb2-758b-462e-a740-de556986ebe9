import 'package:map_fields/map_fields.dart';

class ContratoNovo {
  final int idTransportadora;
  final String transportadora;
  final DateTime? dataConfirmouCadastro;
  final String textoAceite;
  final String? cnpj;
  final String? url;

  ContratoNovo({
    required this.idTransportadora,
    required this.transportadora,
    required this.dataConfirmouCadastro,
    required this.textoAceite,
    required this.cnpj,
    required this.url,
  });

  bool get pendente => dataConfirmouCadastro == null;

  factory ContratoNovo.fromJson(Map<String, dynamic> json) {
    final MapFields mapFields = MapFields.load(json);

    return ContratoNovo(
      idTransportadora: mapFields.getInt('IDTransportadora', 0),
      transportadora: mapFields.getString('Transportadora', ''),
      dataConfirmouCadastro: mapFields.getDateTimeNullable(
        'DataConfirmouCadastro',
      ),
      textoAceite: mapFields.getString('TextoAceite', ''),
      cnpj: mapFields.getString('CNPJ', ''),
      url: mapFields.getString('URL', ''),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'IDTransportadora': idTransportadora,
      'Transportadora': transportadora,
      'DataConfirmouCadastro': dataConfirmouCadastro,
      'TextoAceite': textoAceite,
      'CNPJ': cnpj,
      'URL': url,
    };
  }

  static List<ContratoNovo> fromJsonList(data) {
    final List result = data.map((e) => ContratoNovo.fromJson(e)).toList();
    return result.cast<ContratoNovo>();
  }
}
