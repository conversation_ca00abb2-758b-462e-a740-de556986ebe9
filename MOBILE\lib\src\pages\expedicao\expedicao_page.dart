import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/material.dart';
import 'package:octalog/src/components/custom_scaffold/custom_scaffold.dart';
import 'package:octalog/src/pages/expedicao/expedicao_state.dart';
import 'package:octalog/src/pages/expedicao/expedicao_store.dart';
import 'package:octalog/src/pages/expedicao/widget/widget_carousel.dart';
import 'package:octalog/src/pages/expedicao/widget/widget_expedicao_card.dart';
import 'package:octalog/src/utils/theme_colors.dart';
//import 'package:octalog/src/utils/theme_colors.dart';
import 'package:octalog/src/utils/extesion.dart';

import '../../components/loading_ls/loading_ls.dart';
import '../../helpers/login/login.dart';

class ExpedicaoPage extends StatefulWidget {
  const ExpedicaoPage({super.key});

  @override
  State<ExpedicaoPage> createState() => _ExpedicaoPageState();
}

class _ExpedicaoPageState extends State<ExpedicaoPage> {
  final ExpedicaoStore store = ExpedicaoStore();
  DateTimeRange? selectedDateRange;
  @override
  void initState() {
    store.setExpedicoes();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<ExpedicaoState>(
      valueListenable: store.state,
      builder: (context, state, child) {
        return CustomScaffold(
          onPop: () {
            Navigator.of(context).pop();
          },
          title: 'Extrato de Entregas',
          cameraTela: store.setExpedicoes,
          iconsCamera: Icons.refresh,
          isColorIcon: false,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(
                  left: 10,
                  top: 10,
                ),
                child: Row(
                  children: [
                    const SizedBox(
                      width: 8,
                    ),
                    Expanded(
                      flex: 2,
                      child: GestureDetector(
                        onTap: () async {
                          await showDialog(
                            context: context,
                            builder: (context) {
                              return Dialog(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                backgroundColor: Colors.transparent,
                                clipBehavior: Clip.antiAlias,
                                child: Material(
                                  color: Colors.white,
                                  child: SizedBox(
                                    width: MediaQuery.of(context).size.width,
                                    height: 400,
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        CalendarDatePicker2WithActionButtons(
                                          onCancelTapped: () {
                                            Navigator.of(context).pop();
                                          },
                                          onValueChanged: (value) {
                                            store.setDataInicioFimBusca(
                                                value[0] ?? DateTime.now(),
                                                value[1] ?? DateTime.now());
                                            Navigator.of(context).pop();
                                          },
                                          value: [
                                            state.dataInicio,
                                            state.dataFim,
                                          ],
                                          config:
                                              CalendarDatePicker2WithActionButtonsConfig(
                                            yearTextStyle:  TextStyle(
                                              color: ThemeColors.customOrange(context),
                                              fontSize: 18,
                                            ),
                                            okButton:  Text(
                                              'Ok',
                                              style: TextStyle(
                                                color:
                                                    ThemeColors.customOrange(context),
                                                fontSize: 18,
                                              ),
                                            ),
                                            cancelButton:  Text(
                                              'Cancelar',
                                              style: TextStyle(
                                                color:
                                                    ThemeColors.customOrange(context),
                                                fontSize: 18,
                                              ),
                                            ),
                                            buttonPadding:
                                                const EdgeInsets.symmetric(
                                              horizontal: 36.0,
                                            ),
                                            controlsTextStyle:  TextStyle(
                                              color: ThemeColors.customOrange(context),
                                              fontSize: 18,
                                            ),
                                            dayTextStyle: const TextStyle(
                                              // color: ThemeColors.customOrange(context),
                                              fontSize: 17,
                                            ),
                                            selectedYearTextStyle:
                                                 TextStyle(
                                              color: ThemeColors.customWhite(context),
                                              fontSize: 18,
                                            ),
                                            dayBorderRadius:
                                                const BorderRadius.all(
                                              Radius.circular(5),
                                            ),
                                            selectedDayHighlightColor:
                                                ThemeColors.customOrange(context)
                                                    .withOpacity(0.9),
                                            calendarViewMode:
                                                CalendarDatePicker2Mode.day,
                                            firstDate: DateTime.now().subtract(
                                                const Duration(days: 365)),
                                            lastDate: DateTime.now(),
                                            calendarType:
                                                CalendarDatePicker2Type.range,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            },
                          );
                        },
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              " Nome: ${Login.instance.usuarioLogado?.nomeCompleto ?? ''}",
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                              style:  TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 20,
                                  color: ThemeColors.customOrange(context)),
                            ),
                            Card(
                              color: Colors.white,
                              elevation: 1,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(5),
                              ),
                              child: Container(
                                decoration: const BoxDecoration(
                                  color: Color.fromARGB(197, 243, 243, 243),
                                ),
                                padding: const EdgeInsets.only(
                                  left: 3,
                                  right: 10,
                                  top: 5,
                                  bottom: 5,
                                ),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                     SizedBox(
                                      width: 10,
                                      child: Icon(
                                        Icons.calendar_today_outlined,
                                        size: 18,
                                        color: ThemeColors.customOrange(context),
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 10,
                                    ),
                                    Text(
                                      "${state.dataInicio.dataPtBr} - ${state.dataFim.dataPtBr}",
                                      style: const TextStyle(
                                        fontSize: 17,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 68,
                width: MediaQuery.of(context).size.width,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: [
                    WidgetCarouselExpedicao(
                      label: 'Entregas',
                      value: state.expedicaoPageModel.entregas.toString(),
                    ),
                    WidgetCarouselExpedicao(
                      label: 'Negativas',
                      value: state.expedicaoPageModel.negativas.toString(),
                    ),
                    WidgetCarouselExpedicao(
                      label: 'Dias Trabalhados',
                      value:
                          state.expedicaoPageModel.diasTrabalhados.toString(),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Padding(
                padding: const EdgeInsets.only(
                  left: 10,
                  top: 10,
                  bottom: 10,
                ),
                child: Text(
                  'Entregas por dia',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    color: ThemeColors.customGrey(context).withOpacity(0.8),
                  ),
                ),
              ),
              Expanded(
                child: Builder(
                  builder: (context) {
                    if (state.loading) {
                      return const Center(child: LoadingLs());
                    }
                    if (state.expedicaoPageModel.entregasLista.isEmpty) {
                      return Center(
                        child: Text(
                          'Nenhuma expedição encontrada',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                            color: ThemeColors.customGrey(context).withOpacity(0.8),
                          ),
                        ),
                      );
                    }
                    return ListView.builder(
                      shrinkWrap: true,
                      itemCount: state.expedicaoPageModel.entregasLista.length,
                      itemBuilder: (context, index) {
                        final expedicao =
                            state.expedicaoPageModel.entregasLista[index];
                        return ExpedicaoCard(
                          expedicao: expedicao,
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
