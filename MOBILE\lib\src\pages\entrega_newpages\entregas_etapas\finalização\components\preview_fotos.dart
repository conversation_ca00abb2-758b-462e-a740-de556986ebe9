import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:image_picker/image_picker.dart';
import 'package:octalog/src/components/loading_ls/loading_ls.dart';

import '../../../../../components/buttom_ls/button_ls_custom.dart';
import '../../../../../utils/colors-dart';

class PreviewImageSemRosto extends StatefulWidget {
  final XFile imageFile;

  const PreviewImageSemRosto({super.key, required this.imageFile});

  @override
  State<PreviewImageSemRosto> createState() => _PreviewImageSemRostoState();
}

class _PreviewImageSemRostoState extends State<PreviewImageSemRosto> {
  String? aviso;
  bool loadingTela = true;
  bool liberar = false;
  init() async {
    final inputImage = InputImage.fromFilePath(widget.imageFile.path);

    final options = FaceDetectorOptions(
      enableClassification: true,
      enableLandmarks: true,
      enableContours: false,
      enableTracking: false,
      performanceMode: FaceDetectorMode.accurate,
    );
    final faceDetector = FaceDetector(options: options);
    final List<Face> faces = await faceDetector.processImage(inputImage);
    faceDetector.close();
    log('faces: ${faces.length}');
    if (faces.isNotEmpty) {
      aviso = 'Impossivel prosseguir. \nPessoa indentificada.';
    } else {
      liberar = true;
    }

    loadingTela = false;

    setState(() {});
  }

  @override
  void initState() {
    init();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: loadingTela
          ? const Center(child: LoadingLs())
          : Stack(
              children: [
                Container(
                  color: Colors.black,
                  child: Image.file(
                    File(widget.imageFile.path),
                    fit: BoxFit.contain,
                    height: double.infinity,
                    width: double.infinity,
                    alignment: Alignment.center,
                  ),
                ),
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    color: Colors.black.withOpacity(0.5),
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        Visibility(
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 20),
                            child: Text(
                              aviso != null ? aviso! : "",
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            Expanded(
                              child: ButtonLsCustom(
                                text: 'TIRAR OUTRA',
                                colorBackground: ThemeColors.customGrey(context),
                                onPressed: () {
                                  Navigator.pop(context, false);
                                },
                              ),
                            ),
                            Visibility(
                              visible: liberar,
                              child: const SizedBox(width: 20),
                            ),
                            Visibility(
                              visible: liberar,
                              child: Expanded(
                                child: ButtonLsCustom(
                                  text: 'GOSTEI',
                                  onPressed: () {
                                    Navigator.pop(context, true);
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}
