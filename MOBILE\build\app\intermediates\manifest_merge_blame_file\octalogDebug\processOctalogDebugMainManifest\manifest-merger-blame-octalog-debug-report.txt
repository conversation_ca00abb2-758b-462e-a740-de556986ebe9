1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.octalog"
4    android:versionCode="25"
5    android:versionName="1.2.3" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:10:2-64
15-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:10:19-61
16    <!-- Add permissions for location access -->
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
17-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:6:5-78
17-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:6:22-76
18    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
18-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:7:5-80
18-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:7:22-78
19    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
19-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:9:2-75
19-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:9:19-73
20    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
20-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:11:2-76
20-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:11:19-73
21    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
21-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:11:2-76
21-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:11:19-73
22    <uses-permission android:name="android.permission.WAKE_LOCK" />
22-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:12:2-65
22-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:12:19-62
23    <uses-permission android:name="android.permission.CAMERA" />
23-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:13:2-62
23-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:13:19-59
24    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
24-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:14:2-72
24-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:14:19-69
25    <uses-permission android:name="android.permission.VIBRATE" />
25-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:15:2-63
25-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:15:19-60
26    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
26-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:16:2-74
26-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:16:19-71
27    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
27-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:17:2-78
27-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:17:19-75
28    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
28-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:18:2-76
28-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:18:19-73
29    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
29-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:19:2-73
29-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:19:19-71
30    <uses-permission android:name="android.permission.RECORD_AUDIO" />
30-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:20:2-68
30-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:20:19-65
31    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
31-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:22:2-76
31-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:22:19-73
32    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
32-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:23:2-77
32-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:23:19-74
33    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
33-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:24:2-78
33-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:24:19-75
34    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
34-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:25:2-77
34-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:25:19-74
35    <!--
36 <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
37		tools:ignore="ScopedStorage" />
38    -->
39    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
39-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:28:2-73
39-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:28:19-70
40    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
40-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:29:2-72
40-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:29:19-69
41    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
41-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:30:2-72
41-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:30:19-69
42
43    <uses-feature android:name="android.hardware.camera" />
43-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:31:2-57
43-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:31:16-54
44    <uses-feature android:name="android.hardware.camera.autofocus" />
44-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:32:2-67
44-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:32:16-64
45    <!--
46 Required to query activities that can process text, see:
47         https://developer.android.com/training/package-visibility and
48         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
49
50         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
51    -->
52    <queries>
52-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:71:5-76:15
53        <intent>
53-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:72:9-75:18
54            <action android:name="android.intent.action.PROCESS_TEXT" />
54-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:73:13-72
54-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:73:21-70
55
56            <data android:mimeType="text/plain" />
56-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:74:13-50
56-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:74:19-48
57        </intent>
58        <intent>
58-->[:file_picker] C:\projetos\octa.log\MOBILE\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
59            <action android:name="android.intent.action.GET_CONTENT" />
59-->[:file_picker] C:\projetos\octa.log\MOBILE\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
59-->[:file_picker] C:\projetos\octa.log\MOBILE\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
60
61            <data android:mimeType="*/*" />
61-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:74:13-50
61-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:74:19-48
62        </intent>
63    </queries> <!-- Create a unique permission for your app and use it so only your app can receive your OneSignal messages. -->
64    <permission
64-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:10:5-12:47
65        android:name="com.octalog.permission.C2D_MESSAGE"
65-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:11:9-63
66        android:protectionLevel="signature" />
66-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:12:9-44
67
68    <uses-permission android:name="com.octalog.permission.C2D_MESSAGE" /> <!-- c2dm RECEIVE are basic requirements for push messages through Google's FCM -->
68-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:14:5-79
68-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:14:22-76
69    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" /> <!-- START: ShortcutBadger -->
69-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:19:5-82
69-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:19:22-79
70    <!-- Samsung -->
71    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
71-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:31:5-86
71-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:31:22-83
72    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
72-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:32:5-87
72-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:32:22-84
73    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
73-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:33:5-81
73-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:33:22-78
74    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
74-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:34:5-83
74-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:34:22-80
75    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
75-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:35:5-88
75-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:35:22-85
76    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
76-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:36:5-92
76-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:36:22-89
77    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
77-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:37:5-84
77-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:37:22-81
78    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
78-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:38:5-83
78-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:38:22-80
79    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
79-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:39:5-91
79-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:39:22-88
80    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
80-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:40:5-92
80-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:40:22-89
81    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- ZUK -->
81-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:41:5-93
81-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:41:22-90
82    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- OPPO -->
82-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:42:5-73
82-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:42:22-70
83    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
83-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:43:5-82
83-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:43:22-79
84    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- EvMe -->
84-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:44:5-83
84-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:44:22-80
85    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
85-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:45:5-88
85-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:45:22-85
86    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
86-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:46:5-89
86-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:46:22-86
87
88    <permission
88-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
89        android:name="com.octalog.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
89-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
90        android:protectionLevel="signature" />
90-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
91
92    <uses-permission android:name="com.octalog.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
92-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
92-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
93    <uses-permission
93-->[com.github.davidliu:audioswitch:89582c47c9a04c62f90aa5e57251af4800a62c9a] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\160f0e0b2f19cfe5268e794069515506\transformed\jetified-audioswitch-89582c47c9a04c62f90aa5e57251af4800a62c9a\AndroidManifest.xml:9:5-11:38
94        android:name="android.permission.BLUETOOTH"
94-->[com.github.davidliu:audioswitch:89582c47c9a04c62f90aa5e57251af4800a62c9a] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\160f0e0b2f19cfe5268e794069515506\transformed\jetified-audioswitch-89582c47c9a04c62f90aa5e57251af4800a62c9a\AndroidManifest.xml:10:9-52
95        android:maxSdkVersion="30" />
95-->[com.github.davidliu:audioswitch:89582c47c9a04c62f90aa5e57251af4800a62c9a] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\160f0e0b2f19cfe5268e794069515506\transformed\jetified-audioswitch-89582c47c9a04c62f90aa5e57251af4800a62c9a\AndroidManifest.xml:11:9-35
96
97    <application
98        android:name="android.app.Application"
99        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
99-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
100        android:debuggable="true"
101        android:extractNativeLibs="true"
102        android:icon="@mipmap/launcher_icon"
103        android:label="@string/app_name" >
104        <activity
105            android:name="com.octalog.MainActivity"
106            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
107            android:exported="true"
108            android:hardwareAccelerated="true"
109            android:launchMode="singleTop"
110            android:taskAffinity=""
111            android:theme="@style/LaunchTheme"
112            android:windowSoftInputMode="adjustResize" >
113
114            <!--
115                 Specifies an Android theme to apply to this Activity as soon as
116                 the Android process has started. This theme is visible to the user
117                 while the Flutter UI initializes. After that, this theme continues
118                 to determine the Window background behind the Flutter UI.
119            -->
120            <meta-data
121                android:name="io.flutter.embedding.android.NormalTheme"
122                android:resource="@style/NormalTheme" />
123
124            <intent-filter>
125                <action android:name="android.intent.action.MAIN" />
126
127                <category android:name="android.intent.category.LAUNCHER" />
128            </intent-filter>
129        </activity>
130        <!--
131             Don't delete the meta-data below.
132             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
133        -->
134        <meta-data
135            android:name="flutterEmbedding"
136            android:value="2" />
137
138        <receiver android:name="com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentReceiver" />
138-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-119
138-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:19-116
139
140        <service
140-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-14:72
141            android:name="com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService"
141-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-109
142            android:enabled="true"
142-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-35
143            android:exported="false"
143-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
144            android:permission="android.permission.BIND_JOB_SERVICE" />
144-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-69
145        <service
145-->[:geolocator_android] C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
146            android:name="com.baseflow.geolocator.GeolocatorLocationService"
146-->[:geolocator_android] C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
147            android:enabled="true"
147-->[:geolocator_android] C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
148            android:exported="false"
148-->[:geolocator_android] C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
149            android:foregroundServiceType="location" />
149-->[:geolocator_android] C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
150        <service
150-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
151            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
151-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
152            android:exported="false"
152-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
153            android:permission="android.permission.BIND_JOB_SERVICE" />
153-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
154        <service
154-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
155            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
155-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
156            android:exported="false" >
156-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
157            <intent-filter>
157-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
158                <action android:name="com.google.firebase.MESSAGING_EVENT" />
158-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
158-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
159            </intent-filter>
160        </service>
161
162        <receiver
162-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
163            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
163-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
164            android:exported="true"
164-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
165            android:permission="com.google.android.c2dm.permission.SEND" >
165-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
166            <intent-filter>
166-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
167                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
167-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
167-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
168            </intent-filter>
169        </receiver>
170
171        <service
171-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
172            android:name="com.google.firebase.components.ComponentDiscoveryService"
172-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:18-89
173            android:directBootAware="true"
173-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
174            android:exported="false" >
174-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
175            <meta-data
175-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
176                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
176-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
177                android:value="com.google.firebase.components.ComponentRegistrar" />
177-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
178            <meta-data
178-->[:firebase_remote_config] C:\projetos\octa.log\MOBILE\build\firebase_remote_config\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
179                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firebaseremoteconfig.FlutterFirebaseAppRegistrar"
179-->[:firebase_remote_config] C:\projetos\octa.log\MOBILE\build\firebase_remote_config\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-139
180                android:value="com.google.firebase.components.ComponentRegistrar" />
180-->[:firebase_remote_config] C:\projetos\octa.log\MOBILE\build\firebase_remote_config\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
181            <meta-data
181-->[:firebase_core] C:\projetos\octa.log\MOBILE\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
182                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
182-->[:firebase_core] C:\projetos\octa.log\MOBILE\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
183                android:value="com.google.firebase.components.ComponentRegistrar" />
183-->[:firebase_core] C:\projetos\octa.log\MOBILE\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
184            <meta-data
184-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
185                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
185-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
186                android:value="com.google.firebase.components.ComponentRegistrar" />
186-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
187            <meta-data
187-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
188                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
188-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
189                android:value="com.google.firebase.components.ComponentRegistrar" />
189-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
190            <meta-data
190-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:29:13-31:85
191                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar"
191-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:30:17-128
192                android:value="com.google.firebase.components.ComponentRegistrar" />
192-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:31:17-82
193            <meta-data
193-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:32:13-34:85
194                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
194-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:33:17-117
195                android:value="com.google.firebase.components.ComponentRegistrar" />
195-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:34:17-82
196            <meta-data
196-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
197                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
197-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
198                android:value="com.google.firebase.components.ComponentRegistrar" />
198-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
199            <meta-data
199-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
200                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
200-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
201                android:value="com.google.firebase.components.ComponentRegistrar" />
201-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
202            <meta-data
202-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcc8d2985ec1ab09d1e21c43fe50ab02\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
203                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
203-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcc8d2985ec1ab09d1e21c43fe50ab02\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
204                android:value="com.google.firebase.components.ComponentRegistrar" />
204-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcc8d2985ec1ab09d1e21c43fe50ab02\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
205            <meta-data
205-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
206                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
206-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
207                android:value="com.google.firebase.components.ComponentRegistrar" />
207-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
208            <meta-data
208-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf6a27b1025e5f977c0f155cbd2c4f7c\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
209                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
209-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf6a27b1025e5f977c0f155cbd2c4f7c\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
210                android:value="com.google.firebase.components.ComponentRegistrar" />
210-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf6a27b1025e5f977c0f155cbd2c4f7c\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
211            <meta-data
211-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e32c06b9e94e0afe422db7a8cc6919d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
212                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
212-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e32c06b9e94e0afe422db7a8cc6919d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
213                android:value="com.google.firebase.components.ComponentRegistrar" />
213-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e32c06b9e94e0afe422db7a8cc6919d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
214        </service>
215
216        <provider
216-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
217            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
217-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
218            android:authorities="com.octalog.flutterfirebasemessaginginitprovider"
218-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
219            android:exported="false"
219-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
220            android:initOrder="99" />
220-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
221        <provider
221-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
222            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
222-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
223            android:authorities="com.octalog.flutter.image_provider"
223-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
224            android:exported="false"
224-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
225            android:grantUriPermissions="true" >
225-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
226            <meta-data
226-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
227                android:name="android.support.FILE_PROVIDER_PATHS"
227-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
228                android:resource="@xml/flutter_image_picker_file_paths" />
228-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
229        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
230        <service
230-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
231            android:name="com.google.android.gms.metadata.ModuleDependencies"
231-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
232            android:enabled="false"
232-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
233            android:exported="false" >
233-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
234            <intent-filter>
234-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
235                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
235-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
235-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
236            </intent-filter>
237
238            <meta-data
238-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
239                android:name="photopicker_activity:0:required"
239-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
240                android:value="" />
240-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
241        </service>
242
243        <provider
243-->[:open_file_android] C:\projetos\octa.log\MOBILE\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-19:20
244            android:name="com.crazecoder.openfile.FileProvider"
244-->[:open_file_android] C:\projetos\octa.log\MOBILE\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
245            android:authorities="com.octalog.fileProvider.com.crazecoder.openfile"
245-->[:open_file_android] C:\projetos\octa.log\MOBILE\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-88
246            android:exported="false"
246-->[:open_file_android] C:\projetos\octa.log\MOBILE\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
247            android:grantUriPermissions="true"
247-->[:open_file_android] C:\projetos\octa.log\MOBILE\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
248            android:requestLegacyExternalStorage="true" >
248-->[:open_file_android] C:\projetos\octa.log\MOBILE\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-56
249            <meta-data
249-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
250                android:name="android.support.FILE_PROVIDER_PATHS"
250-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
251                android:resource="@xml/filepaths" />
251-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
252        </provider>
253
254        <activity
254-->[:url_launcher_android] C:\projetos\octa.log\MOBILE\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
255            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
255-->[:url_launcher_android] C:\projetos\octa.log\MOBILE\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
256            android:exported="false"
256-->[:url_launcher_android] C:\projetos\octa.log\MOBILE\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
257            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
257-->[:url_launcher_android] C:\projetos\octa.log\MOBILE\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
258        <!--
259        Service for holding metadata. Cannot be instantiated.
260        Metadata will be merged from other manifests.
261        -->
262        <service
262-->[androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41ce894b414f4e410f366a4a6ad7589\transformed\jetified-camera-core-1.3.4\AndroidManifest.xml:29:9-33:78
263            android:name="androidx.camera.core.impl.MetadataHolderService"
263-->[androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41ce894b414f4e410f366a4a6ad7589\transformed\jetified-camera-core-1.3.4\AndroidManifest.xml:30:13-75
264            android:enabled="false"
264-->[androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41ce894b414f4e410f366a4a6ad7589\transformed\jetified-camera-core-1.3.4\AndroidManifest.xml:31:13-36
265            android:exported="false" >
265-->[androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41ce894b414f4e410f366a4a6ad7589\transformed\jetified-camera-core-1.3.4\AndroidManifest.xml:32:13-37
266            <meta-data
266-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a600f391fc985fb6fe77353ae0360c6c\transformed\jetified-camera-camera2-1.3.4\AndroidManifest.xml:30:13-32:89
267                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
267-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a600f391fc985fb6fe77353ae0360c6c\transformed\jetified-camera-camera2-1.3.4\AndroidManifest.xml:31:17-103
268                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
268-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a600f391fc985fb6fe77353ae0360c6c\transformed\jetified-camera-camera2-1.3.4\AndroidManifest.xml:32:17-86
269        </service>
270        <service
270-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:9:9-15:19
271            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
271-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:10:13-91
272            android:directBootAware="true"
272-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:17:13-43
273            android:exported="false" >
273-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:11:13-37
274            <meta-data
274-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:12:13-14:85
275                android:name="com.google.firebase.components:com.google.mlkit.vision.face.internal.FaceRegistrar"
275-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:13:17-114
276                android:value="com.google.firebase.components.ComponentRegistrar" />
276-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:14:17-82
277            <meta-data
277-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\087b3f5fae32c8257c73d14fa1c12986\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
278                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
278-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\087b3f5fae32c8257c73d14fa1c12986\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
279                android:value="com.google.firebase.components.ComponentRegistrar" />
279-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\087b3f5fae32c8257c73d14fa1c12986\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
280            <meta-data
280-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70809b849f79bdfe99712e94237d768f\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
281                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
281-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70809b849f79bdfe99712e94237d768f\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
282                android:value="com.google.firebase.components.ComponentRegistrar" />
282-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70809b849f79bdfe99712e94237d768f\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
283            <meta-data
283-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:20:13-22:85
284                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
284-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:21:17-120
285                android:value="com.google.firebase.components.ComponentRegistrar" />
285-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:22:17-82
286        </service>
287
288        <provider
288-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:9:9-13:38
289            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
289-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:10:13-78
290            android:authorities="com.octalog.mlkitinitprovider"
290-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:11:13-69
291            android:exported="false"
291-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:12:13-37
292            android:initOrder="99" />
292-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:13:13-35
293
294        <receiver
294-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:49:9-60:20
295            android:name="com.onesignal.notifications.receivers.FCMBroadcastReceiver"
295-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:50:13-86
296            android:exported="true"
296-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:51:13-36
297            android:permission="com.google.android.c2dm.permission.SEND" >
297-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:52:13-73
298
299            <!-- High priority so OneSignal payloads can be filtered from other FCM receivers -->
300            <intent-filter android:priority="999" >
300-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:55:13-59:29
300-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:55:28-50
301                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
301-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
301-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
302
303                <category android:name="com.octalog" />
303-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:58:17-61
303-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:58:27-58
304            </intent-filter>
305        </receiver>
306
307        <service
307-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:62:9-68:19
308            android:name="com.onesignal.notifications.services.HmsMessageServiceOneSignal"
308-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:63:13-91
309            android:exported="false" >
309-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:64:13-37
310            <intent-filter>
310-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:65:13-67:29
311                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
311-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:66:17-81
311-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:66:25-78
312            </intent-filter>
313        </service> <!-- CAUTION: OneSignal backend includes the activity name in the payload, modifying the name without sync may result in notification click not firing -->
314        <activity
314-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:69:9-77:20
315            android:name="com.onesignal.NotificationOpenedActivityHMS"
315-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:70:13-71
316            android:exported="true"
316-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:71:13-36
317            android:noHistory="true"
317-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:72:13-37
318            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
318-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:73:13-72
319            <intent-filter>
319-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:74:13-76:29
320                <action android:name="android.intent.action.VIEW" />
320-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:75:17-69
320-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:75:25-66
321            </intent-filter>
322        </activity>
323
324        <receiver
324-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:79:9-81:39
325            android:name="com.onesignal.notifications.receivers.NotificationDismissReceiver"
325-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:80:13-93
326            android:exported="true" />
326-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:81:13-36
327        <receiver
327-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:82:9-89:20
328            android:name="com.onesignal.notifications.receivers.BootUpReceiver"
328-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:83:13-80
329            android:exported="true" >
329-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:84:13-36
330            <intent-filter>
330-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:85:13-88:29
331                <action android:name="android.intent.action.BOOT_COMPLETED" />
331-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:86:17-79
331-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:86:25-76
332                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
332-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:87:17-82
332-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:87:25-79
333            </intent-filter>
334        </receiver>
335        <receiver
335-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:90:9-96:20
336            android:name="com.onesignal.notifications.receivers.UpgradeReceiver"
336-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:91:13-81
337            android:exported="true" >
337-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:92:13-36
338            <intent-filter>
338-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:93:13-95:29
339                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
339-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:94:17-84
339-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:94:25-81
340            </intent-filter>
341        </receiver>
342
343        <activity
343-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:98:9-104:75
344            android:name="com.onesignal.notifications.activities.NotificationOpenedActivity"
344-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:99:13-93
345            android:excludeFromRecents="true"
345-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:100:13-46
346            android:exported="true"
346-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:101:13-36
347            android:noHistory="true"
347-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:102:13-37
348            android:taskAffinity=""
348-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:103:13-36
349            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
349-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:104:13-72
350        <activity
350-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:105:9-110:75
351            android:name="com.onesignal.notifications.activities.NotificationOpenedActivityAndroid22AndOlder"
351-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:106:13-110
352            android:excludeFromRecents="true"
352-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:107:13-46
353            android:exported="true"
353-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:108:13-36
354            android:noHistory="true"
354-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:109:13-37
355            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
355-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:110:13-72
356
357        <service
357-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:13:9-16:72
358            android:name="com.onesignal.core.services.SyncJobService"
358-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:14:13-70
359            android:exported="false"
359-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:15:13-37
360            android:permission="android.permission.BIND_JOB_SERVICE" />
360-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:16:13-69
361
362        <activity
362-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:18:9-21:75
363            android:name="com.onesignal.core.activities.PermissionsActivity"
363-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:19:13-77
364            android:exported="false"
364-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:20:13-37
365            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
365-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:21:13-72
366
367        <receiver
367-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
368            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
368-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
369            android:exported="true"
369-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
370            android:permission="com.google.android.c2dm.permission.SEND" >
370-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
371            <intent-filter>
371-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
372                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
372-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
372-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
373            </intent-filter>
374
375            <meta-data
375-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
376                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
376-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
377                android:value="true" />
377-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
378        </receiver>
379        <!--
380             FirebaseMessagingService performs security checks at runtime,
381             but set to not exported to explicitly avoid allowing another app to call it.
382        -->
383        <service
383-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
384            android:name="com.google.firebase.messaging.FirebaseMessagingService"
384-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
385            android:directBootAware="true"
385-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
386            android:exported="false" >
386-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
387            <intent-filter android:priority="-500" >
387-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
388                <action android:name="com.google.firebase.MESSAGING_EVENT" />
388-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
388-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
389            </intent-filter>
390        </service>
391
392        <activity
392-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07660f349a2fe95c07a6bc7026689012\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
393            android:name="com.google.android.gms.common.api.GoogleApiActivity"
393-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07660f349a2fe95c07a6bc7026689012\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
394            android:exported="false"
394-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07660f349a2fe95c07a6bc7026689012\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
395            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
395-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07660f349a2fe95c07a6bc7026689012\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
396
397        <provider
397-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
398            android:name="com.google.firebase.provider.FirebaseInitProvider"
398-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
399            android:authorities="com.octalog.firebaseinitprovider"
399-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
400            android:directBootAware="true"
400-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
401            android:exported="false"
401-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
402            android:initOrder="100" />
402-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
403
404        <uses-library
404-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
405            android:name="androidx.window.extensions"
405-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
406            android:required="false" />
406-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
407        <uses-library
407-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
408            android:name="androidx.window.sidecar"
408-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
409            android:required="false" />
409-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
410
411        <provider
411-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
412            android:name="androidx.startup.InitializationProvider"
412-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
413            android:authorities="com.octalog.androidx-startup"
413-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
414            android:exported="false" >
414-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
415            <meta-data
415-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
416                android:name="androidx.work.WorkManagerInitializer"
416-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
417                android:value="androidx.startup" />
417-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
418            <meta-data
418-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95a194f62dbd62c14638e0c38a7881a4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
419                android:name="androidx.emoji2.text.EmojiCompatInitializer"
419-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95a194f62dbd62c14638e0c38a7881a4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
420                android:value="androidx.startup" />
420-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95a194f62dbd62c14638e0c38a7881a4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
421            <meta-data
421-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
422                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
422-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
423                android:value="androidx.startup" />
423-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
424            <meta-data
424-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
425                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
425-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
426                android:value="androidx.startup" />
426-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
427        </provider>
428
429        <service
429-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
430            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
430-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
431            android:directBootAware="false"
431-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
432            android:enabled="@bool/enable_system_alarm_service_default"
432-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
433            android:exported="false" />
433-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
434        <service
434-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
435            android:name="androidx.work.impl.background.systemjob.SystemJobService"
435-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
436            android:directBootAware="false"
436-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
437            android:enabled="@bool/enable_system_job_service_default"
437-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
438            android:exported="true"
438-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
439            android:permission="android.permission.BIND_JOB_SERVICE" />
439-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
440        <service
440-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
441            android:name="androidx.work.impl.foreground.SystemForegroundService"
441-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
442            android:directBootAware="false"
442-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
443            android:enabled="@bool/enable_system_foreground_service_default"
443-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
444            android:exported="false" />
444-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
445
446        <receiver
446-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
447            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
447-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
448            android:directBootAware="false"
448-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
449            android:enabled="true"
449-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
450            android:exported="false" />
450-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
451        <receiver
451-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
452            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
452-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
453            android:directBootAware="false"
453-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
454            android:enabled="false"
454-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
455            android:exported="false" >
455-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
456            <intent-filter>
456-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
457                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
457-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
457-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
458                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
458-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
458-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
459            </intent-filter>
460        </receiver>
461        <receiver
461-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
462            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
462-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
463            android:directBootAware="false"
463-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
464            android:enabled="false"
464-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
465            android:exported="false" >
465-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
466            <intent-filter>
466-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
467                <action android:name="android.intent.action.BATTERY_OKAY" />
467-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
467-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
468                <action android:name="android.intent.action.BATTERY_LOW" />
468-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
468-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
469            </intent-filter>
470        </receiver>
471        <receiver
471-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
472            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
472-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
473            android:directBootAware="false"
473-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
474            android:enabled="false"
474-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
475            android:exported="false" >
475-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
476            <intent-filter>
476-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
477                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
477-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
477-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
478                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
478-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
478-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
479            </intent-filter>
480        </receiver>
481        <receiver
481-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
482            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
482-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
483            android:directBootAware="false"
483-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
484            android:enabled="false"
484-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
485            android:exported="false" >
485-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
486            <intent-filter>
486-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
487                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
487-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
487-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
488            </intent-filter>
489        </receiver>
490        <receiver
490-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
491            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
491-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
492            android:directBootAware="false"
492-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
493            android:enabled="false"
493-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
494            android:exported="false" >
494-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
495            <intent-filter>
495-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
496                <action android:name="android.intent.action.BOOT_COMPLETED" />
496-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:86:17-79
496-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:86:25-76
497                <action android:name="android.intent.action.TIME_SET" />
497-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
497-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
498                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
498-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
498-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
499            </intent-filter>
500        </receiver>
501        <receiver
501-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
502            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
502-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
503            android:directBootAware="false"
503-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
504            android:enabled="@bool/enable_system_alarm_service_default"
504-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
505            android:exported="false" >
505-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
506            <intent-filter>
506-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
507                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
507-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
507-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
508            </intent-filter>
509        </receiver>
510        <receiver
510-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
511            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
511-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
512            android:directBootAware="false"
512-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
513            android:enabled="true"
513-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
514            android:exported="true"
514-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
515            android:permission="android.permission.DUMP" >
515-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
516            <intent-filter>
516-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
517                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
517-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
517-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
518            </intent-filter>
519        </receiver>
520
521        <meta-data
521-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf636ef2f0738047fe8129f320ef339e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
522            android:name="com.google.android.gms.version"
522-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf636ef2f0738047fe8129f320ef339e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
523            android:value="@integer/google_play_services_version" />
523-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf636ef2f0738047fe8129f320ef339e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
524
525        <receiver
525-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
526            android:name="androidx.profileinstaller.ProfileInstallReceiver"
526-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
527            android:directBootAware="false"
527-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
528            android:enabled="true"
528-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
529            android:exported="true"
529-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
530            android:permission="android.permission.DUMP" >
530-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
531            <intent-filter>
531-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
532                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
532-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
532-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
533            </intent-filter>
534            <intent-filter>
534-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
535                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
535-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
535-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
536            </intent-filter>
537            <intent-filter>
537-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
538                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
538-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
538-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
539            </intent-filter>
540            <intent-filter>
540-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
541                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
541-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
541-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
542            </intent-filter>
543        </receiver>
544
545        <service
545-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
546            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
546-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
547            android:exported="false" >
547-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
548            <meta-data
548-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
549                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
549-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
550                android:value="cct" />
550-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
551        </service>
552        <service
552-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
553            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
553-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
554            android:exported="false"
554-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
555            android:permission="android.permission.BIND_JOB_SERVICE" >
555-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
556        </service>
557
558        <receiver
558-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
559            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
559-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
560            android:exported="false" />
560-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
561
562        <service
562-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca09e878cbb074545c6b4079b0555c5\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
563            android:name="androidx.room.MultiInstanceInvalidationService"
563-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca09e878cbb074545c6b4079b0555c5\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
564            android:directBootAware="true"
564-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca09e878cbb074545c6b4079b0555c5\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
565            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
565-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca09e878cbb074545c6b4079b0555c5\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
566        <activity
566-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2748b27b9a4960040fddad0a76748cc0\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
567            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
567-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2748b27b9a4960040fddad0a76748cc0\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
568            android:exported="false"
568-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2748b27b9a4960040fddad0a76748cc0\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
569            android:stateNotNeeded="true"
569-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2748b27b9a4960040fddad0a76748cc0\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
570            android:theme="@style/Theme.PlayCore.Transparent" />
570-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2748b27b9a4960040fddad0a76748cc0\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
571    </application>
572
573</manifest>
