import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:asuka/asuka.dart' as asuka;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:image_picker/image_picker.dart';
import 'package:latlong2/latlong.dart';
import 'package:octalog/src/database/config_blob/config_database.dart';
import 'package:octalog/src/database/offline_request/offline_request_database.dart';
import 'package:octalog/src/database/offline_request/offline_request_hive.dart';
import 'package:octalog/src/helpers/gps/gps_position.dart';
import 'package:octalog/src/helpers/login/login.dart';
import 'package:octalog/src/helpers/web_connector.dart';
import 'package:octalog/src/models_new/cliente_new.dart';
import 'package:octalog/src/models_new/position_data_location.dart';
import 'package:octalog/src/pages/sac_page/enum/message_type.dart';
import 'package:octalog/src/pages/sac_page/model/messagem_chat.dart';
import 'package:octalog/src/pages/sac_page/sac_page.state.dart';
import 'package:octalog/src/utils/extesion.dart';
import 'package:signalr_core/signalr_core.dart';

import '../../../../errors.dart';
import '../../../fcm_files/audio_play.dart';
import '../../database/sac/sac_atendimento.dart';
import '../../utils/offline_helper.dart';
import '../../utils/theme_colors.dart';
import 'enum/sap_page_etapa.dart';
import 'model/sac_model_fila.dart';

class SacPageStore {
  final ValueNotifier<SacPageState> state =
      ValueNotifier<SacPageState>(SacPageState.init());
  final pedidosSac = PedidosSacDatabase.instance;
  var connection = WebConnector();

  void setEtapa(SacPageEtapa etapa) {
    state.value = state.value.setEtapa(etapa);
  }

  void setLoading(isLoading) {
    state.value = state.value.setLoading(isLoading);
  }

  void setMensagem(mensagem) {
    state.value = state.value.setMensagem(mensagem);
  }

  void setIDStatusAtividadeIDos(int idStatusAtividade, int idos) {
    state.value = state.value.setIDStatusAtividadeIDos(idStatusAtividade, idos);
  }

  void setBotaoMensagem(bool isBotaoMensagem) {
    state.value = state.value.setBotaoMensagem(isBotaoMensagem);
  }

  void setIndicadorDeBuscaFila(bool? value) {
    state.value = state.value.setIndicadorDeBuscaFila(value);
  }

  void disposeChat() {
    stopListenEvents();
    closeSocket();
  }

  Future salvarLocalSacPedidos() async {
    await pedidosSac.setSac(
      PedidosSacModel(
        idStatusAtividade: state.value.idStatusAtividade,
        idos: state.value.idos,
        idSacAtendimento:
            state.value.sacModelFila?.chamado.idSacAtendimento ?? 0,
        status: [],
      ),
    );
  }

  Future deletarLocalSacEmAberto() async {
    await pedidosSac.deletSac(
      state.value.idos,
      state.value.idStatusAtividade,
      state.value.sacModelFila?.chamado.idSacAtendimento ?? 0,
    );
  }

  void setBotaoCancelarSAC(bool value) {
    state.value = state.value.setBotaoCancelarSAC(value);
    state.value = state.value.setSacCancelado(value);
  }

  Future<void> cancelarSac(BuildContext context) async {
    try {
      final response = await connection.delete(
        "/sac/cancelar",
        queryParameters: {
          "IDSacAtendimento":
              state.value.sacModelFila?.chamado.idSacAtendimento ?? 0,
        },
      );
      if (response.statusCode == 200) {
        setBotaoCancelarSAC(true);
        deletarLocalSacEmAberto();
        final data = response.data;
        final filaModel = SacModelFila.fromMap(data);
        state.value = state.value.setSacModelFila(filaModel);
        state.value = state.value.setEtapa(SacPageEtapa.finalizado);
      }
    } on ConnectionError catch (e) {
      if (e.status == 400) {
        offlineStore.setOffline(false);
        final mensagemJson = jsonDecode(e.response);
        final sac = SacModelFila.fromMap(mensagemJson);

        await showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(sac.tituloMensagemSac ?? ""),
            content: Text(sac.mensagem ?? ""),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(
                  "Ok",
                  style: TextStyle(color: ThemeColors.customOrange(context)),
                ),
              )
            ],
          ),
        );
      } else {
        await showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text("Atendimento já iniciado"),
            content: const Text(
                "Não será possível cancelar, o atendente já está atuando no seu chamado. Aguarde mais um pouco."),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(
                  "Ok",
                  style: TextStyle(color: ThemeColors.customOrange(context)),
                ),
              )
            ],
          ),
        );
      }
    }
  }

  Future<void> abrirSacAtendimento() async {
    await salvarLocalSacPedidos();
    try {
      setBotaoMensagem(true);
      final body = {
        "IDOS": state.value.idos,
        "InfoInicialAgente": state.value.mensagem,
        "IDStatusAtividade": state.value.idStatusAtividade,
        "AgenteDesejaAguardarSAC": state.value.agenteDesejaAguardarSAC,
        "DataContatoAgente":
            DateTime.now().dataHoraServidorFomart.toIso8601String(),
      };
      final response = await connection
          .post("/sac/solicitar", body: body)
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = response.data;
        final filaModel = SacModelFila.fromMap(data);
        state.value = state.value.setSacModelFila(filaModel);
        state.value = state.value.setEtapa(SacPageEtapa.espera);
      }
    } on ConnectionError catch (e) {
      offlineStore.setOffline(false);
      try {
        final mensagemJson = jsonDecode(e.response);
        SacModelFila sac = SacModelFila.fromMap(mensagemJson);
        state.value = state.value.setSacModelFila(sac);
        state.value = state.value.setEtapa(SacPageEtapa.finalizado);
      } catch (e) {
        setBotaoCancelarSAC(true);
        state.value = state.value.setSacModelFila(
          SacModelFila(
            fila: 0,
            icone: "alert",
            mensagem: null,
            tituloMensagemSac: "Erro",
            chamado: SacChamadoModel(
              mensagemSac:
                  "Verifique sua conexão com a internet e tente novamente",
              tituloMensagemSac: "Erro",
              filaInicial: 0,
              nomeAtendente: "",
              ocorrencia: "",
              idSacAtendimento: 0,
              os: "",
            ),
          ),
        );
        state.value = state.value.setEtapa(SacPageEtapa.finalizado);
      }
    }
    setBotaoMensagem(false);
  }

  setLoadingSacEmAberto(bool value) {
    state.value = state.value.setLoadingSacEmAberto(value);
  }

  Future<void> abrirFilaEmAndamento(int idSacAtendimento) async {
    setLoadingSacEmAberto(true);
    setIndicadorDeBuscaFila(true);
    try {
      var connection = WebConnector();
      final response = await connection.get(
        "/sac/fila",
        queryParameters: {
          "IDSacAtendimento": idSacAtendimento,
        },
      );
      if (response.statusCode == 200) {
        final data = response.data;
        final filaModel = SacModelFila.fromMap(data);
        state.value = state.value.setSacModelFila(filaModel);
        state.value = state.value.setEtapa(SacPageEtapa.espera);
      }
    } on ConnectionError catch (e) {
      if (e.status == 400) {
        offlineStore.setOffline(false);
        final mensagemJson = jsonDecode(e.response);
        offlineStore.setOffline(false);
        final sac = SacModelFila.fromMap(mensagemJson);
        state.value = state.value.setSacModelFila(sac);
        state.value = state.value.setEtapa(SacPageEtapa.finalizado);
      }
    }
    setLoadingSacEmAberto(false);
  }

  Future<void> initBuscarFila() async {
    int fila = 1;
    int errosRestantesInternet = 0;
    while (fila != 0) {
      setIndicadorDeBuscaFila(true);
      try {
        final response = await connection.get(
          "/sac/fila",
          queryParameters: {
            "IDSacAtendimento":
                state.value.sacModelFila?.chamado.idSacAtendimento ?? 0,
          },
        );
        if (response.statusCode == 200) {
          final data = response.data;
          final filaModel = SacModelFila.fromMap(data);
          state.value = state.value.setSacModelFila(filaModel);

          loopConexao();
          if (state.value.sacModelFila?.fila == 0) {
            offlineStore.setOffline(false);
            state.value = state.value.setSacModelFila(filaModel);
            state.value = state.value.setEtapa(SacPageEtapa.finalizado);
          } else {
            setIndicadorDeBuscaFila(false);
            await Future.delayed(
              const Duration(seconds: 14),
            );
          }
        }
      } on ConnectionError catch (e) {
        if (e.status == 400) {
          offlineStore.setOffline(false);
          fila = 0;
          final mensagemJson = jsonDecode(e.response);
          offlineStore.setOffline(false);
          final sac = SacModelFila.fromMap(mensagemJson);
          state.value = state.value.setSacModelFila(sac);
          state.value = state.value.setEtapa(SacPageEtapa.finalizado);
        } else {
          errosRestantesInternet++;
          if (errosRestantesInternet >= 10) {
            fila = 0;
            offlineStore.setOffline(false);
            final mensagemJson = jsonDecode(e.response);
            final sac = SacModelFila.fromMap(mensagemJson);
            state.value = state.value.setSacModelFila(sac);
            state.value = state.value.setEtapa(SacPageEtapa.finalizado);
          }
        }
      } catch (_) {
        setIndicadorDeBuscaFila(null);
        errosRestantesInternet++;
        if (errosRestantesInternet >= 8) {
          fila = 0;
          setBotaoCancelarSAC(true);
          state.value = state.value.setSacModelFila(
            SacModelFila(
              fila: 0,
              icone: "alert",
              mensagem:
                  "Devido a problemas de conexão, o chamado foi cancelado",
              chamado: SacChamadoModel(
                mensagemSac: "Erro na conexão com a internet",
                tituloMensagemSac: "Erro",
                filaInicial: 0,
                nomeAtendente: "",
                ocorrencia: "",
                idSacAtendimento: 0,
                os: "",
              ),
            ),
          );
          state.value = state.value.setEtapa(SacPageEtapa.finalizado);
        }
        await Future.delayed(
          const Duration(seconds: 5),
        );
        setIndicadorDeBuscaFila(null);
        await Future.delayed(
          const Duration(seconds: 15),
        );
      }
    }
  }

  Future<void> initSocket() async {
    log("Iniciando socket");
    String idSacAtendimentoString =
        state.value.sacModelFila!.chamado.idSacAtendimento.toString();
    final config = await ConfigDatabase.instance.getConfig();

    String api = config.linkHub;

    final socket = HubConnectionBuilder()
        .withUrl(
            "$api/hub-chat?IDSacAtendimento=$idSacAtendimentoString-AGENTE")
        .withAutomaticReconnect([0, 2000, 10000, 30000, 30000]).build();

    await socket.start();

    if (socket.state == HubConnectionState.connected) {
      log("Conectado");
      state.value = state.value.setSocket(socket);
      await receberMensagens();
    } else {
      state.value = state.value.setSocket(socket);
      log("Desconectado");
    }
  }

  Future<void> loopConexao() async {
    bool isConnectadoSocket = false;

    isConnectadoSocket =
        state.value.socket?.state == HubConnectionState.connected;

    if (!isConnectadoSocket) {
      try {
        await closeSocket();
        await initSocket();
      } catch (e) {
        log(e.toString());
      }
    }
  }

  Future<void> sendMessage(String message) async {
    try {
      final socket = state.value.socket;

      final messagemChat = MessagemChat(
        idSacAtendimento:
            state.value.sacModelFila?.chamado.idSacAtendimento ?? 0,
        iDmensagem: "0",
        usuario: Login.instance.usuarioLogado?.nomeCompleto ?? "Não informado",
        conteudo: message,
        origem: MessagemTipo.agente,
        token: Login.instance.usuarioLogado?.token ?? '',
      );

      final messagemChatJson = messagemChat.toJson();

      await socket!.invoke(
        "EnviarMensagem",
        args: [messagemChatJson],
      );
    } catch (e) {
      log(e.toString());
    }
  }

  Future<void> closeSocket() async {
    try {
      await state.value.socket?.stop();
    } catch (_) {}
  }

  Future<void> stopListenEvents() async {
    state.value.socket?.off("ReceberMensagem");
  }

  Future<void> receberMensagens() async {
    log("Recebendo mensagens");
    state.value.socket?.on(
      "ReceberMensagem",
      (message) {
        log(message.toString());

        if (state.value.etapa != SacPageEtapa.homeChat) {
          setEtapa(SacPageEtapa.homeChat);
          playAudio();
        }

        final messagemChat = MessagemChat.fromJson(
          Map<String, dynamic>.from(message![0]),
        );

        addMenagemChat(messagemChat);
      },
    );
  }

  Future<void> mensagemRecebida(String iDmensagem) async {
    try {
      final socket = state.value.socket;
      await socket!.invoke(
        "MensagemRecebida",
        args: [iDmensagem],
      );
    } catch (e) {
      log(e.toString());
    }
  }

  String? verificarHyperLinkDestinoDoGoogleMpas(String message) {
    // se a string conter o link do google maps
    if (message.contains("https://www.google.com/maps/")) {
      // regex para pegar o link do google maps
      final regex = RegExp(
        r"https://www.google.com/maps/.*",
      );
      // retorna o link do google maps
      return regex.stringMatch(message);
    }
    return null;
  }

  bool verificarHyperLinkDeFoto(String message) {
    final regex = RegExp(
      r"https://.*\.(jpg|jpeg|png|gif)",
    );
    return regex.hasMatch(message);
  }

  String removerHyperLink(String message) {
    final regex = RegExp(
      r"https://www.google.com/maps/.*",
    );
    return message.replaceAll(regex, "");
  }

  void addMenagemChat(MessagemChat message) {
    state.value = state.value.setMessagesChat(message);
    if (message.origem == MessagemTipo.sac) {}
  }

  Future<void> uploaAddFoto(XFile? foto) async {
    if (foto == null) return;

    final imageFile = File(foto.path);
    final Uint8List imageBytes = imageFile.readAsBytesSync();
    final String? linkFoto = await WebConnector().postImageBlobStorage(
        fileName: foto.name, contentType: "image/jpeg", content: imageBytes);
    if (linkFoto != null) {
      sendMessage(linkFoto);
    }
  }

  void setAgenteDesejaAguardarSAC() {
    state.value = state.value.setAgenteDesejaAguardarSAC(
      !state.value.agenteDesejaAguardarSAC,
    );
  }

  void setExibirBotaoEsperarSAC() {
    state.value = state.value.setExibirBotaoEsperarSAC(true);
  }

  Future verificarDistancia(ClienteNew? endereco) async {
    final config = await ConfigDatabase.instance.getConfig();

    if (config.sacsAutomaticoNoLocalPermitidos != []) {
      if (!config.sacsAutomaticoNoLocalPermitidos
          .contains(state.value.idStatusAtividade)) {
        return;
      }
    }

    state.value = state.value.setLoadingLocalizacao(true);

    final liberar = config.ativarSacAutomaticoNoLocal;
    if (!liberar) {
      state.value = state.value.setLoadingLocalizacao(false);
      return;
    }

    PositionDataLocation? latLngAgente = await GpsHelper.instance.updateLoc();
    if (latLngAgente == null) {
      state.value = state.value.setLoadingLocalizacao(false);
      return;
    }

    LatLng? latLngCliente = LatLng(endereco?.lat ?? latLngAgente.latitude,
        endereco?.long ?? latLngAgente.longitude);

    final distance = Geolocator.distanceBetween(
      latLngAgente.latitude,
      latLngAgente.longitude,
      latLngCliente.latitude,
      latLngCliente.longitude,
    );
    final distanciaMaxima = config.sacDistanciaMinimaParaSerAtendimento;
    if (config.sacDistanciaMinimaParaSerAtendimento == 0 ||
        distance < distanciaMaxima) {
      setExibirBotaoEsperarSAC();
    }
    state.value = state.value.setLoadingLocalizacao(false);
  }

  Future<void> _upLoadFotoGenerica(
    XFile fotoInicio,
    String origem, [
    bool popUp = false,
  ]) async {
    final off = OfflineRequest.novo(
      enviado: false,
      body: {
        "Foto": null,
        "Origem": origem,
        "DataHora": DateTime.now().dataHoraServidorFomart.toIso8601String(),
        "IDOS": [state.value.idos],
      },
      endpoint: '/atividades/fotos',
      fileBytes: await fotoInicio.readAsBytes(),
      idAtividade: state.value.idos,
      fileName: fotoInicio.name,
      headers: {},
      method: 'POST',
      fileLink: null,
    );
    await OfflineRequestDatabase.instance.addData(off);
    if (popUp) {
      asuka.AsukaSnackbar.success("A foto foi salva com sucesso!").show();
    }
  }

  Future<void> upLoadFotoSac(XFile foto) async {
    await _upLoadFotoGenerica(foto, "SAC");
  }
}
