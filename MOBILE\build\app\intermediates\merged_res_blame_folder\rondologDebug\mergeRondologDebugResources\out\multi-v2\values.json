{"logs": [{"outputFile": "com.octalog.app-mergeRondologDebugResources-68:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e22c70eb4700b3262896da0db5fa32c7\\transformed\\jetified-in-app-messages-5.1.29\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,231", "endColumns": "175,84", "endOffsets": "226,311"}, "to": {"startLines": "445,446", "startColumns": "4,4", "startOffsets": "28256,28432", "endColumns": "175,84", "endOffsets": "28427,28512"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\504a3b9bc759ca6028567aaea36c5498\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "331,347,375,3137,3142", "startColumns": "4,4,4,4,4", "startOffsets": "20136,20890,22365,177937,178107", "endLines": "331,347,375,3141,3145", "endColumns": "56,64,63,24,24", "endOffsets": "20188,20950,22424,178102,178251"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\23aabf00f3a34e0b600bc8ec6a919265\\transformed\\work-runtime-2.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "64,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "2266,2331,2401,2465", "endColumns": "64,69,63,60", "endOffsets": "2326,2396,2460,2521"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\87eca1956ddb86a2a7193da8df59556f\\transformed\\jetified-activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "344,372", "startColumns": "4,4", "startOffsets": "20746,22201", "endColumns": "41,59", "endOffsets": "20783,22256"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\07660f349a2fe95c07a6bc7026689012\\transformed\\jetified-play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "99,100,101,102,103,104,105,106,420,421,422,423,424,425,426,427,429,430,431,432,433,434,435,436,437,3231,3647", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4776,4866,4946,5036,5126,5206,5287,5367,25463,25568,25749,25874,25981,26161,26284,26400,26670,26858,26963,27144,27269,27444,27592,27655,27717,180650,194188", "endLines": "99,100,101,102,103,104,105,106,420,421,422,423,424,425,426,427,429,430,431,432,433,434,435,436,437,3243,3665", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4861,4941,5031,5121,5201,5282,5362,5442,25563,25744,25869,25976,26156,26279,26395,26498,26853,26958,27139,27264,27439,27587,27650,27712,27791,180960,194600"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\52f643eeb6fba9363586e00d8d04b656\\transformed\\jetified-location-5.1.29\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,127", "endColumns": "71,93", "endOffsets": "122,216"}, "to": {"startLines": "447,448", "startColumns": "4,4", "startOffsets": "28517,28589", "endColumns": "71,93", "endOffsets": "28584,28678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cdc1fd5a4f8cb18094ee01b408d70e6e\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "5,28,29,60,61,62,68,69,70,71,72,73,74,77,78,79,80,81,82,83,84,85,86,91,92,107,108,109,110,111,112,113,114,115,116,118,119,120,121,122,123,124,125,126,127,128,129,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,246,247,251,252,253,254,255,256,257,283,284,285,286,287,288,289,290,326,327,328,329,334,342,343,348,370,376,377,378,379,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,458,463,464,465,466,467,468,476,477,481,485,496,501,507,514,518,522,527,531,535,539,543,547,551,557,561,567,571,577,581,586,590,593,597,603,607,613,617,623,626,630,634,638,642,646,647,648,649,652,655,658,661,665,666,667,668,669,672,674,676,678,683,684,688,694,698,699,701,713,714,718,724,728,729,730,734,761,765,766,770,798,970,996,1167,1193,1224,1232,1238,1254,1276,1281,1286,1296,1305,1314,1318,1325,1344,1351,1352,1361,1364,1367,1371,1375,1379,1382,1383,1388,1393,1403,1408,1415,1421,1422,1425,1429,1434,1436,1438,1441,1444,1446,1450,1453,1460,1463,1466,1470,1472,1476,1478,1480,1482,1486,1494,1502,1514,1520,1529,1532,1543,1546,1547,1552,1553,1590,1659,1729,1730,1740,1749,1901,1903,1907,1910,1913,1916,1919,1922,1925,1928,1932,1935,1938,1941,1945,1948,1952,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1978,1980,1981,1982,1983,1984,1985,1986,1987,1989,1990,1992,1993,1995,1997,1998,2000,2001,2002,2003,2004,2005,2007,2008,2009,2010,2011,2023,2025,2027,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2041,2043,2044,2045,2046,2047,2048,2049,2051,2055,2067,2068,2069,2070,2071,2072,2076,2077,2078,2079,2081,2083,2085,2087,2089,2090,2091,2092,2094,2096,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2112,2113,2114,2115,2117,2119,2120,2122,2123,2125,2127,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2142,2143,2144,2145,2147,2148,2149,2150,2151,2153,2155,2157,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2179,2254,2257,2260,2263,2277,2294,2336,2339,2368,2395,2404,2468,2836,2885,2923,3061,3185,3209,3215,3244,3265,3389,3417,3423,3567,3599,3666,3737,3837,3857,3912,3924,3950", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "277,943,988,2035,2076,2131,2526,2590,2660,2721,2796,2872,2949,3187,3272,3354,3430,3506,3583,3661,3767,3873,3952,4281,4338,5447,5521,5596,5661,5727,5787,5848,5920,5993,6060,6185,6244,6303,6362,6421,6480,6534,6588,6641,6695,6749,6803,7058,7132,7211,7284,7358,7429,7501,7573,7646,7703,7761,7834,7908,7982,8057,8129,8202,8272,8343,8403,8464,8533,8602,8672,8746,8822,8886,8963,9039,9116,9181,9250,9327,9402,9471,9539,9616,9682,9743,9840,9905,9974,10073,10144,10203,10261,10318,10377,10441,10512,10584,10656,10728,10800,10867,10935,11003,11062,11125,11189,11279,11370,11430,11496,11563,11629,11699,11763,11816,11883,11944,12011,12124,12182,12245,12310,12375,12450,12523,12595,12639,12686,12732,12781,12842,12903,12964,13026,13090,13154,13218,13283,13346,13406,13467,13533,13592,13652,13714,13785,13845,14714,14800,15050,15140,15227,15315,15397,15480,15570,17295,17347,17405,17450,17516,17580,17637,17694,19871,19928,19976,20025,20280,20650,20697,20955,22126,22429,22493,22555,22615,22936,23010,23080,23158,23212,23282,23367,23415,23461,23522,23585,23651,23715,23786,23849,23914,23978,24039,24100,24152,24225,24299,24368,24443,24517,24591,24732,29315,29676,29754,29844,29932,30028,30118,30700,30789,31036,31317,31983,32268,32661,33138,33360,33582,33858,34085,34315,34545,34775,35005,35232,35651,35877,36302,36532,36960,37179,37462,37670,37801,38028,38454,38679,39106,39327,39752,39872,40148,40449,40773,41064,41378,41515,41646,41751,41993,42160,42364,42572,42843,42955,43067,43172,43289,43503,43649,43789,43875,44223,44311,44557,44975,45224,45306,45404,46061,46161,46413,46837,47092,47186,47275,47512,49536,49778,49880,50133,52289,62970,64486,75181,76709,78466,79092,79512,80773,82038,82294,82530,83077,83571,84176,84374,84954,86322,86697,86815,87353,87510,87706,87979,88235,88405,88546,88610,88975,89342,90018,90282,90620,90973,91067,91253,91559,91821,91946,92073,92312,92523,92642,92835,93012,93467,93648,93770,94029,94142,94329,94431,94538,94667,94942,95450,95946,96823,97117,97687,97836,98568,98740,98824,99160,99252,101636,106867,112238,112300,112878,113462,121409,121522,121751,121911,122063,122234,122400,122569,122736,122899,123142,123312,123485,123656,123930,124129,124334,124664,124748,124844,124940,125038,125138,125240,125342,125444,125546,125648,125748,125844,125956,126085,126208,126339,126470,126568,126682,126776,126916,127050,127146,127258,127358,127474,127570,127682,127782,127922,128058,128222,128352,128510,128660,128801,128945,129080,129192,129342,129470,129598,129734,129866,129996,130126,130238,131136,131282,131426,131564,131630,131720,131796,131900,131990,132092,132200,132308,132408,132488,132580,132678,132788,132840,132918,133024,133116,133220,133330,133452,133615,134182,134262,134362,134452,134562,134652,134893,134987,135093,135185,135285,135397,135511,135627,135743,135837,135951,136063,136165,136285,136407,136489,136593,136713,136839,136937,137031,137119,137231,137347,137469,137581,137756,137872,137958,138050,138162,138286,138353,138479,138547,138675,138819,138947,139016,139111,139226,139339,139438,139547,139658,139769,139870,139975,140075,140205,140296,140419,140513,140625,140711,140815,140911,140999,141117,141221,141325,141451,141539,141647,141747,141837,141947,142031,142133,142217,142271,142335,142441,142527,142637,142721,143125,145741,145859,145974,146054,146415,147001,148405,148483,149827,151188,151576,154419,164657,166320,167991,174804,179105,179856,180118,180965,181344,185622,186476,186705,191313,192653,194605,197005,201129,201873,204004,204344,205655", "endLines": "5,28,29,60,61,62,68,69,70,71,72,73,74,77,78,79,80,81,82,83,84,85,86,91,92,107,108,109,110,111,112,113,114,115,116,118,119,120,121,122,123,124,125,126,127,128,129,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,246,247,251,252,253,254,255,256,257,283,284,285,286,287,288,289,290,326,327,328,329,334,342,343,348,370,376,377,378,379,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,458,463,464,465,466,467,475,476,480,484,488,500,506,513,517,521,526,530,534,538,542,546,550,556,560,566,570,576,580,585,589,592,596,602,606,612,616,622,625,629,633,637,641,645,646,647,648,651,654,657,660,664,665,666,667,668,671,673,675,677,682,683,687,693,697,698,700,712,713,717,723,727,728,729,733,760,764,765,769,797,969,995,1166,1192,1223,1231,1237,1253,1275,1280,1285,1295,1304,1313,1317,1324,1343,1350,1351,1360,1363,1366,1370,1374,1378,1381,1382,1387,1392,1402,1407,1414,1420,1421,1424,1428,1433,1435,1437,1440,1443,1445,1449,1452,1459,1462,1465,1469,1471,1475,1477,1479,1481,1485,1493,1501,1513,1519,1528,1531,1542,1545,1546,1551,1552,1557,1658,1728,1729,1739,1748,1749,1902,1906,1909,1912,1915,1918,1921,1924,1927,1931,1934,1937,1940,1944,1947,1951,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1977,1979,1980,1981,1982,1983,1984,1985,1986,1988,1989,1991,1992,1994,1996,1997,1999,2000,2001,2002,2003,2004,2006,2007,2008,2009,2010,2011,2024,2026,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2042,2043,2044,2045,2046,2047,2048,2050,2054,2058,2067,2068,2069,2070,2071,2075,2076,2077,2078,2080,2082,2084,2086,2088,2089,2090,2091,2093,2095,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2111,2112,2113,2114,2116,2118,2119,2121,2122,2124,2126,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2141,2142,2143,2144,2146,2147,2148,2149,2150,2152,2154,2156,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2253,2256,2259,2262,2276,2282,2303,2338,2367,2394,2403,2467,2830,2839,2912,2950,3078,3208,3214,3220,3264,3388,3408,3422,3426,3572,3633,3677,3802,3856,3911,3923,3949,3956", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "327,983,1032,2071,2126,2188,2585,2655,2716,2791,2867,2944,3022,3267,3349,3425,3501,3578,3656,3762,3868,3947,4027,4333,4391,5516,5591,5656,5722,5782,5843,5915,5988,6055,6123,6239,6298,6357,6416,6475,6529,6583,6636,6690,6744,6798,6852,7127,7206,7279,7353,7424,7496,7568,7641,7698,7756,7829,7903,7977,8052,8124,8197,8267,8338,8398,8459,8528,8597,8667,8741,8817,8881,8958,9034,9111,9176,9245,9322,9397,9466,9534,9611,9677,9738,9835,9900,9969,10068,10139,10198,10256,10313,10372,10436,10507,10579,10651,10723,10795,10862,10930,10998,11057,11120,11184,11274,11365,11425,11491,11558,11624,11694,11758,11811,11878,11939,12006,12119,12177,12240,12305,12370,12445,12518,12590,12634,12681,12727,12776,12837,12898,12959,13021,13085,13149,13213,13278,13341,13401,13462,13528,13587,13647,13709,13780,13840,13908,14795,14882,15135,15222,15310,15392,15475,15565,15656,17342,17400,17445,17511,17575,17632,17689,17743,19923,19971,20020,20071,20309,20692,20741,20996,22153,22488,22550,22610,22667,23005,23075,23153,23207,23277,23362,23410,23456,23517,23580,23646,23710,23781,23844,23909,23973,24034,24095,24147,24220,24294,24363,24438,24512,24586,24727,24797,29363,29749,29839,29927,30023,30113,30695,30784,31031,31312,31564,32263,32656,33133,33355,33577,33853,34080,34310,34540,34770,35000,35227,35646,35872,36297,36527,36955,37174,37457,37665,37796,38023,38449,38674,39101,39322,39747,39867,40143,40444,40768,41059,41373,41510,41641,41746,41988,42155,42359,42567,42838,42950,43062,43167,43284,43498,43644,43784,43870,44218,44306,44552,44970,45219,45301,45399,46056,46156,46408,46832,47087,47181,47270,47507,49531,49773,49875,50128,52284,62965,64481,75176,76704,78461,79087,79507,80768,82033,82289,82525,83072,83566,84171,84369,84949,86317,86692,86810,87348,87505,87701,87974,88230,88400,88541,88605,88970,89337,90013,90277,90615,90968,91062,91248,91554,91816,91941,92068,92307,92518,92637,92830,93007,93462,93643,93765,94024,94137,94324,94426,94533,94662,94937,95445,95941,96818,97112,97682,97831,98563,98735,98819,99155,99247,99525,106862,112233,112295,112873,113457,113548,121517,121746,121906,122058,122229,122395,122564,122731,122894,123137,123307,123480,123651,123925,124124,124329,124659,124743,124839,124935,125033,125133,125235,125337,125439,125541,125643,125743,125839,125951,126080,126203,126334,126465,126563,126677,126771,126911,127045,127141,127253,127353,127469,127565,127677,127777,127917,128053,128217,128347,128505,128655,128796,128940,129075,129187,129337,129465,129593,129729,129861,129991,130121,130233,130373,131277,131421,131559,131625,131715,131791,131895,131985,132087,132195,132303,132403,132483,132575,132673,132783,132835,132913,133019,133111,133215,133325,133447,133610,133767,134257,134357,134447,134557,134647,134888,134982,135088,135180,135280,135392,135506,135622,135738,135832,135946,136058,136160,136280,136402,136484,136588,136708,136834,136932,137026,137114,137226,137342,137464,137576,137751,137867,137953,138045,138157,138281,138348,138474,138542,138670,138814,138942,139011,139106,139221,139334,139433,139542,139653,139764,139865,139970,140070,140200,140291,140414,140508,140620,140706,140810,140906,140994,141112,141216,141320,141446,141534,141642,141742,141832,141942,142026,142128,142212,142266,142330,142436,142522,142632,142716,142836,145736,145854,145969,146049,146410,146643,147513,148478,149822,151183,151571,154414,164467,164787,167685,169343,175371,179851,180113,180313,181339,185617,186223,186700,186851,191523,193731,194912,200026,201868,203999,204339,205650,205853"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1f37faca982b225960866422b14b0e48\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "373", "startColumns": "4", "startOffsets": "22261", "endColumns": "53", "endOffsets": "22310"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\765cbe31314e92111ddb6ec950f59a6b\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "4,2176,2951,2957", "startColumns": "4,4,4,4", "startOffsets": "216,142980,169348,169559", "endLines": "4,2178,2956,3040", "endColumns": "60,12,24,24", "endOffsets": "272,143120,169554,174070"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1a36ee1e87beff061ef9f2c053c4f171\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "87,88,89,90,234,235,439,441,442,443", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "4032,4090,4156,4219,13913,13984,27834,27959,28026,28105", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "4085,4151,4214,4276,13979,14051,27897,28021,28100,28169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c4042ec9e8cc7d9cdf61197d11f01375\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "411", "startColumns": "4", "startOffsets": "24802", "endColumns": "82", "endOffsets": "24880"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\73db24a2829d8fb9cf5663cfbb96cfeb\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,6,12,20,31,43,49,55,56,57,58,59,330,2283,2289,3678,3686,3701", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,332,505,724,1097,1411,1599,1786,1839,1899,1951,1996,20076,146648,146843,194917,195199,195813", "endLines": "2,11,19,27,42,48,54,55,56,57,58,59,330,2288,2293,3685,3700,3716", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,500,719,938,1406,1594,1781,1834,1894,1946,1991,2030,20131,146838,146996,195194,195808,196462"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\54b8830a716a856dc674cceb64efb5c6\\transformed\\jetified-android-pdf-viewer-3.2.0-beta.3\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "7", "endColumns": "24", "endOffsets": "380"}, "to": {"startLines": "3593", "startColumns": "4", "startOffsets": "192323", "endLines": "3598", "endColumns": "24", "endOffsets": "192648"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0df03071d3ea5a13ca8a701a85da690b\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "374", "startColumns": "4", "startOffsets": "22315", "endColumns": "49", "endOffsets": "22360"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fec79dfcef552889e3835cc7bab572a\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "63,132,276,277,278,279,280,281,282,339,340,341,381,382,438,440,449,455,460,461,462,1558,1750,1753,1759,1765,1768,1774,1778,1781,1788,1794,1797,1803,1808,1813,1820,1822,1828,1834,1842,1847,1854,1859,1865,1869,1876,1880,1886,1892,1895,1899,1900,2831,2874,3041,3079,3221,3409,3427,3491,3501,3511,3518,3524,3634,3803,3820", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2193,6989,16862,16926,16981,17049,17116,17181,17238,20493,20541,20589,22740,22803,27796,27902,28683,29175,29439,29578,29628,99530,113553,113658,113903,114241,114387,114727,114939,115102,115509,115847,115970,116309,116548,116805,117176,117236,117574,117860,118309,118601,118989,119294,119638,119883,120213,120420,120688,120961,121105,121306,121353,164472,165919,174075,175376,180318,186228,186856,188781,189063,189368,189630,189890,193736,200031,200561", "endLines": "63,132,276,277,278,279,280,281,282,339,340,341,381,382,438,440,449,457,460,461,462,1574,1752,1758,1764,1767,1773,1777,1780,1787,1793,1796,1802,1807,1812,1819,1821,1827,1833,1841,1846,1853,1858,1864,1868,1875,1879,1885,1891,1894,1898,1899,1900,2835,2884,3060,3082,3230,3416,3490,3500,3510,3517,3523,3566,3646,3819,3836", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2261,7053,16921,16976,17044,17111,17176,17233,17290,20536,20584,20645,22798,22861,27829,27954,28722,29310,29573,29623,29671,100963,113653,113898,114236,114382,114722,114934,115097,115504,115842,115965,116304,116543,116800,117171,117231,117569,117855,118304,118596,118984,119289,119633,119878,120208,120415,120683,120956,121100,121301,121348,121404,164652,166315,174799,175520,180645,186471,188776,189058,189363,189625,189885,191308,194183,200556,201124"}}, {"source": "C:\\projetos\\octa.log\\MOBILE\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1583,1587", "startColumns": "4,4", "startOffsets": "101286,101467", "endLines": "1586,1589", "endColumns": "12,12", "endOffsets": "101462,101631"}}, {"source": "C:\\projetos\\octa.log\\MOBILE\\android\\app\\src\\rondolog\\res\\values\\colors.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "56", "endOffsets": "107"}, "to": {"startLines": "117", "startColumns": "4", "startOffsets": "6128", "endColumns": "56", "endOffsets": "6180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f46bbd13206153774356ca6103aa890\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "30,75,76,93,94,130,131,239,240,241,242,243,244,245,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,336,337,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,383,413,414,415,416,417,418,419,459,2012,2013,2017,2018,2022,2174,2175,2840,2913,3083,3116,3146,3179", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1037,3027,3099,4396,4461,6857,6926,14226,14296,14364,14436,14506,14567,14641,15884,15945,16006,16068,16132,16194,16255,16323,16423,16483,16549,16622,16691,16748,16800,17748,17820,17896,17961,18020,18079,18139,18199,18259,18319,18379,18439,18499,18559,18619,18679,18738,18798,18858,18918,18978,19038,19098,19158,19218,19278,19338,19397,19457,19517,19576,19635,19694,19753,19812,20380,20415,21001,21056,21119,21174,21232,21290,21351,21414,21471,21522,21572,21633,21690,21756,21790,21825,22866,24952,25019,25091,25160,25229,25303,25375,29368,130378,130495,130696,130806,131007,142841,142913,164792,167690,175525,177256,178256,178938", "endLines": "30,75,76,93,94,130,131,239,240,241,242,243,244,245,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,336,337,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,383,413,414,415,416,417,418,419,459,2012,2016,2017,2021,2022,2174,2175,2845,2922,3115,3136,3178,3184", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1092,3094,3182,4456,4522,6921,6984,14291,14359,14431,14501,14562,14636,14709,15940,16001,16063,16127,16189,16250,16318,16418,16478,16544,16617,16686,16743,16795,16857,17815,17891,17956,18015,18074,18134,18194,18254,18314,18374,18434,18494,18554,18614,18674,18733,18793,18853,18913,18973,19033,19093,19153,19213,19273,19333,19392,19452,19512,19571,19630,19689,19748,19807,19866,20410,20445,21051,21114,21169,21227,21285,21346,21409,21466,21517,21567,21628,21685,21751,21785,21820,21855,22931,25014,25086,25155,25224,25298,25370,25458,29434,130490,130691,130801,131002,131131,142908,142975,164990,167986,177251,177932,178933,179100"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f80248970efbd3bafd1721bfcda96536\\transformed\\jetified-firebase-messaging-24.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "444", "startColumns": "4", "startOffsets": "28174", "endColumns": "81", "endOffsets": "28251"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32b951ec29728b45cedd679ddbec605b\\transformed\\jetified-notifications-5.1.29\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,136", "endColumns": "80,82", "endOffsets": "131,214"}, "to": {"startLines": "450,451", "startColumns": "4,4", "startOffsets": "28727,28808", "endColumns": "80,82", "endOffsets": "28803,28886"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\31710a1d11dfb3018b4d584b1dcc524f\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "248,249,250,258,259,260,335,3573", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "14887,14946,14994,15661,15736,15812,20314,191528", "endLines": "248,249,250,258,259,260,335,3592", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "14941,14989,15045,15731,15807,15879,20375,192318"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2748b27b9a4960040fddad0a76748cc0\\transformed\\jetified-core-common-2.0.4\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "2059", "startColumns": "4", "startOffsets": "133772", "endLines": "2066", "endColumns": "8", "endOffsets": "134177"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2c52fddffb4a4858b65e09bc36907dcb\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,35,36,37,38,45,47,50,7", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,107,168,230,292,2179,2238,2295,2349,2763,2827,2953,356", "endLines": "2,3,4,5,6,35,36,37,44,46,49,52,34", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "102,163,225,287,351,2233,2290,2344,2758,2822,2948,3076,2174"}, "to": {"startLines": "3,95,96,97,98,236,237,238,489,1575,1577,1580,2846", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "164,4527,4588,4650,4712,14056,14115,14172,31569,100968,101032,101158,164995", "endLines": "3,95,96,97,98,236,237,238,495,1576,1579,1582,2873", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "211,4583,4645,4707,4771,14110,14167,14221,31978,101027,101153,101281,165914"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f6da13a9846494f8148c639e8691af2b\\transformed\\jetified-appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2304,2320,2326,3717,3733", "startColumns": "4,4,4,4,4", "startOffsets": "147518,147943,148121,196467,196878", "endLines": "2319,2325,2335,3732,3736", "endColumns": "24,24,24,24,24", "endOffsets": "147938,148116,148400,196873,197000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\952d4bb05156a6bae798ebc3d4f6b56b\\transformed\\jetified-core-5.1.29\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,180,263", "endColumns": "124,82,75", "endOffsets": "175,258,334"}, "to": {"startLines": "452,453,454", "startColumns": "4,4,4", "startOffsets": "28891,29016,29099", "endColumns": "124,82,75", "endOffsets": "29011,29094,29170"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cf636ef2f0738047fe8129f320ef339e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "380,428", "startColumns": "4,4", "startOffsets": "22672,26503", "endColumns": "67,166", "endOffsets": "22735,26665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\07d5ffcb9c6912ffd610dc84a8004380\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "371", "startColumns": "4", "startOffsets": "22158", "endColumns": "42", "endOffsets": "22196"}}, {"source": "C:\\projetos\\octa.log\\MOBILE\\build\\app\\generated\\res\\resValues\\rondolog\\debug\\values\\gradleResValues.xml", "from": {"startLines": "6", "startColumns": "4", "startOffsets": "168", "endColumns": "66", "endOffsets": "230"}, "to": {"startLines": "412", "startColumns": "4", "startOffsets": "24885", "endColumns": "66", "endOffsets": "24947"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5c90255c19f9c683385e871d79ee1ace\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "332,333,338,345,346,365,366,367,368,369", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "20193,20233,20450,20788,20843,21860,21914,21966,22015,22076", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "20228,20275,20488,20838,20885,21909,21961,22010,22071,22121"}}]}]}