1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.octalog.up360"
4    android:versionCode="30"
5    android:versionName="1.2.4" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:10:2-64
15-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:10:19-61
16    <!-- Add permissions for location access -->
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
17-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:6:5-78
17-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:6:22-76
18    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
18-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:7:5-80
18-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:7:22-78
19    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
19-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:9:2-75
19-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:9:19-73
20    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
20-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:11:2-76
20-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:11:19-73
21    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
21-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:11:2-76
21-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:11:19-73
22    <uses-permission android:name="android.permission.WAKE_LOCK" />
22-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:12:2-65
22-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:12:19-62
23    <uses-permission android:name="android.permission.CAMERA" />
23-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:13:2-62
23-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:13:19-59
24    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
24-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:14:2-72
24-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:14:19-69
25    <uses-permission android:name="android.permission.VIBRATE" />
25-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:15:2-63
25-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:15:19-60
26    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
26-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:16:2-74
26-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:16:19-71
27    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
27-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:17:2-78
27-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:17:19-75
28    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
28-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:18:2-76
28-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:18:19-73
29    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
29-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:19:2-73
29-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:19:19-71
30    <uses-permission android:name="android.permission.RECORD_AUDIO" />
30-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:20:2-68
30-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:20:19-65
31    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
31-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:22:2-76
31-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:22:19-73
32    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
32-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:23:2-77
32-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:23:19-74
33    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
33-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:24:2-78
33-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:24:19-75
34    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
34-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:25:2-77
34-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:25:19-74
35    <!--
36 <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
37		tools:ignore="ScopedStorage" /> 
38	<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
39	<uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
40	<uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
41    -->
42    <uses-feature android:name="android.hardware.camera" />
42-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:31:2-57
42-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:31:16-54
43    <uses-feature android:name="android.hardware.camera.autofocus" />
43-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:32:2-67
43-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:32:16-64
44    <!--
45 Required to query activities that can process text, see:
46         https://developer.android.com/training/package-visibility and
47         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
48
49         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
50    -->
51    <queries>
51-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:71:5-76:15
52        <intent>
52-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:72:9-75:18
53            <action android:name="android.intent.action.PROCESS_TEXT" />
53-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:73:13-72
53-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:73:21-70
54
55            <data android:mimeType="text/plain" />
55-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:74:13-50
55-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:74:19-48
56        </intent>
57        <intent>
57-->[:file_picker] C:\projetos\octa.log\MOBILE\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
58            <action android:name="android.intent.action.GET_CONTENT" />
58-->[:file_picker] C:\projetos\octa.log\MOBILE\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
58-->[:file_picker] C:\projetos\octa.log\MOBILE\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
59
60            <data android:mimeType="*/*" />
60-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:74:13-50
60-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:74:19-48
61        </intent>
62    </queries> <!-- Create a unique permission for your app and use it so only your app can receive your OneSignal messages. -->
63    <permission
63-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:10:5-12:47
64        android:name="com.octalog.up360.permission.C2D_MESSAGE"
64-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:11:9-63
65        android:protectionLevel="signature" />
65-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:12:9-44
66
67    <uses-permission android:name="com.octalog.up360.permission.C2D_MESSAGE" /> <!-- c2dm RECEIVE are basic requirements for push messages through Google's FCM -->
67-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:14:5-79
67-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:14:22-76
68    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" /> <!-- START: ShortcutBadger -->
68-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:19:5-82
68-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:19:22-79
69    <!-- Samsung -->
70    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
70-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:31:5-86
70-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:31:22-83
71    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
71-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:32:5-87
71-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:32:22-84
72    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
72-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:33:5-81
72-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:33:22-78
73    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
73-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:34:5-83
73-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:34:22-80
74    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
74-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:35:5-88
74-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:35:22-85
75    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
75-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:36:5-92
75-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:36:22-89
76    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
76-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:37:5-84
76-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:37:22-81
77    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
77-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:38:5-83
77-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:38:22-80
78    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
78-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:39:5-91
78-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:39:22-88
79    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
79-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:40:5-92
79-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:40:22-89
80    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- ZUK -->
80-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:41:5-93
80-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:41:22-90
81    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- OPPO -->
81-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:42:5-73
81-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:42:22-70
82    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
82-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:43:5-82
82-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:43:22-79
83    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- EvMe -->
83-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:44:5-83
83-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:44:22-80
84    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
84-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:45:5-88
84-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:45:22-85
85    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
85-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:46:5-89
85-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:46:22-86
86
87    <permission
87-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
88        android:name="com.octalog.up360.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
88-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
89        android:protectionLevel="signature" />
89-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
90
91    <uses-permission android:name="com.octalog.up360.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
91-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
91-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
92    <uses-permission
92-->[com.github.davidliu:audioswitch:89582c47c9a04c62f90aa5e57251af4800a62c9a] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\160f0e0b2f19cfe5268e794069515506\transformed\jetified-audioswitch-89582c47c9a04c62f90aa5e57251af4800a62c9a\AndroidManifest.xml:9:5-11:38
93        android:name="android.permission.BLUETOOTH"
93-->[com.github.davidliu:audioswitch:89582c47c9a04c62f90aa5e57251af4800a62c9a] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\160f0e0b2f19cfe5268e794069515506\transformed\jetified-audioswitch-89582c47c9a04c62f90aa5e57251af4800a62c9a\AndroidManifest.xml:10:9-52
94        android:maxSdkVersion="30" />
94-->[com.github.davidliu:audioswitch:89582c47c9a04c62f90aa5e57251af4800a62c9a] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\160f0e0b2f19cfe5268e794069515506\transformed\jetified-audioswitch-89582c47c9a04c62f90aa5e57251af4800a62c9a\AndroidManifest.xml:11:9-35
95
96    <application
97        android:name="android.app.Application"
98        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
98-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
99        android:debuggable="true"
100        android:extractNativeLibs="true"
101        android:icon="@mipmap/launcher_icon"
102        android:label="@string/app_name" >
103        <activity
104            android:name="com.octalog.MainActivity"
105            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
106            android:exported="true"
107            android:hardwareAccelerated="true"
108            android:launchMode="singleTop"
109            android:taskAffinity=""
110            android:theme="@style/LaunchTheme"
111            android:windowSoftInputMode="adjustResize" >
112
113            <!--
114                 Specifies an Android theme to apply to this Activity as soon as
115                 the Android process has started. This theme is visible to the user
116                 while the Flutter UI initializes. After that, this theme continues
117                 to determine the Window background behind the Flutter UI.
118            -->
119            <meta-data
120                android:name="io.flutter.embedding.android.NormalTheme"
121                android:resource="@style/NormalTheme" />
122
123            <intent-filter>
124                <action android:name="android.intent.action.MAIN" />
125
126                <category android:name="android.intent.category.LAUNCHER" />
127            </intent-filter>
128        </activity>
129        <!--
130             Don't delete the meta-data below.
131             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
132        -->
133        <meta-data
134            android:name="flutterEmbedding"
135            android:value="2" />
136
137        <receiver android:name="com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentReceiver" />
137-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-119
137-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:19-116
138
139        <service
139-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-14:72
140            android:name="com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService"
140-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-109
141            android:enabled="true"
141-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-35
142            android:exported="false"
142-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
143            android:permission="android.permission.BIND_JOB_SERVICE" />
143-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-69
144        <service
144-->[:geolocator_android] C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
145            android:name="com.baseflow.geolocator.GeolocatorLocationService"
145-->[:geolocator_android] C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
146            android:enabled="true"
146-->[:geolocator_android] C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
147            android:exported="false"
147-->[:geolocator_android] C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
148            android:foregroundServiceType="location" />
148-->[:geolocator_android] C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
149        <service
149-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
150            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
150-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
151            android:exported="false"
151-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
152            android:permission="android.permission.BIND_JOB_SERVICE" />
152-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
153        <service
153-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
154            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
154-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
155            android:exported="false" >
155-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
156            <intent-filter>
156-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
157                <action android:name="com.google.firebase.MESSAGING_EVENT" />
157-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
157-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
158            </intent-filter>
159        </service>
160
161        <receiver
161-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
162            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
162-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
163            android:exported="true"
163-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
164            android:permission="com.google.android.c2dm.permission.SEND" >
164-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
165            <intent-filter>
165-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
166                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
166-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
166-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
167            </intent-filter>
168        </receiver>
169
170        <service
170-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
171            android:name="com.google.firebase.components.ComponentDiscoveryService"
171-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:18-89
172            android:directBootAware="true"
172-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
173            android:exported="false" >
173-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
174            <meta-data
174-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
175                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
175-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
176                android:value="com.google.firebase.components.ComponentRegistrar" />
176-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
177            <meta-data
177-->[:firebase_remote_config] C:\projetos\octa.log\MOBILE\build\firebase_remote_config\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
178                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firebaseremoteconfig.FlutterFirebaseAppRegistrar"
178-->[:firebase_remote_config] C:\projetos\octa.log\MOBILE\build\firebase_remote_config\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-139
179                android:value="com.google.firebase.components.ComponentRegistrar" />
179-->[:firebase_remote_config] C:\projetos\octa.log\MOBILE\build\firebase_remote_config\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
180            <meta-data
180-->[:firebase_core] C:\projetos\octa.log\MOBILE\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
181                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
181-->[:firebase_core] C:\projetos\octa.log\MOBILE\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
182                android:value="com.google.firebase.components.ComponentRegistrar" />
182-->[:firebase_core] C:\projetos\octa.log\MOBILE\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
183            <meta-data
183-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
184                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
184-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
185                android:value="com.google.firebase.components.ComponentRegistrar" />
185-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
186            <meta-data
186-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
187                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
187-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
188                android:value="com.google.firebase.components.ComponentRegistrar" />
188-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
189            <meta-data
189-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:29:13-31:85
190                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar"
190-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:30:17-128
191                android:value="com.google.firebase.components.ComponentRegistrar" />
191-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:31:17-82
192            <meta-data
192-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:32:13-34:85
193                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
193-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:33:17-117
194                android:value="com.google.firebase.components.ComponentRegistrar" />
194-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:34:17-82
195            <meta-data
195-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
196                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
196-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
197                android:value="com.google.firebase.components.ComponentRegistrar" />
197-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
198            <meta-data
198-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
199                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
199-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
200                android:value="com.google.firebase.components.ComponentRegistrar" />
200-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
201            <meta-data
201-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcc8d2985ec1ab09d1e21c43fe50ab02\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
202                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
202-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcc8d2985ec1ab09d1e21c43fe50ab02\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
203                android:value="com.google.firebase.components.ComponentRegistrar" />
203-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcc8d2985ec1ab09d1e21c43fe50ab02\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
204            <meta-data
204-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
205                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
205-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
206                android:value="com.google.firebase.components.ComponentRegistrar" />
206-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
207            <meta-data
207-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf6a27b1025e5f977c0f155cbd2c4f7c\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
208                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
208-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf6a27b1025e5f977c0f155cbd2c4f7c\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
209                android:value="com.google.firebase.components.ComponentRegistrar" />
209-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf6a27b1025e5f977c0f155cbd2c4f7c\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
210            <meta-data
210-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e32c06b9e94e0afe422db7a8cc6919d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
211                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
211-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e32c06b9e94e0afe422db7a8cc6919d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
212                android:value="com.google.firebase.components.ComponentRegistrar" />
212-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e32c06b9e94e0afe422db7a8cc6919d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
213        </service>
214
215        <provider
215-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
216            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
216-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
217            android:authorities="com.octalog.up360.flutterfirebasemessaginginitprovider"
217-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
218            android:exported="false"
218-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
219            android:initOrder="99" />
219-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
220        <provider
220-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
221            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
221-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
222            android:authorities="com.octalog.up360.flutter.image_provider"
222-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
223            android:exported="false"
223-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
224            android:grantUriPermissions="true" >
224-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
225            <meta-data
225-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
226                android:name="android.support.FILE_PROVIDER_PATHS"
226-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
227                android:resource="@xml/flutter_image_picker_file_paths" />
227-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
228        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
229        <service
229-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
230            android:name="com.google.android.gms.metadata.ModuleDependencies"
230-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
231            android:enabled="false"
231-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
232            android:exported="false" >
232-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
233            <intent-filter>
233-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
234                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
234-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
234-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
235            </intent-filter>
236
237            <meta-data
237-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
238                android:name="photopicker_activity:0:required"
238-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
239                android:value="" />
239-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
240        </service>
241
242        <provider
242-->[:open_file_android] C:\projetos\octa.log\MOBILE\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-19:20
243            android:name="com.crazecoder.openfile.FileProvider"
243-->[:open_file_android] C:\projetos\octa.log\MOBILE\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
244            android:authorities="com.octalog.up360.fileProvider.com.crazecoder.openfile"
244-->[:open_file_android] C:\projetos\octa.log\MOBILE\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-88
245            android:exported="false"
245-->[:open_file_android] C:\projetos\octa.log\MOBILE\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
246            android:grantUriPermissions="true"
246-->[:open_file_android] C:\projetos\octa.log\MOBILE\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
247            android:requestLegacyExternalStorage="true" >
247-->[:open_file_android] C:\projetos\octa.log\MOBILE\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-56
248            <meta-data
248-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
249                android:name="android.support.FILE_PROVIDER_PATHS"
249-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
250                android:resource="@xml/filepaths" />
250-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
251        </provider>
252
253        <activity
253-->[:url_launcher_android] C:\projetos\octa.log\MOBILE\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
254            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
254-->[:url_launcher_android] C:\projetos\octa.log\MOBILE\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
255            android:exported="false"
255-->[:url_launcher_android] C:\projetos\octa.log\MOBILE\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
256            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
256-->[:url_launcher_android] C:\projetos\octa.log\MOBILE\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
257        <!--
258        Service for holding metadata. Cannot be instantiated.
259        Metadata will be merged from other manifests.
260        -->
261        <service
261-->[androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41ce894b414f4e410f366a4a6ad7589\transformed\jetified-camera-core-1.3.4\AndroidManifest.xml:29:9-33:78
262            android:name="androidx.camera.core.impl.MetadataHolderService"
262-->[androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41ce894b414f4e410f366a4a6ad7589\transformed\jetified-camera-core-1.3.4\AndroidManifest.xml:30:13-75
263            android:enabled="false"
263-->[androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41ce894b414f4e410f366a4a6ad7589\transformed\jetified-camera-core-1.3.4\AndroidManifest.xml:31:13-36
264            android:exported="false" >
264-->[androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41ce894b414f4e410f366a4a6ad7589\transformed\jetified-camera-core-1.3.4\AndroidManifest.xml:32:13-37
265            <meta-data
265-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a600f391fc985fb6fe77353ae0360c6c\transformed\jetified-camera-camera2-1.3.4\AndroidManifest.xml:30:13-32:89
266                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
266-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a600f391fc985fb6fe77353ae0360c6c\transformed\jetified-camera-camera2-1.3.4\AndroidManifest.xml:31:17-103
267                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
267-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a600f391fc985fb6fe77353ae0360c6c\transformed\jetified-camera-camera2-1.3.4\AndroidManifest.xml:32:17-86
268        </service>
269        <service
269-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:9:9-15:19
270            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
270-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:10:13-91
271            android:directBootAware="true"
271-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:17:13-43
272            android:exported="false" >
272-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:11:13-37
273            <meta-data
273-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:12:13-14:85
274                android:name="com.google.firebase.components:com.google.mlkit.vision.face.internal.FaceRegistrar"
274-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:13:17-114
275                android:value="com.google.firebase.components.ComponentRegistrar" />
275-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:14:17-82
276            <meta-data
276-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\087b3f5fae32c8257c73d14fa1c12986\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
277                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
277-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\087b3f5fae32c8257c73d14fa1c12986\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
278                android:value="com.google.firebase.components.ComponentRegistrar" />
278-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\087b3f5fae32c8257c73d14fa1c12986\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
279            <meta-data
279-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70809b849f79bdfe99712e94237d768f\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
280                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
280-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70809b849f79bdfe99712e94237d768f\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
281                android:value="com.google.firebase.components.ComponentRegistrar" />
281-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70809b849f79bdfe99712e94237d768f\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
282            <meta-data
282-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:20:13-22:85
283                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
283-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:21:17-120
284                android:value="com.google.firebase.components.ComponentRegistrar" />
284-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:22:17-82
285        </service>
286
287        <provider
287-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:9:9-13:38
288            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
288-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:10:13-78
289            android:authorities="com.octalog.up360.mlkitinitprovider"
289-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:11:13-69
290            android:exported="false"
290-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:12:13-37
291            android:initOrder="99" />
291-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:13:13-35
292
293        <receiver
293-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:49:9-60:20
294            android:name="com.onesignal.notifications.receivers.FCMBroadcastReceiver"
294-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:50:13-86
295            android:exported="true"
295-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:51:13-36
296            android:permission="com.google.android.c2dm.permission.SEND" >
296-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:52:13-73
297
298            <!-- High priority so OneSignal payloads can be filtered from other FCM receivers -->
299            <intent-filter android:priority="999" >
299-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:55:13-59:29
299-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:55:28-50
300                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
300-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
300-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
301
302                <category android:name="com.octalog.up360" />
302-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:58:17-61
302-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:58:27-58
303            </intent-filter>
304        </receiver>
305
306        <service
306-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:62:9-68:19
307            android:name="com.onesignal.notifications.services.HmsMessageServiceOneSignal"
307-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:63:13-91
308            android:exported="false" >
308-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:64:13-37
309            <intent-filter>
309-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:65:13-67:29
310                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
310-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:66:17-81
310-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:66:25-78
311            </intent-filter>
312        </service> <!-- CAUTION: OneSignal backend includes the activity name in the payload, modifying the name without sync may result in notification click not firing -->
313        <activity
313-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:69:9-77:20
314            android:name="com.onesignal.NotificationOpenedActivityHMS"
314-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:70:13-71
315            android:exported="true"
315-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:71:13-36
316            android:noHistory="true"
316-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:72:13-37
317            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
317-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:73:13-72
318            <intent-filter>
318-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:74:13-76:29
319                <action android:name="android.intent.action.VIEW" />
319-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:75:17-69
319-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:75:25-66
320            </intent-filter>
321        </activity>
322
323        <receiver
323-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:79:9-81:39
324            android:name="com.onesignal.notifications.receivers.NotificationDismissReceiver"
324-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:80:13-93
325            android:exported="true" />
325-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:81:13-36
326        <receiver
326-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:82:9-89:20
327            android:name="com.onesignal.notifications.receivers.BootUpReceiver"
327-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:83:13-80
328            android:exported="true" >
328-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:84:13-36
329            <intent-filter>
329-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:85:13-88:29
330                <action android:name="android.intent.action.BOOT_COMPLETED" />
330-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:86:17-79
330-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:86:25-76
331                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
331-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:87:17-82
331-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:87:25-79
332            </intent-filter>
333        </receiver>
334        <receiver
334-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:90:9-96:20
335            android:name="com.onesignal.notifications.receivers.UpgradeReceiver"
335-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:91:13-81
336            android:exported="true" >
336-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:92:13-36
337            <intent-filter>
337-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:93:13-95:29
338                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
338-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:94:17-84
338-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:94:25-81
339            </intent-filter>
340        </receiver>
341
342        <activity
342-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:98:9-104:75
343            android:name="com.onesignal.notifications.activities.NotificationOpenedActivity"
343-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:99:13-93
344            android:excludeFromRecents="true"
344-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:100:13-46
345            android:exported="true"
345-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:101:13-36
346            android:noHistory="true"
346-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:102:13-37
347            android:taskAffinity=""
347-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:103:13-36
348            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
348-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:104:13-72
349        <activity
349-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:105:9-110:75
350            android:name="com.onesignal.notifications.activities.NotificationOpenedActivityAndroid22AndOlder"
350-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:106:13-110
351            android:excludeFromRecents="true"
351-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:107:13-46
352            android:exported="true"
352-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:108:13-36
353            android:noHistory="true"
353-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:109:13-37
354            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
354-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:110:13-72
355
356        <service
356-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:13:9-16:72
357            android:name="com.onesignal.core.services.SyncJobService"
357-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:14:13-70
358            android:exported="false"
358-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:15:13-37
359            android:permission="android.permission.BIND_JOB_SERVICE" />
359-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:16:13-69
360
361        <activity
361-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:18:9-21:75
362            android:name="com.onesignal.core.activities.PermissionsActivity"
362-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:19:13-77
363            android:exported="false"
363-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:20:13-37
364            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
364-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:21:13-72
365
366        <receiver
366-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
367            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
367-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
368            android:exported="true"
368-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
369            android:permission="com.google.android.c2dm.permission.SEND" >
369-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
370            <intent-filter>
370-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
371                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
371-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
371-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
372            </intent-filter>
373
374            <meta-data
374-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
375                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
375-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
376                android:value="true" />
376-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
377        </receiver>
378        <!--
379             FirebaseMessagingService performs security checks at runtime,
380             but set to not exported to explicitly avoid allowing another app to call it.
381        -->
382        <service
382-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
383            android:name="com.google.firebase.messaging.FirebaseMessagingService"
383-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
384            android:directBootAware="true"
384-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
385            android:exported="false" >
385-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
386            <intent-filter android:priority="-500" >
386-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
387                <action android:name="com.google.firebase.MESSAGING_EVENT" />
387-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
387-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
388            </intent-filter>
389        </service>
390
391        <activity
391-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07660f349a2fe95c07a6bc7026689012\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
392            android:name="com.google.android.gms.common.api.GoogleApiActivity"
392-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07660f349a2fe95c07a6bc7026689012\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
393            android:exported="false"
393-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07660f349a2fe95c07a6bc7026689012\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
394            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
394-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07660f349a2fe95c07a6bc7026689012\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
395
396        <provider
396-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
397            android:name="com.google.firebase.provider.FirebaseInitProvider"
397-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
398            android:authorities="com.octalog.up360.firebaseinitprovider"
398-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
399            android:directBootAware="true"
399-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
400            android:exported="false"
400-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
401            android:initOrder="100" />
401-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
402
403        <uses-library
403-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
404            android:name="androidx.window.extensions"
404-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
405            android:required="false" />
405-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
406        <uses-library
406-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
407            android:name="androidx.window.sidecar"
407-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
408            android:required="false" />
408-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
409
410        <provider
410-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
411            android:name="androidx.startup.InitializationProvider"
411-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
412            android:authorities="com.octalog.up360.androidx-startup"
412-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
413            android:exported="false" >
413-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
414            <meta-data
414-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
415                android:name="androidx.work.WorkManagerInitializer"
415-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
416                android:value="androidx.startup" />
416-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
417            <meta-data
417-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95a194f62dbd62c14638e0c38a7881a4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
418                android:name="androidx.emoji2.text.EmojiCompatInitializer"
418-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95a194f62dbd62c14638e0c38a7881a4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
419                android:value="androidx.startup" />
419-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95a194f62dbd62c14638e0c38a7881a4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
420            <meta-data
420-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
421                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
421-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
422                android:value="androidx.startup" />
422-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
423            <meta-data
423-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
424                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
424-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
425                android:value="androidx.startup" />
425-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
426        </provider>
427
428        <service
428-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
429            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
429-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
430            android:directBootAware="false"
430-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
431            android:enabled="@bool/enable_system_alarm_service_default"
431-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
432            android:exported="false" />
432-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
433        <service
433-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
434            android:name="androidx.work.impl.background.systemjob.SystemJobService"
434-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
435            android:directBootAware="false"
435-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
436            android:enabled="@bool/enable_system_job_service_default"
436-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
437            android:exported="true"
437-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
438            android:permission="android.permission.BIND_JOB_SERVICE" />
438-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
439        <service
439-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
440            android:name="androidx.work.impl.foreground.SystemForegroundService"
440-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
441            android:directBootAware="false"
441-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
442            android:enabled="@bool/enable_system_foreground_service_default"
442-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
443            android:exported="false" />
443-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
444
445        <receiver
445-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
446            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
446-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
447            android:directBootAware="false"
447-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
448            android:enabled="true"
448-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
449            android:exported="false" />
449-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
450        <receiver
450-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
451            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
451-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
452            android:directBootAware="false"
452-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
453            android:enabled="false"
453-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
454            android:exported="false" >
454-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
455            <intent-filter>
455-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
456                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
456-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
456-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
457                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
457-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
457-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
458            </intent-filter>
459        </receiver>
460        <receiver
460-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
461            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
461-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
462            android:directBootAware="false"
462-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
463            android:enabled="false"
463-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
464            android:exported="false" >
464-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
465            <intent-filter>
465-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
466                <action android:name="android.intent.action.BATTERY_OKAY" />
466-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
466-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
467                <action android:name="android.intent.action.BATTERY_LOW" />
467-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
467-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
468            </intent-filter>
469        </receiver>
470        <receiver
470-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
471            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
471-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
472            android:directBootAware="false"
472-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
473            android:enabled="false"
473-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
474            android:exported="false" >
474-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
475            <intent-filter>
475-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
476                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
476-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
476-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
477                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
477-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
477-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
478            </intent-filter>
479        </receiver>
480        <receiver
480-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
481            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
481-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
482            android:directBootAware="false"
482-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
483            android:enabled="false"
483-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
484            android:exported="false" >
484-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
485            <intent-filter>
485-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
486                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
486-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
486-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
487            </intent-filter>
488        </receiver>
489        <receiver
489-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
490            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
490-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
491            android:directBootAware="false"
491-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
492            android:enabled="false"
492-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
493            android:exported="false" >
493-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
494            <intent-filter>
494-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
495                <action android:name="android.intent.action.BOOT_COMPLETED" />
495-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:86:17-79
495-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:86:25-76
496                <action android:name="android.intent.action.TIME_SET" />
496-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
496-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
497                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
497-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
497-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
498            </intent-filter>
499        </receiver>
500        <receiver
500-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
501            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
501-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
502            android:directBootAware="false"
502-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
503            android:enabled="@bool/enable_system_alarm_service_default"
503-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
504            android:exported="false" >
504-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
505            <intent-filter>
505-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
506                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
506-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
506-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
507            </intent-filter>
508        </receiver>
509        <receiver
509-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
510            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
510-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
511            android:directBootAware="false"
511-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
512            android:enabled="true"
512-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
513            android:exported="true"
513-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
514            android:permission="android.permission.DUMP" >
514-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
515            <intent-filter>
515-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
516                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
516-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
516-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
517            </intent-filter>
518        </receiver>
519
520        <meta-data
520-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf636ef2f0738047fe8129f320ef339e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
521            android:name="com.google.android.gms.version"
521-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf636ef2f0738047fe8129f320ef339e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
522            android:value="@integer/google_play_services_version" />
522-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf636ef2f0738047fe8129f320ef339e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
523
524        <receiver
524-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
525            android:name="androidx.profileinstaller.ProfileInstallReceiver"
525-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
526            android:directBootAware="false"
526-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
527            android:enabled="true"
527-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
528            android:exported="true"
528-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
529            android:permission="android.permission.DUMP" >
529-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
530            <intent-filter>
530-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
531                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
531-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
531-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
532            </intent-filter>
533            <intent-filter>
533-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
534                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
534-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
534-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
535            </intent-filter>
536            <intent-filter>
536-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
537                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
537-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
537-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
538            </intent-filter>
539            <intent-filter>
539-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
540                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
540-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
540-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
541            </intent-filter>
542        </receiver>
543
544        <service
544-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
545            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
545-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
546            android:exported="false" >
546-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
547            <meta-data
547-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
548                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
548-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
549                android:value="cct" />
549-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
550        </service>
551        <service
551-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
552            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
552-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
553            android:exported="false"
553-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
554            android:permission="android.permission.BIND_JOB_SERVICE" >
554-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
555        </service>
556
557        <receiver
557-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
558            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
558-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
559            android:exported="false" />
559-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
560
561        <service
561-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca09e878cbb074545c6b4079b0555c5\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
562            android:name="androidx.room.MultiInstanceInvalidationService"
562-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca09e878cbb074545c6b4079b0555c5\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
563            android:directBootAware="true"
563-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca09e878cbb074545c6b4079b0555c5\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
564            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
564-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca09e878cbb074545c6b4079b0555c5\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
565        <activity
565-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2748b27b9a4960040fddad0a76748cc0\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
566            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
566-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2748b27b9a4960040fddad0a76748cc0\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
567            android:exported="false"
567-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2748b27b9a4960040fddad0a76748cc0\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
568            android:stateNotNeeded="true"
568-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2748b27b9a4960040fddad0a76748cc0\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
569            android:theme="@style/Theme.PlayCore.Transparent" />
569-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2748b27b9a4960040fddad0a76748cc0\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
570    </application>
571
572</manifest>
