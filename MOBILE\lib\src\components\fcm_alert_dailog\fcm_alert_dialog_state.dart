import 'package:asuka/asuka.dart' as asuka;
import 'package:flutter/material.dart';
import 'package:octalog/src/components/fcm_alert_dailog/models/fcm_alert_dados.dart';
import 'package:octalog/src/components/fcm_alert_dailog/models/fcm_deslocamento_get.dart';
import 'package:octalog/src/utils/theme_colors.dart';

enum FcmAlertPageStep {
  deslocamento(title: 'Deslocamento'),
  chegada(title: 'Chegada'),
  coleta(title: '<PERSON><PERSON>');

  final String title;
  const FcmAlertPageStep({required this.title});
}

class FcmAlertState {
  final FcmDeslocamentoGet fcmAlertDados;
  final List<int> codigos;
  final String liberadoPor;
  final bool isLoading;
  final FcmAlertPageStep fcmAlertPageStep;
  final String filtro;
  final bool isLoadingButton;
  FcmAlertState({
    required this.fcmAlertDados,
    required this.codigos,
    required this.liberadoPor,
    required this.isLoading,
    required this.fcmAlertPageStep,
    required this.filtro,
    required this.isLoadingButton,
  });

  factory FcmAlertState.initial(FcmDeslocamentoGet dados) {
    return FcmAlertState(
      fcmAlertDados: dados,
      codigos: [],
      isLoading: false,
      liberadoPor: '',
      fcmAlertPageStep: dados.step,
      filtro: '',
      isLoadingButton: false,
    );
  }

  FcmAlertState addCodigo(String codigo) {
    final list = fcmAlertDados.pedidos.where((p) => p.os.toUpperCase() == codigo.toUpperCase()).toList();
    if (list.isEmpty) {
      if (fcmAlertDados.coletarPedidoSemIntegracao) {
        FcmPedido pedido = FcmPedido(cliente: 'Sem informações', os: codigo, status: '', title: 'Endereço na etiqueta', idOs: 0);
        // codigos.add(codigo);
        fcmAlertDados.pedidos.add(pedido);
      } else {
        asuka.Asuka.showDialog(
          barrierColor: Colors.black.withOpacity(0.5),
          builder:
              (ctx) => AlertDialog(
                title: const Text('Pedido sem integração'),
                content: const Text('Nesta loja não é possível coletar pedidos sem integração. Procure seu coordenador.', textAlign: TextAlign.start),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.pop(ctx);
                    },
                    child: Text('Ok', style: TextStyle(color: ThemeColors.customOrange(ctx))),
                  ),
                ],
              ),
        );
      }
    } else {
      for (var element in list) {
        if (!codigos.contains(element.idOs)) {
          codigos.add(element.idOs);
        }
      }
    }
    return this;
  }

  FcmAlertState addIDOS(int idos) {
    codigos.add(idos);
    return this;
  }

  FcmAlertState removeIDOS(int idos) {
    codigos.remove(idos);
    return this;
  }

  FcmAlertState removeCodigo(String codigo) {
    final list = fcmAlertDados.pedidos.where((p) => p.os.toUpperCase() == codigo.toUpperCase()).toList();
    if (list.isNotEmpty) {
      codigos.remove(list.first.idOs);
    }
    return this;
  }

  FcmAlertState removePedido(FcmPedido pedido) {
    fcmAlertDados.pedidos.remove(pedido);
    codigos.remove(pedido.idOs);
    return this;
  }

  List<FcmPedido> get clientesUnicos {
    final original = fcmAlertDados.pedidos;
    final clientes = <FcmPedido>[];
    for (final pedido in original) {
      if (!clientes.any((p) => p.cliente == pedido.cliente)) {
        clientes.add(pedido);
      }
    }
    return clientes.toList();
  }

  List<FcmPedido> get clientesUnicosFiltrados {
    final original = fcmAlertDados.pedidos;
    final clientes = <FcmPedido>[];
    for (final pedido in original) {
      if (!clientes.any((p) => p.cliente == pedido.cliente) && pedido.cliente.toLowerCase().contains(filtro.toLowerCase()) ||
          pedido.os.toLowerCase().contains(filtro.toLowerCase()) ||
          pedido.title.toLowerCase().contains(filtro.toLowerCase())) {
        if (clientes.any((p) => p.cliente == pedido.cliente)) {
          if (isOsInClientesColetados(pedido.os)) {
            clientes.add(pedido);
          }
        } else {
          clientes.add(pedido);
        }
      }
    }
    return clientes.toList();
  }

  List<FcmPedido> get coletados {
    final encontrados = fcmAlertDados.pedidos.where((p) => codigos.contains(p.idOs)).toList();
    final novos = fcmAlertDados.pedidos.where((p) => p.idOs == 0).toList();
    return [...encontrados, ...novos];
  }

  List<FcmPedido> get clientesColetados {
    final novos = fcmAlertDados.pedidos.where((p) => p.idOs == 0).toList();
    final clientes = fcmAlertDados.pedidos.where((a) => codigos.contains(a.idOs)).map((e) => e.cliente).toSet().toList();
    novos.addAll(clientes.map((e) => coletados.firstWhere((p) => p.cliente == e)));
    return novos;
  }

  //clienteColetadounicos
  List<FcmPedido> get clientesColetadosUnicos {
    final original = clientesColetados;
    final clientes = <FcmPedido>[];
    for (final pedido in original) {
      if (!clientes.any((p) => p.cliente == pedido.cliente)) {
        clientes.add(pedido);
      }
    }
    return clientes.toList();
  }

  //verificar se o os está na lista de pedidos clientesColetados
  bool isOsInClientesColetados(String os) {
    return clientesColetados.any((p) => p.os == os);
  }

  List<FcmPedido> get naoColetados {
    return fcmAlertDados.pedidos.where((p) => p.idOs != 0).where((p) => !codigos.contains(p.idOs)).toList();
  }

  List<FcmPedido> get clientesNaoColetados {
    return fcmAlertDados.pedidos
        .where((p) => !codigos.contains(p.idOs))
        .map((p) => p.cliente)
        .toSet()
        .map(
          (c) => FcmPedido(
            idOs: fcmAlertDados.pedidos.firstWhere((p) => p.cliente == c).idOs,
            os: fcmAlertDados.pedidos.firstWhere((p) => p.cliente == c).os,
            cliente: c,
            status: fcmAlertDados.pedidos.firstWhere((p) => p.cliente == c).status,
            title: fcmAlertDados.pedidos.firstWhere((p) => p.cliente == c).title,
          ),
        )
        .toList();
  }

  int pedidosRestantesCliente(String cliente) {
    final nc = naoColetados.where((p) => p.cliente == cliente);
    return nc.length;
  }

  List<FcmPedido> pedidosColetadosCliente(String cliente) {
    final result = coletados.where((p) => p.cliente == cliente && p.cliente != '');

    return result.toList();
  }

  List<FcmPedido> pedidosTotaisCliente(String cliente) {
    final result = fcmAlertDados.pedidos.where((p) => p.cliente == cliente && p.cliente != '');

    return result.toList();
  }

  FcmAlertState copyWith({
    FcmDeslocamentoGet? fcmAlertDados,
    bool? isLoading,
    FcmAlertPageStep? fcmAlertPageStep,
    List<int>? codigos,
    String? liberadoPor,
    List<FcmPedido>? pedidos,
    String? filtro,
    bool? isLoadingButton,
  }) {
    final dados = fcmAlertDados ?? this.fcmAlertDados;
    return FcmAlertState(
      filtro: filtro ?? this.filtro,
      fcmAlertDados: dados.copyWith(pedidos: pedidos),
      isLoading: isLoading ?? this.isLoading,
      fcmAlertPageStep: fcmAlertPageStep ?? this.fcmAlertPageStep,
      codigos: codigos ?? this.codigos,
      liberadoPor: liberadoPor ?? this.liberadoPor,
      isLoadingButton: isLoadingButton ?? this.isLoadingButton,
    );
  }
}
