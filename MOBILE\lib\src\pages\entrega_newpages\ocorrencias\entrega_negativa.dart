import 'package:asuka/asuka.dart' as asuka;
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/utils/theme_colors.dart';
//import 'package:url_launcher/url_launcher.dart';

import '../../../components/buttom_ls/button_ls_custom.dart';
import '../../../database/config_blob/config_database.dart';
import '../../../database/sac/sac_atendimento.dart';
import '../../../models/status_atividades.dart';
import '../../../models_new/cliente_new.dart';
import '../../../utils/colors-dart';
import '../../home/<USER>';
import '../../home/<USER>';
//import '../../sac_page/sac_page.dart';
import '../controller/entrega_new_etapa.dart';
import '../controller/entrega_new_store.dart';

class EntregaNegativa extends StatefulWidget {
  final EntregaNewStore store;
  final bool isOcorrenciaGlobal;
  final int? indexClienteRestante;
  final int? idOsSingle;
  final String? osSingle;
  final bool removerAcareacao;

  const EntregaNegativa({
    super.key,
    required this.store,
    this.indexClienteRestante,
    required this.isOcorrenciaGlobal,
    this.idOsSingle,
    this.osSingle,
    required this.removerAcareacao,
  });

  @override
  State<EntregaNegativa> createState() => _EntregaNegativaState();
}

class _EntregaNegativaState extends State<EntregaNegativa> {
  bool get isOcorrenciaGlobal {
    return widget.isOcorrenciaGlobal;
  }

  List<StatusAtividadesChild> get getOcorrencias {
    final bool deslocando =
        widget.store.state.value.etapa == EntregaNewEtapa.deslocando;
    final bool foto = widget.store.state.value.etapa == EntregaNewEtapa.foto;
    final bool finalizar =
        widget.store.state.value.etapa == EntregaNewEtapa.finalizar;
    final bool inicio =
        widget.store.state.value.etapa == EntregaNewEtapa.inicio;

    if (inicio) {
      if (clienteEscolhido!.acareacao) {
        return widget.store.statusAtividades.ocorrenciasInicio
            .where((element) => element.acareacao)
            .toList();
      }
      return widget.store.statusAtividades.ocorrenciasInicio;
    }
    if (foto || deslocando) {
      if (clienteEscolhido!.acareacao) {
        return widget.store.statusAtividades.ocorrenciasChegada
            .where((element) => element.acareacao)
            .toList();
      }
      return widget.store.statusAtividades.ocorrenciasChegada;
    }
    if (finalizar) {
      if (clienteEscolhido!.acareacao) {
        return widget.store.statusAtividades.problemasEntregas
            .where((element) => element.acareacao)
            .toList();
      }
      return widget.store.statusAtividades.problemasEntregas
          .where((element) =>
              (element.idStatusAtividade != 11 && widget.removerAcareacao))
          .toList();
    }
    return [];
  }

  ClienteNew? get clienteEscolhido {
    if (widget.store.state.value.restantes.length == 1 &&
        widget.store.state.value.atividade.clientes.length == 1) {
      return widget.store.state.value.restantes.first;
    }
    if (widget.store.state.value.restantes.isEmpty) return null;
    if (widget.indexClienteRestante != null) {
      return widget.store.state.value.restantes
          .elementAt(widget.indexClienteRestante!);
    }
    return widget.store.state.value.restantes.first;
  }

  int? idStatusAtividade;
  String? statusAtividade;
  bool liberarBotaoConfimar = false;
  List<PedidosSacModel> listOsstatusPedido = [];
  bool botaoSacObrigatorio = false;
  bool ocorrenciaObrigatoria = true;
  bool loadingButton = false;

  @override
  void initState() {
    _init();
    super.initState();
  }

  _init() async {
    final instance = PedidosSacDatabase.instance;
    final responseConfig = await ConfigDatabase.instance.getConfig();
    setState(() {
      botaoSacObrigatorio = responseConfig.ativarBotaoSacObrigatorio;
    });
    listOsstatusPedido = await instance
        .getSac(widget.store.state.value.restantes.first.idosList.first);
  }

  void verificarLiberarBotao(int statusAtividade) {
    bool liberar = listOsstatusPedido
        .where((element) => element.idStatusAtividade == statusAtividade)
        .isNotEmpty;
    liberarBotaoConfimar = liberar;

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Builder(
        builder: (ctx) {
          final bool inicio =
              widget.store.state.value.etapa == EntregaNewEtapa.inicio;
          final bool deslocando =
              widget.store.state.value.etapa == EntregaNewEtapa.deslocando;
          final bool foto =
              widget.store.state.value.etapa == EntregaNewEtapa.foto;
          final bool finalizar =
              widget.store.state.value.etapa == EntregaNewEtapa.finalizar;

          return SizedBox(
            height: inicio || deslocando || foto == true
                ? MediaQuery.of(ctx).size.height * 0.50
                : finalizar == true
                    ? MediaQuery.of(ctx).size.height * 0.92
                    : MediaQuery.of(ctx).size.height * 0.88,
            width: MediaQuery.of(ctx).size.width,
            child: Column(
              children: [
                const SizedBox(
                  height: 12,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 80),
                  child: Container(
                    decoration: const BoxDecoration(
                      color: Colors.grey,
                      borderRadius: BorderRadius.all(
                        Radius.circular(50),
                      ),
                    ),
                    height: 5,
                    width: 50,
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                SizedBox(
                  height: 30,
                  width: double.infinity,
                  child: Center(
                    child: Text(
                      'Problema na entrega${(widget.osSingle ?? '').isEmpty ? '' : ' - ${widget.osSingle}'}',
                      style: GoogleFonts.roboto(
                        fontSize: 18,
                        color: ThemeColors.customBlack(context),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Expanded(
                  flex: 2,
                  child: Scrollbar(
                    thumbVisibility: true,
                    thickness: 6,
                    radius: const Radius.circular(10),
                    child: ListView.builder(
                      itemCount: getOcorrencias.length,
                      itemBuilder: (_, index) {
                        final ocorrencia = getOcorrencias[index];
                        final escolhido =
                            ocorrencia.idStatusAtividade == idStatusAtividade;
                        return Padding(
                          padding: const EdgeInsets.only(left: 10, right: 10),
                          child: ListTile(
                            onTap: () {
                              setState(() {
                                idStatusAtividade =
                                    ocorrencia.idStatusAtividade;
                                statusAtividade = ocorrencia.nome;
                                ocorrenciaObrigatoria =
                                    ocorrencia.sacObrigatorio;
                              });

                              verificarLiberarBotao(
                                  ocorrencia.idStatusAtividade);
                            },
                            title: Padding(
                              padding: const EdgeInsets.only(bottom: 5),
                              child: Row(
                                children: [
                                  Icon(
                                    escolhido
                                        ? Icons.radio_button_checked
                                        : Icons.radio_button_unchecked,
                                    color: escolhido
                                        ? ThemeColors.customGreen(context)
                                        : ThemeColors.customGreyLight(context),
                                    size: escolhido ? 20 : 14,
                                  ),
                                  const SizedBox(width: 15),
                                  Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      SizedBox(
                                        child: Text(
                                          ocorrencia.nome,
                                          style: GoogleFonts.roboto(
                                            fontSize: 15,
                                            color: ThemeColors.customBlack(context),
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(
                    left: 20,
                    right: 10,
                    bottom: 10,
                  ),
                  child: Row(
                    children: [
                      // Expanded(
                      //   child: ButtonLsCustom(
                      //     message: idStatusAtividade == null ? 'Selecione' : null,
                      //     onPressed: () async {
                      //       final config = await ConfigDatabase.instance.getConfig();
                      //       final agenteSemTeste = config.agenteTesteSac;

                      //       if (!agenteSemTeste) {
                      //         Navigator.of(ctx).pop();

                      //         final fone = config.foneSac;
                      //         launchUrl(Uri.parse('tel://$fone'));
                      //         return;
                      //       }

                      //       if (idStatusAtividade == null) return;
                      //       if (idStatusAtividade == 0) return;

                      //       await showDialog(
                      //         context: context,
                      //         builder: (context) => SacPage(
                      //           idStatusAtividade: idStatusAtividade ?? -1,
                      //           idos: clienteEscolhido?.idosList.first ?? -1,
                      //           endereco: widget.store.state.value.atividade,
                      //           clienteEscolhido: clienteEscolhido,
                      //         ),
                      //       );
                      //       await _init();

                      //       verificarLiberarBotao(idStatusAtividade!);
                      //     },
                      //     text: 'SAC',
                      //     colorBackground: Colors.blue,
                      //   ),
                      // ),
                      const SizedBox(width: 10),
                      Visibility(
                        //visible: (liberarBotaoConfimar || !botaoSacObrigatorio || !ocorrenciaObrigatoria),
                        child: Expanded(
                          flex: 2,
                          child: ButtonLsCustom(
                            isLoading: loadingButton,
                            message:
                                idStatusAtividade == null ? 'Selecione' : null,
                            onPressed: idStatusAtividade == -1
                                ? null
                                : () async {
                                    setState(() => loadingButton = true);
                                    final instance =
                                        PedidosSacDatabase.instance;
                                    await instance.deletSac(
                                        widget.store.state.value.restantes.first
                                            .idosList.first,
                                        idStatusAtividade!,
                                        idStatusAtividade!);
                                    final config = await ConfigDatabase.instance
                                        .getConfig();
                                    if (config.buscarHome) {
                                      HomeController.instance
                                          .setHaveNegativa(true);
                                    }
                                    if (statusAtividade ==
                                            "Pedido Extraviado" ||
                                        statusAtividade ==
                                            "Pedido Danificado") {
                                      bool isboolPedido = await showDialog(
                                        context: context,
                                        builder: (ctx) {
                                          return AlertDialog(
                                            title: Text(
                                              'Aviso',
                                              style: GoogleFonts.roboto(
                                                color: ThemeColors.customBlack(context),
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                            content: Text(
                                              '$statusAtividade, tem certeza que deseja continuar? ',
                                              style: GoogleFonts.roboto(),
                                            ),
                                            actions: [
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                mainAxisSize: MainAxisSize.max,
                                                children: [
                                                  TextButton(
                                                    child: Text(
                                                      'CANCELAR',
                                                      style: GoogleFonts.roboto(
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          color: Colors
                                                              .grey.shade800),
                                                    ),
                                                    onPressed: () {
                                                      setState(() =>
                                                          loadingButton =
                                                              false);
                                                      Navigator.of(ctx)
                                                          .pop(false);
                                                    },
                                                  ),
                                                  TextButton(
                                                    child: Text(
                                                      'CONFIRMAR',
                                                      style: GoogleFonts.roboto(
                                                        color: ColorsCustom
                                                            .customOrange,
                                                      ),
                                                    ),
                                                    onPressed: () {
                                                      if (config.buscarHome) {
                                                        HomeController.instance
                                                            .setHaveNegativa(
                                                                true);
                                                      }
                                                      Navigator.of(ctx)
                                                          .pop(true);
                                                    },
                                                  ),
                                                ],
                                              ),
                                            ],
                                          );
                                        },
                                      );
                                      if (isboolPedido == false) return;
                                    }
                                    if (idStatusAtividade == null) return;
                                    asuka.AsukaSnackbar.success(
                                      snackBarAction: SnackBarAction(
                                        label: 'OK',
                                        onPressed: () {
                                          asuka.Asuka.hideCurrentSnackBar();
                                        },
                                      ),
                                      'Negativa $statusAtividade enviada',
                                    ).show();
                                    if (widget.idOsSingle != null) {
                                      await widget.store
                                          .negativaSingle(
                                        widget.idOsSingle!,
                                        widget.indexClienteRestante!,
                                        idStatusAtividade!,
                                        statusAtividade!,
                                      )
                                          .then(
                                        (value) async {
                                          if (widget.store.state.value.restantes
                                              .isEmpty) {
                                            await widget.store
                                                .limparBanco(
                                              widget.store.state.value.etapa ==
                                                  EntregaNewEtapa.inicio,
                                            )
                                                .then(
                                              (value) async {
                                                if (config.buscarHome) {
                                                  setState(() =>
                                                      loadingButton = false);
                                                  HomeController.instance
                                                      .setHaveNegativa(true);
                                                }
                                                setState(() =>
                                                    loadingButton = false);
                                                Navigator.of(context)
                                                    .pushAndRemoveUntil(
                                                        MaterialPageRoute(
                                                            builder:
                                                                (context) =>
                                                                    const Home(
                                                                      enterAtividade:
                                                                          false,
                                                                    )),
                                                        (route) => false);
                                              },
                                            );
                                          } else {
                                            setState(
                                                () => loadingButton = false);
                                            Navigator.of(context).pop();
                                          }
                                        },
                                      );
                                    } else {
                                      if (isOcorrenciaGlobal) {
                                        await widget.store
                                            .negativaConclusao(
                                          idStatusAtividade!,
                                          statusAtividade!,
                                        )
                                            .then((value) {
                                          if (config.buscarHome) {
                                            HomeController.instance
                                                .setHaveNegativa(true);
                                          }
                                          Navigator.of(context)
                                              .pushAndRemoveUntil(
                                                  MaterialPageRoute(
                                                    builder: (context) =>
                                                        const Home(
                                                      enterAtividade: false,
                                                    ),
                                                  ),
                                                  (route) => false);
                                        });
                                      } else {
                                        widget.store
                                            .clienteNegativa(
                                          idStatusAtividade!,
                                          widget.indexClienteRestante!,
                                          statusAtividade!,
                                        )
                                            .then(
                                          (value) {
                                            if (widget.store.state.value
                                                .restantes.isEmpty) {
                                              widget.store
                                                  .limparBanco(
                                                widget.store.state.value
                                                        .etapa ==
                                                    EntregaNewEtapa.inicio,
                                              )
                                                  .then(
                                                (value) {
                                                  if (config.buscarHome) {
                                                    HomeController.instance
                                                        .setHaveNegativa(true);
                                                  }
                                                  Navigator.of(context)
                                                      .pushAndRemoveUntil(
                                                          MaterialPageRoute(
                                                              builder:
                                                                  (context) =>
                                                                      const Home(
                                                                        enterAtividade:
                                                                            false,
                                                                      )),
                                                          (route) => false);
                                                },
                                              );
                                            } else {
                                              Navigator.of(context).pop();
                                            }
                                          },
                                        );
                                      }
                                    }
                                  },
                            text: 'CONFIRMAR',
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
