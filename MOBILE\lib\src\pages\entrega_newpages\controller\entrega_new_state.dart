import 'dart:convert';

import 'package:hive/hive.dart';
import 'package:latlong2/latlong.dart';
import 'package:octalog/src/models_new/cliente_new.dart';
import 'package:octalog/src/models_new/endereco_new.dart';
import 'package:map_fields/map_fields.dart';

import 'entrega_new_etapa.dart';

class EntregaNewState {
  final EnderecoNew atividade;
  final EntregaNewEtapa etapa;
  final DateTime? inicio;
  final DateTime? fim;
  final String? urlFoto;
  final String? urlFotoRomaneio;
  final String? urlFotoAcareacao;
  final bool loadingFoto;
  final bool loadingFotoRomaneio;
  final int statusAtividadesEscolhido;
  final String statusAtividadesEscolhidoNome;
  final List<ClienteNew> restantes;
  final bool loading;
  final String? entreguePara;
  final EntregaNewState? stateOld;
  final int? indexClienteEscolhido;
  final List<int>? bytes;
  final List<int>? bytesRomaneio;
  final List<int>? bytesAcareacao;
  final LatLng? inicioLocalizacao;
  final LatLng? fimLocalizacao;
  final bool? foraDoLocal;
  final bool acareacaoRealizadaSucesso;
  final bool assinaturaPreenchida;
  final bool erroAcareacaoRealizadaSucesso;
  final String? timerDataFisicoChegadaNoLocalContador;
  EntregaNewState({
    required this.atividade,
    required this.etapa,
    required this.inicio,
    required this.fim,
    required this.urlFoto,
    required this.urlFotoRomaneio,
    required this.urlFotoAcareacao,
    required this.loadingFoto,
    required this.loadingFotoRomaneio,
    required this.statusAtividadesEscolhido,
    required this.statusAtividadesEscolhidoNome,
    required this.restantes,
    required this.bytes,
    required this.bytesRomaneio,
    required this.bytesAcareacao,
    required this.entreguePara,
    this.loading = false,
    this.stateOld,
    required this.indexClienteEscolhido,
    required this.inicioLocalizacao,
    required this.fimLocalizacao,
    this.foraDoLocal,
    required this.acareacaoRealizadaSucesso,
    required this.assinaturaPreenchida,
    required this.erroAcareacaoRealizadaSucesso,
    required this.timerDataFisicoChegadaNoLocalContador,
  });

  bool get unico => restantes.length == 1;

  factory EntregaNewState.initial(
    EnderecoNew atividade, [
    List<ClienteNew>? restantes,
  ]) {
    return EntregaNewState(
      atividade: atividade,
      etapa: EntregaNewEtapa.inicio,
      loadingFoto: false,
      loadingFotoRomaneio: false,
      inicio: null,
      fim: null,
      urlFoto: null,
      urlFotoRomaneio: null,
      urlFotoAcareacao: null,
      statusAtividadesEscolhido: -1,
      statusAtividadesEscolhidoNome: '',
      entreguePara: '',
      restantes: restantes ?? atividade.clientes,
      bytes: null,
      bytesRomaneio: null,
      bytesAcareacao: null,
      indexClienteEscolhido: null,
      inicioLocalizacao: null,
      fimLocalizacao: null,
      foraDoLocal: null,
      acareacaoRealizadaSucesso: false,
      assinaturaPreenchida: false,
      erroAcareacaoRealizadaSucesso: false,
      timerDataFisicoChegadaNoLocalContador: '',
    );
  }

  EntregaNewState limpaClienteEscolhido() {
    return EntregaNewState(
      atividade: atividade,
      etapa: etapa,
      inicio: inicio,
      fim: fim,
      urlFoto: urlFoto,
      urlFotoRomaneio: urlFotoRomaneio,
      urlFotoAcareacao: urlFotoAcareacao,
      loadingFoto: loadingFoto,
      loadingFotoRomaneio: loadingFotoRomaneio,
      statusAtividadesEscolhido: statusAtividadesEscolhido,
      statusAtividadesEscolhidoNome: statusAtividadesEscolhidoNome,
      restantes: restantes,
      loading: loading,
      entreguePara: entreguePara,
      stateOld: this,
      bytes: bytes,
      bytesRomaneio: bytesRomaneio,
      bytesAcareacao: bytesAcareacao,
      indexClienteEscolhido: null,
      inicioLocalizacao: inicioLocalizacao,
      fimLocalizacao: fimLocalizacao,
      foraDoLocal: foraDoLocal,
      acareacaoRealizadaSucesso: acareacaoRealizadaSucesso,
      assinaturaPreenchida: assinaturaPreenchida,
      erroAcareacaoRealizadaSucesso: erroAcareacaoRealizadaSucesso,
      timerDataFisicoChegadaNoLocalContador:
          timerDataFisicoChegadaNoLocalContador,
    );
  }

  EntregaNewState copyWith({
    EnderecoNew? atividade,
    EntregaNewEtapa? etapa,
    DateTime? inicio,
    DateTime? fim,
    String? urlFoto,
    String? urlFotoRomaneio,
    String? urlFotoAcareacao,
    bool? loadingFoto,
    bool? loadingFotoRomaneio,
    int? statusAtividadesEscolhido,
    String? statusAtividadesEscolhidoNome,
    List<ClienteNew>? restantes,
    String? entreguePara,
    bool? loading,
    EntregaNewState? stateOld,
    int? indexClienteEscolhido,
    List<int>? bytes,
    List<int>? bytesRomaneio,
    List<int>? bytesAcareacao,
    LatLng? inicioLocalizacao,
    LatLng? fimLocalizacao,
    bool? foraDoLocal,
    bool? acareacaoRealizadaSucesso,
    bool? assinaturaPreenchida,
    bool? erroAcareacaoRealizadaSucesso,
    String? timerDataFisicoChegadaNoLocalContador,
  }) {
    return EntregaNewState(
      atividade: atividade ?? this.atividade,
      etapa: etapa ?? this.etapa,
      inicio: inicio ?? this.inicio,
      fim: fim ?? this.fim,
      urlFoto: urlFoto ?? this.urlFoto,
      urlFotoRomaneio: urlFotoRomaneio ?? this.urlFotoRomaneio,
      urlFotoAcareacao: urlFotoAcareacao ?? this.urlFotoAcareacao,
      loadingFoto: loadingFoto ?? this.loadingFoto,
      loadingFotoRomaneio: loadingFotoRomaneio ?? this.loadingFotoRomaneio,
      entreguePara: entreguePara ?? this.entreguePara,
      statusAtividadesEscolhido:
          statusAtividadesEscolhido ?? this.statusAtividadesEscolhido,
      statusAtividadesEscolhidoNome:
          statusAtividadesEscolhidoNome ?? this.statusAtividadesEscolhidoNome,
      restantes: restantes ?? this.restantes,
      loading: loading ?? this.loading,
      stateOld: stateOld ?? this.stateOld,
      indexClienteEscolhido:
          indexClienteEscolhido ?? this.indexClienteEscolhido,
      bytes: bytes ?? this.bytes,
      bytesRomaneio: bytesRomaneio ?? this.bytesRomaneio,
      bytesAcareacao: bytesAcareacao ?? this.bytesAcareacao,
      inicioLocalizacao: inicioLocalizacao ?? this.inicioLocalizacao,
      fimLocalizacao: fimLocalizacao ?? this.fimLocalizacao,
      foraDoLocal: foraDoLocal ?? this.foraDoLocal,
      acareacaoRealizadaSucesso:
          acareacaoRealizadaSucesso ?? this.acareacaoRealizadaSucesso,
      assinaturaPreenchida: assinaturaPreenchida ?? this.assinaturaPreenchida,
      erroAcareacaoRealizadaSucesso:
          erroAcareacaoRealizadaSucesso ?? this.erroAcareacaoRealizadaSucesso,
      timerDataFisicoChegadaNoLocalContador:
          timerDataFisicoChegadaNoLocalContador ??
              this.timerDataFisicoChegadaNoLocalContador,
    );
  }

  EntregaNewState setNullTimerDataFisicoChegadaNoLocalContador() {
    return EntregaNewState(
      atividade: atividade,
      etapa: etapa,
      inicio: inicio,
      fim: fim,
      urlFoto: urlFoto,
      urlFotoRomaneio: urlFotoRomaneio,
      urlFotoAcareacao: urlFotoAcareacao,
      loadingFoto: loadingFoto,
      loadingFotoRomaneio: loadingFotoRomaneio,
      entreguePara: entreguePara,
      statusAtividadesEscolhido: statusAtividadesEscolhido,
      statusAtividadesEscolhidoNome: statusAtividadesEscolhidoNome,
      restantes: restantes,
      loading: loading,
      stateOld: stateOld,
      indexClienteEscolhido: indexClienteEscolhido,
      bytes: bytes,
      bytesRomaneio: bytesRomaneio,
      bytesAcareacao: bytesAcareacao,
      inicioLocalizacao: inicioLocalizacao,
      fimLocalizacao: fimLocalizacao,
      foraDoLocal: foraDoLocal,
      acareacaoRealizadaSucesso: acareacaoRealizadaSucesso,
      assinaturaPreenchida: assinaturaPreenchida,
      erroAcareacaoRealizadaSucesso: erroAcareacaoRealizadaSucesso,
      timerDataFisicoChegadaNoLocalContador: null,
    );
  }

  EntregaNewState semFoto() {
    return EntregaNewState(
      atividade: atividade,
      etapa: etapa,
      inicio: inicio,
      fim: fim,
      urlFoto: urlFoto,
      urlFotoRomaneio: urlFotoRomaneio,
      urlFotoAcareacao: urlFotoAcareacao,
      loadingFoto: loadingFoto,
      loadingFotoRomaneio: loadingFotoRomaneio,
      entreguePara: entreguePara,
      statusAtividadesEscolhido: statusAtividadesEscolhido,
      statusAtividadesEscolhidoNome: statusAtividadesEscolhidoNome,
      restantes: restantes,
      loading: loading,
      stateOld: stateOld,
      indexClienteEscolhido: indexClienteEscolhido,
      bytes: null,
      bytesRomaneio: bytesRomaneio,
      bytesAcareacao: bytesAcareacao,
      inicioLocalizacao: inicioLocalizacao,
      fimLocalizacao: fimLocalizacao,
      foraDoLocal: foraDoLocal,
      acareacaoRealizadaSucesso: acareacaoRealizadaSucesso,
      assinaturaPreenchida: assinaturaPreenchida,
      erroAcareacaoRealizadaSucesso: erroAcareacaoRealizadaSucesso,
      timerDataFisicoChegadaNoLocalContador:
          timerDataFisicoChegadaNoLocalContador,
    );
  }

  EntregaNewState semFotoRomaneio() {
    return EntregaNewState(
      atividade: atividade,
      etapa: etapa,
      inicio: inicio,
      fim: fim,
      urlFoto: urlFoto,
      urlFotoRomaneio: urlFotoRomaneio,
      urlFotoAcareacao: urlFotoAcareacao,
      loadingFoto: loadingFoto,
      loadingFotoRomaneio: loadingFotoRomaneio,
      entreguePara: entreguePara,
      statusAtividadesEscolhido: statusAtividadesEscolhido,
      statusAtividadesEscolhidoNome: statusAtividadesEscolhidoNome,
      restantes: restantes,
      loading: loading,
      stateOld: stateOld,
      indexClienteEscolhido: indexClienteEscolhido,
      bytes: bytes,
      bytesRomaneio: null,
      bytesAcareacao: bytesAcareacao,
      inicioLocalizacao: inicioLocalizacao,
      fimLocalizacao: fimLocalizacao,
      foraDoLocal: foraDoLocal,
      acareacaoRealizadaSucesso: acareacaoRealizadaSucesso,
      assinaturaPreenchida: assinaturaPreenchida,
      erroAcareacaoRealizadaSucesso: erroAcareacaoRealizadaSucesso,
      timerDataFisicoChegadaNoLocalContador:
          timerDataFisicoChegadaNoLocalContador,
    );
  }

  EntregaNewState semFotoAcareacao() {
    return EntregaNewState(
      atividade: atividade,
      etapa: etapa,
      inicio: inicio,
      fim: fim,
      urlFoto: urlFoto,
      urlFotoRomaneio: urlFotoRomaneio,
      urlFotoAcareacao: urlFotoAcareacao,
      loadingFoto: loadingFoto,
      loadingFotoRomaneio: loadingFotoRomaneio,
      entreguePara: entreguePara,
      statusAtividadesEscolhido: statusAtividadesEscolhido,
      statusAtividadesEscolhidoNome: statusAtividadesEscolhidoNome,
      restantes: restantes,
      loading: loading,
      stateOld: stateOld,
      indexClienteEscolhido: indexClienteEscolhido,
      bytes: bytes,
      bytesRomaneio: bytesRomaneio,
      bytesAcareacao: null,
      inicioLocalizacao: inicioLocalizacao,
      fimLocalizacao: fimLocalizacao,
      foraDoLocal: foraDoLocal,
      acareacaoRealizadaSucesso: acareacaoRealizadaSucesso,
      assinaturaPreenchida: assinaturaPreenchida,
      erroAcareacaoRealizadaSucesso: erroAcareacaoRealizadaSucesso,
      timerDataFisicoChegadaNoLocalContador:
          timerDataFisicoChegadaNoLocalContador,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'atividade': json.encode(atividade.toHiveMap()),
      'etapa': etapa.index,
      'inicio': inicio?.millisecondsSinceEpoch,
      'fim': fim?.millisecondsSinceEpoch,
      'urlFoto': urlFoto,
      'urlFotoRomaneio': urlFotoRomaneio,
      'urlFotoAcareacao': urlFotoAcareacao,
      'entreguePara': entreguePara,
      'restantes': restantes.map((c) => c.toHiveMap()).toList(),
      'bytes': bytes,
      'romaneioCanhoto': bytesRomaneio,
      'acareacao': bytesAcareacao,
      'foraDoLocal': foraDoLocal,
      'inicioLocalizacao': {
        'latitude': inicioLocalizacao?.latitude,
        'longitude': inicioLocalizacao?.longitude,
      },
      'fimLocalizacao': {
        'latitude': fimLocalizacao?.latitude,
        'longitude': fimLocalizacao?.longitude,
      },
      'acareacaoRealizadaSucesso': acareacaoRealizadaSucesso,
      'assinaturaPreenchida': assinaturaPreenchida,
      'erroAcareacaoRealizadaSucesso': erroAcareacaoRealizadaSucesso,
      'timerDataFisicoChegadaNoLocalContador':
          timerDataFisicoChegadaNoLocalContador,
    };
  }

  factory EntregaNewState.fromMap(Map<String, dynamic> map) {
    final f = MapFields.load(map);
    final atividadeMap = map['atividade'] ?? {};

    Map<String, dynamic>? inicioLocalizacaoMap = map['inicioLocalizacao'] ?? {};
    Map<String, dynamic>? fimLocalizacaoMap = map['fimLocalizacao'] ?? {};

    if (inicioLocalizacaoMap?['latitude'] == null ||
        inicioLocalizacaoMap?['longitude'] == null) {
      inicioLocalizacaoMap = null;
    }

    if (fimLocalizacaoMap?['latitude'] == null ||
        fimLocalizacaoMap?['longitude'] == null) {
      fimLocalizacaoMap = null;
    }

    final inicioLocalizacao = inicioLocalizacaoMap != null
        ? LatLng(
            inicioLocalizacaoMap['latitude'] as double,
            inicioLocalizacaoMap['longitude'] as double,
          )
        : null;
    final fimLocalizacao = fimLocalizacaoMap != null
        ? LatLng(
            fimLocalizacaoMap['latitude'] as double,
            fimLocalizacaoMap['longitude'] as double,
          )
        : null;

    return EntregaNewState(
      atividade: EnderecoNew.fromHiveMap(
        atividadeMap is String ? jsonDecode(atividadeMap) : atividadeMap,
      ),
      etapa: EntregaNewEtapa.values[f.getInt('etapa', 0)],
      inicio: f.getDateTimeNullable('inicio'),
      fim: f.getDateTimeNullable('fim'),
      urlFoto: f.getStringNullable('urlFoto'),
      urlFotoRomaneio: f.getStringNullable('urlFotoRomaneio'),
      urlFotoAcareacao: f.getStringNullable('urlFotoAcareacao'),
      bytes: f.getListNullable<int>('bytes'),
      bytesRomaneio: f.getListNullable<int>('bytesRomaneio'),
      bytesAcareacao: f.getListNullable<int>('bytesAcareacao'),
      loadingFoto: false,
      loadingFotoRomaneio: false,
      entreguePara: f.getStringNullable('entreguePara'),
      statusAtividadesEscolhido: -1,
      statusAtividadesEscolhidoNome: '',
      restantes: f
          .getList<Map<String, dynamic>>('restantes', <Map<String, dynamic>>[])
          .toList()
          .cast<Map<String, dynamic>>()
          .map((c) => ClienteNew.fromHiveMap(c))
          .toList(),
      indexClienteEscolhido: null,
      inicioLocalizacao: inicioLocalizacao,
      fimLocalizacao: fimLocalizacao,
      foraDoLocal: f.getBoolNullable('foraDoLocal'),
      acareacaoRealizadaSucesso: f.getBool('acareacaoRealizadaSucesso', false),
      assinaturaPreenchida: f.getBool('assinaturaPreenchida', false),
      erroAcareacaoRealizadaSucesso:
          f.getBool('erroAcareacaoRealizadaSucesso', false),
      timerDataFisicoChegadaNoLocalContador:
          f.getStringNullable('timerDataFisicoChegadaNoLocalContador'),
    );
  }
}

class EntregaNewDb {
  EntregaNewDb._();
  static final EntregaNewDb instance = EntregaNewDb._();

  bool _boxIniciada = false;

  late Box _box;

  Future<void> _iniciarBanco() async {
    if (_boxIniciada) return;
    _box = await Hive.openBox('entrega_new_db');
    _boxIniciada = true;
  }

  Future<Box> _getBanco() async {
    await _iniciarBanco();
    return _box;
  }

  Future<EntregaNewState?> getData() async {
    final box = await _getBanco();
    try {
      final map = box.get('state');
      if (map == null) return null;
      final resp = jsonDecode(jsonEncode(map));
      return EntregaNewState.fromMap(resp);
    } catch (e) {
      return null;
    }
  }

  Future<void> saveData(EntregaNewState state) async {
    final box = await _getBanco();
    box.put('state', state.toMap());
  }

  Future<void> clearData() async {
    final box = await _getBanco();
    box.delete('state');
  }
}
