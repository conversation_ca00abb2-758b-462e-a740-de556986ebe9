import 'dart:convert';
import 'dart:io';

abstract class GZipUtil {
  static String decompress(String value) {
    final decompresedString = base64.decode(value);
    final gzipBytes = GZipCodec().decode(decompresedString);
    return utf8.decode(gzipBytes);
  }

  static String compress(String value) {
    final gzipBytes = GZipCodec().encode(utf8.encode(value));
    final compressedString = base64.encode(gzipBytes);
    return compressedString;
  }
}
