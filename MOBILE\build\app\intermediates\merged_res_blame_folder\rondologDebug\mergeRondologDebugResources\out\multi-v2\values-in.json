{"logs": [{"outputFile": "com.octalog.app-mergeRondologDebugResources-68:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f46bbd13206153774356ca6103aa890\\transformed\\core-1.13.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2809,2904,3006,3103,3200,3306,3424,6605", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "2899,3001,3098,3195,3301,3419,3534,6701"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\07660f349a2fe95c07a6bc7026689012\\transformed\\jetified-play-services-base-18.5.0\\res\\values-in\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,578,680,831,954,1065,1167,1329,1430,1590,1712,1863,2003,2063,2119", "endColumns": "102,159,121,101,150,122,110,101,161,100,159,121,150,139,59,55,74", "endOffsets": "295,455,577,679,830,953,1064,1166,1328,1429,1589,1711,1862,2002,2062,2118,2193"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3539,3646,3810,3936,4042,4197,4324,4439,4677,4843,4948,5112,5238,5393,5537,5601,5661", "endColumns": "106,163,125,105,154,126,114,105,165,104,163,125,154,143,63,59,78", "endOffsets": "3641,3805,3931,4037,4192,4319,4434,4540,4838,4943,5107,5233,5388,5532,5596,5656,5735"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fec79dfcef552889e3835cc7bab572a\\transformed\\preference-1.2.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,261,341,477,646,731", "endColumns": "68,86,79,135,168,84,78", "endOffsets": "169,256,336,472,641,726,805"}, "to": {"startLines": "54,56,60,61,64,65,66", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5740,5909,6304,6384,6706,6875,6960", "endColumns": "68,86,79,135,168,84,78", "endOffsets": "5804,5991,6379,6515,6870,6955,7034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1a36ee1e87beff061ef9f2c053c4f171\\transformed\\browser-1.8.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,362", "endColumns": "99,97,108,100", "endOffsets": "150,248,357,458"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5809,5996,6094,6203", "endColumns": "99,97,108,100", "endOffsets": "5904,6089,6198,6299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cf636ef2f0738047fe8129f320ef339e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-in\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4545", "endColumns": "131", "endOffsets": "4672"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cdc1fd5a4f8cb18094ee01b408d70e6e\\transformed\\appcompat-1.6.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,2809", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,2889"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,6520", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,6600"}}]}]}