  BroadcastReceiver android.content  ContentResolver android.content  Context android.content  Intent android.content  IntentFilter android.content  Context !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  	eventSink !android.content.BroadcastReceiver  isAirModeOn !android.content.BroadcastReceiver  contentResolver android.content.Context  getCONTENTResolver android.content.Context  getContentResolver android.content.Context  registerReceiver android.content.Context  setContentResolver android.content.Context  unregisterReceiver android.content.Context  ACTION_AIRPLANE_MODE_CHANGED android.content.Intent  Build 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  RELEASE android.os.Build.VERSION  SDK_INT android.os.Build.VERSION  JELLY_BEAN_MR1 android.os.Build.VERSION_CODES  Settings android.provider  Global android.provider.Settings  System android.provider.Settings  AIRPLANE_MODE_ON  android.provider.Settings.Global  getInt  android.provider.Settings.Global  AIRPLANE_MODE_ON  android.provider.Settings.System  getInt  android.provider.Settings.System  NonNull androidx.annotation  AirplaneModeCheckerPlugin  com.u14h4i.airplane_mode_checker  Any  com.u14h4i.airplane_mode_checker  Boolean  com.u14h4i.airplane_mode_checker  Build  com.u14h4i.airplane_mode_checker  EventChannel  com.u14h4i.airplane_mode_checker  Intent  com.u14h4i.airplane_mode_checker  IntentFilter  com.u14h4i.airplane_mode_checker  
MethodChannel  com.u14h4i.airplane_mode_checker  Settings  com.u14h4i.airplane_mode_checker  	eventSink  com.u14h4i.airplane_mode_checker  isAirModeOn  com.u14h4i.airplane_mode_checker  Any :com.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin  Boolean :com.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin  BroadcastReceiver :com.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin  Build :com.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin  Context :com.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin  EventChannel :com.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin  
FlutterPlugin :com.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin  Intent :com.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin  IntentFilter :com.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin  
MethodCall :com.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin  
MethodChannel :com.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin  NonNull :com.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin  Result :com.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin  Settings :com.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin  airplaneModeReceiver :com.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin  checkInitialAirplaneMode :com.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin  context :com.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin  eventChannel :com.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin  	eventSink :com.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin  isAirModeOn :com.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin  
methodChannel :com.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin  getEVENTSink bcom.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin.airplaneModeReceiver.<no name provided>  getEventSink bcom.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin.airplaneModeReceiver.<no name provided>  getISAirModeOn bcom.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin.airplaneModeReceiver.<no name provided>  getIsAirModeOn bcom.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin.airplaneModeReceiver.<no name provided>  isAirModeOn bcom.u14h4i.airplane_mode_checker.AirplaneModeCheckerPlugin.airplaneModeReceiver.<no name provided>  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getAPPLICATIONContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBINARYMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  EventChannel io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  	EventSink %io.flutter.plugin.common.EventChannel  
StreamHandler %io.flutter.plugin.common.EventChannel  setStreamHandler %io.flutter.plugin.common.EventChannel  success /io.flutter.plugin.common.EventChannel.EventSink  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  Build 	java.lang  EventChannel 	java.lang  Intent 	java.lang  IntentFilter 	java.lang  
MethodChannel 	java.lang  Settings 	java.lang  	eventSink 	java.lang  isAirModeOn 	java.lang  Any kotlin  Boolean kotlin  Build kotlin  EventChannel kotlin  Int kotlin  Intent kotlin  IntentFilter kotlin  
MethodChannel kotlin  Nothing kotlin  Settings kotlin  String kotlin  Unit kotlin  	eventSink kotlin  isAirModeOn kotlin  Build kotlin.annotation  EventChannel kotlin.annotation  Intent kotlin.annotation  IntentFilter kotlin.annotation  
MethodChannel kotlin.annotation  Settings kotlin.annotation  	eventSink kotlin.annotation  isAirModeOn kotlin.annotation  Build kotlin.collections  EventChannel kotlin.collections  Intent kotlin.collections  IntentFilter kotlin.collections  
MethodChannel kotlin.collections  Settings kotlin.collections  	eventSink kotlin.collections  isAirModeOn kotlin.collections  Build kotlin.comparisons  EventChannel kotlin.comparisons  Intent kotlin.comparisons  IntentFilter kotlin.comparisons  
MethodChannel kotlin.comparisons  Settings kotlin.comparisons  	eventSink kotlin.comparisons  isAirModeOn kotlin.comparisons  Build 	kotlin.io  EventChannel 	kotlin.io  Intent 	kotlin.io  IntentFilter 	kotlin.io  
MethodChannel 	kotlin.io  Settings 	kotlin.io  	eventSink 	kotlin.io  isAirModeOn 	kotlin.io  Build 
kotlin.jvm  EventChannel 
kotlin.jvm  Intent 
kotlin.jvm  IntentFilter 
kotlin.jvm  
MethodChannel 
kotlin.jvm  Settings 
kotlin.jvm  	eventSink 
kotlin.jvm  isAirModeOn 
kotlin.jvm  Build 
kotlin.ranges  EventChannel 
kotlin.ranges  Intent 
kotlin.ranges  IntentFilter 
kotlin.ranges  
MethodChannel 
kotlin.ranges  Settings 
kotlin.ranges  	eventSink 
kotlin.ranges  isAirModeOn 
kotlin.ranges  Build kotlin.sequences  EventChannel kotlin.sequences  Intent kotlin.sequences  IntentFilter kotlin.sequences  
MethodChannel kotlin.sequences  Settings kotlin.sequences  	eventSink kotlin.sequences  isAirModeOn kotlin.sequences  Build kotlin.text  EventChannel kotlin.text  Intent kotlin.text  IntentFilter kotlin.text  
MethodChannel kotlin.text  Settings kotlin.text  	eventSink kotlin.text  isAirModeOn kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               