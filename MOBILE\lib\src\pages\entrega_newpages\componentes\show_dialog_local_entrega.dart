import 'package:dotted_decoration/dotted_decoration.dart';
import 'package:flutter/material.dart';

import '../../../components/buttom_ls/button_ls_custom.dart';
import '../controller/entrega_new_store.dart';

class ShowDialogLocalEntrega extends StatefulWidget {
  final EntregaNewStore store;
  const ShowDialogLocalEntrega({
    super.key,
    required this.store,
  });

  @override
  State<ShowDialogLocalEntrega> createState() => _ShowDialogLocalEntregaState();
}

class _ShowDialogLocalEntregaState extends State<ShowDialogLocalEntrega> {
  bool isClick = false;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Center(
            child: Container(
              width: MediaQuery.of(context).size.width * .95,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(
                      height: 20,
                    ),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          widget.store.setFinalizar(true);
                        });
                      },
                      child: Container(
                        height: 65,
                        width: double.infinity,
                        decoration: widget.store.state.value.foraDoLocal == true
                            ? BoxDecoration(
                                border:
                                    Border.all(color: Colors.orange, width: 2),
                                borderRadius: BorderRadius.circular(5),
                                color: const Color.fromARGB(255, 255, 230, 216),
                              )
                            : DottedDecoration(
                                shape: Shape.box,
                                color: Colors.grey,
                                borderRadius: BorderRadius.circular(5),
                                strokeWidth: 1,
                              ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 5),
                              child: Icon(
                                widget.store.state.value.foraDoLocal == true
                                    ? Icons.check_circle_outlined
                                    : Icons.circle_outlined,
                                color:
                                    widget.store.state.value.foraDoLocal == true
                                        ? Colors.orange
                                        : Colors.grey,
                                size: 30,
                              ),
                            ),
                            const Expanded(
                              child: Text(
                                'Estou no local correto, o aplicativo que está com endereço errado.',
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                            ),
                            const SizedBox(
                              width: 5,
                            )
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 15,
                    ),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          widget.store.setFinalizar(false);
                        });
                      },
                      child: Container(
                        height: 65,
                        width: double.infinity,
                        decoration: widget.store.state.value.foraDoLocal ==
                                false
                            ? BoxDecoration(
                                border:
                                    Border.all(color: Colors.orange, width: 2),
                                borderRadius: BorderRadius.circular(5),
                                color: const Color.fromARGB(255, 255, 230, 216),
                              )
                            : DottedDecoration(
                                shape: Shape.box,
                                color: Colors.grey,
                                borderRadius: BorderRadius.circular(5),
                                strokeWidth: 1,
                              ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 5),
                              child: Icon(
                                widget.store.state.value.foraDoLocal == false
                                    ? Icons.check_circle_outlined
                                    : Icons.circle_outlined,
                                color: widget.store.state.value.foraDoLocal ==
                                        false
                                    ? Colors.orange
                                    : Colors.grey,
                                size: 30,
                              ),
                            ),
                            const Expanded(
                              child: Text(
                                'Precisei fazer a baixa em local diferente do endereço da entrega.',
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                            ),
                            const SizedBox(
                              width: 5,
                            )
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 15,
                    ),
                    ButtonLsCustom(
                      text: 'CONFIRMAR',
                      isLoading: isClick,
                      onPressed: widget.store.state.value.foraDoLocal == null
                          ? null
                          : () {
                              setState(() {
                                isClick = true;
                              });
                              if (Navigator.of(context).canPop()) {
                                Navigator.pop(context,
                                    widget.store.state.value.foraDoLocal);
                              }
                            },
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
