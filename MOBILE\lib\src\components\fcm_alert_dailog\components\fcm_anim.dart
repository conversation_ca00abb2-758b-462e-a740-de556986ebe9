import 'package:flutter/material.dart';

class FcmAnimTest extends StatefulWidget {
  const FcmAnimTest({super.key});

  @override
  State<FcmAnimTest> createState() => _FcmAnimTestState();
}

class _FcmAnimTestState extends State<FcmAnimTest> {
  // int value = 0;
  // int max = 2;

  // void increment() {
  //   setState(() {
  //     value = value == max ? 0 : value + 1;
  //   });
  // }

  // Future<void> init() async {
  //   while (true) {
  //     increment();
  //     await Future.delayed(const Duration(milliseconds: 300));
  //   }
  // }

  // @override
  // void initState() {
  //   super.initState();
  //   init();
  // }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(
        4,
        (index) => Padding(
          padding: const EdgeInsets.all(1.0),
          child: Animated<PERSON>ontainer(
            duration: const Duration(milliseconds: 300),
            height: //index == value ? 6 : 14,
                10,
            width: 3,
            color: Colors.black54,
          ),
        ),
      ).toList(),
    );
  }
}
