import 'package:flutter/material.dart';
import 'package:map_fields/map_fields.dart';

class MensagensNew {
  final String descricao;
  final String cor;
  MensagensNew({
    required this.descricao,
    required this.cor,
  });

  Map<String, dynamic> toHiveMap() {
    return {
      'descricao': descricao,
      'cor': cor,
    };
  }

  factory MensagensNew.fromHiveMap(Map<String, dynamic> map) {
    return MensagensNew(
      descricao: map['descricao'] as String,
      cor: map['cor'] as String,
    );
  }

  factory MensagensNew.fromJson(Map<String, dynamic> map) {
    final m = MapFields.load(map);
    return MensagensNew(
      descricao: m.getString('Descricao'),
      cor: m.getString('Cor'),
    );
  }

  Color getColor() {
    return Color(int.parse('0xFF$cor'));
  }
}
