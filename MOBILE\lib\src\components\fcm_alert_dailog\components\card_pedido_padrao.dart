import 'package:flutter/material.dart';
import 'package:octalog/src/utils/theme_colors.dart';
import '../models/fcm_alert_dados.dart';

class CardPedidoPadrao extends StatefulWidget {
  final String nomeCliente;
  final String endereco;
  final String status;
  final List<FcmPedido> pedidos;
  final int coletados;
  const CardPedidoPadrao({
    super.key,
    required this.nomeCliente,
    required this.endereco,
    required this.status,
    required this.pedidos,
    required this.coletados,
  });

  @override
  State<CardPedidoPadrao> createState() => _CardPedidoPadraomState();
}

class _CardPedidoPadraomState extends State<CardPedidoPadrao> {
  bool onExpansion = false;
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      color: ThemeColors.customBlue(context),
      shape:  RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(0),
        ),
        side: BorderSide(
          width: 2,
          color: ThemeColors.customOrange(context),
        ),
      ),
      child: Theme(
        data: ThemeData(
          dividerColor: Colors.transparent,
          colorScheme: ColorScheme.fromSwatch().copyWith(),
        ).copyWith(
          dividerColor: Colors.transparent,
        ),
        child: ExpansionTile(
          onExpansionChanged: (value) {
            setState(() {
              onExpansion = value;
            });
          },
          trailing: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                decoration: BoxDecoration(
                 // color: const Color.fromARGB(255, 253, 203, 178),
                  borderRadius: BorderRadius.circular(5),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 5,
                  vertical: 2,
                ),
                child: Text(
                  widget.coletados > 1
                      ? '${widget.coletados} Pedidos'
                      : '${widget.coletados} Pedido',
                  style: const TextStyle(
                    fontSize: 10,
                    color: Color(0xFF757474),
                    fontWeight: FontWeight.w800,
                  ),
                ),
              ),
              Icon(
                onExpansion ? Icons.expand_less : Icons.expand_more,
                color: ThemeColors.customOrange(context),
              )
            ],
          ),
          tilePadding: const EdgeInsets.symmetric(
            horizontal: 15.0,
            vertical: 10.0,
          ),
          title: Text(
            widget.nomeCliente,
            style:  TextStyle(
              fontSize: 16.0,
              fontWeight: FontWeight.w500,
              color: ThemeColors.customOrange(context),
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 8,
              ),
              Text(
                widget.endereco,
                style: TextStyle(
                  fontSize: 14.0,
                  fontWeight: FontWeight.w500,
                  color: ThemeColors.customBlack(context).withOpacity(0.6),
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              widget.status.isNotEmpty
                  ? Text(
                      widget.status,
                      style: TextStyle(
                        fontSize: 14.0,
                        fontWeight: FontWeight.w500,
                        color: Colors.red.withOpacity(0.6),
                      ),
                    )
                  : Container(),
            ],
          ),
          children: <Widget>[
             Padding(
              padding: EdgeInsets.symmetric(
                horizontal: 15.0,
              ),
              child: SizedBox(
                width: double.infinity,
                child: Divider(
                  color: ThemeColors.customGrey(context),
                  thickness: 0.5,
                ),
              ),
            ),
            ...List.generate(
              widget.pedidos.length,
              (index) => Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 15.0,
                  vertical: 3.0,
                ),
                child: Row(
                  children: [
                    const SizedBox(
                      width: 5,
                    ),
                    Text(
                      widget.pedidos[index].os,
                      style: const TextStyle(
                          fontSize: 16.0, fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            )
          ],
        ),
      ),
    );
  }
}
