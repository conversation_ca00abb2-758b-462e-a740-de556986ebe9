// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:map_fields/map_fields.dart';

class FotoNew {
  final String link;
  FotoNew({
    required this.link,
  });

  Map<String, dynamic> toHiveMap() {
    return {
      'link': link,
    };
  }

  factory FotoNew.fromHiveMap(Map<String, dynamic> map) {
    final f = MapFields.load(map);
    return FotoNew(
      link: f.getString('link', ''),
    );
  }

  factory FotoNew.fromJson(Map<String, dynamic> map) {
    final f = MapFields.load(map);
    return FotoNew(
      link: f.getString('link', ''),
    );
  }

  FotoNew copyWith({
    String? link,
  }) {
    return FotoNew(
      link: link ?? this.link,
    );
  }
}
