import 'package:flutter/material.dart';
import 'package:octalog/src/helpers/web_connector.dart';
import 'package:octalog/src/utils/extesion.dart';
import 'package:octalog/src/utils/offline_helper.dart';

import 'expedicao_state.dart';
import 'model/expedicao_model.dart';

class ExpedicaoStore {
  final state = ValueNotifier<ExpedicaoState>(
    ExpedicaoState(
      expedicaoPageModel: ExpedicaoPageModel(entregas: 0, negativas: 0, diasTrabalhados: 0, entregasLista: []),
      dataInicio: DateTime(DateTime.now().year, DateTime.now().month, 1),
      dataFim: DateTime.now(),
      loading: false,
    ),
  );

  void setLoading(bool value) {
    state.value = state.value.copyWith(loading: value);
  }

  Future<void> setDataInicioFimBusca(DateTime inicio, DateTime fim) async {
    state.value = state.value.copyWith(dataInicio: inicio, dataFim: fim);
    await setExpedicoes();
  }

  Future setExpedicoes() async {
    setLoading(true);
    try {
      final res = await WebConnector().get(
        '/relatorio/extrato',
        queryParameters: {
          'dataInicio': state.value.dataInicio.dataHoraServidorFomart.toIso8601String(),
          'dataFim': state.value.dataFim.dataHoraServidorFomart.toIso8601String(),
        },
      );
      ExpedicaoPageModel expedicoes = ExpedicaoPageModel.fromJson(res.data);
      state.value = state.value.copyWith(expedicaoPageModel: expedicoes);
      offlineStore.setOffline(false);
      //await Future.delayed(const Duration(seconds: 2));
      setLoading(false);
    } catch (_) {
      offlineStore.setOffline(true);
      setLoading(false);
    }
  }
}
