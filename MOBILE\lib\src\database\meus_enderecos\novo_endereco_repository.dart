import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:octalog/src/helpers/web_connector.dart';
import 'package:octalog/src/models/novo_endereco_model.dart';

class NovoEnderecoRepository {
  Future<void> sendNovoEndereco(NovoEnderecoModel endereco) async {
    WebConnector connector = WebConnector();
    try {
      debugPrint(jsonEncode(endereco.toMap()));
      final response = await connector.post(
        '/agente/novoendereco',
        body: endereco.toMap(),
      );
      debugPrint(response.data);
    } catch (e) {
      debugPrint('${e}error');
    }
  }
}
