import 'package:flutter/material.dart';
import '../../config/flavor_config.dart';

/// Widget que exibe imagens específicas do flavor atual.
///
/// Automaticamente carrega a imagem correta baseada no flavor configurado.
///
/// Exemplo de uso:
/// ```dart
/// // Para exibir a imagem como widget
/// FlavorImage(
///   assetName: 'logo.png',
///   width: 100,
///   height: 100,
/// )
///
/// // Para obter apenas a URL da imagem
/// String logoUrl = FlavorImage.getImageUrl('logo.png');
///
/// // Ou usando a instância do widget
/// FlavorImage logoWidget = FlavorImage(assetName: 'logo.png');
/// String logoUrl = logoWidget.imageUrl;
/// ```
class FlavorImage extends StatelessWidget {
  final String assetName;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Color? color;
  final BlendMode? colorBlendMode;
  final double? scale;

  const FlavorImage({
    super.key,
    required this.assetName,
    this.width,
    this.height,
    this.fit,
    this.color,
    this.colorBlendMode,
    this.scale,
  });

  /// Retorna a URL/path da imagem para o flavor atual
  String get imageUrl {
    final config = FlavorConfig.instance;
    return config.getAsset(assetName);
  }

  /// Método estático para obter a URL de uma imagem sem criar o widget
  static String getImageUrl(String assetName) {
    final config = FlavorConfig.instance;
    return config.getAsset(assetName);
  }

  @override
  Widget build(BuildContext context) {
    final config = FlavorConfig.instance;
    final assetPath = config.getAsset(assetName);

    return Image.asset(
      assetPath,
      width: width,
      height: height,
      fit: fit,
      color: color,
      colorBlendMode: colorBlendMode,
      scale: scale,
      errorBuilder: (context, error, stackTrace) {
        // Fallback para asset genérico se não encontrar o específico do flavor
        return Image.asset(
          'assets/images/$assetName',
          width: width,
          height: height,
          fit: fit,
          color: color,
          colorBlendMode: colorBlendMode,
          scale: scale,
          errorBuilder: (context, error, stackTrace) {
            // Se nem o fallback funcionar, mostra um ícone de erro
            return Icon(
              Icons.image_not_supported,
              size: width ?? height ?? 50,
              color: Colors.grey,
            );
          },
        );
      },
    );
  }
}

// Widget específico para logos
class FlavorLogo extends StatelessWidget {
  final double width;
  final double height;
  final Color? color;

  const FlavorLogo({
    super.key,
    this.width = 200,
    this.height = 200,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return FlavorImage(
      assetName: 'logo200.png',
      width: width,
      height: height,
      color: color,
    );
  }
}

// Widget específico para logos brancos
class FlavorLogoWhite extends StatelessWidget {
  final double width;
  final double height;

  const FlavorLogoWhite({
    super.key,
    this.width = 200,
    this.height = 200,
  });

  @override
  Widget build(BuildContext context) {
    return FlavorImage(
      assetName: 'logo200.png',
      width: width,
      height: height,
    );
  }
}
