// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:octalog/src/models/acareacao.dart';
import 'package:octalog/src/models/id_status_atividade_enum.dart';
import 'package:octalog/src/utils/extesion.dart';
import 'package:map_fields/map_fields.dart';

import 'foto_new.dart';
import 'tag_new.dart';
import 'volume_new.dart';

class ClienteNew {
  final String nomeCliente;
  final String telefone;
  final bool exibirNumeroClienteNaEntrega;
  final String complemento;
  final String transportadora;
  final String pontoReferencia;
  final DateTime? horaSLA;
  final String mensagem;
  final String? logo;
  final String loja;
  final double? valorRecebido;
  final double? valorReceber;
  final String? nomeRecebedor;
  final DateTime? dataTermino;
  final String tipo;
  final int idStatusAtividade;
  final String statusAtividade;
  final bool receita;
  final bool romaneioCanhoto;
  final bool termolabil;
  final bool receberValor;
  final bool reentrega;
  final bool acareacao;
  final bool atrasado;
  final double? latitude;
  final double? longitude;
  final List<FotoNew> fotos;
  final List<TagNew> tags;
  final List<VolumeNew> volumes;
  final AcareacaoInfoModel? info;
  final bool preencherAutomaticoNomeClienteEntrega;
  final bool? assinaturaObrigatoria;

  ClienteNew({
    required this.nomeCliente,
    required this.telefone,
    required this.transportadora,
    required this.exibirNumeroClienteNaEntrega,
    required this.complemento,
    required this.pontoReferencia,
    this.horaSLA,
    required this.mensagem,
    this.logo,
    required this.loja,
    this.valorRecebido,
    this.valorReceber,
    this.nomeRecebedor,
    this.dataTermino,
    required this.tipo,
    required this.idStatusAtividade,
    required this.statusAtividade,
    required this.receita,
    required this.romaneioCanhoto,
    required this.termolabil,
    required this.receberValor,
    required this.reentrega,
    required this.acareacao,
    required this.atrasado,
    this.latitude,
    this.longitude,
    required this.fotos,
    required this.tags,
    required this.volumes,
    this.info,
    required this.preencherAutomaticoNomeClienteEntrega,
    required this.assinaturaObrigatoria,
  });

  List<String> get links => fotos.map((e) => e.link).toList();

  String get nomeClienteTratado =>
      nomeCliente.trim().isEmpty ? 'Consumidor Final' : nomeCliente;

  double get lat => latitude ?? 0;
  double get long => longitude ?? 0;

  bool get receberValorObrigatorio => receberValor && (valorReceber ?? 0) != 0;

  Map<String, dynamic> toHiveMap() {
    return {
      'exibirNumeroClienteNaEntrega': exibirNumeroClienteNaEntrega,
      'nomeCliente': nomeCliente,
      'transportadora': transportadora,
      'telefone': telefone,
      'complemento': complemento,
      'pontoReferencia': pontoReferencia,
      'horaSLA': horaSLA?.toIso8601String(),
      'mensagem': mensagem,
      'logo': logo,
      'loja': loja,
      'valorRecebido': valorRecebido,
      'valorReceber': valorReceber,
      'nomeRecebedor': nomeRecebedor,
      'dataTermino': dataTermino?.toIso8601String(),
      'tipo': tipo,
      'idStatusAtividade': idStatusAtividade,
      'statusAtividade': statusAtividade,
      'receita': receita,
      'romaneioCanhoto': romaneioCanhoto,
      'termolabil': termolabil,
      'receberValor': receberValor,
      'reentrega': reentrega,
      'acareacao': acareacao,
      'atrasado': atrasado,
      'latitude': latitude,
      'longitude': longitude,
      'fotos': fotos.map((e) => e.toHiveMap()).toList(),
      'tags': tags.map((e) => e.toHiveMap()).toList(),
      'volumes': volumes.map((e) => e.toHiveMap()).toList(),
      'info': info?.toHiveMap(),
      'preencherAutomaticoNomeClienteEntrega':
          preencherAutomaticoNomeClienteEntrega,
      'assinaturaObrigatoria': assinaturaObrigatoria,
    };
  }

  factory ClienteNew.fromHiveMap(Map<String, dynamic> map) {
    final c = MapFields.load(map);
    final info = c.getMapNullable<String, dynamic>('info');
    return ClienteNew(
      nomeCliente: c.getString('nomeCliente', 'Consumidor Final'),
      complemento: c.getString('complemento', ''),
      transportadora: c.getString('transportadora', ''),
      telefone: c.getString('telefone', ''),
      exibirNumeroClienteNaEntrega:
          c.getBool('exibirNumeroClienteNaEntrega', false),
      pontoReferencia: c.getString('pontoReferencia', ''),
      horaSLA: c.getDateTimeNullable('horaSLA'),
      mensagem: c.getString('mensagem', ''),
      logo: c.getStringNullable('logo'),
      loja: c.getString('loja', ''),
      valorRecebido: c.getDoubleNullable('valorRecebido'),
      valorReceber: c.getDoubleNullable('valorReceber'),
      nomeRecebedor: c.getStringNullable('nomeRecebedor'),
      dataTermino: c.getDateTimeNullable('dataTermino'),
      tipo: c.getString('tipo', ''),
      idStatusAtividade: c.getInt('idStatusAtividade', 0),
      statusAtividade: c.getString('statusAtividade', ''),
      receita: c.getBool('receita', false),
      romaneioCanhoto: c.getBool('romaneioCanhoto', false),
      termolabil: c.getBool('termolabil', false),
      receberValor: c.getBool('receberValor', false),
      reentrega: c.getBool('reentrega', false),
      acareacao: c.getBool('acareacao', false),
      atrasado: c.getBool('atrasado', false),
      latitude: c.getDoubleNullable('latitude'),
      longitude: c.getDoubleNullable('longitude'),
      fotos: c
          .getList<Map<String, dynamic>>('fotos', [])
          .map<FotoNew>((Map<String, dynamic> e) => FotoNew.fromHiveMap(e))
          .toList(),
      tags: c
          .getList<Map<String, dynamic>>('tags', [])
          .map<TagNew>((Map<String, dynamic> e) => TagNew.fromHiveMap(e))
          .toList(),
      volumes: c
          .getList<Map<String, dynamic>>('volumes', [])
          .map<VolumeNew>((Map<String, dynamic> e) => VolumeNew.fromHiveMap(e))
          .toList(),
      info: info == null ? null : AcareacaoInfoModel.fromHiveMap(info),
      preencherAutomaticoNomeClienteEntrega:
          c.getBool('preencherAutomaticoNomeClienteEntrega', true),
      assinaturaObrigatoria: c.getBool('assinaturaObrigatoria', false),
    );
  }

  factory ClienteNew.fromJson(Map<String, dynamic> map) {
    final c = MapFields.load(map);
    final info = c.getMapNullable<String, dynamic>('Info');
    return ClienteNew(
      nomeCliente: c.getString('NomeCliente', 'Consumidor Final'),
      complemento: c.getString('Complemento', ''),
      transportadora: c.getString('Transportadora', ''),
      telefone: c.getString('Telefone', ''),
      exibirNumeroClienteNaEntrega:
          c.getBool('ExibirNumeroClienteNaEntrega', false),
      pontoReferencia: c.getString('PontoReferencia', ''),
      horaSLA: c.getDateTimeNullable('HoraSLA'),
      mensagem: c.getString('Mensagem', ''),
      logo: c.getStringNullable('Logo'),
      loja: c.getString('Loja', ''),
      valorRecebido: c.getDoubleNullable('ValorRecebido'),
      valorReceber: c.getDoubleNullable('ValorReceber'),
      nomeRecebedor: c.getStringNullable('NomeRecebedor'),
      dataTermino: c.getDateTimeNullable('DataTermino'),
      tipo: c.getString('Tipo', ''),
      idStatusAtividade: c.getInt('IDStatusAtividade', 0),
      statusAtividade: c.getString('StatusAtividade', ''),
      receita: c.getBool('Receita', false),
      romaneioCanhoto: c.getBool('RomaneioCanhoto', false),
      termolabil: c.getBool('Termolabil', false),
      receberValor: c.getBool('ReceberValor', false),
      reentrega: c.getBool('Reentrega', false),
      acareacao: c.getBool('Acareacao', false),
      atrasado: c.getBool('Atrasado', false),
      latitude: c.getDoubleNullable('Latitude'),
      longitude: c.getDoubleNullable('Longitude'),
      fotos: c
          .getList<String>('Fotos', [])
          .map<FotoNew>((String e) => FotoNew(link: e))
          .toList(),
      tags: c
          .getList<Map<String, dynamic>>('Tags', [])
          .map<TagNew>((Map<String, dynamic> e) => TagNew.fromJson(e))
          .toList(),
      volumes: c
          .getList<Map<String, dynamic>>('Volumes', [])
          .map<VolumeNew>((Map<String, dynamic> e) => VolumeNew.fromJson(e))
          .toList(),
      info: info == null ? null : AcareacaoInfoModel.fromJson(info),
      preencherAutomaticoNomeClienteEntrega:
          c.getBool('PreencherAutomaticoNomeClienteEntrega', true),
      assinaturaObrigatoria: c.getBool('AssinaturaObrigatoria', false),
    );
  }

  List<IdStatusAtividadeEnum> get status {
    return [getIdStatusAtividadeEnum(idStatusAtividade)];
  }

  List<String> get statusString =>
      volumes.map(((e) => e.idos.toString())).toList();

  List<int> get idosList => volumes.map((e) => e.idos).toList();

  bool find(String search) {
    return nomeCliente
            .toLowerCase()
            .trim()
            .contains(search.toLowerCase().trim()) ||
        telefone.toLowerCase().trim().contains(search.toLowerCase().trim()) ||
        complemento
            .toLowerCase()
            .trim()
            .contains(search.toLowerCase().trim()) ||
        pontoReferencia
            .toLowerCase()
            .trim()
            .contains(search.toLowerCase().trim()) ||
        volumes.where((pedido) => pedido.find(search)).isNotEmpty;
  }

  String get iniciais => nomeCliente.iniciais;

  ClienteNew copyWith({
    String? nomeCliente,
    String? telefone,
    String? transportadora,
    bool? exibirNumeroClienteNaEntrega,
    String? complemento,
    String? pontoReferencia,
    DateTime? horaSLA,
    String? mensagem,
    String? logo,
    String? loja,
    double? valorRecebido,
    double? valorReceber,
    String? nomeRecebedor,
    DateTime? dataTermino,
    String? tipo,
    int? idStatusAtividade,
    String? statusAtividade,
    bool? receita,
    bool? romaneioCanhoto,
    bool? termolabil,
    bool? receberValor,
    bool? reentrega,
    bool? acareacao,
    bool? atrasado,
    double? latitude,
    double? longitude,
    List<FotoNew>? fotos,
    List<TagNew>? tags,
    List<VolumeNew>? volumes,
    AcareacaoInfoModel? info,
    bool? preencherAutomaticoNomeClienteEntrega,
    bool? assinaturaObrigatoria,
  }) {
    return ClienteNew(
      nomeCliente: nomeCliente ?? this.nomeCliente,
      telefone: telefone ?? this.telefone,
      transportadora:transportadora ?? this.transportadora,
      exibirNumeroClienteNaEntrega:
          exibirNumeroClienteNaEntrega ?? this.exibirNumeroClienteNaEntrega,
      complemento: complemento ?? this.complemento,
      pontoReferencia: pontoReferencia ?? this.pontoReferencia,
      horaSLA: horaSLA ?? this.horaSLA,
      mensagem: mensagem ?? this.mensagem,
      logo: logo ?? this.logo,
      loja: loja ?? this.loja,
      valorRecebido: valorRecebido ?? this.valorRecebido,
      valorReceber: valorReceber ?? this.valorReceber,
      nomeRecebedor: nomeRecebedor ?? this.nomeRecebedor,
      dataTermino: dataTermino ?? this.dataTermino,
      tipo: tipo ?? this.tipo,
      idStatusAtividade: idStatusAtividade ?? this.idStatusAtividade,
      statusAtividade: statusAtividade ?? this.statusAtividade,
      receita: receita ?? this.receita,
      romaneioCanhoto: romaneioCanhoto ?? this.romaneioCanhoto,
      termolabil: termolabil ?? this.termolabil,
      receberValor: receberValor ?? this.receberValor,
      reentrega: reentrega ?? this.reentrega,
      acareacao: acareacao ?? this.acareacao,
      atrasado: atrasado ?? this.atrasado,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      fotos: fotos ?? this.fotos,
      tags: tags ?? this.tags,
      volumes: volumes ?? this.volumes,
      info: info ?? this.info,
      preencherAutomaticoNomeClienteEntrega:
          preencherAutomaticoNomeClienteEntrega ??
              this.preencherAutomaticoNomeClienteEntrega,
      assinaturaObrigatoria:
          assinaturaObrigatoria ?? this.assinaturaObrigatoria,
    );
  }

  ClienteNew whereStatus(IdStatusAtividadeEnum value) {
    return copyWith(
        volumes:
            volumes.where((element) => element.idos == value.index).toList());
  }

  ClienteNew withStatus(int value, String newSituacao) {
    return copyWith(
      idStatusAtividade: value,
      statusAtividade: newSituacao,
    );
  }

  @override
  String toString() {
    return 'ClienteNew(nomeCliente: $nomeCliente, transportadora: $transportadora, telefone: $telefone, complemento: $complemento, pontoReferencia: $pontoReferencia, volume: $volumes, tags: $tags)';
  }

  @override
  bool operator ==(covariant ClienteNew other) {
    if (identical(this, other)) return true;

    return other.nomeCliente == nomeCliente &&
        other.telefone == telefone &&
        other.complemento == complemento &&
        other.pontoReferencia == pontoReferencia;
  }

  @override
  int get hashCode {
    return nomeCliente.hashCode ^
        telefone.hashCode ^
        complemento.hashCode ^
        pontoReferencia.hashCode;
  }
}
