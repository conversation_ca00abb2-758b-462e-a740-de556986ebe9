com.octalog.app-jetified-lifecycle-process-2.7.0-0 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\res
com.octalog.app-jetified-play-services-base-18.5.0-1 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07660f349a2fe95c07a6bc7026689012\transformed\jetified-play-services-base-18.5.0\res
com.octalog.app-lifecycle-runtime-2.7.0-2 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07d5ffcb9c6912ffd610dc84a8004380\transformed\lifecycle-runtime-2.7.0\res
com.octalog.app-jetified-fragment-ktx-1.7.1-3 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c2fe07ca1553c701c96a682f9b78a46\transformed\jetified-fragment-ktx-1.7.1\res
com.octalog.app-lifecycle-viewmodel-2.7.0-4 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df03071d3ea5a13ca8a701a85da690b\transformed\lifecycle-viewmodel-2.7.0\res
com.octalog.app-jetified-core-ktx-1.13.1-5 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1012b3d72a26572e790cb673f126d840\transformed\jetified-core-ktx-1.13.1\res
com.octalog.app-lifecycle-livedata-2.7.0-6 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\141e4ef551e1154e2ac426f165237904\transformed\lifecycle-livedata-2.7.0\res
com.octalog.app-work-runtime-ktx-2.8.1-7 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1462606f126f20e9fbf3f0e6f87e8686\transformed\work-runtime-ktx-2.8.1\res
com.octalog.app-browser-1.8.0-8 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a36ee1e87beff061ef9f2c053c4f171\transformed\browser-1.8.0\res
com.octalog.app-jetified-savedstate-1.2.1-9 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f37faca982b225960866422b14b0e48\transformed\jetified-savedstate-1.2.1\res
com.octalog.app-jetified-annotation-experimental-1.4.1-10 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\216891d106ff14188e4b2f0c556bc89f\transformed\jetified-annotation-experimental-1.4.1\res
com.octalog.app-work-runtime-2.8.1-11 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\res
com.octalog.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-12 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23adfe722c8911b0c72ccd7b6c20c672\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.octalog.app-jetified-core-common-2.0.4-13 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2748b27b9a4960040fddad0a76748cc0\transformed\jetified-core-common-2.0.4\res
com.octalog.app-cardview-1.0.0-14 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c52fddffb4a4858b65e09bc36907dcb\transformed\cardview-1.0.0\res
com.octalog.app-lifecycle-livedata-core-2.7.0-15 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30580628b6a4c90a31a9185e5e7442f2\transformed\lifecycle-livedata-core-2.7.0\res
com.octalog.app-recyclerview-1.0.0-16 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31710a1d11dfb3018b4d584b1dcc524f\transformed\recyclerview-1.0.0\res
com.octalog.app-jetified-notifications-5.1.29-17 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\res
com.octalog.app-jetified-lifecycle-livedata-core-ktx-2.7.0-18 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35d43ac5b93081fc719d5206591bc8b3\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.octalog.app-jetified-camera-lifecycle-1.3.4-19 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3776aed7364c7005ceaebea8b0d07e88\transformed\jetified-camera-lifecycle-1.3.4\res
com.octalog.app-jetified-datastore-preferences-release-20 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e70339c6033b424474d01ed8bf2ee0e\transformed\jetified-datastore-preferences-release\res
com.octalog.app-jetified-lifecycle-viewmodel-ktx-2.7.0-21 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4cf4aaf0c2a5f8e9be19976f292485c2\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.octalog.app-fragment-1.7.1-22 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\504a3b9bc759ca6028567aaea36c5498\transformed\fragment-1.7.1\res
com.octalog.app-sqlite-framework-2.3.0-23 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\522cc99b16944fe8a3f56ff78feb02e1\transformed\sqlite-framework-2.3.0\res
com.octalog.app-jetified-core-1.0.0-24 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5231da36518c5740ff6ec8f636c4adaa\transformed\jetified-core-1.0.0\res
com.octalog.app-jetified-location-5.1.29-25 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52f643eeb6fba9363586e00d8d04b656\transformed\jetified-location-5.1.29\res
com.octalog.app-jetified-android-pdf-viewer-3.2.0-beta.3-26 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54b8830a716a856dc674cceb64efb5c6\transformed\jetified-android-pdf-viewer-3.2.0-beta.3\res
com.octalog.app-slidingpanelayout-1.2.0-27 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b2e1faab8b81a1d02bc31ca8ba888c2\transformed\slidingpanelayout-1.2.0\res
com.octalog.app-transition-1.4.1-28 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c90255c19f9c683385e871d79ee1ace\transformed\transition-1.4.1\res
com.octalog.app-preference-1.2.1-29 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6fec79dfcef552889e3835cc7bab572a\transformed\preference-1.2.1\res
com.octalog.app-jetified-profileinstaller-1.3.1-30 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\res
com.octalog.app-jetified-savedstate-ktx-1.2.1-31 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73983292212b7db8ee4526aa2f086e1d\transformed\jetified-savedstate-ktx-1.2.1\res
com.octalog.app-jetified-window-1.2.0-32 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\res
com.octalog.app-sqlite-2.3.0-33 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7409cb136939289a3f71c3145316d990\transformed\sqlite-2.3.0\res
com.octalog.app-jetified-emoji2-views-helper-1.2.0-34 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7554e7f8574e8e9996d5b0d32aa8837d\transformed\jetified-emoji2-views-helper-1.2.0\res
com.octalog.app-coordinatorlayout-1.0.0-35 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\765cbe31314e92111ddb6ec950f59a6b\transformed\coordinatorlayout-1.0.0\res
com.octalog.app-core-1.13.1-36 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\res
com.octalog.app-webkit-1.12.1-37 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80812f0e193ab0497f129d7a30001e85\transformed\webkit-1.12.1\res
com.octalog.app-jetified-activity-1.9.3-38 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eca1956ddb86a2a7193da8df59556f\transformed\jetified-activity-1.9.3\res
com.octalog.app-jetified-window-java-1.2.0-39 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8bb9749c6545c229064751184c200f17\transformed\jetified-window-java-1.2.0\res
com.octalog.app-core-runtime-2.2.0-40 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8bc22c9aee1a6640ae9b0a38be5c9ed3\transformed\core-runtime-2.2.0\res
com.octalog.app-jetified-core-5.1.29-41 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\res
com.octalog.app-jetified-emoji2-1.2.0-42 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95a194f62dbd62c14638e0c38a7881a4\transformed\jetified-emoji2-1.2.0\res
com.octalog.app-jetified-datastore-release-43 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\973a8f19afc88ef8595e829184e66215\transformed\jetified-datastore-release\res
com.octalog.app-jetified-camera-camera2-1.3.4-44 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a600f391fc985fb6fe77353ae0360c6c\transformed\jetified-camera-camera2-1.3.4\res
com.octalog.app-jetified-datastore-core-release-45 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9f3b8c08aa98c50dc3947165cdbc861\transformed\jetified-datastore-core-release\res
com.octalog.app-localbroadcastmanager-1.1.0-46 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aff2a7bfa08e908128c419b32552e999\transformed\localbroadcastmanager-1.1.0\res
com.octalog.app-jetified-tracing-1.2.0-47 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba2395cf8fc4cc2c2d9a7b0f13aa21c0\transformed\jetified-tracing-1.2.0\res
com.octalog.app-jetified-startup-runtime-1.1.1-48 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4042ec9e8cc7d9cdf61197d11f01375\transformed\jetified-startup-runtime-1.1.1\res
com.octalog.app-appcompat-1.6.1-49 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdc1fd5a4f8cb18094ee01b408d70e6e\transformed\appcompat-1.6.1\res
com.octalog.app-jetified-play-services-basement-18.4.0-50 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf636ef2f0738047fe8129f320ef339e\transformed\jetified-play-services-basement-18.4.0\res
com.octalog.app-jetified-lifecycle-service-2.7.0-51 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9025f9b33edb75c4092f32eaead3b32\transformed\jetified-lifecycle-service-2.7.0\res
com.octalog.app-room-runtime-2.5.0-52 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca09e878cbb074545c6b4079b0555c5\transformed\room-runtime-2.5.0\res
com.octalog.app-jetified-in-app-messages-5.1.29-53 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22c70eb4700b3262896da0db5fa32c7\transformed\jetified-in-app-messages-5.1.29\res
com.octalog.app-jetified-camera-core-1.3.4-54 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41ce894b414f4e410f366a4a6ad7589\transformed\jetified-camera-core-1.3.4\res
com.octalog.app-jetified-firebase-common-21.0.0-55 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\res
com.octalog.app-jetified-lifecycle-runtime-ktx-2.7.0-56 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee40907df6635dadbd2abe5161721138\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.octalog.app-jetified-activity-ktx-1.9.3-57 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f63e4fb308e51274fbafb363fde41672\transformed\jetified-activity-ktx-1.9.3\res
com.octalog.app-jetified-appcompat-resources-1.6.1-58 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6da13a9846494f8148c639e8691af2b\transformed\jetified-appcompat-resources-1.6.1\res
com.octalog.app-jetified-firebase-messaging-24.1.1-59 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\res
com.octalog.app-debug-60 C:\projetos\octa.log\MOBILE\android\app\src\debug\res
com.octalog.app-main-61 C:\projetos\octa.log\MOBILE\android\app\src\main\res
com.octalog.app-rondolog-62 C:\projetos\octa.log\MOBILE\android\app\src\rondolog\res
com.octalog.app-rondologDebug-63 C:\projetos\octa.log\MOBILE\android\app\src\rondologDebug\res
com.octalog.app-debug-64 C:\projetos\octa.log\MOBILE\build\airplane_mode_checker\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-65 C:\projetos\octa.log\MOBILE\build\android_id\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-rondolog-66 C:\projetos\octa.log\MOBILE\build\app\generated\res\pngs\rondolog\debug
com.octalog.app-rondolog-67 C:\projetos\octa.log\MOBILE\build\app\generated\res\resValues\rondolog\debug
com.octalog.app-packageRondologDebugResources-68 C:\projetos\octa.log\MOBILE\build\app\intermediates\incremental\rondologDebug\packageRondologDebugResources\merged.dir
com.octalog.app-packageRondologDebugResources-69 C:\projetos\octa.log\MOBILE\build\app\intermediates\incremental\rondologDebug\packageRondologDebugResources\stripped.dir
com.octalog.app-rondologDebug-70 C:\projetos\octa.log\MOBILE\build\app\intermediates\merged_res\rondologDebug\mergeRondologDebugResources
com.octalog.app-debug-71 C:\projetos\octa.log\MOBILE\build\camera_android\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-72 C:\projetos\octa.log\MOBILE\build\connectivity_plus\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-73 C:\projetos\octa.log\MOBILE\build\device_info_plus\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-74 C:\projetos\octa.log\MOBILE\build\file_picker\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-75 C:\projetos\octa.log\MOBILE\build\firebase_core\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-76 C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-77 C:\projetos\octa.log\MOBILE\build\firebase_remote_config\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-78 C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-79 C:\projetos\octa.log\MOBILE\build\flutter_keyboard_visibility\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-80 C:\projetos\octa.log\MOBILE\build\flutter_pdfview\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-81 C:\projetos\octa.log\MOBILE\build\flutter_plugin_android_lifecycle\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-82 C:\projetos\octa.log\MOBILE\build\flutter_ringtone_player\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-83 C:\projetos\octa.log\MOBILE\build\flutter_webrtc\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-84 C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-85 C:\projetos\octa.log\MOBILE\build\google_mlkit_commons\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-86 C:\projetos\octa.log\MOBILE\build\google_mlkit_face_detection\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-87 C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-88 C:\projetos\octa.log\MOBILE\build\in_app_review\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-89 C:\projetos\octa.log\MOBILE\build\mobile_scanner\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-90 C:\projetos\octa.log\MOBILE\build\onesignal_flutter\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-91 C:\projetos\octa.log\MOBILE\build\open_file_android\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-92 C:\projetos\octa.log\MOBILE\build\package_info_plus\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-93 C:\projetos\octa.log\MOBILE\build\path_provider_android\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-94 C:\projetos\octa.log\MOBILE\build\pdfrx\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-95 C:\projetos\octa.log\MOBILE\build\permission_handler_android\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-96 C:\projetos\octa.log\MOBILE\build\rive_common\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-97 C:\projetos\octa.log\MOBILE\build\shared_preferences_android\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-98 C:\projetos\octa.log\MOBILE\build\speech_to_text\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-99 C:\projetos\octa.log\MOBILE\build\sqflite_android\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-100 C:\projetos\octa.log\MOBILE\build\sqlite3_flutter_libs\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-101 C:\projetos\octa.log\MOBILE\build\system_info_plus\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-102 C:\projetos\octa.log\MOBILE\build\url_launcher_android\intermediates\packaged_res\debug\packageDebugResources
com.octalog.app-debug-103 C:\projetos\octa.log\MOBILE\build\webview_flutter_android\intermediates\packaged_res\debug\packageDebugResources
