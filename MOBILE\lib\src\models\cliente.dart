// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:map_fields/map_fields.dart';

import 'id_status_atividade_enum.dart';
import 'pedido.dart';
import 'tag.dart';

class Cliente {
  final String nomeCliente;
  final String telefone;
  final String complemento;
  final String pontoReferencia;
  final List<Pedido> pedidos;
  final List<Tag> tags;
  Cliente({
    required this.nomeCliente,
    required this.complemento,
    required this.telefone,
    required this.pontoReferencia,
    required this.pedidos,
    required this.tags,
  });

  Cliente copyWith({
    String? nomeCliente,
    String? telefone,
    String? complemento,
    String? pontoReferencia,
    List<Pedido>? pedidos,
    List<Tag>? tags,
  }) {
    return Cliente(
      nomeCliente: nomeCliente ?? this.nomeCliente,
      telefone: telefone ?? this.telefone,
      complemento: complemento ?? this.complemento,
      pontoReferencia: pontoReferencia ?? this.pontoReferencia,
      pedidos: pedidos ?? this.pedidos,
      tags: tags ?? this.tags,
    );
  }

  Cliente get apenasEntrega {
    return copyWith(
      pedidos: pedidos
          .map((e) => e.apenasEntrega)
          .where((e) => e.volumes.isNotEmpty)
          .toList(),
    );
  }

  Cliente whereStatus(IdStatusAtividadeEnum value) {
    return copyWith(
      pedidos: pedidos
          .map((e) => e.whereStatus(value))
          .where((e) => e.volumes.isNotEmpty)
          .toList(),
    );
  }

  Cliente withStatus(int value, String newSituacao) {
    return copyWith(
      pedidos: pedidos
          .map((e) => e.withStatus(value, newSituacao))
          .where((e) => e.volumes.isNotEmpty)
          .toList(),
    );
  }

  Map<String, dynamic> toHiveMap() {
    return {
      'nomeCliente': nomeCliente,
      'complemento': complemento,
      'telefone': telefone,
      'pontoReferencia': pontoReferencia,
      'pedidos': pedidos.map((e) => e.toHiveMap()).toList(),
      'tags': tags.map((e) => e.toHiveMap()).toList(),
    };
  }

  factory Cliente.fromHiveMap(Map<String, dynamic> map) {
    final MapFields c = MapFields.load(map);
    return Cliente(
      // nomeCliente: map['nomeCliente'] as String,
      // complemento: map['complemento'] as String,
      // telefone: map['telefone'] as String,
      // pontoReferencia: map['pontoReferencia'] as String,
      // pedidos: (map['pedidos'] as List<dynamic>)
      //     .map((e) => Pedido.fromHiveMap(e))
      //     .toList(),
      // tags: (map['tags'] as List<dynamic>)
      //     .map((e) => Tag.fromHiveMap(e))
      //     .toList(),
      nomeCliente: c.getString('nomeCliente', ''),
      complemento: c.getString('complemento', ''),
      telefone: c.getString('telefone', ''),
      pontoReferencia: c.getString('pontoReferencia', ''),

      pedidos: c
          .getList<Map<String, dynamic>>('pedidos')
          .map((e) => Pedido.fromHiveMap(e))
          .toList(),
      tags: c
          .getList<Map<String, dynamic>>('tags')
          .map((e) => Tag.fromHiveMap(e))
          .toList(),
    );
  }

  factory Cliente.fromJson(Map<String, dynamic> map) {
    final MapFields c = MapFields.load(map);
    return Cliente(
      // nomeCliente: (map['NomeCliente'] ?? 'Consumidor Final') as String,
      // complemento: (map['Complemento'] ?? '') as String,
      // telefone: (map['Telefone'] ?? '') as String,
      // pontoReferencia: (map['PontoReferencia'] ?? '') as String,
      // pedidos: ((map['Pedidos'] ?? []).cast<Map<String, dynamic>>())
      //     .map<Pedido>((Map<String, dynamic> e) => Pedido.fromJson(e))
      //     .toList(),
      // tags: ((map['Tags'] ?? []).cast<Map<String, dynamic>>())
      //     .map<Tag>((Map<String, dynamic> e) => Tag.fromJson(e))
      //     .toList(),
      nomeCliente: c.getString('NomeCliente', ''),
      complemento: c.getString('Complemento', ''),
      telefone: c.getString('Telefone', ''),
      pontoReferencia: c.getString('PontoReferencia', ''),
      pedidos: c
          .getList<Map<String, dynamic>>('Pedidos')
          .map<Pedido>((e) => Pedido.fromJson(e))
          .toList(),
      tags: c
          .getList<Map<String, dynamic>>('Tags')
          .map<Tag>((e) => Tag.fromJson(e))
          .toList(),
    );
  }

  int get pedidosLength => pedidos.length;

  int get volumesLength => pedidos.fold(
      0, (int total, Pedido pedido) => total + pedido.volumes.length);

  List<IdStatusAtividadeEnum> get status {
    List<IdStatusAtividadeEnum> statusi = [];
    final p1 = pedidos.map((e) => e.status).toList();
    for (var e in p1) {
      statusi.addAll(e);
    }
    return statusi.toSet().toList();
  }

  List<String> get statusString =>
      pedidos.map((e) => e.statusString).expand((es) => es).toSet().toList();
  // List<String> get statusString => status.map((e) => e.title).toList();

  bool find(String search) {
    return nomeCliente
            .toLowerCase()
            .trim()
            .contains(search.toLowerCase().trim()) ||
        telefone.toLowerCase().trim().contains(search.toLowerCase().trim()) ||
        complemento
            .toLowerCase()
            .trim()
            .contains(search.toLowerCase().trim()) ||
        pontoReferencia
            .toLowerCase()
            .trim()
            .contains(search.toLowerCase().trim()) ||
        pedidos.where((pedido) => pedido.find(search)).isNotEmpty;
  }

  List<int> get idosList {
    List<int> idos = [];
    for (var pedido in pedidos) {
      idos.addAll(pedido.idosList);
    }
    return idos;
  }

  String get iniciais {
    if (nomeCliente.isEmpty) {
      return 'SN';
    }
    final nomes = nomeCliente.trim().split(' ').map((e) => e.trim()).toList();
    nomes.removeWhere((e) => e.isEmpty);
    if (nomes.length == 1) {
      return nomes[0].substring(0, 1).toUpperCase();
    }
    return '${nomes[0].substring(0, 1).toUpperCase()}${nomes[1].substring(0, 1).toUpperCase()}';
  }

  bool get receberValor {
    return pedidos.where((pedido) => pedido.receberValor).isNotEmpty;
  }

  @override
  String toString() {
    return 'Cliente(nomeCliente: $nomeCliente, telefone: $telefone, complemento: $complemento, pontoReferencia: $pontoReferencia, pedidos: $pedidos, tags: $tags)';
  }

  @override
  bool operator ==(covariant Cliente other) {
    if (identical(this, other)) return true;

    return other.nomeCliente == nomeCliente &&
        other.telefone == telefone &&
        other.complemento == complemento &&
        other.pontoReferencia == pontoReferencia;
  }

  @override
  int get hashCode {
    return nomeCliente.hashCode ^
        telefone.hashCode ^
        complemento.hashCode ^
        pontoReferencia.hashCode;
  }
}
