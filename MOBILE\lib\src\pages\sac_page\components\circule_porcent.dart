import 'package:flutter/material.dart';
import 'package:octalog/src/utils/theme_colors.dart';

//import './../../../utils/colors.dart';

class CirculePorcent extends StatelessWidget {
  final int fila;
  final bool atendimentoIniciado;
  final bool? buscando;

  const CirculePorcent({
    super.key,
    required this.fila,
    required this.atendimentoIniciado,
    this.buscando,
  });

  @override
  Widget build(BuildContext context) {
    double porcentagem = 1 - (fila / 100).clamp(0.0, 1.0);
    if (atendimentoIniciado) porcentagem = 1.0;

    return SizedBox(
      width: MediaQuery.of(context).size.width / 1.5,
      height: MediaQuery.of(context).size.width / 1.5,
      child: Stack(
        children: [
          Center(
            child: Container(
              width: MediaQuery.of(context).size.width / 1.5,
              height: MediaQuery.of(context).size.width / 1.5,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
              ),
              child: CircularProgressIndicator(
                strokeWidth: 10,
                backgroundColor: ThemeColors.customGrey(context),
                valueColor: AlwaysStoppedAnimation<Color>(
                  buscando == true
                      ? ThemeColors.customOrange(context)
                      : ThemeColors.customOrange(context).withOpacity(0.9),
                ),
                value: porcentagem,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: atendimentoIniciado
                    ? [
                        const Text(
                          "Seu atendimento foi iniciado\naguarde o contato do atendente!",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            textBaseline: TextBaseline.alphabetic,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ]
                    : [
                        const Text(
                          "Sua posição na fila é ",
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          "$filaº",
                          style: const TextStyle(
                            fontSize: 30,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Text(
                          "Assim que chegar a sua vez, você será redirecionado para o atendimento.",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            textBaseline: TextBaseline.alphabetic,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                      ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
