import 'dart:async';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/components/buttom_ls/button_ls_custom.dart';
import 'package:octalog/src/components/loading_ls/loading_ls.dart';
import 'package:octalog/src/database/meus_enderecos/delete_endereco.dart';
import 'package:octalog/src/database/roterizar/roterizar_repository.dart';
import 'package:octalog/src/models/meus_enderecos_model.dart';
import 'package:octalog/src/models/roterizar_model.dart';
import 'package:octalog/src/pages/home/<USER>/custom_enum_navigation.dart';
import 'package:octalog/src/pages/meus_enderecos/endereco_cep.dart';
import 'package:octalog/src/pages/meus_enderecos/roterizando_page.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../database/meus_enderecos/meus_enderecos_repository.dart';
import '../../database/meus_enderecos/meus_enderecos_shared_preferences.dart';
import '../../helpers/gps/gps_contract.dart';
import '../../utils/colors.dart';
import '../home/<USER>';

class MeusEnderecos extends StatefulWidget {
  final HomeController controller;
  const MeusEnderecos({super.key, required this.controller});

  @override
  State<MeusEnderecos> createState() => _MeusEnderecosState();
}

class _MeusEnderecosState extends State<MeusEnderecos> {
  List<MeusEnderecosModel> enderecosList = <MeusEnderecosModel>[];
  bool loadingData = false;
  int selectedIndex = 0;
  bool _isButtonDisabled = false;

  _alternaButton() {
    setState(() => _isButtonDisabled = !_isButtonDisabled);
  }

  Future<void> loadingEndereco() async {
    enderecosList = await getEndereco();
    final index = await lerEndereco();
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        selectedIndex = index;
        enderecosList;
      });
    });
  }

  @override
  initState() {
    super.initState();

    loadingEndereco();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void enviarRota() async {
    try {
      _alternaButton();
      if (!loadingData) {
        final position = await GpsHelperContract.instance.receberLocalizacao();
        final endereco = enderecosList[selectedIndex];
        debugPrint(enderecosList.toString());
        await RoterizarRepository().sendRoterizar(
          RoterizarModel(
            inicioLatitude: position.latitude,
            inicioLongitude: position.longitude,
            fimLatitude: endereco.latitude,
            fimLongitude: endereco.longitude,
          ),
        );

        await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const RoterizandoPage(),
          ),
        );
      }

      await widget.controller.fetchAtividades();
      widget.controller.setPageSelected(HomeNavigationEnum.route);
      loadingData = false;
    } catch (_) {
      loadingData = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: getEndereco(),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return pageview(context);
        } else {
          return const LoadingLs();
        }
      },
    );
  }

  Widget pageview(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        title: Text(
          'Meus Endereços',
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        backgroundColor: const Color.fromRGBO(230, 230, 230, 1),
      ),
      backgroundColor: const Color.fromRGBO(230, 230, 230, 1),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Expanded(
            child: enderecosList.isEmpty
                ? Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Image.asset(
                      //   'assets/images/mapa_circular.png',
                      //   height: MediaQuery.of(context).size.height * 0.4,
                      // ),
                      // Padding(
                      //   padding: const EdgeInsets.only(top: 10),
                      //   child: Text(
                      //     'Para melhor roteirização,',
                      //     textAlign: TextAlign.center,
                      //     style: GoogleFonts.roboto(
                      //       fontSize: MediaQuery.of(context).size.height * 0.02,
                      //       color: ColorsCustom.customBlack,
                      //       fontWeight: FontWeight.bold,
                      //     ),
                      //   ),
                      // ),
                      // const SizedBox(
                      //   height: 1,
                      // ),
                      Padding(
                        padding: const EdgeInsets.only(right: 15, left: 15),
                        child: Text(
                          'Para melhor roteirização, informe o endereço que irá após concluír as entregas.',
                          textAlign: TextAlign.center,
                          style: GoogleFonts.roboto(
                            fontSize: MediaQuery.of(context).size.height * 0.02,
                            color: ColorsCustom.customBlack,
                            fontWeight: FontWeight.w800,
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 30),
                        child: SizedBox(
                          width: 85,
                          height: 85,
                          child: CircleAvatar(
                            backgroundColor:
                                ThemeColors.customOrange(context),
                            child: SizedBox(
                              width: 70,
                              height: 70,
                              child: CircleAvatar(
                                backgroundColor: ThemeColors.customOrange(context),
                                child: IconButton(
                                  iconSize: 30,
                                  icon: const Icon(
                                    Icons.arrow_forward_ios_outlined,
                                    color: Colors.white,
                                  ),
                                  onPressed: () async {
                                    await Navigator.of(context).push(
                                      MaterialPageRoute(
                                        builder: (context) => EnderecoCep(
                                            controller: widget.controller),
                                      ),
                                    );
                                    loadingEndereco();
                                  },
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                : SingleChildScrollView(
                    child: Column(
                      children: List.generate(enderecosList.length, (index) {
                        final MeusEnderecosModel endereco =
                            enderecosList[index];

                        return Padding(
                          padding: const EdgeInsets.only(
                              left: 10, right: 10, top: 20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Container(
                                width: double.infinity,
                                alignment: Alignment.center,
                                child: Card(
                                  color: selectedIndex == index
                                      ? const Color(0xFFFEF5EE)
                                      : const Color.fromRGBO(230, 230, 230, 1),
                                  elevation: 0.5,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.only(
                                        top: 10, bottom: 10),
                                    child: ListTile(
                                      onTap: () async {
                                        await gravarEndereco(index);
                                        setState(
                                          () {
                                            selectedIndex = index;
                                          },
                                        );
                                      },
                                      title: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            endereco.apelido.isNotEmpty
                                                ? endereco.apelido
                                                : 'Não informado',
                                            style: GoogleFonts.roboto(
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const SizedBox(
                                            height: 10,
                                          ),
                                          Text(
                                            endereco.enderecoSubtititle,
                                            style: GoogleFonts.roboto(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w400,
                                            ),
                                          ),
                                          const SizedBox(
                                            // use SizedBox
                                            height: 10,
                                          ),
                                        ],
                                      ),
                                      subtitle: Text(
                                        index == selectedIndex
                                            ? 'Esse e seu endereço principal'
                                            : '',
                                        style: GoogleFonts.roboto(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                      trailing: Column(
                                        children: [
                                          IconButton(
                                            onPressed: () async {
                                              removerEndereco();
                                              selectedIndex = 0;
                                              await _deleteMeuEndereco(
                                                  endereco);
                                            },
                                            icon: Icon(Icons.delete,
                                                color:
                                                    ThemeColors.customOrange(context)),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                    ),
                  ),
          ),
          Container(
            padding: const EdgeInsets.only(bottom: 90),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.only(
                      left: 20,
                    ),
                    height: 60,
                    child: enderecosList.isEmpty
                        ? Container()
                        : Center(
                            child: ButtonLsCustom(
                              horizontal: 5,
                              colorBackground: ColorsCustom.customGrey,
                              text:
                                  _isButtonDisabled ? '....' : 'Novo Endereço',
                              onPressed: _isButtonDisabled
                                  ? null
                                  : () async {
                                      await Navigator.of(context).push(
                                        MaterialPageRoute(
                                          builder: (context) => EnderecoCep(
                                              controller: widget.controller),
                                        ),
                                      );
                                      loadingEndereco();
                                    },
                            ),
                          ),
                  ),
                ),
                const SizedBox(
                  width: 10,
                ),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.only(right: 20),
                    height: 60,
                    child: enderecosList.isEmpty
                        ? Container()
                        : ButtonLsCustom(
                            text: _isButtonDisabled
                                ? 'Carregando...'
                                : 'Calcular Rota',
                            onPressed: _isButtonDisabled ? null : enviarRota,
                          ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future _deleteMeuEndereco(MeusEnderecosModel meusEnderecosModel) async {
    getEndereco().then((value) {
      setState(() {
        final int indexOf = enderecosList.indexOf(meusEnderecosModel);
        enderecosList.removeAt(indexOf);
      });
    });

    final response =
        await DeleteEndereco().delete(meusEnderecosModel.idAgenteEndereco);
    return response;
  }
}
