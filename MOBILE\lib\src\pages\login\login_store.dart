import 'package:flutter/cupertino.dart';
import 'package:octalog/src/models/user.dart';
import 'package:octalog/src/pages/login/login_state.dart';
import 'package:octalog/src/utils/offline_helper.dart';

import '../../helpers/login/login.dart';

class LoginStore {
  final String _fakeLoginID = '3329';
  final String _fakeLoginSenha = 'AL';

  ValueNotifier<LoginState> state = ValueNotifier(LoginState());

  void setLoading(bool value) {
    state.value = state.value.copyWith(loading: value);
  }

  void setErro(bool value) {
    state.value = state.value.copyWith(error: value);
  }

  void setSuccess(bool value) {
    state.value = state.value.copyWith(success: value);
  }

  void setMessage(String value) {
    state.value = state.value.copyWith(message: value);
  }

  UserData? _fakeLogin(String id, String senha) {
    if (id == _fakeLoginID && senha.toString().trim().toUpperCase() == _fakeLoginSenha) {
      offlineStore.setForceFakeOnline(true);
      return UserData(
        nomeCompleto: 'Agente de Teste Das Lojas',
        telefone: '32999999999',
        email: '<EMAIL>',
        uf: 'SP',
        foto: '',
        cpf: '34448306020',
        idTipoAgenteUberizado: 0,
        userLogin: UserLogin(usuario: id, senha: senha, token: "****************************", atualizarCadastro: false),
        permiteTransferenciaMobile: false,
        loginMocked: true,
      );
    }
    return null;
  }

  Future<UserData?> _loginUsuario(String id, String senha) async {
    if (id == _fakeLoginID) {
      return _fakeLogin(id, senha);
    }
    return await Login.instance
        .login(id, senha)
        .timeout(
          const Duration(seconds: 30),
          onTimeout: () {
            setMessage('Tempo de conexão excedido \n Verifique sua conexão com a internet e tente novamente');
            setErro(true);
            setLoading(false);
            return null;
          },
        );
  }

  Future<UserData?> login(id, senha) async {
    setLoading(true);
    setErro(false);
    setSuccess(false);
    try {
      final response = await _loginUsuario(id, senha);
      setLoading(false);
      if (response == null) {
        if (!state.value.error) {
          setMessage(Login.instance.mensagem);

          if (Login.instance.usuarioInativo) {
            setErro(true);
          } else {
            setErro(true);
          }
          return null;
        }
      } else {
        setSuccess(true);
        return response;
      }
    } catch (e) {
      setErro(true);
      setMessage(e.toString());
      setLoading(false);
      return null;
    }
    return null;
  }

  void setVisiblePassword(bool value) {
    state.value = state.value.copyWith(visiblePassword: value);
  }
}
