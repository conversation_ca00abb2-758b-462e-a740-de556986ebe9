#!/bin/bash

# Script para gerar ícones personalizados por flavor
# Uso: ./scripts/generate_icons.sh [flavor]
# Exemplo: ./scripts/generate_icons.sh octalog

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para exibir ajuda
show_help() {
    echo -e "${BLUE}Gerador de Ícones por Flavor - Octalog${NC}"
    echo ""
    echo "Uso: $0 [flavor]"
    echo ""
    echo "Flavors disponíveis:"
    echo "  octalog   - Cliente Octalog"
    echo "  arcargo   - Cliente ArCargo"
    echo "  connect   - Cliente Connect"
    echo "  rondolog  - Cliente RondoLog"
    echo "  spotlog   - Cliente SpotLog"
    echo "  all       - Gerar ícones para todos os flavors"
    echo ""
    echo "Exemplos:"
    echo "  $0 octalog"
    echo "  $0 all"
    echo ""
}

# Função para gerar ícones de um flavor específico
generate_flavor_icons() {
    local flavor=$1
    local config_file="flutter_launcher_icons-${flavor}.yaml"
    
    echo -e "${YELLOW}Gerando ícones para o flavor: ${flavor}${NC}"
    
    # Verificar se o arquivo de configuração existe
    if [ ! -f "$config_file" ]; then
        echo -e "${RED}Erro: Arquivo de configuração não encontrado: $config_file${NC}"
        return 1
    fi
    
    # Verificar se a imagem do logo existe
    local image_path=$(grep "image_path:" "$config_file" | head -1 | sed 's/.*image_path: *"\([^"]*\)".*/\1/')
    if [ ! -f "$image_path" ]; then
        echo -e "${RED}Erro: Imagem não encontrada: $image_path${NC}"
        echo -e "${YELLOW}Verifique se o arquivo existe e tem a extensão correta.${NC}"
        return 1
    fi
    
    echo -e "${BLUE}Usando configuração: $config_file${NC}"
    echo -e "${BLUE}Imagem do logo: $image_path${NC}"
    
    # Gerar os ícones
    flutter pub run flutter_launcher_icons:main -f "$config_file"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Ícones gerados com sucesso para o flavor: ${flavor}${NC}"
    else
        echo -e "${RED}❌ Erro ao gerar ícones para o flavor: ${flavor}${NC}"
        return 1
    fi
}

# Função principal
main() {
    # Verificar se estamos no diretório correto
    if [ ! -f "pubspec.yaml" ]; then
        echo -e "${RED}Erro: Execute este script a partir do diretório raiz do projeto Flutter${NC}"
        exit 1
    fi
    
    # Verificar se o flutter_launcher_icons está instalado
    if ! flutter pub deps | grep -q "flutter_launcher_icons"; then
        echo -e "${RED}Erro: flutter_launcher_icons não está instalado${NC}"
        echo -e "${YELLOW}Execute: flutter pub get${NC}"
        exit 1
    fi
    
    local flavor=$1
    
    # Se nenhum parâmetro foi passado, mostrar ajuda
    if [ -z "$flavor" ]; then
        show_help
        exit 1
    fi
    
    # Processar o flavor solicitado
    case $flavor in
        "octalog"|"arcargo"|"connect"|"rondolog"|"spotlog")
            generate_flavor_icons "$flavor"
            ;;
        "all")
            echo -e "${BLUE}Gerando ícones para todos os flavors...${NC}"
            for f in octalog arcargo connect rondolog spotlog; do
                generate_flavor_icons "$f"
                echo ""
            done
            echo -e "${GREEN}✅ Todos os ícones foram gerados!${NC}"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            echo -e "${RED}Erro: Flavor inválido: $flavor${NC}"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Executar função principal
main "$@"
