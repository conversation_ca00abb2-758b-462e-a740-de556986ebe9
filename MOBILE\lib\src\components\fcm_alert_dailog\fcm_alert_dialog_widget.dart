import 'package:flutter/material.dart';
import 'package:octalog/src/components/fcm_alert_dailog/models/fcm_deslocamento_get.dart';
import 'package:octalog/src/components/fcm_alert_dailog/sub_widgets/fcm_alert_dialog_chegada.dart';
import 'package:octalog/src/components/fcm_alert_dailog/sub_widgets/fcm_alert_dialog_coleta_home.dart';
import 'package:octalog/src/components/fcm_alert_dailog/sub_widgets/fcm_alert_dialog_coleta_nova.dart';
import 'package:octalog/src/components/fcm_alert_dailog/sub_widgets/ls_alert_dialog_devolucao.dart';

import '../uberizado_single/uberizado_nova_pagina.dart';
import 'fcm_alert_dialog_state.dart';
import 'fcm_alert_dialog_store.dart';
import 'sub_widgets/fcm_alert_dialog_deslocamento.dart';

class FcmAlertDialogWidget extends StatefulWidget {
  final FcmDeslocamentoGet dados;
  const FcmAlertDialogWidget({
    super.key,
    required this.dados,
  });

  @override
  State<FcmAlertDialogWidget> createState() => _FcmAlertDialogWidgetState();
}

class _FcmAlertDialogWidgetState extends State<FcmAlertDialogWidget> {
  late final FcmAlertDialogStore store;

  @override
  void initState() {
    store = FcmAlertDialogStore(widget.dados);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<FcmAlertState>(
      valueListenable: store.state,
      builder: (ctx, state, _) {
        final step = state.fcmAlertPageStep;
        switch (step) {
          case FcmAlertPageStep.deslocamento:
            return FcmAlertDailogDeslocamento(store: store);
          case FcmAlertPageStep.chegada:
            return FcmAlertDailogChegada(store: store);
          case FcmAlertPageStep.coleta:
            if (store.state.value.fcmAlertDados.reverso) {
              return FcmAlertDailogDevolucao(store: store);
            }
            if (state.fcmAlertDados.tipoModalidadeColeta == 1) {
              return FcmAlertDialogColetaHome(store: store);
            }
            if (state.fcmAlertDados.tipoModalidadeColeta == 2) {
              return FcmAlertDailogColetaNova(store: store);
            }
            if (state.fcmAlertDados.tipoModalidadeColeta == 3) {
              return UberizadoNovaPagina(store: store);
            }
            return UberizadoNovaPagina(store: store);
        }
      },
    );
  }
}
