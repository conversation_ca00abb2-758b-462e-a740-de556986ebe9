import 'dart:convert';

import 'package:hive/hive.dart';
import 'package:octalog/src/models_new/endereco_new.dart';

class EnderecoNewOfflineDatabase {
  EnderecoNewOfflineDatabase._() {
    _init();
  }
  static final EnderecoNewOfflineDatabase instance =
      EnderecoNewOfflineDatabase._();

  late Box<String> _box;
  bool _initialized = false;
  bool _waiting = false;

  Future<void> _init() async {
    while (_waiting) {
      await Future.delayed(const Duration(milliseconds: 300));
    }
    if (!_initialized) {
      _waiting = true;
      try {
        _box = await Hive.openBox<String>('endereconew_offline_map');
        _initialized = true;
      } catch (_) {}
      _waiting = false;
    }
  }

  Future<List<EnderecoNew>> getAtividades() async {
    await _init();
    final data = _box.values;
    if (data.isEmpty) {
      return [];
    }
    final d = data.map((e) => EnderecoNew.fromHiveMap(jsonDecode(e))).toList();
    return d;
  }

  Future<void> updateAtividade(EnderecoNew enderecoNew) async {
    await _init();
    final data =
        _box.values.map((e) => EnderecoNew.fromHiveMap(jsonDecode(e))).toList();
    final index = data.indexWhere(
        (e) => e.idEnderecoCliente == enderecoNew.idEnderecoCliente);
    if (index == -1) {
      _box.add(json.encode(enderecoNew.toHiveMap()));
    } else {
      _box.putAt(index, json.encode(enderecoNew.toHiveMap()));
    }
  }

  Future<void> clear() async {
    await _init();
    await _box.clear();
  }

  Future<void> deleteWhereContain(List<int> idsAtividadeDel) async {
    await _init();
    final keys = _box.keys;
    List values = keys
        .where((e) => idsAtividadeDel.contains(
            EnderecoNew.fromHiveMap(jsonDecode(_box.get(e) ?? '{}'))
                .idEnderecoCliente))
        .toList();
    return await _box.deleteAll(values);
  }
}
