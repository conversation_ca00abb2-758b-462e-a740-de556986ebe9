import 'dart:convert';

import 'package:airplane_mode_checker/airplane_mode_checker.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
// import 'package:countly_flutter/countly_flutter.dart';
import 'package:drift/drift.dart';
import 'package:flutter/foundation.dart';
import 'package:octalog/src/helpers/airplane.dart';
import 'package:mime/mime.dart';
import 'package:path/path.dart';

import '../../helpers/login/login.dart';
import '../../helpers/web_connector.dart';
import '../../models/user.dart';
import '../../pages/home/<USER>';
import '../../utils/gzip_util.dart';
import '../config_blob/config_database.dart';
import '../log_database/log_database.dart';
import 'offline_request_drift_model.dart';
import 'offline_request_hive.dart';

class OfflineRequestDatabase {
  OfflineRequestDatabase._() {
    _init();
    _enviar();
  }
  static final OfflineRequestDatabase instance = OfflineRequestDatabase._();

  // late Box<String> _box;
  bool _initialized = false;
  bool _waiting = false;

  Future<void> _init() async {
    while (_waiting) {
      await Future.delayed(const Duration(milliseconds: 300));
    }
    if (!_initialized) {
      _waiting = true;
      try {
        // _box = await Hive.openBox<String>('offline_request_map');
        _initialized = true;
      } catch (_) {}
      _waiting = false;
    }
  }

  static Future<List<Map<String, dynamic>>> convertStringToMap(List<String> vvs) async {
    return vvs.map<Map<String, dynamic>>((e) {
      return json.decode(e);
    }).toList();
    // final values = vvs.slices(100);
    // final valuesResult = await Future.wait(values.map((e) async {
    //   return e.map((r) => json.decode(r) as Map<String, dynamic>).toList();
    // }));
    // return valuesResult.expand((e) => e).toList();
  }

  static Future<List<String>> convertMapToString(List<Map<String, dynamic>> vvs) async {
    return vvs.map((e) => json.encode(e)).toList();
    // final values = vvs.slices(100);
    // final valuesResult = await Future.wait(values.map((e) async {
    //   return e.map((r) => json.encode(r)).toList();
    // }));
    // return valuesResult.expand((e) => e).toList();
  }

  Future<List<OfflineRequest>> getRestanteValues() {
    return _getValues();
  }

  Future<List<OfflineRequest>> _getValues() async {
    await _init();
    // final valuesSplit = _box.values.toList().slices(50);
    // final futures = valuesSplit.map((e) async {
    //   return await compute(convertStringToMap, _box.values.toList());
    // }).toList();
    // final values = await Future.wait(futures);
    // return values.expand((e) => e).map((e) {
    // final values = await compute(convertStringToMap, _box.values.toList());
    final db = OfflineRequestDatabaseDrift.instance;
    final values = await (db.select(db.offlineRequestDrift)).get();
    return values.map((e) => OfflineRequest.fromDrift(e)).toList();
  }

  Future<void> deleteEnviados() async {
    final db = OfflineRequestDatabaseDrift.instance;
    final ontem = DateTime.now().subtract(const Duration(days: 1));
    await Future.wait([
      (db.delete(db.offlineRequestDrift)..where((tbl) => tbl.enviado.equals(true) & tbl.dataHora.isSmallerThanValue(ontem))).go(),
      // _box.deleteAll(_box.values
      //     .where(
      //       (l) => OfflineRequest.fromHiveMap(
      //         json.decode(l),
      //       ).enviado,
      //     )
      //     .toList()),
    ]);
  }

  Future<void> _enviar() async {
    while (!_initialized) {
      await Future.delayed(const Duration(seconds: 1));
    }
    while (true) {
      String logString = '';
      String errorString = '';
      late final OfflineRequest envio;
      try {
        final list = (await _getValues()).where((l) => !l.enviado).toList();

        if (list.isEmpty) {
          // await deleteEnviados();
          if (HomeController.instance.state.value.havenegativa) {
            await HomeController.instance.fetchAtividades();

            HomeController.instance.setHaveNegativa(false);
          }
          await Future.delayed(const Duration(seconds: 5));
          debugPrint('List ${list.isEmpty}');
        } else {
          final obj = list.first;
          envio = obj;
          logString = obj.endpoint;

          final resp = await _enviarEvento(obj, obj.dataHora);

          debugPrint('passei aqui: $resp _______ ${obj.dataHora}-------->>>>>');
          if (resp == null) {
            // final index = (await _getValues()).indexWhere(
            //   (i) => i.uuid == list.first.uuid,
            // );
            debugPrint('passei depois do null: $resp');
            // if (index != -1) {
            // final map = list.first.copyWith(enviado: true).toHiveMap();
            // await _box.putAt(index, json.encode(map));
            final uuid = obj.uuid;
            final db = OfflineRequestDatabaseDrift.instance;
            await (db.update(db.offlineRequestDrift)..where((tbl) => tbl.uuid.equals(uuid))).write(const OfflineRequestDriftCompanion(enviado: Value(true)));
            debugPrint('passei aqui ultimo: $resp');
            // }
            await LogDatabase.instance.logSuccess('Envio Offline', obj.endpoint, 'Enviado com sucesso!', {});
          } else {
            errorString = resp;
            if (!resp.contains('Link da imagem:')) {
              throw Exception('$resp |||| ${obj.endpoint}');
            }
          }
        }
      } catch (e) {
        try {
          final statusAirplaneMode = await Airplane.checkAirplaneMode();
          final connectivityResult = await (Connectivity().checkConnectivity());
          final usuario =
              Login.instance.usuarioLogado ??
              UserData(
                nomeCompleto: 'Usuario não logado',
                telefone: '',
                cpf: '',
                email: '',
                uf: '',
                foto: '',
                userLogin: UserLogin(senha: '', usuario: '', atualizarCadastro: false, token: 'Sem token'),
                idTipoAgenteUberizado: 0,
                permiteTransferenciaMobile: true,
              );

          // final error = {
          //   'airplane_mode': statusAirplaneMode == AirplaneModeStatus.on,
          //   'airplane_mode_status': statusAirplaneMode.toString(),
          //   'connectivity': connectivityResult == ConnectivityResult.none,
          //   'connectivity_result': connectivityResult.toString(),
          //   'user': usuario.nomeCompleto,
          //   'token': usuario.usuario,
          // };

          await LogDatabase.instance.logError('Envio Offline', errorString.isEmpty ? e.toString() : errorString, logString, {
            'body': envio.body,
            'headers': envio.headers,
            'endpoint': envio.endpoint,
            'data_hora': envio.dataHora?.toIso8601String(),
            'enviado': envio.enviado,
            'airplane_mode': statusAirplaneMode == AirplaneModeStatus.on,
            'airplane_mode_status': statusAirplaneMode.toString(),
            'connectivity': connectivityResult == ConnectivityResult.none,
            'connectivity_result': connectivityResult.toString(),
            'user': usuario.nomeCompleto,
            'token': usuario.usuario,
          });
          // Countly.recordDartError(
          //   Exception('ERRO EVENTO - $e - $error'),
          //   s,
          // );
        } catch (e) {
          // Countly.recordDartError(
          //   Exception("Erro desconhecido no envio da request: $e"),
          //   s,
          // );
        }
        debugPrint('final');

        await Future.delayed(const Duration(seconds: 10));
      }
    }
  }

  Future<int> getQtdEnventosRestantes() async {
    final restantes = (await _getValues()).where((e) => !e.enviado).length;

    await _init();
    debugPrint('restantes: $restantes');
    return restantes;
  }

  Future<List<int>> idsAtividadesOffline() async {
    await _init();
    final ats = (await _getValues()).where((a) => !a.enviado).toList();
    return ats.map((a) => a.idAtividade).toList();
  }

  Future<List<int>> idsAtividadesOfflineForReplace() async {
    await _init();
    final hoje = DateTime.now();
    final horaAtras = hoje.add(const Duration(minutes: -15));
    final ats = (await _getValues()).where((a) => !a.enviado || (a.dataHora ?? DateTime.now()).isAfter(horaAtras)).toList();
    return ats.map((a) => a.idAtividade).toList();
  }

  Future<void> addData(OfflineRequest data) async {
    await _init();
    // await _box.add(json.encode(data.toHiveMap()));
    final db = OfflineRequestDatabaseDrift.instance;
    await db.into(db.offlineRequestDrift).insert(data.toDrift());
  }

  Future<String?> _enviarEvento(OfflineRequest data, [DateTime? dataEnviada]) async {
    try {
      final conn = WebConnector();
      if (data.isPhoto && (data.fileLink?.isEmpty ?? true)) {
        Uint8List content = data.fileBytes!;
        String contentType = lookupMimeType(basename(data.fileName ?? '')) ?? '';
        final resp = await conn.postImageBlobStorage(content: content, contentType: contentType, fileName: data.fileName ?? '');
        if (resp == null) {
          return 'Erro ao enviar imagem';
        }
        final newBody = data.body ?? {};
        newBody['Foto'] = resp;
        // final data2 = data.copyWith(
        //   fileLink: resp,
        //   body: newBody,
        // );
        // final index = (await _getValues()).indexWhere(
        //   (i) => i.uuid == data.uuid,
        // );
        // await _box.putAt(index, json.encode(data2.toHiveMap()));
        final uuid = data.uuid;
        final db = OfflineRequestDatabaseDrift.instance;
        await (db.update(db.offlineRequestDrift)
          ..where((tbl) => tbl.uuid.equals(uuid))).write(OfflineRequestDriftCompanion(fileLink: Value(resp), body: Value(json.encode(newBody))));
        return 'Link da imagem: $resp';
      } else if (data.method == 'GET') {
        final resp = await conn.get(data.endpoint, headers: data.headers);
        if (resp.statusCode == 200) {
          return null;
        } else {
          return resp.statusCode.toString();
        }
      } else if (data.method == 'PUT') {
        try {
          if (data.endpoint == '/deslocamento/aceite') {
            final body = data.body;
            String? latitude = body?['Latitude'];
            String? longitude = body?['Longitude'];
            latitude = latitude == "null" ? null : latitude;
            longitude = longitude == "null" ? null : longitude;
            body?['Latitude'] = latitude;
            body?['Longitude'] = longitude;
            data = data.copyWith(body: body);
          }
        } catch (_) {}
        final resp = await conn.put(data.endpoint, body: data.body, headers: data.headers);
        if (resp.statusCode == 200) {
          return null;
        } else {
          return resp.statusCode.toString();
        }
      } else if (data.method == 'POST') {
        bool isEntrega = data.endpoint == '/atividades/entrega' || data.endpoint == '/entrega/entrega';

        if (isEntrega) {
          try {
            final body = data.body;
            String? dataEntrega = body?['Entrega']['DataHora'];
            /*
            if (dataEntrega != null && dataEntrega.contains('T')) {
              final dt = DateTime.parse(dataEntrega);
              final bodyJson = data.body;
              bodyJson?['Entrega']['DataHora'] =
                  '${dt.year}-${dt.month.toString().padLeft(2, '0')}-${dt.day.toString().padLeft(2, '0')} ${dt.hour.toString().padLeft(2, '0')}:${dt.minute.toString().padLeft(2, '0')}';
              data = data.copyWith(body: bodyJson);
            }
            */
            if (dataEntrega != null && !dataEntrega.contains('T')) {
              dataEntrega = dataEntrega.replaceAll(' ', 'T');
              dataEntrega += ':00.000';
              final bodyJson = data.body;
              bodyJson?['DataHora'] = dataEntrega;
              data = data.copyWith(body: bodyJson);
            }
          } catch (_) {}
        }
        final config = await ConfigDatabase.instance.getConfig();
        final databody = config.usarGZipSincronismo && isEntrega ? GZipUtil.compress(jsonEncode(data.body ?? {})) : data.body;
        final resp = await conn.post(
          data.endpoint,
          body: databody,
          headers: data.headers,
          queryParameters: {'UseGZipUpload': config.usarGZipSincronismo && isEntrega},
        );
        if (resp.statusCode == 200) {
          return null;
        } else {
          return resp.statusCode.toString();
        }
      } else if (data.method == 'DELETE') {
        final resp = await conn.delete(data.endpoint, headers: data.headers);
        if (resp.statusCode == 200) {
          return null;
        } else {
          return resp.statusCode.toString();
        }
      }
      return null;
    } catch (e) {
      return e.toString();
    }
  }
}
