#!/bin/bash

# Script para build iOS com flavors
# Uso: ./scripts/build_ios.sh [flavor] [build_type]
# Exemplo: ./scripts/build_ios.sh connect release

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para exibir ajuda
show_help() {
    echo -e "${BLUE}Build iOS com Flavors${NC}"
    echo ""
    echo "Uso: $0 [flavor] [build_type]"
    echo ""
    echo "Flavors disponíveis:"
    echo "  - octalog   (padrão)"
    echo "  - up360"
    echo "  - connect"
    echo "  - rondolog"
    echo "  - spotlog"
    echo "  - boyviny"
    echo ""
    echo "Build types:"
    echo "  - release   (padrão)"
    echo "  - debug"
    echo ""
    echo "Exemplos:"
    echo "  $0 connect release"
    echo "  $0 up360 debug"
    echo "  $0 octalog"
    echo ""
}

# Verificar argumentos
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# Definir flavor (padrão: octalog)
FLAVOR=${1:-octalog}

# Definir build type (padrão: release)
BUILD_TYPE=${2:-release}

echo -e "${BLUE}🍎 Iniciando build iOS${NC}"
echo -e "${BLUE}📱 Flavor: ${YELLOW}$FLAVOR${NC}"
echo -e "${BLUE}🔧 Build Type: ${YELLOW}$BUILD_TYPE${NC}"
echo ""

# Validar flavor
case $FLAVOR in
    "octalog"|"up360"|"connect"|"rondolog"|"spotlog"|"boyviny")
        echo -e "${GREEN}✅ Flavor válido: $FLAVOR${NC}"
        ;;
    *)
        echo -e "${RED}❌ Flavor inválido: $FLAVOR${NC}"
        echo -e "${YELLOW}Flavors disponíveis: octalog, up360, connect, rondolog, spotlog, boyviny${NC}"
        exit 1
        ;;
esac

# Validar build type
case $BUILD_TYPE in
    "debug"|"release"|"profile")
        echo -e "${GREEN}✅ Build type válido: $BUILD_TYPE${NC}"
        ;;
    *)
        echo -e "${RED}❌ Build type inválido: $BUILD_TYPE${NC}"
        echo -e "${YELLOW}Build types disponíveis: debug, release, profile${NC}"
        exit 1
        ;;
esac

echo ""

# Configurar iOS para o flavor
echo -e "${BLUE}🔧 Configurando iOS para flavor: ${YELLOW}$FLAVOR${NC}"
./scripts/configure_ios_flavor.sh "$FLAVOR" "$BUILD_TYPE"

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Erro ao configurar iOS para o flavor: $FLAVOR${NC}"
    exit 1
fi

echo ""

# Gerar ícones para o flavor
echo -e "${BLUE}🎨 Gerando ícones para o flavor: ${YELLOW}$FLAVOR${NC}"
if [ -f "scripts/generate_icons.sh" ]; then
    ./scripts/generate_icons.sh "$FLAVOR"
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Erro ao gerar ícones para o flavor: $FLAVOR${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠️  Script de geração de ícones não encontrado, continuando sem gerar ícones...${NC}"
fi

echo ""

# Limpar build anterior
echo -e "${BLUE}🧹 Limpando build anterior...${NC}"
flutter clean
flutter pub get

echo ""

# Executar build iOS
echo -e "${BLUE}🚀 Executando build iOS...${NC}"
echo -e "${YELLOW}Comando: flutter build ios --$BUILD_TYPE --flavor $FLAVOR --dart-define=FLAVOR=$FLAVOR${NC}"

flutter build ios --$BUILD_TYPE --flavor "$FLAVOR" --dart-define=FLAVOR="$FLAVOR"

if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}✅ Build iOS concluído com sucesso!${NC}"
    echo -e "${BLUE}📱 Flavor: ${YELLOW}$FLAVOR${NC}"
    echo -e "${BLUE}🔧 Build Type: ${YELLOW}$BUILD_TYPE${NC}"
    echo ""
    echo -e "${BLUE}📁 Localização do build:${NC}"
    echo -e "${YELLOW}build/ios/iphoneos/Runner.app${NC}"
    echo ""
    echo -e "${BLUE}🚀 Para abrir no Xcode:${NC}"
    echo -e "${YELLOW}open ios/Runner.xcworkspace${NC}"
else
    echo ""
    echo -e "${RED}❌ Erro no build iOS${NC}"
    exit 1
fi
