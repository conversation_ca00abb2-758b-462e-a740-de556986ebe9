// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/components/buttom_ls/button_ls_custom.dart';
import 'package:octalog/src/components/flavor_image/flavor_image.dart';
import 'package:octalog/src/pages/coleta_home/coleta_home_state.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../components/loading_ls/loading_ls.dart';
import '../../components/scan_page/scan_page.dart';
//
import '../home/<USER>';
import 'coleta_home_store.dart';
import 'components/card_pedido_home.dart';

class ColetaHome extends StatefulWidget {
  final HomeController controller;
  const ColetaHome({super.key, required this.controller});

  @override
  State<ColetaHome> createState() => _ColetaHomeState();
}

class _ColetaHomeState extends State<ColetaHome> {
  final TextEditingController _controllerBarr = TextEditingController();
  final store = ColetaHomeStore();
  bool isVisibleCollectButton = false;

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: ValueListenableBuilder<ColetaHomeState>(
        valueListenable: store.state,
        builder: (_, state, __) {
          return KeyboardVisibilityBuilder(
            builder: (context, isKeyboardVisible) {
              return SafeArea(
                child: Scaffold(
                  backgroundColor: Colors.transparent,
                  body: Padding(
                    padding: const EdgeInsets.only(top: 20, left: 10, right: 10),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [Text('Coleta de Pedidos', style: GoogleFonts.roboto(fontSize: 18, fontWeight: FontWeight.bold))],
                        ),
                        const SizedBox(height: 20),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 10),
                          child: SizedBox(
                            height: 54,
                            child: Row(
                              children: [
                                Expanded(
                                  child: TextField(
                                    autofocus: true,
                                    enabled: !state.loadinTela,
                                    style: const TextStyle(fontSize: 20, height: 1),
                                    controller: _controllerBarr,
                                    onChanged: ((value) {
                                      setState(() => isVisibleCollectButton = value.isNotEmpty);
                                    }),
                                    textInputAction: TextInputAction.search,
                                    decoration: InputDecoration(
                                      hintText: 'Digite pedido, cliente ou endereço',
                                      hintStyle: const TextStyle(color: Colors.grey, fontSize: 15),
                                      prefixIcon: Icon(Icons.search, color: Colors.grey.shade600),
                                      suffixIcon: SizedBox(
                                        width: 80,
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.end,
                                          children: [
                                            IconButton(
                                              onPressed: () async {
                                                final response = await Navigator.of(context).push(
                                                  MaterialPageRoute(builder: (context) => const ScanPage(title: 'Aponte a câmera para o código de barras')),
                                                );
                                                if (response.isNotEmpty ?? false) {
                                                  store.coletar(os: response, context: context);
                                                }
                                              },
                                              icon: const Icon(Icons.qr_code_scanner),
                                            ),
                                            if (isKeyboardVisible) ...[
                                              Container(
                                                width: 27,
                                                height: 27,
                                                alignment: Alignment.bottomCenter,
                                                child: GestureDetector(
                                                  onTap: () {
                                                    FocusScope.of(context).requestFocus(FocusNode());
                                                  },
                                                  child: FlavorImage(assetName: 'keyboard_close.png', width: 25, color: Colors.black54),
                                                ),
                                              ),
                                              const SizedBox(width: 5),
                                            ],
                                          ],
                                        ),
                                      ),
                                      filled: true,
                                      fillColor: const Color.fromARGB(255, 207, 207, 207),
                                      border: const OutlineInputBorder(borderSide: BorderSide.none, borderRadius: BorderRadius.all(Radius.circular(8))),
                                    ),
                                  ),
                                ),
                                if (isVisibleCollectButton) ...[
                                  const SizedBox(width: 5),
                                  ButtonLsCustom(
                                    message: _controllerBarr.text.trim() == '' ? 'Digite o código do pedido' : null,
                                    text: ' Coletar ',
                                    onPressed:
                                        state.loadinTela
                                            ? null
                                            : () async {
                                              await store.coletar(os: _controllerBarr.text.toUpperCase(), context: context);

                                              setState(() {
                                                isKeyboardVisible = false;
                                                isVisibleCollectButton = false;

                                                _controllerBarr.clear();
                                              });
                                            },
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 5),
                        Expanded(
                          child: Builder(
                            builder: (_) {
                              if (state.loadinTela) {
                                return const Center(child: LoadingLs());
                              }
                              if (state.pedidos.isEmpty) {
                                return Column(
                                  children: [
                                    SizedBox(height: MediaQuery.of(context).size.height / 4),
                                    Center(
                                      child: Text(
                                        'Para coletar os pedidos da loja, digite ou \nleia o código de barra',
                                        style: GoogleFonts.roboto(fontSize: 16, fontWeight: FontWeight.bold),
                                        overflow: TextOverflow.clip,
                                        maxLines: 3,
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    Text(
                                      'Após a leitura os pedidos estarão disponível\r\npara entrega na lista de pedidos.',
                                      style: GoogleFonts.roboto(fontSize: 14, fontWeight: FontWeight.w400),
                                      overflow: TextOverflow.fade,
                                      maxLines: 3,
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                );
                              }
                              return Column(
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                                    children: [
                                       Expanded(child: Divider(color: ThemeColors.customOrange(context), thickness: 2, indent: 13, endIndent: 13)),
                                      Text(
                                        '${state.pedidos.length} ${state.pedidos.length == 1 ? 'Pedido' : 'Pedidos'} Coletados',
                                        style:  TextStyle(color: ThemeColors.customOrange(context), fontWeight: FontWeight.w500, fontSize: 17),
                                      ),
                                       Expanded(child: Divider(color: ThemeColors.customOrange(context), thickness: 2, indent: 13, endIndent: 13)),
                                    ],
                                  ),
                                  const SizedBox(width: 5),
                                  Expanded(
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 5),
                                      child: ListView.builder(
                                        itemCount: state.clientes.length,
                                        itemBuilder: (context, index) {
                                          final cliente = state.clientes[index];
                                          final pedidosCliente = state.pedidos.where((e) => e.cliente == cliente).toList();
                                          final endereco = pedidosCliente.first.enderecoCompleto;

                                          return CardPedidoHome(nomeCliente: cliente, endereco: endereco, pedidos: pedidosCliente, store: store);
                                        },
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
