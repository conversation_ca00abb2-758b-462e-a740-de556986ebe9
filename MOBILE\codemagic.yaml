workflows:
  ios-octalog-release:
    name: iOS Release (octalog)
    max_build_duration: 60
    environment:
      flutter: stable
      xcode: latest
    working_directory: mobile
    scripts:
      - flutter pub get
      - flutter build ios --flavor octalog --release --no-codesign
    artifacts:
      - build/ios/ipa/*.ipa
    publishing:
      app_store_connect:
        api_key: $APP_STORE_API_KEY
        key_id: $APP_STORE_KEY_ID
        issuer_id: $APP_STORE_ISSUER_ID

  ios-up360-release:
    name: iOS Release (up360)
    max_build_duration: 60
    environment:
      flutter: stable
      xcode: latest
    working_directory: mobile
    scripts:
      - flutter clean
      - flutter pub get
      - flutter build ios --release --flavor up360 --dart-define=FLAVOR=up360 --no-codesign
    artifacts:
      - build/ios/ipa/*.ipa
    publishing:
      app_store_connect:
        api_key: $APP_STORE_API_KEY
        key_id: $APP_STORE_KEY_ID
        issuer_id: $APP_STORE_ISSUER_ID

  ios-connect-release:
    name: iOS Release (connect)
    max_build_duration: 60
    environment:
      flutter: stable
      xcode: latest
    working_directory: mobile
    scripts:
      - flutter pub get
      - flutter build ios --flavor connect --release --no-codesign
    artifacts:
      - build/ios/ipa/*.ipa
    publishing:
      app_store_connect:
        api_key: $APP_STORE_API_KEY
        key_id: $APP_STORE_KEY_ID
        issuer_id: $APP_STORE_ISSUER_ID

  # ios-rondolog-release:
  #   name: iOS Release (rondolog)
  #   max_build_duration: 60
  #   environment:
  #     flutter: stable
  #     xcode: latest
  #   working_directory: mobile
  #   scripts:
  #     - flutter pub get
  #     - flutter build ios --flavor rondolog --release --no-codesign
  #   artifacts:
  #     - build/ios/ipa/*.ipa
  #   publishing:
  #     app_store_connect:
  #       api_key: $APP_STORE_API_KEY
  #       key_id: $APP_STORE_KEY_ID
  #       issuer_id: $APP_STORE_ISSUER_ID

  # ios-spotlog-release:
  #   name: iOS Release (spotlog)
  #   max_build_duration: 60
  #   environment:
  #     flutter: stable
  #     xcode: latest
  #   working_directory: mobile
  #   scripts:
  #     - flutter pub get
  #     - flutter build ios --flavor spotlog --release --no-codesign
  #   artifacts:
  #     - build/ios/ipa/*.ipa
  #   publishing:
  #     app_store_connect:
  #       api_key: $APP_STORE_API_KEY
  #       key_id: $APP_STORE_KEY_ID
  #       issuer_id: $APP_STORE_ISSUER_ID

  # ios-boyviny-release:
  #   name: iOS Release (boyviny)
  #   max_build_duration: 60
  #   environment:
  #     flutter: stable
  #     xcode: latest
  #   working_directory: mobile
  #   scripts:
  #     - flutter pub get
  #     - flutter build ios --flavor boyviny --release --no-codesign
  #   artifacts:
  #     - build/ios/ipa/*.ipa
  #   publishing:
  #     app_store_connect:
  #       api_key: $APP_STORE_API_KEY
  #       key_id: $APP_STORE_KEY_ID
  #       issuer_id: $APP_STORE_ISSUER_ID
