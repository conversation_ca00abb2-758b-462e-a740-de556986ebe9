import com.android.build.gradle.AppExtension

val android = project.extensions.getByType(AppExtension::class.java)

android.apply {
    flavorDimensions("flavor")

    productFlavors {
        create("octalog") {
            dimension = "flavor"
            applicationId = "com.octalog"
            resValue(type = "string", name = "app_name", value = "Octalog")
        }
        create("up360") {
            dimension = "flavor"
            applicationId = "com.octalog.up360"
            resValue(type = "string", name = "app_name", value = "UP360")
        }
        create("connect") {
            dimension = "flavor"
            applicationId = "com.octalog.connect"
            resValue(type = "string", name = "app_name", value = "Connect ExpressLog")
        }
        create("spotlog") {
            dimension = "flavor"
            applicationId = "com.octalog.spotlog"
            resValue(type = "string", name = "app_name", value = "SpotLog")
        }
        create("rondolog") {
            dimension = "flavor"
            applicationId = "com.octalog.rondolog"
            resValue(type = "string", name = "app_name", value = "RondoLog")
        }
    }
}