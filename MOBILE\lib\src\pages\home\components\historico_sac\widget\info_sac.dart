import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import 'package:octalog/src/components/custom_scaffold/custom_scaffold.dart';
import 'package:octalog/src/utils/extesion.dart';

import '../../../../../components/fcm_alert_dailog/components/fcm_appbar_custom.dart';
import '../../../../../components/image_perfil/image_perfil.dart';
import '../../../../../components/loading_ls/loading_ls.dart';
import '../../../../../models/historico_sac.dart';
import '../../../../../utils/theme_colors.dart';
import '../../widget_acareacao/acareacao_map.dart';

class InfoSac extends StatefulWidget {
  final HistoricoSacModel historico;
  const InfoSac({super.key, required this.historico});

  @override
  State<InfoSac> createState() => _InfoSacState();
}

class _InfoSacState extends State<InfoSac> {
  HistoricoSacModel get historico => widget.historico;
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = 0;
    _pageController = PageController(initialPage: _currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      child: Column(
        children: [
          const Padding(
            padding: EdgeInsets.only(
              left: 10,
              right: 10,
            ),
            child: FcmAppBarCustom(
              title: 'Dados do SAC',
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          Builder(
            builder: (context) {
              final List<Widget> listwidgets = [
                SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: MapWidgetAcareacao(
                    latLngEntrega: LatLng(
                      (historico.latitudeAgente),
                      (historico.longitudeAgente),
                    ),
                    latLngCliente: LatLng(
                      (historico.latitudeAgente),
                      (historico.longitudeAgente),
                    ),
                  ),
                ),
                ...widget.historico.fotos
                        ?.map(
                          (e) => CachedNetworkImage(
                            imageUrl: e,
                            placeholder: (context, url) => const LoadingLs(),
                            errorWidget: (context, url, error) =>
                                const Icon(Icons.error),
                          ),
                        )
                        .toList() ??
                    []
              ];
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 220.0,
                      child: PageView.builder(
                        controller: _pageController,
                        onPageChanged: (index) {
                          setState(() {
                            _currentIndex = index;
                          });
                        },
                        itemCount: listwidgets.length,
                        itemBuilder: (context, index) {
                          return listwidgets[index];
                        },
                      ),
                    ),
                    Stack(
                      children: [
                        if (listwidgets.length <= 1)
                          Padding(
                            padding: const EdgeInsets.only(
                              left: 10,
                            ),
                            child: Container(
                              margin: const EdgeInsets.only(
                                top: 10,
                              ),
                              width: 100,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(4),
                                color: ThemeColors.customRed(context),
                              ),
                              alignment: Alignment.center,
                              child: const Text(
                                'SEM FOTO',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 13,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        Center(
                          child: Padding(
                            padding: const EdgeInsets.all(7.0),
                            child: SizedBox(
                              height: 25,
                              width: MediaQuery.of(context).size.width * 0.5,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: listwidgets
                                    .asMap()
                                    .entries
                                    .map((indicator) {
                                  return GestureDetector(
                                    onTap: () {
                                      _pageController.animateToPage(
                                        indicator.key,
                                        duration:
                                            const Duration(milliseconds: 300),
                                        curve: Curves.easeInOut,
                                      );
                                    },
                                    child: Container(
                                      width: 13,
                                      height: 13,
                                      margin: const EdgeInsets.symmetric(
                                        vertical: 8.0,
                                      ),
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: _currentIndex == indicator.key
                                            ? ThemeColors.customOrange(context)
                                            : ThemeColors.customGrey(context),
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.only(
                  left: 20.0,
                  right: 8.0,
                  bottom: 8.0,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      historico.enderecoDoCliente,
                      style: TextStyle(
                        fontSize: 17,
                        fontWeight: FontWeight.w600,
                        color: ThemeColors.customBlackWhite(context),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 10,
                        bottom: 15,
                      ),
                      child: Row(
                        children: [
                          SizedBox(
                            width: 60,
                            height: 60,
                            child: ImagePerfil(
                              fontSize: 30,
                              url: historico.lojaLogo,
                              iniciais: historico.lojaNome.iniciais,
                            ),
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                historico.lojaNome,
                                style: TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w600,
                                  color: ThemeColors.customOrange(context),
                                ),
                              ),
                              Text(
                                historico.os,
                                style: TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w600,
                                  color: ThemeColors.customBlackWhite(context),
                                ),
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                    _buildRichText('Cliente:', historico.nomeCliente),
                    _buildRichText('Status:', historico.statusAtividade),
                    _buildRichText('Atendente:', historico.nomeAtendente),
                    _buildRichText('Inicio do atendimento:',
                        historico.dataContatoAgente.dataHoraPtBr),
                    _buildRichText(
                        'Mensagem Agente:', historico.mensagemAgente),
                    _buildRichText(
                        'Mensagem Atendente:', historico.mensagemAtendente),
                    _buildRichText('Finalização do atendimento:',
                        historico.dataAtendimentoFinalizado.dataHoraPtBr),
                    _buildRichText('Tempo Atendimento:',
                        '${historico.tempoAtendimentoMinutos} min'),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRichText(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            text: label,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeColors.customBlackWhite(context),
            ),
            children: [
              TextSpan(
                text: " $value",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.normal,
                  color: ThemeColors.customBlackWhite(context),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 6,
        ),
      ],
    );
  }
}
