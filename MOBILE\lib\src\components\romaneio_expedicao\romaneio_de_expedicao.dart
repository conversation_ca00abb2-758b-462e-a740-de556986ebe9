import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/errors.dart';
import 'package:octalog/src/components/buttom_custom/button_custom.dart';
import 'package:octalog/src/components/fcm_alert_dailog/components/fcm_appbar_custom.dart';
import 'package:octalog/src/components/fcm_alert_dailog/components/fcm_porcent.dart';
import 'package:octalog/src/helpers/web_connector.dart';
import 'package:octalog/src/pages/home/<USER>';
import 'package:octalog/src/utils/theme_colors.dart';
import 'package:octalog/src/utils/offline_helper.dart';
import 'package:map_fields/map_fields.dart';

class AvisoRomanioExpedicao {
  final bool liberado;
  final String mensagem;

  const AvisoRomanioExpedicao({required this.liberado, required this.mensagem});

  factory AvisoRomanioExpedicao.fromJson(Map<String, dynamic> json) {
    final m = MapFields.load(json);
    return AvisoRomanioExpedicao(liberado: m.getBool('liberado', false), mensagem: m.getString('mensagem', ''));
  }
}

class RomaneioExpedicao extends StatefulWidget {
  const RomaneioExpedicao({super.key});

  @override
  State<RomaneioExpedicao> createState() => _RomaneioExpedicaoState();
}

class _RomaneioExpedicaoState extends State<RomaneioExpedicao> {
  final TextEditingController _controller = TextEditingController();
  bool isLoading = false;
  bool isAviso = false;
  bool liberarloop = false;
  AvisoRomanioExpedicao? aviso;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _init();
    _startPolling();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startPolling() {
    _timer = Timer.periodic(const Duration(seconds: 5), (Timer t) async {
      // if (liberarloop) {
      //   await _init();
      // }
      await _init();
    });
  }

  Future<void> _init() async {
    try {
      final conn = WebConnector();
      var result = await conn.get('/atividades/verificar-romaneio-em-aberto');
      setState(() {
        aviso = AvisoRomanioExpedicao.fromJson(result.data);
        isAviso = aviso!.liberado;
        liberarloop = isAviso;
      });
      log(aviso!.mensagem);
    } catch (_) {}
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: SafeArea(
          child: SizedBox(
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
            child: Padding(
              padding: const EdgeInsets.only(left: 10, right: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Padding(padding: EdgeInsets.only(top: 15), child: FcmAppBarCustom(title: "Expedição de Pedidos")),
                  const Center(child: Padding(padding: EdgeInsets.only(top: 18), child: Image(image: AssetImage('assets/images/expedicao.jpg')))),
                  const Padding(padding: EdgeInsets.only(top: 10, bottom: 10), child: FcmPorcent()),
                  const _ConferenciaFisicaWidget(),
                  SizedBox(height: MediaQuery.of(context).size.height * 0.1),
                  Visibility(
                    visible: !isAviso,
                    replacement: _AvisoWidget(aviso: aviso),
                    child: _QuantidadeVolumesWidget(controller: _controller, isLoading: isLoading, onConfirm: _confirmVolumes),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _confirmVolumes() async {
    if (_controller.text.isEmpty) {
      return showDialogCustom(title: 'Quantidade de volumes', mensagem: 'Informe a quantidade de volumes');
    }

    setState(() {
      isLoading = true;
    });

    final conn = WebConnector();
    try {
      await conn.get('/atividades/liberar-romaneio', queryParameters: {'qtdpedidos': _controller.text});
      await HomeController.instance.fetchAtividades();
      Navigator.of(context).pop();
    } on ConnectionError catch (e) {
      _init();
      return showDialogCustom(title: 'Quantidade de volumes', mensagem: e.response);
    } finally {
      offlineStore.setOffline(false);
      setState(() {
        isLoading = false;
      });
    }
  }

  showDialogCustom({required String title, required String mensagem}) {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(mensagem),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('OK', style: TextStyle(color: ThemeColors.customOrange(context))),
            ),
          ],
        );
      },
    );
  }
}

class _ConferenciaFisicaWidget extends StatelessWidget {
  const _ConferenciaFisicaWidget();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 10),
      child: MediaQuery(
        data: const MediaQueryData(textScaler: TextScaler.linear(1.0)),
        child: Text(
          "Conferência física de pedidos",
          style: GoogleFonts.roboto(fontSize: 18, color: ThemeColors.customOrange(context), fontWeight: FontWeight.bold),
        ),
      ),
    );
  }
}

class _AvisoWidget extends StatelessWidget {
  final AvisoRomanioExpedicao? aviso;

  const _AvisoWidget({required this.aviso});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: MediaQuery(
          data: MediaQuery.of(context).copyWith(textScaler: const TextScaler.linear(1.0)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(aviso?.mensagem ?? '', style: TextStyle(fontSize: 16, color: ThemeColors.customBlack(context), fontWeight: FontWeight.w700)),
              const SizedBox(height: 20),
              Text(
                "Procure a expedição para verificar o seu romaneio.",
                style: TextStyle(fontSize: 16, color: ThemeColors.customBlack(context), fontWeight: FontWeight.w700),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _QuantidadeVolumesWidget extends StatelessWidget {
  final TextEditingController controller;
  final bool isLoading;
  final VoidCallback onConfirm;

  const _QuantidadeVolumesWidget({required this.controller, required this.isLoading, required this.onConfirm});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 10),
          child: MediaQuery(
            data: MediaQuery.of(context).copyWith(textScaler: const TextScaler.linear(1.0)),
            child: Text(
              "Para garantir que os pedidos fiquem disponíveis em seu aplicativo, informe a quantidade de volumes recebidos.",
              style: GoogleFonts.roboto(fontSize: 15, color: ThemeColors.customBlack(context), fontWeight: FontWeight.w700),
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 10, top: 15, bottom: 20),
          child: MediaQuery(
            data: MediaQuery.of(context).copyWith(textScaler: const TextScaler.linear(1.0)),
            child: Text(
              "Você tem 2 tentativas. Em caso de divergência, será necessário entrar em contato com o setor de expedição.",
              style: GoogleFonts.roboto(fontSize: 15, color: ThemeColors.customBlack(context), fontWeight: FontWeight.w700),
            ),
          ),
        ),
        SizedBox(
          height: 54,
          width: double.infinity,
          child: TextField(
            keyboardType: TextInputType.number,
            controller: controller,
            style: const TextStyle(fontSize: 20, height: 1),
            textInputAction: TextInputAction.done,
            decoration: const InputDecoration(
              hintText: 'Quantidade de volumes',
              hintStyle: TextStyle(color: Colors.grey, fontSize: 18),
              filled: true,
              fillColor: Color.fromARGB(255, 238, 238, 238),
              border: OutlineInputBorder(borderSide: BorderSide.none, borderRadius: BorderRadius.all(Radius.circular(8))),
            ),
          ),
        ),
        Padding(padding: const EdgeInsets.only(top: 10, bottom: 20), child: ButtonLsCustom(text: 'CONFIRMAR', isLoading: isLoading, onPressed: onConfirm)),
      ],
    );
  }
}
