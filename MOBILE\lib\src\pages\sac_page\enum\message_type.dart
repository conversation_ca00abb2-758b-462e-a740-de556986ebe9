enum MessagemTipo {
  agente,
  sac,
  sistema,
}

extension MessagemTipoExtension on MessagemTipo {
  String get value {
    switch (this) {
      case MessagemTipo.sac:
        return 'Sac';
      case MessagemTipo.agente:
        return 'Agente';
      default:
        return 'Sistema';
    }
  }

  static fromString(String string) {
    switch (string) {
      case 'Sac':
        return MessagemTipo.sac;
      case 'Agente':
        return MessagemTipo.agente;
      default:
        return MessagemTipo.sistema;
    }
  }
}
