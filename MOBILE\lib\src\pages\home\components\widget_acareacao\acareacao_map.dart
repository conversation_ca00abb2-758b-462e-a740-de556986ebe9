import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../../../utils/colors-dart';

class MapWidgetAcareacao extends StatelessWidget {
  final LatLng latLngEntrega;
  final LatLng latLngCliente;
  const MapWidgetAcareacao(
      {super.key, required this.latLngEntrega, required this.latLngCliente});

  @override
  Widget build(BuildContext context) {
    final loc = latLngEntrega;
    final loc2 = latLngCliente;
    return FlutterMap(
      mapController: MapController(),
      options: MapOptions(
        enableMultiFingerGestureRace: true,
        enableScrollWheel: true,
        rotation: 0,
        center: loc,
        zoom: 16.2,
      ),
      nonRotatedChildren: const [
        Positioned(
          bottom: 5,
          right: 5,
          child: Padding(
            padding: EdgeInsets.all(8.0),
            child: Text(
              "Octalog",
              style: TextStyle(color: Colors.black, fontSize: 16),
            ),
          ),
        ),
      ],
      children: [
        TileLayer(
          urlTemplate: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
          subdomains: const ['a', 'b', 'c'],
        ),
        MarkerLayer(
          markers: [
            Marker(
              width: 80.0,
              height: 80.0,
              point: loc,
              builder: (ctx) => const Icon(
                Icons.location_on,
                color: ColorsCustom.customRed,
              ),
            ),
            Marker(
              width: 80.0,
              height: 80.0,
              point: loc2,
              builder: (ctx) =>  Icon(
                Icons.location_on,
                color: ThemeColors.customOrange(context),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
