import 'package:octalog/src/components/fcm_alert_dailog/models/fcm_alert_dados.dart';
import 'package:map_fields/map_fields.dart';

class FcmColetaFinalizacaoBody {
  final String nomeLiberacao;
  final int idLocal;
  final int idDeslocamento;
  final List<FcmPedido> coletados;
  final List<FcmPedido> naoColetados;
  final List<FcmPedido> devolucao;
  FcmColetaFinalizacaoBody({
    required this.nomeLiberacao,
    required this.idLocal,
    required this.idDeslocamento,
    required this.coletados,
    required this.naoColetados,
    required this.devolucao,
  });

  bool get isNotValid => nomeLiberacao.isEmpty;

  factory FcmColetaFinalizacaoBody.fromMap(Map<String, dynamic> map) {
    final f = MapFields.load(map);
    return FcmColetaFinalizacaoBody(
      nomeLiberacao: f.getString('nomeLiberacao', ''),
      idLocal: f.getInt('idLocal', 0),
      idDeslocamento: f.getInt('idDeslocamento', 0),
      coletados: f.getList('coletados', []),
      naoColetados: f.getList('naoColetados', []),
      devolucao: f.getList('devolucao', []),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'NomeLiberacao': nomeLiberacao,
      'IDLocal': idLocal,
      'IDDeslocamento': idDeslocamento,
      'Coletados': coletados.map((e) => e.toMap()).toList(),
      'NaoColetados': naoColetados.map((e) => e.toMap()).toList(),
      'Devolucao': devolucao.map((e) => e.toMap()).toList(),
    };
  }

  Map<String, dynamic> toMapKlevSend() {
    return {
      'NomeLiberacao': nomeLiberacao,
      'IDLocal': idLocal,
      'IDDeslocamento': idDeslocamento,
      'Coletados': coletados.map((e) => e.toMapKlevSend()).toList(),
      'NaoColetados': naoColetados.map((e) => e.toMapKlevSend()).toList(),
      'Devolucao': devolucao.map((e) => e.toMapKlevSend()).toList(),
    };
  }
}
