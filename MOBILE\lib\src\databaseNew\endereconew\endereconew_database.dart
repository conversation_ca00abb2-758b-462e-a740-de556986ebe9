import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:octalog/src/models_new/endereco_new.dart';

import '../../database/offline_request/offline_request_database.dart';

class EnderecoNewDatabase {
  static final EnderecoNewDatabase instance = EnderecoNewDatabase();
  late Box<String> _base;
  bool _initialized = false;

  EnderecoNewDatabase() {
    _init();
  }

  Future<void> _init() async {
    if (!_initialized) {
      _base = await Hive.openBox('endereco_new_hive_map');
      _initialized = true;
    }
  }

  Future<List<EnderecoNew>> getEnderecoNew() async {
    await _init();
    final vs = await compute(
      OfflineRequestDatabase.convertStringToMap,
      _base.values.toList(),
    );
    final data = vs.map((e) => EnderecoNew.fromHiveMap(e)).toList();
    if (data.isEmpty) {
      return [];
    }
    return data;
  }

  Future<void> setAtividades(List<EnderecoNew> enderecoNew) async {
    await _init();
    await _base.clear();
    final values = await compute(
      OfflineRequestDatabase.convertMapToString,
      enderecoNew.map((e) => e.toHiveMap()).toList(),
    );
    await _base.addAll(values);
  }
}
