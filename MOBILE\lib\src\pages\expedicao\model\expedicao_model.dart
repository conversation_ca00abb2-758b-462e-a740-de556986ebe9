import 'package:map_fields/map_fields.dart';

class ExpedicaoPageModel {
  final int entregas;
  final int negativas;
  final int diasTrabalhados;
  final List<EntregasListaModel> entregasLista;

  ExpedicaoPageModel({
    required this.entregas,
    required this.negativas,
    required this.diasTrabalhados,
    required this.entregasLista,
  });

  factory ExpedicaoPageModel.fromJson(Map<String, dynamic> json) {
    final e = MapFields.load(json);
    final entregasLista = e
        .getList('EntregasLista', [])
        .map((e) => EntregasListaModel.fromJson(e))
        .toList();

    entregasLista.sort((a, b) => b.data.compareTo(a.data));

    return ExpedicaoPageModel(
      entregas: e.getInt('Entregas', 0),
      negativas: e.getInt('Negativas', 0),
      diasTrabalhados: e.getInt('DiasTrabalhados', 0),
      entregasLista: entregasLista,
    );
  }
}

class EntregasListaModel {
  final DateTime data;
  final int entregaConcluidas;
  final int negativas;
  final List<EnderecosModel> enderecos;

  EntregasListaModel({
    required this.data,
    required this.entregaConcluidas,
    required this.negativas,
    required this.enderecos,
  });

  factory EntregasListaModel.fromJson(Map<String, dynamic> json) {
    final e = MapFields.load(json);
    return EntregasListaModel(
      data: e.getDateTime('Data', DateTime.now()),
      entregaConcluidas: e.getInt('EntregasConcluidas', 0),
      negativas: e.getInt('Negativas', 0),
      enderecos: e
          .getList('Enderecos', [])
          .map((e) => EnderecosModel.fromJson(e))
          .toList(),
    );
  }
}

class EnderecosModel {
  final String endereco;
  final DateTime dataTermino;
  final List<PedidosModel> pedidos;

  EnderecosModel({
    required this.endereco,
    required this.dataTermino,
    required this.pedidos,
  });

  factory EnderecosModel.fromJson(Map<String, dynamic> json) {
    final e = MapFields.load(json);
    final pedidos =
        e.getList('Pedidos', []).map((e) => PedidosModel.fromJson(e)).toList();
    return EnderecosModel(
      endereco: e.getString('Endereco', ''),
      dataTermino: e.getDateTime('DataTermino', DateTime.now()),
      pedidos: pedidos,
    );
  }
}

class PedidosModel {
  final String os;
  final String nomeCliente;
  final String local;
  final int volumes;

  PedidosModel({
    required this.os,
    required this.nomeCliente,
    required this.local,
    required this.volumes,
  });

  factory PedidosModel.fromJson(Map<String, dynamic> json) {
    final e = MapFields.load(json);
    return PedidosModel(
      os: e.getString('OS', ""),
      nomeCliente: e.getString('NomeCliente', ''),
      local: e.getString('Local', ''),
      volumes: e.getInt('Volumes', 0),
    );
  }
}
