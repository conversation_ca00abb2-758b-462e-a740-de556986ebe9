Icom.pravera.flutter_activity_recognition.FlutterActivityRecognitionPlugin>com.pravera.flutter_activity_recognition.MethodCallHandlerImpl7com.pravera.flutter_activity_recognition.PreferencesKey4com.pravera.flutter_activity_recognition.RequestCode:com.pravera.flutter_activity_recognition.StreamHandlerImpl:com.pravera.flutter_activity_recognition.errors.ErrorCodes<com.pravera.flutter_activity_recognition.models.ActivityDataBcom.pravera.flutter_activity_recognition.models.ActivityPermissionEcom.pravera.flutter_activity_recognition.service.ActivityDataCallbackKcom.pravera.flutter_activity_recognition.service.ActivityPermissionCallbackJcom.pravera.flutter_activity_recognition.service.ActivityPermissionManagerTcom.pravera.flutter_activity_recognition.service.ActivityPermissionManager.CompanionRcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentReceiverQcom.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService[com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService.CompanionKcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManagerUcom.pravera.flutter_activity_recognition.service.ActivityRecognitionManager.CompanionGcom.pravera.flutter_activity_recognition.utils.ActivityRecognitionUtilsQcom.pravera.flutter_activity_recognition.utils.ActivityRecognitionUtils.Companion?com.pravera.flutter_activity_recognition.utils.ErrorHandleUtilsIcom.pravera.flutter_activity_recognition.utils.ErrorHandleUtils.Companion                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           