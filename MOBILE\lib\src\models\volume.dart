// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:map_fields/map_fields.dart';

import 'id_status_atividade_enum.dart';
import 'tag.dart';

class Volume {
  final int idos;
  final String os;
  final String tipo;
  final int idStatusAtividade;
  final String statusAtividade;
  final bool receita;
  final bool romaneioCanhoto;
  final bool termoLabio;
  final bool receberValor;
  final bool reentrega;
  final List<Tag> tags;
  final double? latitude;
  final double? longitude;

  Volume({
    required this.idos,
    required this.os,
    required this.tipo,
    required this.idStatusAtividade,
    required this.statusAtividade,
    required this.receita,
    required this.romaneioCanhoto,
    required this.termoLabio,
    required this.receberValor,
    required this.reentrega,
    required this.tags,
    required this.latitude,
    required this.longitude,
  });

  Map<String, dynamic> toHiveMap() {
    return {
      'idos': idos,
      'os': os,
      'tipo': tipo,
      'idStatusAtividade': idStatusAtividade,
      'statusAtividade': statusAtividade,
      'receita': receita,
      'romaneioCanhoto': romaneioCanhoto,
      'termoLabio': termoLabio,
      'receberValor': receberValor,
      'reentrega': reentrega,
      'tags': tags.map((e) => e.toHiveMap()).toList(),
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  factory Volume.fromHiveMap(Map<String, dynamic> map) {
    final MapFields v = MapFields.load(map);
    return Volume(
      // idos: map['idos'] as int,
      // os: map['os'] as String,
      // tipo: map['tipo'] as String,
      // idStatusAtividade: map['idStatusAtividade'] as int,
      // statusAtividade: map['statusAtividade'] as String,
      // receita: map['receita'] as bool,
      // romaneioCanhoto: map['romaneioCanhoto'] as bool,
      // termoLabio: map['termoLabio'] as bool,
      // receberValor: map['receberValor'] as bool,
      // reentrega: map['reentrega'] as bool,
      // tags: (map['tags'] as List<dynamic>)
      // .map((e) => Tag.fromHiveMap(e))
      // .toList(),
      // latitude: map['latitude'] as double?,
      // longitude: map['longitude'] as double?,
      idos: v.getInt('idos', 0),
      os: v.getString('os', ''),
      tipo: v.getString('tipo', ''),
      idStatusAtividade: v.getInt('idStatusAtividade', 0),

      statusAtividade: v.getString('statusAtividade', ''),
      receita: v.getBool('receita', false),
      romaneioCanhoto: v.getBool('romaneioCanhoto', false),
      termoLabio: v.getBool('termoLabio', false),
      receberValor: v.getBool('receberValor', false),
      reentrega: v.getBool('reentrega', false),
      tags: (v.getList('tags', [])).map((e) => Tag.fromHiveMap(e)).toList(),
      latitude: v.getDoubleNullable('latitude'),
      longitude: v.getDoubleNullable('longitude'),
    );
  }

  factory Volume.fromJson(Map<String, dynamic> json) {
    final MapFields v = MapFields.load(json);
    return Volume(
      idos: v.getInt('IDOS', 0),
      os: v.getString('OS', ''),
      tipo: v.getString('Tipo', ''),
      idStatusAtividade: v.getInt('IDStatusAtividade', 0),
      statusAtividade: v.getString('StatusAtividade', ''),
      receita: v.getBool('Receita', false),
      romaneioCanhoto: v.getBool('RomaneioCanhoto', false),
      termoLabio: v.getBool('Termolabio', false),
      receberValor: v.getBool('ReceberValor', false),
      reentrega: v.getBool('Reentrega', false),
      tags: (v.getList('Tags', [])).map((e) => Tag.fromJson(e)).toList(),
      latitude: v.getDoubleNullable('Latitude'),
      longitude: v.getDoubleNullable('Longitude'),
    );
  }

  String get statustitle => idStatusAtividade.statusTitle;

  IdStatusAtividadeEnum? get idStatusAtividadeEnum {
    if ([4, 21, 28, 29].contains(idStatusAtividade)) {
      return IdStatusAtividadeEnum.entrega;
    } else if ([5, 6, 13, 14, 16, 20].contains(idStatusAtividade)) {
      return IdStatusAtividadeEnum.negativa;
    } else if ([10, 11, 22, 24, 35].contains(idStatusAtividade)) {
      return IdStatusAtividadeEnum.entregue;
    } else if ([7, 8, 9, 12, 15, 19, 32, 33, 34].contains(idStatusAtividade)) {
      return IdStatusAtividadeEnum.cancelada;
    } else {
      return null;
    }
  }

  bool find(String search) {
    return idos.toString().toLowerCase().contains(search.toLowerCase()) ||
        os.toLowerCase().contains(search.toLowerCase());
  }

  Volume copyWith({
    int? idos,
    String? os,
    String? tipo,
    int? idStatusAtividade,
    String? statusAtividade,
    bool? receita,
    bool? romaneioCanhoto,
    bool? termoLabio,
    bool? receberValor,
    bool? reentrega,
    List<Tag>? tags,
    double? latitude,
    double? longitude,
  }) {
    return Volume(
      idos: idos ?? this.idos,
      os: os ?? this.os,
      tipo: tipo ?? this.tipo,
      idStatusAtividade: idStatusAtividade ?? this.idStatusAtividade,
      statusAtividade: statusAtividade ?? this.statusAtividade,
      receita: receita ?? this.receita,
      romaneioCanhoto: romaneioCanhoto ?? this.romaneioCanhoto,
      termoLabio: termoLabio ?? this.termoLabio,
      receberValor: receberValor ?? this.receberValor,
      reentrega: reentrega ?? this.reentrega,
      tags: tags ?? this.tags,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
    );
  }
}

extension on int {
  static final Map<int, String> _idsStatusTitle = {
    1: 'Integrado',
    2: 'Conferência Física',
    3: 'Armazenado',
    4: 'Despachado',
    5: 'Localidade Não Atendida',
    6: 'Cliente Ausente',
    7: 'Pedido Danificado',
    8: 'Entrega Cancelada',
    9: 'Pedido Extraviado',
    10: 'Entrega realizada com sucesso',
    11: 'Coleta Realizada',
    12: 'Pedido Recusado',
    13: 'Estabelecimento Fechado',
    14: 'Endereço Não Localizado',
    15: 'Cancelado pelo cliente',
    16: 'Cliente Inexistente',
    17: 'Outros',
    19: 'Devolvido',
    20: 'Número não localizado',
    21: 'Inicio de deslocamento ao cliente',
    22: 'Coleta de receita realizada',
    23: 'Inicio de descolamento à farmácia',
    24: 'Entrega de receita na farmácia',
    25: 'Coleta Pedidos',
    26: 'Não Coletado',
    27: 'Transbordo',
    28: 'Chegada na Farmácia',
    29: 'Chegada no Cliente',
    30: 'Postado nos correios',
  };

  String get statusTitle => _idsStatusTitle[this] ?? 'Status não localizado';
}
