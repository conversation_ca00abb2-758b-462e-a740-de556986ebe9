import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/pages/sac_page/sac_page_store.dart';

import '../../../components/buttom_ls/button_ls_custom.dart';
import '../sac_page.state.dart';

class AvisoSacPage extends StatefulWidget {
  final SacPageStore store;
  const AvisoSacPage({
    super.key,
    required this.store,
  });

  @override
  State<AvisoSacPage> createState() => _AvisoSacPageState();
}

class _AvisoSacPageState extends State<AvisoSacPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<SacPageState>(
      valueListenable: widget.store.state,
      builder: (_, SacPageState value, __) {
        final sacModel = value.sacModelFila;
        return Scaffold(
          body: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                height: 50,
                width: MediaQuery.of(context).size.width,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(width: 50),
                    Expanded(
                      child: Text(
                        sacModel?.tituloMensagemSac ??
                            sacModel?.chamado.tituloMensagemSac ??
                            '',
                        textAlign: TextAlign.center,
                        softWrap: true,
                        style: GoogleFonts.roboto(
                          fontSize: 20,
                          fontWeight: FontWeight.w500,
                          color: const Color.fromARGB(255, 0, 0, 0),
                        ),
                      ),
                    ),
                    const SizedBox(width: 60),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 35,
                      ),
                      child: Image.asset(
                        sacModel!.icone.isNotEmpty
                            ? "assets/sac/${sacModel.icone}.png"
                            : 'assets/sac/alert.png',
                        height: MediaQuery.of(context).size.height * 0.3,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 20,
                      ),
                      child: Text(
                        sacModel.mensagem ??
                            sacModel.chamado.mensagemSac ??
                            sacModel.mensagem ??
                            '',
                        textAlign: TextAlign.center,
                        style: GoogleFonts.roboto(
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                          color: const Color.fromARGB(255, 0, 0, 0),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          floatingActionButton: Padding(
            padding: EdgeInsets.only(
              left: MediaQuery.of(context).size.width * 0.08,
            ),
            child: SizedBox(
              width: MediaQuery.of(context).size.width,
              height: 50,
              child: ButtonLsCustom(
                text: 'CONTINUAR',
                onPressed: () async {
                  if (value.sacCancelado) {
                    await widget.store.deletarLocalSacEmAberto();
                  }
                  widget.store.disposeChat();
                  Navigator.pop(context);
                },
              ),
            ),
          ),
        );
      },
    );
  }
}
