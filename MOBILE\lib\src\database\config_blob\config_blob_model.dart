import 'package:map_fields/map_fields.dart';

// var bloqueiosCDsLatLong = {
//   {-23.544152, -46.620273},
//   {-23.544152, -46.620273}
// };
class ConfigBlobModelHive {
  final String url;
  final String fotoAgente;
  final String fotoFachada;
  final String foneSac;
  final int? timerBuscarAutomatico;
  final String? mapbox;
  final int? distanciaMetrosFotoFaixada;
  final int qtdeForceSincronismoItens;
  final int distanciaMetrosChegadaDeslocamento;
  final bool bloqueioDeslocamentoForaDoLocal;
  final bool buscarHome;
  final bool agenteTesteSac;
  final List<BloqueiosCDsLatLong> bloqueiosCDsLatLong;
  final bool bloqueioProximoCds;
  final bool bloqueioAcareacaoForaDoLocal;
  final bool ativarBotaoSacObrigatorio;
  final bool usarGZipBuscar;
  final bool usarGZipSincronismo;
  final double maximoValorReceber;
  final String linkTutorial;
  final List<AppsFakeGps> appsFakeGps;
  final String linkHub;
  final bool ativarLigacao;
  final int timerDataFisicoAteChegadaLocal;
  final int timerSegundaBaixaForaDoLocal;
  final List<int> timersProximasBaixasForaDoLocal;
  final bool ativarSacAutomaticoNoLocal;
  final List<int> sacsAutomaticoNoLocalPermitidos;
  final int sacDistanciaMinimaParaSerAtendimento;
  ConfigBlobModelHive({
    required this.url,
    required this.fotoAgente,
    required this.fotoFachada,
    required this.foneSac,
    this.timerBuscarAutomatico,
    this.mapbox,
    this.distanciaMetrosFotoFaixada,
    required this.qtdeForceSincronismoItens,
    required this.distanciaMetrosChegadaDeslocamento,
    required this.bloqueioDeslocamentoForaDoLocal,
    required this.buscarHome,
    required this.agenteTesteSac,
    required this.bloqueiosCDsLatLong,
    required this.bloqueioProximoCds,
    required this.bloqueioAcareacaoForaDoLocal,
    required this.ativarBotaoSacObrigatorio,
    required this.usarGZipBuscar,
    required this.usarGZipSincronismo,
    required this.maximoValorReceber,
    required this.linkTutorial,
    required this.appsFakeGps,
    required this.linkHub,
    required this.ativarLigacao,
    required this.timerDataFisicoAteChegadaLocal,
    required this.timerSegundaBaixaForaDoLocal,
    required this.timersProximasBaixasForaDoLocal,
    required this.ativarSacAutomaticoNoLocal,
    required this.sacsAutomaticoNoLocalPermitidos,
    this.sacDistanciaMinimaParaSerAtendimento = 0,
  });

  factory ConfigBlobModelHive.fromJson(Map<String, dynamic> map) {
    final c = MapFields.load(map);

    return ConfigBlobModelHive(
      url: c.getString('url', ''),
      fotoAgente: c.getString('fotoAgente', ''),
      fotoFachada: c.getString('fotoFachada', ''),
      foneSac: c.getString('foneSac', ''),
      timerBuscarAutomatico: c.getInt('timerBuscarAutomatico', 240),
      mapbox: c.getString('mapbox', ''),
      distanciaMetrosFotoFaixada: c.getInt('distanciaMetrosFotoFaixada', 400),
      qtdeForceSincronismoItens: c.getInt('qtdeForceSincronismoItens', 60),
      distanciaMetrosChegadaDeslocamento: c.getInt('distanciaMetrosChegadaDeslocamento', 400),
      bloqueioDeslocamentoForaDoLocal: c.getBool('bloqueioDeslocamentoForaDoLocal', true),
      buscarHome: c.getBool('buscarHome', false),
      agenteTesteSac: c.getBool('agenteTesteSac', true),
      bloqueiosCDsLatLong: c.getList<Map<String, dynamic>>("bloqueiosCDsLatLong", []).map((e) => BloqueiosCDsLatLong.fromJson(e)).toList(),
      bloqueioProximoCds: c.getBool('bloqueioProximoCds', true),
      bloqueioAcareacaoForaDoLocal: c.getBool('bloqueioAcareacaoForaDoLocal', true),
      ativarBotaoSacObrigatorio: c.getBool('ativarBotaoSacObrigatorio', true),
      usarGZipBuscar: c.getBool('usarGZipBuscar', true),
      usarGZipSincronismo: c.getBool('usarGZipSincronismo', false),
      maximoValorReceber: c.getDouble('maximoValorReceber', 0),
      linkTutorial: c.getString('linkTutorialLs', ''),
      appsFakeGps: c.getList<Map<String, dynamic>>("appsFakeGps", []).map((e) => AppsFakeGps.fromJson(e)).toList(),
      linkHub: c.getString('linkHub', ''),
      ativarLigacao: c.getBool('ativarLigacao', true),
      timerDataFisicoAteChegadaLocal: c.getInt('timerDataFisicoAteChegadaLocal', 0),
      timerSegundaBaixaForaDoLocal: c.getInt('timerSegundaBaixaForaDoLocal', 0),
      timersProximasBaixasForaDoLocal: c.getList<int>('timersProximasBaixasForaDoLocal', []),
      ativarSacAutomaticoNoLocal: c.getBool('ativarSacAutomaticoNoLocal', false),
      sacsAutomaticoNoLocalPermitidos: c.getList<int>('sacsAutomaticoNoLocalPermitidos', []),
      sacDistanciaMinimaParaSerAtendimento: c.getInt('sacDistanciaMinimaParaSerAtendimento', 0),
    );
  }
  /*
  factory ConfigBlobModelHive.def() {
    final map = <String, dynamic>{
      "blobStorage": {
        "url":
            "DefaultEndpointsProtocol=https;AccountName=lsarquivos;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
        "fotoAgente": "mobile-foto-agente",
        "fotoFachada": "mobile-foto-fachada",
        "foneSac": "***********",
        "timerSegundosAtualizaLista": 240,
        "timerSegundosVerificaRoteirizacao": 10,
        "timerSegundosEnvioGPS": 60,
        "distanciaMetrosFotoFaixada": 400,
        "distanciaMetrosChegadaDeslocamento": 400,
        "forceSincronismoItens": 60,
        "mapbox": "pk.eyJ1IjoidG9uaW5ob2NlcmV6bzcwbWIiLCJhIjoiY2x1Ymk3cXhlMHU3czJscWVjb3JzNWZjcCJ9.iYUWPkufhfUEiqVGjfjebw",
        "deslocamentoForaDoLocal": true,
        "desabilitarBuscarHome": true,
        "agenteTesteSac": true,
        "bloqueiosCDsLatLong": [
          {"Nome": "CD SÃO PAULO", "Latitude": -23.5241871, "Longitude": -46.7462068},
        ],
        "bloqueioProximoCds": false,
        "bloqueioAcareacaoForaDoLocal": true,
        "ativarBotaoSacObrigatorio": false,
        "usarGZipBuscar": true,
        "usarGZipSincronismo": true,
        "buscarHome": false,
        "timerBuscarAutomatico": 240,
        "qtdeForceSincronismoItens": 60,
        "bloqueioDeslocamentoForaDoLocal": true,
        "bloquearFakeGPS": true,
        "maximoValorReceber": 0,
        "linkTutorialLs": null,
        "appsFakeGps": [],
        "linkChat": null,
        "timerDataFisicoAteChegadaLocal": 5,
        "timerSegundaBaixaForaDoLocal": 0,
        "timersProximasBaixasForaDoLocal": [],
        "linkHub": "https://api-hub-ls.azurewebsites.net",
        "ativarLigacao": true,
        "ativarSacAutomaticoNoLocal": false,
        "sacsAutomaticoNoLocalPermitidos": [],
        "sacDistanciaMinimaParaSerAtendimento": 0,
      },
    };
    return ConfigBlobModelHive.fromJson(map);
  }
*/
  Map<String, dynamic> toHiveMap() {
    return {
      'url': url,
      'fotoAgente': fotoAgente,
      'fotoFachada': fotoFachada,
      'foneSac': foneSac,
      'timerBuscarAutomatico': timerBuscarAutomatico,
      'mapbox': mapbox,
      'distanciaMetrosFotoFaixada': distanciaMetrosFotoFaixada,
      'qtdeForceSincronismoItens': qtdeForceSincronismoItens,
      'distanciaMetrosChegadaDeslocamento': distanciaMetrosChegadaDeslocamento,
      'bloqueioDeslocamentoForaDoLocal': bloqueioDeslocamentoForaDoLocal,
      'buscarHome': buscarHome,
      'agenteTesteSac': agenteTesteSac,
      'bloqueioProximoCds': bloqueioProximoCds,
      'bloqueioAcareacaoForaDoLocal': bloqueioAcareacaoForaDoLocal,
      'bloqueiosCDsLatLong': bloqueiosCDsLatLong.map((e) => e.toJson()).toList(),
      'ativarBotaoSacObrigatorio': ativarBotaoSacObrigatorio,
      'usarGZipBuscar': usarGZipBuscar,
      'usarGZipSincronismo': usarGZipSincronismo,
      'maximoValorReceber': maximoValorReceber,
      'linkTutorialLs': linkTutorial,
      'appsFakeGps': appsFakeGps.map((e) => e.toJson()).toList(),
      'linkHub': linkHub,
      'ativarLigacao': ativarLigacao,
      'timerDataFisicoAteChegadaLocal': timerDataFisicoAteChegadaLocal,
      'timerSegundaBaixaForaDoLocal': timerSegundaBaixaForaDoLocal,
      'timersProximasBaixasForaDoLocal': timersProximasBaixasForaDoLocal,
      'ativarSacAutomaticoNoLocal': ativarSacAutomaticoNoLocal,
      'sacsAutomaticoNoLocalPermitidos': sacsAutomaticoNoLocalPermitidos,
      'sacDistanciaMinimaParaSerAtendimento': sacDistanciaMinimaParaSerAtendimento,
    };
  }
}

class BloqueiosCDsLatLong {
  final String nome;
  final double latitude;
  final double longitude;

  BloqueiosCDsLatLong({required this.nome, required this.latitude, required this.longitude});

  factory BloqueiosCDsLatLong.fromJson(Map<String, dynamic> map) {
    final c = MapFields.load(map);

    return BloqueiosCDsLatLong(nome: c.getString('Nome', ''), latitude: c.getDouble('Latitude', 0), longitude: c.getDouble('Longitude', 0));
  }

  Map<String, dynamic> toJson() {
    return {'Nome': nome, 'Latitude': latitude, 'Longitude': longitude};
  }
}

class AppsFakeGps {
  final String nome;
  final String package;
  final bool bloquear;
  AppsFakeGps({required this.nome, required this.package, required this.bloquear});

  factory AppsFakeGps.fromJson(Map<String, dynamic> map) {
    final c = MapFields.load(map);

    return AppsFakeGps(nome: c.getString('nome', ''), package: c.getString('package', ''), bloquear: c.getBool('bloquear', false));
  }

  Map<String, dynamic> toJson() {
    return {'nome': nome, 'package': package, 'bloquear': bloquear};
  }
}
