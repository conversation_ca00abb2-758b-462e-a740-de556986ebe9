import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:octalog/src/components/loading_ls/loading_ls.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../helpers/web_connector.dart';
import '../home/<USER>/notificacoes/notificacao.dart';
import '../home/<USER>/notificacoes/notificacoes_widget.dart';
import 'notificacoes_store.dart';

class NotificacoesPage extends StatefulWidget {
  const NotificacoesPage({super.key});

  @override
  State<NotificacoesPage> createState() => _NotificacoesPageState();
}

class _NotificacoesPageState extends State<NotificacoesPage> {
  final NotificacaoStore store = NotificacaoStore();

  @override
  void initState() {
    super.initState();
    store.loadNotificacoes();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: store.state,
      builder: (context, state, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Notificações'),
            backgroundColor: ThemeColors.customOrange(context),
            centerTitle: true,
            actions: [
              IconButton(icon: const Icon(Icons.refresh), onPressed: state.loading ? null : () => store.loadNotificacoes()),
              // if (kDebugMode)
              //   IconButton(
              //     icon: const Icon(Icons.info),
              //     onPressed: () async {
              //       const conteudo = "";
              //       final not = Notificacao(
              //         id: 0,
              //         titulo: '',
              //         dataVisualizacao: DateTime.now(),
              //         cadastroIncompleto: false,
              //         paginas: [PaginasNotificacao(ordem: 0, conteudo: conteudo)],
              //       );
              //       await Navigator.of(context).push(
              //         MaterialPageRoute(
              //           builder: (ctx) {
              //             return NotificacoesWidget(notificacao: not);
              //           },
              //         ),
              //       );
              //     },
              //   ),
            ],
          ),
          body: Builder(
            builder: (context) {
              if (state.error) {
                return const Center(child: Text('Falha ao carregar notificações.'));
              }
              if (state.loading) {
                return const Center(child: LoadingLs());
              }
              if (state.notificacoes.isEmpty) {
                return const Center(child: Text('Nenhuma notificação encontrada.'));
              }
              return ListView.builder(
                itemCount: state.notificacoes.length,
                itemBuilder: (_, index) {
                  final notificacao = state.notificacoes[index];
                  return ListTile(
                    onTap: () async {
                      store.loadNotificacoes();
                      final conn = WebConnector();
                      final resposta = await conn.get('/notificacoes/buscar', queryParameters: {'IDNotificacaoMobile': notificacao.id});
                      final not = Notificacao.fromMap(resposta.data);
                      await Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (ctx) {
                            return NotificacoesWidget(notificacao: not);
                          },
                        ),
                      );
                    },
                    title: Text(notificacao.titulo),
                    subtitle: Text(notificacao.dataFormatada),
                  );
                },
              );
            },
          ),
        );
      },
    );
  }
}
