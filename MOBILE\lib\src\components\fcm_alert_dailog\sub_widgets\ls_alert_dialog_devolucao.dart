import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:octalog/src/components/fcm_alert_dailog/fcm_alert_dialog_state.dart';
import 'package:octalog/src/components/fcm_alert_dailog/models/fcm_deslocamento_get.dart';
import 'package:octalog/src/pages/home/<USER>';
import 'package:octalog/src/pages/home/<USER>';
import 'package:octalog/src/utils/extesion.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../../database/log_database/log_database.dart';

import '../../buttom_ls/button_ls_custom.dart';
import '../../uberizado_single/uberizado_loja_card.dart';
import '../components/card_pedido_padrao.dart';
import '../components/fcm_appbar_custom.dart';
import '../fcm_alert_dialog_store.dart';

class FcmAlertDailogDevolucao extends StatefulWidget {
  final FcmAlertDialogStore store;
  const FcmAlertDailogDevolucao({
    super.key,
    required this.store,
  });

  @override
  State<FcmAlertDailogDevolucao> createState() =>
      _FcmAlertDailogDevolucaoState();
}

class _FcmAlertDailogDevolucaoState extends State<FcmAlertDailogDevolucao> {
  bool isLoading = false;
  FcmDeslocamentoGet get fcmAlertDadosPedidos =>
      widget.store.state.value.fcmAlertDados;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.store.getDadosPedidos(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (fcmAlertDadosPedidos.titulo.isNotEmpty) {
          showDialog(
              context: context,
              builder: (ctx) {
                return AlertDialog(
                  title: const Text('Deseja sair da coleta?'),
                  content: const Text(
                      'Se sair da coleta, todos os dados serão perdidos'),
                  actions: [
                    TextButton(
                      onPressed: () {
                        Navigator.of(ctx).pop();
                      },
                      child: const Text(
                        'CANCELAR',
                        style: TextStyle(color: Colors.orange),
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.of(ctx).pop();
                        Navigator.of(context).pop();
                      },
                      child:  Text(
                        'CONFIRMAR',
                        style: TextStyle(color: ThemeColors.customOrange(context)),
                      ),
                    ),
                  ],
                );
              });

          return false;
        }
        return true;
      },
      child: ValueListenableBuilder<FcmAlertState>(
        valueListenable: widget.store.state,
        builder: (_, state, __) {
          return KeyboardVisibilityBuilder(
            builder: (context, isKeyboardVisible) {
              return SafeArea(
                child: Scaffold(
                  body: SingleChildScrollView(
                    child: Column(
                      children: [
                        const SizedBox(
                          height: 5,
                        ),
                        GestureDetector(
                          onTap: () {
                            widget.store.getDadosPedidos(context);
                          },
                          child: Padding(
                            padding: const EdgeInsets.all(15.0),
                            child: FcmAppBarCustom(
                                title:
                                    '${state.naoColetados.length}  ${state.naoColetados.length == 1 ? 'Pedido' : 'Pedidos'} para devolução'),
                          ),
                        ),
                        UberizadoLojaCard(
                          titulo: state.fcmAlertDados.titulo,
                          endereco: state.fcmAlertDados.endereco,
                          foto: state.fcmAlertDados.logo ?? "",
                          iniciais: state.fcmAlertDados.titulo.iniciais,
                        ),
                        const SizedBox(height: 6),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                             Expanded(
                              child: Divider(
                                color: ThemeColors.customOrange(context),
                                thickness: 2,
                                indent: 20,
                                endIndent: 20,
                              ),
                            ),
                            Text(
                              '${state.naoColetados.length} ${state.naoColetados.length == 1 ? ' Pedido' : ' Pedidos'}',
                              style:  TextStyle(
                                color: ThemeColors.customOrange(context),
                                fontWeight: FontWeight.w500,
                                fontSize: 17,
                              ),
                            ),
                             Expanded(
                              child: Divider(
                                color: ThemeColors.customOrange(context),
                                thickness: 2,
                                indent: 20,
                                endIndent: 20,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 6),
                        ConstrainedBox(
                          constraints: BoxConstraints(
                            minHeight: MediaQuery.of(context).size.height * .4,
                          ),
                          child: Container(
                            color: Colors.white,
                            padding: const EdgeInsets.only(left: 15, right: 15),
                            child: ListView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: state.clientesNaoColetados.length,
                              itemBuilder: (context, index) {
                                final pedido =
                                    state.clientesNaoColetados[index];
                                final pedidos =
                                    state.pedidosTotaisCliente(pedido.cliente);
                                final qtdPedido = pedidos.length;

                                return Padding(
                                  padding: const EdgeInsets.only(
                                    bottom: 2,
                                    top: 2,
                                  ),
                                  child: CardPedidoPadrao(
                                    nomeCliente: pedido.cliente,
                                    endereco: pedido.title,
                                    status: pedido.status,
                                    pedidos: pedidos,
                                    coletados: qtdPedido,
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.end,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SizedBox(
                                height: 54,
                                width: double.infinity,
                                child: TextField(
                                  style: const TextStyle(
                                    fontSize: 20,
                                    height: 1,
                                  ),
                                  onChanged: widget.store.setLiberadoPor,
                                  textInputAction: TextInputAction.search,
                                  decoration: InputDecoration(
                                    hintText: 'Devolvido na loja para:',
                                    hintStyle: const TextStyle(
                                      color: Colors.grey,
                                      fontSize: 18,
                                    ),
                                    prefixIcon: Icon(
                                      Icons.supervisor_account_sharp,
                                      color: Colors.grey.shade600,
                                    ),
                                    filled: true,
                                    fillColor: const Color.fromARGB(
                                        255, 238, 238, 238),
                                    border: const OutlineInputBorder(
                                      borderSide: BorderSide.none,
                                      borderRadius: BorderRadius.all(
                                        Radius.circular(8),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              ButtonLsCustom(
                                isLoading: state.isLoadingButton,
                                onPressed: () async {
                                  if (widget.store.state.value.liberadoPor
                                          .length >
                                      4) {
                                    widget.store.setLoadingButton(true);

                                    if (await widget.store
                                        .verificarDistancia()) {
                                      widget.store.setLoadingButton(false);
                                      return;
                                    }

                                    try {
                                      await widget.store
                                          .finalizeColeta(context);
                                      HomeController.instance.fetchAtividades();
                                      Navigator.pushAndRemoveUntil(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => const Home(
                                            enterAtividade: false,
                                          ),
                                        ),
                                        (route) => false,
                                      );
                                    } catch (e) {
                                      await LogDatabase.instance.logError(
                                        '/deslocamento/pedidoscoletados',
                                        '',
                                        'finalizeColeta',
                                        {
                                          'error': e.toString(),
                                          'error_type':
                                              e.runtimeType.toString(),
                                        },
                                      );
                                    }
                                  } else {
                                    await showDialog(
                                      context: context,
                                      builder: (ctx) {
                                        return AlertDialog(
                                          title: const Text('Atenção'),
                                          content: const Text(
                                              'Informe o nome de quem liberou a mercadoria na loja.'),
                                          actions: [
                                            TextButton(
                                              onPressed: () {
                                                Navigator.pop(ctx);
                                              },
                                              child:  Text(
                                                'Ok',
                                                style: TextStyle(
                                                    color: ThemeColors.customOrange(context)),
                                              ),
                                            )
                                          ],
                                        );
                                      },
                                    );
                                  }
                                },
                                text: 'FINALIZAR DEVOLUÇÃO',
                                colorBackground: ThemeColors.customOrange(context),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
