import 'model/pedido_coleta_despachar_model.dart';

class ColetaHomeState {
  final bool loadinTela;
  final bool loadingButton;
  final List<PedidoColetaDespachar> pedidos;
  final PedidoColetaDespachar? pedidoColetaDespachar;
  ColetaHomeState({
    required this.loadinTela,
    required this.loadingButton,
    required this.pedidos,
    this.pedidoColetaDespachar,
  });

  List<String> get clientes {
    final list = <String>[];
    for (var i = 0; i < pedidos.length; i++) {
      if (!list.contains(pedidos[i].cliente)) {
        list.add(pedidos[i].cliente);
      }
    }
    return list.reversed.toList();
  }

  ColetaHomeState copyWith({
    bool? loadinTela,
    bool? loadingButton,
    List<PedidoColetaDespachar>? pedidos,
    PedidoColetaDespachar? pedidoColetaDespachar,
  }) {
    return ColetaHomeState(
      loadinTela: loadinTela ?? this.loadinTela,
      loadingButton: loadingButton ?? this.loadingButton,
      pedidos: pedidos ?? this.pedidos,
      pedidoColetaDespachar:
          pedidoColetaDespachar ?? this.pedidoColetaDespachar,
    );
  }
}
