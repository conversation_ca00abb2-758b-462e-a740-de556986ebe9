import 'dart:convert';
import 'dart:developer';

import 'package:asuka/asuka.dart' as asuka;
import 'package:dio/dio.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:latlong2/latlong.dart';
import 'package:map_fields/map_fields.dart';
import 'package:octalog/errors.dart';
import 'package:octalog/src/helpers/airplane.dart';
import 'package:octalog/src/helpers/versoes_helper.dart';
import 'package:octalog/src/models/expedir_model.dart';
import 'package:octalog/src/models/selfie_agente.dart';
import 'package:octalog/src/models/versoes.dart';
import 'package:octalog/src/pages/home/<USER>/self_moto_boy/self_alert.dart';
import 'package:octalog/src/pages/home/<USER>/transferencia/lojas_model.dart';
import 'package:octalog/src/pages/home/<USER>';

import 'package:octalog/src/utils/theme_colors.dart';

import '../../../../fcm_files/audio_play.dart';
import '../../components/fcm_alert_dailog/models/fcm_deslocamento_get.dart';
import '../../components/fcm_alert_dailog/models/uberizado_model.dart';
import '../../components/fcm_alert_dailog/sub_widgets/ls_fast_receita.dart';
import '../../components/fcm_alert_dailog/sub_widgets/uberizado_show_dialog.dart';
import '../../database/config_blob/config_database.dart';
import '../../database/deslocamento_database/deslocamento_database.dart';
import '../../database/hash_buscar/hash_buscar.dart';
import '../../database/hash_buscar/hash_buscar_database.dart';
import '../../database/log_database/log_database.dart';
import '../../database/log_database/status_agente_database.dart';
import '../../database/offline_request/offline_request_database.dart';
import '../../database/sac/sac_atendimento.dart';
import '../../databaseNew/endereconew/endereconew_database.dart';
import '../../databaseNew/endereconew_offline.dart/endereconew_offline.dart';
import '../../helpers/gps/gps_contract.dart';
import '../../helpers/login/login.dart';
import '../../helpers/web_connector.dart';
import '../../models/id_status_atividade_enum.dart';
import '../../models_new/endereco_new.dart';
import '../../utils/gzip_util.dart';
import '../../utils/offline_helper.dart';
import '../../utils/verify_gps.dart';
import '../../utils/verify_modo_aviao.dart';
import 'components/custom_enum_navigation.dart';
import 'components/notificacoes/notificacao.dart';
import 'components/notificacoes/notificacoes_widget.dart';

class HomeController {
  static final instance = HomeController._();
  HomeController._() {
    _initSearch();
    fetchAtividades(loop: true);
  }

  Future<void> _initSearch() async {
    final box = await Hive.openBox<String>('search_atual');
    final values = box.values.toList();
    if (values.isNotEmpty) {
      state.value = state.value.copyWith(search: values.last);
    }
  }

  final ValueNotifier<HomeState> state = ValueNotifier(HomeState.init());

  void setDataForceUltimaSincronizacao() {
    state.value = state.value.copyWith(dataForceUltimaSincronizacao: DateTime.now());
  }

  Future<void> setIsOnline(bool value) async {
    try {
      state.value = state.value.copyWith(isOnlineLoading: true, isOnline: value);
      await StatusAgenteDatabase.instance.setStatusAgente(value);
      final error = await GpsHelperContract.instance.enviarPosicaoAPI();
      if (error) {
        await StatusAgenteDatabase.instance.setStatusAgente(!value);
        asuka.AsukaSnackbar.alert('Falha ao conectar...').show();
      }
      state.value = state.value.copyWith(isOnlineLoading: false, isOnline: error ? !value : value);
    } catch (e) {
      state.value = state.value.copyWith(isOnlineLoading: false);
    }
  }

  int get totalPedidosQtde {
    final total = state.value.atividadesCompletas.length + state.value.deslocamentosColeta.length;
    return total;
  }

  void setListColetaAdd(bool value) => state.value = state.value.copyWith(listColetaAdd: value);

  Future<void> setSearch(String value) async {
    final box = await Hive.openBox<String>('search_atual');
    final search = value;
    state.value = state.value.copyWith(search: value);
    if (search.isEmpty) {
      await box.clear();
      state.value = state.value.copyWith(isBarcode: false);
    } else {
      await box.add(value);
    }
  }

  void setIsBarcode(bool value) => state.value = state.value.copyWith(isBarcode: value);

  void setPageSelected(HomeNavigationEnum value) {
    setListColetaAdd(false);
    state.value = state.value.copyWith(pageSelected: value);
  }

  void setHaveNegativa(bool value) {
    state.value = state.value.copyWith(havenegativa: value);
  }

  void setIdStatusAtividade(IdStatusAtividadeEnum status) => state.value = state.value.copyWith(statusAtividade: status);

  void setCarregouInicio(bool value) {
    state.value = state.value.copyWith(carregouInicio: value);
    _fetchAtividades();
  }

  void setFimRota(Map<String, dynamic>? map) {
    LatLng? fimRota;
    if (map == null) {
      fimRota = null;
    } else {
      final f = MapFields.load(map);
      final lat = f.getDoubleNullable('FimLatitude');
      final lng = f.getDoubleNullable('FimLongitude');
      if (lat != null && lng != null) {
        fimRota = LatLng(lat, lng);
      } else {
        fimRota = null;
      }
    }
    state.value = state.value.copyWith(fimRota: fimRota);
  }

  Future<int> _fetchAtividades({bool inicioHome = false}) async {
    try {
      while (state.value.rodandoFetchAtividades) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
      state.value = state.value.copyWith(rodandoFetchAtividades: true);
      final atividadesOffline = await EnderecoNewOfflineDatabase.instance.getAtividades();
      final ids = await OfflineRequestDatabase.instance.idsAtividadesOffline();
      final idosList = atividadesOffline.where((a) => ids.contains(a.idEnderecoCliente)).map((e) => e.idosList).toList().expand((e) => e).toSet().toList();

      List<int> idsAtividadeDel = [];
      if (ids.isEmpty) {
        idsAtividadeDel = atividadesOffline.map((e) => e.idEnderecoCliente).toList();
      }
      if (inicioHome) {
        final atividades = await replaceAtividadeOfflineOrig(await EnderecoNewDatabase.instance.getEnderecoNew(), atividadesOffline, ids, idosList);
        state.value = state.value.copyWith(atividadesCompletas: atividades);
      } else {
        try {
          if ((Login.instance.usuarioLogado?.usuario ?? '') == '3329') return 0;
          final hashBusca = await HashBuscarDatabase.instance.getLastBusca();
          final gZipBolean = await ConfigDatabase.instance.getConfig();
          final response = await WebConnector().get(
            '/atividades/buscar',
            headers: {'token': Login.instance.usuarioLogado?.token ?? ''},
            queryParameters: {'HashCodeBuscar': hashBusca.hash, 'UseGZip': gZipBolean.usarGZipBuscar},
          );
          if (response.statusCode == 304) {
            final atividades = await replaceAtividadeOfflineOrig(await EnderecoNewDatabase.instance.getEnderecoNew(), atividadesOffline, ids, idosList);
            state.value = state.value.copyWith(atividadesCompletas: atividades);
          } else if (response.statusCode == 200) {
            final config = await ConfigDatabase.instance.getConfig();

            final Map<String, dynamic> responseData = config.usarGZipBuscar ? jsonDecode(GZipUtil.decompress(response.data)) : response.data;

            await HashBuscarDatabase.instance.setLastBusca(HashBuscar(hash: responseData['HashCodeBuscar'] ?? 0, dados: responseData));
            await EnderecoNewOfflineDatabase.instance.deleteWhereContain(idsAtividadeDel);
            setFimRota(responseData['InicioFimRota']);
            final uberizados = responseData['Uberizado'] ?? [];
            final List<RemoteMessage> uberizadosList =
                uberizados.cast<Map<String, dynamic>>().map<RemoteMessage>((Map<String, dynamic> e) => UberizadoModel.fromMap(e).toRemoteMessage()).toList();
            for (int i = 0; i < uberizadosList.length; i++) {
              if (i == 0) {
                playAudio();
              }
              final u = uberizadosList[i];
              await asuka.Asuka.showDialog(
                barrierColor: Colors.black.withOpacity(0.8),
                barrierDismissible: false,
                builder: (ctx) {
                  final uberizadoreceita = MapFields.load(u.data).getBool('receita', false);

                  if (uberizadoreceita == true) {
                    return LsFastReceita(message: u);
                  } else {
                    return UberizadoShowDailog(message: u);
                  }
                },
              );
            }

            final data = responseData['Enderecos'];

            final List<EnderecoNew> atividades =
                data
                    .cast<Map<String, dynamic>>()
                    .map<EnderecoNew>((Map<String, dynamic> e) => EnderecoNew.fromJson(e))
                    .where((EnderecoNew a) => a.volumesLength > 0)
                    .toList();
            final atividadesSv = await replaceAtividadeOfflineOrig(atividades, atividadesOffline, ids, idosList);
            state.value = state.value.copyWith(atividadesCompletas: atividadesSv);
            await OfflineRequestDatabase.instance.deleteEnviados();
            final dataDeslocamentos = responseData['Deslocamentos'];
            final coletasGet =
                dataDeslocamentos.cast<Map<String, dynamic>>().map<FcmDeslocamentoGet>((Map<String, dynamic> e) => FcmDeslocamentoGet.fromMapKlev(e)).toList()
                    as List<FcmDeslocamentoGet>;
            await DeslocamentoDatabase.instance.deleteWhereNotIn(coletasGet);

            for (final col in coletasGet) {
              await DeslocamentoDatabase.instance.save(col);
            }

            final deslocamentosColeta = await DeslocamentoDatabase.instance.getDeslocamentos();
            state.value = state.value.copyWith(deslocamentosColeta: deslocamentosColeta);
            offlineStore.setOffline(false);
            debugPrint('atividades: ${state.value.atividadesCompletas.length}');
            await EnderecoNewDatabase.instance.setAtividades(state.value.atividadesCompletas);
            final f = MapFields.load(responseData);
            final notificacoes = f.getList<Map<String, dynamic>>('Notificacoes', []);
            await mostrarNotificacoes(notificacoes);

            if (uberizadosList.isNotEmpty) {
              state.value = state.value.copyWith(rodandoFetchAtividades: false);
              await Future.delayed(const Duration(milliseconds: 500));
              return _fetchAtividades();
            }
            SelfieAgente? self = SelfieAgente.fromJson(responseData['SelfieAgente'] ?? {});

            final isExpedicao = f.getBool('RomaneioEmAberto', false);

            state.value = state.value.copyWith(isExpedicao: isExpedicao);

            final Versoes versoes = Versoes.fromMap(responseData['VersoesMobile'] ?? {});

            if (versoes.updateNecessary(versoes.versaoLoja)) {
              await VersoesHelper.updateDialogGlobal();
            }

            final pedidosSac = f.getListNullable<Map<String, dynamic>>('PedidosSac');
            if (pedidosSac != null) {
              final pedidosSacDatabase = PedidosSacDatabase.instance;

              List<PedidosSacModel> pedidosSacModelFinal = [];
              List<PedidosSacModel> pedidosSacModel = pedidosSac.map((e) => PedidosSacModel.fromJson(e)).toList();

              pedidosSacModelFinal = PedidosSacModel.fromJsonWithStatusList(pedidosSacModel);
              for (var pedido in pedidosSacModelFinal) {
                await pedidosSacDatabase.setSac(pedido);
              }
            }

            if (self.iDAgentesSelfInfo != null) {
              await asuka.Asuka.showDialog(
                builder: (ctx) {
                  return SelfAlert(selfie: self);
                },
              );
            }
            final avaliacaoApp = f.getBool('AvaliacaoApp', false);
            if (avaliacaoApp) {
              final InAppReview inAppReview = InAppReview.instance;

              if (await inAppReview.isAvailable()) {
                inAppReview.requestReview();
              }
            }
          } else {
            await LogDatabase.instance.logInfo('Buscar', '/atividades/buscar', '', {'status_code': response.statusCode, 'data': response.data.toString()});
            throw Exception(response.data.toString());
          }
        } on DioException catch (e) {
          await LogDatabase.instance.logError('Buscar', '/atividades/buscar', '', {
            'status_code': e.response?.statusCode.toString(),
            'data': e.response?.data.toString(),
          });
          debugPrint(e.message);
          offlineStore.setOffline(true);
          final ends = await EnderecoNewDatabase.instance.getEnderecoNew();
          final atividades = await replaceAtividadeOfflineOrig(ends, atividadesOffline, ids, idosList);
          state.value = state.value.copyWith(atividadesCompletas: atividades);
          await EnderecoNewDatabase.instance.setAtividades(atividades);
        } catch (e) {
          await LogDatabase.instance.logError('Buscar', '/atividades/buscar', '', {'error': e.toString()});
          debugPrint(e.toString());
          offlineStore.setOffline(true);
          final ends = await EnderecoNewDatabase.instance.getEnderecoNew();
          final atividades = await replaceAtividadeOfflineOrig(ends, atividadesOffline, ids, idosList);
          state.value = state.value.copyWith(atividadesCompletas: atividades);
        }
      }
      state.value = state.value.copyWith(rodandoFetchAtividades: false);
      return 0;
    } catch (e) {
      state.value = state.value.copyWith(rodandoFetchAtividades: false);
      rethrow;
    }
  }

  Future<void> mostrarNotificacoes(List<Map<String, dynamic>> notificacoes) async {
    for (final jsonBody in notificacoes) {
      final not = Notificacao.fromMap(jsonBody);
      await asuka.Asuka.showDialog(
        barrierColor: Colors.black.withOpacity(0.8),
        barrierDismissible: false,
        builder: (ctx) {
          return NotificacoesWidget(notificacao: not);
        },
      );
    }
  }

  Future<void> fetchAtividades({bool loop = false, bool inicioHome = false}) async {
    if (Login.instance.usuarioLogado == null) return;
    if (Login.instance.usuarioLogado?.loginMocked == true) {
      state.value = state.value.copyWith(loading: true);
      await Future.delayed(const Duration(seconds: 3));
      state.value = state.value.copyWith(loading: false);
      return;
    }

    bool rodando = true;
    while (rodando) {
      rodando = loop;
      final gpsEnable = await GpsHelperContract.instance.checaGpsLigado();
      final modoAvviao = await Airplane.checkAirplaneMode();

      await OfflineRequestDatabase.instance.getQtdEnventosRestantes();
      if (!gpsEnable) {
        await asuka.Asuka.showDialog(builder: (ctx) => const VerifyGps());
        return;
      } else if (modoAvviao.index == 0) {
        await asuka.Asuka.showDialog(builder: (ctx) => const VerifiModoAviao());
        return;
      }

      try {
        state.value = state.value.copyWith(loading: true);
        await _fetchAtividades(inicioHome: inicioHome).timeout(
          const Duration(seconds: 120),
          onTimeout: () async {
            await LogDatabase.instance.logInfo('Buscar', '/atividades/buscar', 'timeout', {});
            return 0;
          },
        );
      } catch (e) {
        log('Erro Loop Atividades: $e');
      } finally {
        final deslocamentosColeta = await DeslocamentoDatabase.instance.getDeslocamentos();

        state.value = state.value.copyWith(deslocamentosColeta: deslocamentosColeta);

        state.value = state.value.copyWith(loading: false);
        if (rodando) {
          final config = await ConfigDatabase.instance.getConfig();
          final time = config.timerBuscarAutomatico ?? 30;
          state.value = state.value.copyWith(loading: false, carregouInicio: true);
          final next = DateTime.now().add(Duration(seconds: time));
          while (next.isAfter(DateTime.now())) {
            await Future.delayed(const Duration(seconds: 3));
          }
        }
      }
    }
  }

  Future<List<EnderecoNew>> replaceAtividadeOfflineOrig(List<EnderecoNew> values, List<EnderecoNew> offlines, List<int> ids, List<int> idosList) async {
    if (ids.isEmpty) {
      await EnderecoNewDatabase.instance.setAtividades(values);
      return values;
    }
    final ats = await compute(replaceAtividadeOffline, [values.map((e) => e.toHiveMap()).toList(), offlines.map((e) => e.toHiveMap()).toList(), ids, idosList]);
    final List<EnderecoNew> resposta = ats.toList().map((e) => EnderecoNew.fromHiveMap(e)).toList();
    await EnderecoNewDatabase.instance.setAtividades(resposta);
    return resposta;
  }

  static Future<List<Map<String, dynamic>>> replaceAtividadeOffline(List vs) async {
    List<Map<String, dynamic>> values = vs[0];
    List<Map<String, dynamic>> offlines = vs[1];
    // List<int> ids = vs[2];
    List<int> idosList = vs[3];

    final valuesEnderecoNew = values.map((v) => EnderecoNew.fromHiveMap(v)).toList();
    final offlinesEnderecoNew = offlines.map((v) => EnderecoNew.fromHiveMap(v)).toList();
    // final atividadesOfflineFilter = offlinesEnderecoNew
    //     .where((at) => ids.contains(at.idEnderecoCliente))
    //     .toList();
    // for (var atividadeOffline in atividadesOfflineFilter) {
    //   final index = valuesEnderecoNew.indexWhere(
    //       (e) => e.idEnderecoCliente == atividadeOffline.idEnderecoCliente);
    //   if (index != -1) {
    //     valuesEnderecoNew[index] = atividadeOffline;
    //   }
    // }
    // return valuesEnderecoNew.map((v) => v.toHiveMap()).toList();
    var response = [...valuesEnderecoNew];
    for (final idos in idosList) {
      var indexOffline = offlinesEnderecoNew.indexWhere((e) => e.idosList.contains(idos));
      var indexOnline = valuesEnderecoNew.indexWhere((e) => e.idosList.contains(idos));
      if (indexOnline != -1 && indexOffline != -1) {
        response[indexOnline] = offlinesEnderecoNew[indexOffline];
      }
    }
    return response.map((v) => v.toHiveMap()).toList();
  }

  Future<List<ExpedirModel>?> expedirPedido(String barcode, int? idLocal, bool expedir) async {
    state.value = state.value.copyWith(loading: true);
    try {
      final response = await WebConnector().post('/entrega/expedir', body: {'OS': barcode, 'IDLocal': idLocal, 'Expedir': expedir});
      if (response.statusCode == 200) {
        final data = response.data;
        final body = data is String ? jsonDecode(data) : data;
        return body.map((e) => ExpedirModel.fromJson(e)).toList().cast<ExpedirModel>();
      }
      state.value = state.value.copyWith(loading: false);
    } on ConnectionError catch (e) {
      state.value = state.value.copyWith(loading: false);
      offlineStore.setOffline(true);
      await LogDatabase.instance.logError('Expedir', '/entrega/expedir', '', {'error': e.toString()});
      final mensagemJson = jsonDecode(e.response);
      final mensagem = mensagemJson['Mensagem'];

      await asuka.Asuka.showDialog(
        builder: (ctx) {
          return AlertDialog(
            title: const Text('Aviso'),
            content: Text(mensagem),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(ctx);
                },
                child: const Text('OK'),
              ),
            ],
          );
        },
      );
    } catch (e) {
      offlineStore.setOffline(true);
      state.value = state.value.copyWith(loading: false);
    }
    return null;
  }

  void setLojaOrigem(LojasTransferencia? loja) {
    state.value = state.value.copyWith(lojaOrigem: loja);

    final lojas = state.value.lojasDestino;
    // se existir somente 2 lojas, selecionar a loja de destino automaticamente diferente da loja de origem
    if (lojas.length == 1) {
      setLojaDestino(lojas.first);
    } else if (lojas.length == 2) {
      final lojaDiferente = lojas.firstWhere((element) => element.idLoja != loja?.idLoja, orElse: () => lojas.first);
      setLojaDestino(lojaDiferente);
    }
  }

  void setLojaDestino(LojasTransferencia? loja) {
    state.value = state.value.copyWith(lojaDestino: loja);
  }

  void setTransferenciaComRetorno(bool value) {
    state.value = state.value.copyWith(transferenciaComRetorno: value);
  }

  void setLoadingTelaTransferencia(bool value) {
    state.value = state.value.copyWith(isLoadingTelaTransferencia: value);
  }

  void setLoadingButtonTransferencia(bool value) {
    state.value = state.value.copyWith(loadingButtonTransferencia: value);
  }

  setNullTransferencia({bool isLojaOrigem = false, bool isLojaDestino = false, bool isResto = false}) {
    state.value = state.value.copyWithNull(isLojaOrigem: isLojaOrigem, isLojaDestino: isLojaDestino, isResto: isResto);
  }

  Future<bool> buscarLojasParaTransferencia() async {
    setNullTransferencia(isLojaOrigem: true, isLojaDestino: true, isResto: true);

    try {
      final response = await WebConnector().get('/atividades/locais-transferir');

      if (response.statusCode == 200) {
        final data = response.data;

        final json = data is String ? jsonDecode(data) : data;
        final TransferenciaModel lojas = TransferenciaModel.fromJson(json);
        state.value = state.value.copyWith(lojasTransferencia: lojas);
        // se existir apenas uma loja de origem e uma de destino, já seta
        if (lojas.origens.length == 1) {
          setLojaOrigem(lojas.origens.first);
        }
        if (lojas.destinos.length == 1) {
          setLojaDestino(lojas.destinos.first);
        }
      }
      setLoadingTelaTransferencia(true);
      return true;
    } on ConnectionError catch (e) {
      setNullTransferencia(isLojaOrigem: true, isLojaDestino: true, isResto: true);
      setPageSelected(HomeNavigationEnum.home);
      setLoadingButtonTransferencia(false);
      offlineStore.setOffline(false);

      await asuka.Asuka.showCupertinoDialog(
        builder: (ctx) {
          return AlertDialog(
            title: const Text('Aviso'),
            content: Text(e.response),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(ctx);
                },
                child: const Text('OK', style: TextStyle(color: Colors.orange)),
              ),
            ],
          );
        },
      );
    }
    return false;
  }

  void confirmarTransferencia() async {
    if (state.value.lojaOrigem == null || state.value.lojaDestino == null) {
      await showDialog('Selecione a loja de ${state.value.lojaOrigem == null ? 'origem' : 'destino'}');
      return;
    }

    if (state.value.lojaOrigem == state.value.lojaDestino) {
      await showDialog('Loja de origem e destino são iguais');
      return;
    }

    if (state.value.lojaOrigem!.iDLocalGrupo != state.value.lojaDestino!.iDLocalGrupo) {
      await showDialog('Loja de origem e destino são de grupos diferentes, não é possível transferir pedido');
      return;
    }

    if (state.value.lojaOrigem!.idLoja == state.value.lojaDestino!.idLoja) {
      await showDialog('Loja de origem e destino são iguais, não é possível transferir pedido');
      return;
    }

    try {
      setLoadingButtonTransferencia(true);
      final result = await WebConnector().get(
        '/atividades/transferir',
        queryParameters: {
          'IDLocalOrigem': state.value.lojaOrigem?.idLoja,
          'IDLocalDestino': state.value.lojaDestino?.idLoja,
          'Retorno': state.value.transferenciaComRetorno,
        },
      );

      if (result.statusCode == 200) {
        fetchAtividades();
        setNullTransferencia(isLojaOrigem: true, isLojaDestino: true, isResto: true);
        setPageSelected(HomeNavigationEnum.home);
      }
    } on ConnectionError catch (e) {
      setNullTransferencia(isLojaOrigem: true, isLojaDestino: true, isResto: true);
      setPageSelected(HomeNavigationEnum.home);
      final mensagemJson = jsonDecode(e.response);
      final mensagem = mensagemJson['message'];
      setLoadingButtonTransferencia(false);
      await asuka.Asuka.showDialog(
        builder: (ctx) {
          return AlertDialog(
            title: const Text('Aviso'),
            content: Text(mensagem),
            actions: [
              TextButton(
                onPressed: () async {
                  fetchAtividades();
                  setNullTransferencia(isLojaOrigem: true, isLojaDestino: true, isResto: true);
                  setPageSelected(HomeNavigationEnum.home);
                  setLoadingButtonTransferencia(false);
                  Navigator.pop(ctx);
                },
                child: Text('OK', style: TextStyle(color: ThemeColors.customOrange(ctx))),
              ),
            ],
          );
        },
      );
    }
    setNullTransferencia(isLojaOrigem: true, isLojaDestino: true, isResto: true);
    setPageSelected(HomeNavigationEnum.home);
    setLoadingButtonTransferencia(false);
  }

  Future<void> showDialog(String message) async {
    await asuka.Asuka.showDialog(
      builder: (ctx) {
        return AlertDialog(
          title: const Text('Aviso'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(ctx);
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }
}
