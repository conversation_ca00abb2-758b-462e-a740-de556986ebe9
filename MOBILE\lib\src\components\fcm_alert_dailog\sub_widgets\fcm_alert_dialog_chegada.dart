import "package:asuka/asuka.dart" as asuka;
import 'package:asuka/snackbars/asuka_snack_bar.dart';
import 'package:flutter/material.dart';
import 'package:octalog/src/components/fcm_alert_dailog/components/fcm_porcent.dart';
import 'package:octalog/src/components/fcm_alert_dailog/models/fcm_deslocamento_get.dart';
import 'package:octalog/src/models_new/position_data_location.dart';
import 'package:octalog/src/utils/extesion.dart';
import 'package:octalog/src/utils/theme_colors.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../database/config_blob/config_database.dart';
import '../../../database/envio_erro/envio_erro.dart';
import '../../../database/log_database/log_database.dart';
import '../../../helpers/gps/gps_contract.dart';
import '../../../pages/home/<USER>/aviso_scronimos_pendente.dart';
import '../../../pages/home/<USER>';

import '../../buttom_ls/button_ls_custom.dart';
import '../components/fcm_appbar_custom.dart';
import '../components/fcm_deslocamento_widget.dart';
import '../components/fcm_distance_data.dart';
import '../fcm_alert_dialog_store.dart';

class FcmAlertDailogChegada extends StatefulWidget {
  final FcmAlertDialogStore store;
  const FcmAlertDailogChegada({
    super.key,
    required this.store,
  });

  @override
  State<FcmAlertDailogChegada> createState() => _FcmAlertDailogChegadaState();
}

class _FcmAlertDailogChegadaState extends State<FcmAlertDailogChegada> {
  final controller = HomeController.instance;

  FcmDeslocamentoGet get fcmAlertDados =>
      widget.store.state.value.fcmAlertDados;

  bool isclick = false;
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.only(left: 20, right: 20, top: 10),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.max,
              children: [
                FcmAppBarCustom(
                  title: fcmAlertDados.titulo,
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 18),
                  child: Image.asset('assets/images/image_fmc.png'),
                ),
                const Padding(
                  padding: EdgeInsets.only(top: 10, left: 10, right: 10),
                  child: FcmPorcent(),
                ),
                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: !fcmAlertDados.reverso
                      ?  Text(
                          'Você tem uma coleta para fazer na loja',
                          style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 17,
                              color: ThemeColors.customOrange(context)),
                        )
                      :  Text(
                          'Você tem uma devolução para fazer na loja',
                          style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 17,
                              color: ThemeColors.customOrange(context)),
                        ),
                ),
                const SizedBox(
                  height: 10,
                ),
                FcmDeslocamentoWidget(
                  local: fcmAlertDados.titulo,
                  endereco: fcmAlertDados.endereco,
                ),
                const SizedBox(
                  height: 10,
                ),
                FcmDistanceData(
                  horaFinal: fcmAlertDados.reverso
                      ? null
                      : fcmAlertDados.horario.dataFormatada,
                  destino: fcmAlertDados.destino,
                ),
                const SizedBox(
                  height: 10,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    imagem(
                      image: 'assets/images/google_maps.png',
                      text: 'Maps',
                      onTap: () async {
                        final dest = [
                          fcmAlertDados.latitude,
                          fcmAlertDados.longitude
                        ];
                        final url =
                            Uri.parse('google.navigation:q=${dest.join(',')}');
                        final fallbackUrl = Uri.parse(
                            'https://www.google.com/maps/dir/?api=1&destination=${dest.join(',')}');
                        if (await canLaunchUrl(url)) {
                          await launchUrl(
                            url,
                            mode: LaunchMode.externalApplication,
                          );
                        } else {
                          await launchUrl(
                            fallbackUrl,
                            mode: LaunchMode.externalApplication,
                          );
                        }
                      },
                    ),
                    imagem(
                      image: 'assets/images/waze_2.png',
                      text: 'Waze',
                      onTap: () async {
                        final dest = [
                          fcmAlertDados.latitude,
                          fcmAlertDados.longitude
                        ];
                        final url = Uri.parse('waze://?ll=${dest.join(',')}');
                        final fallbackUrl = Uri.parse(
                            'https://www.waze.com/ul?ll=${dest.join(',')}&navigate=yes');
                        if (await canLaunchUrl(url)) {
                          await launchUrl(
                            url,
                            mode: LaunchMode.externalApplication,
                          );
                        } else {
                          await launchUrl(
                            fallbackUrl,
                            mode: LaunchMode.externalApplication,
                          );
                        }
                      },
                    ),
                  ],
                ),
                const SizedBox(
                  height: 80,
                )
              ],
            ),
          ),
        ),
        floatingActionButton: Padding(
          padding: const EdgeInsets.only(
            left: 40,
            right: 10,
          ),
          child: ButtonLsCustom(
            isLoading: isclick,
            text: 'CHEGADA NA LOJA',
            colorBackground: ThemeColors.customOrange(context),
            onPressed: () async {
              final config = await ConfigDatabase.instance.getConfig();

              if (fcmAlertDados.reverso == false) {
                final listdeslocamento = controller
                    .state.value.deslocamentosColeta
                    .where((deslocamento) =>
                        deslocamento.idLocal == fcmAlertDados.idLocal &&
                        deslocamento.id != fcmAlertDados.id)
                    .toList();
                final listdeslocamentoReverso = listdeslocamento
                    .where((deslocamento) => deslocamento.reverso == true)
                    .toList();

                if (listdeslocamentoReverso.isNotEmpty) {
                  return asuka.Asuka.showDialog(
                    barrierColor: Colors.black.withOpacity(0.5),
                    builder: (context) => AlertDialog(
                      backgroundColor: Colors.white,
                      title: const Text('Atenção'),
                      content: const Text(
                          'Você precisa fazer a devolução antes da coleta.'),
                      actions: [
                        TextButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          child:  Text(
                            'OK',
                            style: TextStyle(color: ThemeColors.customOrange(context)),
                          ),
                        )
                      ],
                    ),
                  );
                }
              }

              if (fcmAlertDados.reverso) {
                bool continuarSicronismo =
                    await entrarTelaSicronismo(minimo: 2);
                if (!continuarSicronismo) return;
              }

              setState(() => isclick = true);

              PositionDataLocation? position =
                  await GpsHelperContract.instance.updateAndGetLastPosition();

              position ??= await GpsHelperContract.instance
                  .updateLoc(timeoutSeconds: 10);

              final distancia =
                  position == null ? -1 : fcmAlertDados.distance(position);

              bool liberadoPelaDistancia = true;

              if (config.bloqueioDeslocamentoForaDoLocal) {
                if (position != null) {
                  liberadoPelaDistancia =
                      distancia < config.distanciaMetrosChegadaDeslocamento;
                } else {
                  await enviarErro(
                    erro: true,
                    metodo: "ChegadaNaLoja",
                    jsonBody: {
                      "distancia": distancia * 1000,
                      "id_deslocamento": fcmAlertDados.id,
                      "latitude_agente": position?.latitude,
                      "longitude_agente": position?.longitude,
                      "latitude_loja": fcmAlertDados.latitude,
                      "longitude_loja": fcmAlertDados.longitude,
                      "data": DateTime.now()
                          .dataHoraServidorFomart
                          .toIso8601String()
                    },
                  );
                }
              }

              setState(() => isclick = false);

              if (position == null) {
                liberadoPelaDistancia = true;
              }

              // fcmAlertDados.latitude for null deixar passar

              if (fcmAlertDados.latitude == 0) {
                liberadoPelaDistancia = true;
              }

              if (liberadoPelaDistancia) {
                try {
                  await widget.store.getDadosPedidos(context);
                  widget.store.chegada();
                } catch (e) {
                  setState(() {
                    isclick = false;
                  });
                  await LogDatabase.instance.logError(
                    'Fcm Alerta Chegada',
                    '',
                    'Fcm Alerta Chegada',
                    {
                      'erro': e.toString(),
                    },
                  );
                  AsukaSnackbar.alert(
                    'Erro ao carregar os dados',
                  ).show();
                }
              } else {
                await enviarErro(
                  erro: !liberadoPelaDistancia,
                  metodo: "ChegadaNaLoja",
                  jsonBody: {
                    "distancia": distancia * 1000,
                    "id_deslocamento": fcmAlertDados.id,
                    "latitude_agente": position?.latitude,
                    "longitude_agente": position?.longitude,
                    "latitude_loja": fcmAlertDados.latitude,
                    "longitude_loja": fcmAlertDados.longitude,
                    "data":
                        DateTime.now().dataHoraServidorFomart.toIso8601String()
                  },
                );

                await asuka.Asuka.showDialog(
                  barrierColor: Colors.black.withOpacity(0.5),
                  builder: (context) => AlertDialog(
                    backgroundColor: Colors.white,
                    title: const Text('Atenção'),
                    content: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        fcmAlertDados.reverso
                            ? const Text(
                                "Sua localização está longe do seu local de devoluçao.")
                            : const Text(
                                "Sua localização está longe do seu local de coleta."),
                        const Text("Você não poderá prosseguir.")
                      ],
                    ),
                    actions: [
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: const Text('CANCELAR',
                            style: TextStyle(color: Colors.red)),
                      ),
                    ],
                  ),
                );
              }
            },
          ),
        ),
      ),
    );
  }

  Widget imagem({
    required String image,
    required String text,
    required Function() onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        height: 70,
        child: Row(
          children: [
            Image.asset(image),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(text),
            )
          ],
        ),
      ),
    );
  }
}
