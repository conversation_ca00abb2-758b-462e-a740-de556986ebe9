import 'dart:convert';

import 'package:hive/hive.dart';
import 'package:map_fields/map_fields.dart';

abstract class LogDatabase {
  static final instance = LogDatabaseImpl();

  Future<void> initLogs();
  Future<void> logSuccess(
    String tag,
    String subTag,
    String message,
    Map<String, dynamic> complementos,
  );
  Future<void> logInfo(
    String tag,
    String subTag,
    String message,
    Map<String, dynamic> complementos,
  );
  Future<void> logError(
    String tag,
    String subTag,
    String message,
    Map<String, dynamic> complementos,
  );
  Future<void> logWarn(
    String tag,
    String subTag,
    String message,
    Map<String, dynamic> complementos,
  );
  Future<List<LogCustom>> getLogs();
}

class LogDatabaseImpl implements LogDatabase {
  late Box<String> database;
  bool initialized = false;
  bool error = false;

  @override
  Future<void> initLogs() async {
    try {
      database = await Hive.openBox<String>('logs_db');
      initialized = true;
    } catch (e) {
      initialized = true;
      error = true;
    }
  }

  Future<void> _log(
    String tag,
    String subTag,
    String message,
    LogType type,
    Map<String, dynamic> complementos,
  ) async {
    while (!initialized) {
      await Future<void>.delayed(const Duration(milliseconds: 100));
    }
    if (error) {
      return;
    }
    final log = LogCustom(
      tag: tag,
      subTag: subTag,
      message: message,
      type: type,
      dateTime: DateTime.now(),
      complementos: complementos,
    );
    await database.add(jsonEncode(log.toJson()));
  }

  @override
  Future<void> logSuccess(
    String tag,
    String subTag,
    String message,
    Map<String, dynamic> complementos,
  ) async {
    await _log(
      tag,
      subTag,
      message,
      LogType.success,
      complementos,
    );
  }

  @override
  Future<void> logInfo(
    String tag,
    String subTag,
    String message,
    Map<String, dynamic> complementos,
  ) async {
    await _log(
      tag,
      subTag,
      message,
      LogType.info,
      complementos,
    );
  }

  @override
  Future<void> logWarn(
    String tag,
    String subTag,
    String message,
    Map<String, dynamic> complementos,
  ) async {
    await _log(
      tag,
      subTag,
      message,
      LogType.warn,
      complementos,
    );
  }

  @override
  Future<void> logError(
    String tag,
    String subTag,
    String message,
    Map<String, dynamic> complementos,
  ) async {
    await _log(
      tag,
      subTag,
      message,
      LogType.error,
      complementos,
    );
  }

  @override
  Future<List<LogCustom>> getLogs() async {
    while (!initialized) {
      await Future<void>.delayed(const Duration(milliseconds: 100));
    }
    if (error) {
      return [
        LogCustom(
          tag: 'Log Error',
          subTag: '',
          message: 'Banco de log não iniciado!',
          dateTime: DateTime.now(),
          type: LogType.error,
          complementos: {},
        ),
      ];
    }
    const int tamanhoMaximo = 50;
    final logs = database.values
        .map((e) => LogCustom.fromJson(jsonDecode(e)))
        .toList()
        .reversed
        .toList();
    final tam = logs.length > tamanhoMaximo ? tamanhoMaximo : logs.length;
    final lista = logs.sublist(0, tam).reversed.toList();
    await database.clear();
    await Future.forEach(lista, (element) async {
      await database.add(jsonEncode(element.toJson()));
    });
    return lista;
  }
}

class LogCustom {
  final String tag;
  final String subTag;
  final String message;
  final DateTime dateTime;
  final LogType type;
  final Map<String, dynamic> complementos;
  LogCustom({
    required this.tag,
    required this.subTag,
    required this.message,
    required this.dateTime,
    required this.type,
    required this.complementos,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'tag': tag,
      'subTag': subTag,
      'message': message,
      'dateTime': dateTime.toIso8601String(),
      'type': type.index,
      'complementos': complementos,
    };
  }

  factory LogCustom.fromMap(Map<String, dynamic> map) {
    final f = MapFields.load(map);
    return LogCustom(
      tag: f.getString('tag', 'Sem tag'),
      subTag: f.getString('subTag', ''),
      message: f.getString('message', ''),
      dateTime: f.getDateTime('dateTime', DateTime(1900)),
      type: LogType.values[f.getInt('type', 0)],
      complementos: f.getMap<String, dynamic>('complementos', {}),
    );
  }

  String toJson() => json.encode(toMap());

  factory LogCustom.fromJson(String source) =>
      LogCustom.fromMap(json.decode(source) as Map<String, dynamic>);
}

enum LogType {
  success,
  info,
  warn,
  error,
}
