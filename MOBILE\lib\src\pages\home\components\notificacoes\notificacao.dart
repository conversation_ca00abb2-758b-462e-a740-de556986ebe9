import 'package:map_fields/map_fields.dart';

class Notificacao {
  final int id;
  final String titulo;
  final List<PaginasNotificacao> paginas;
  final DateTime? dataVisualizacao;
  final bool cadastroIncompleto;
  Notificacao({
    required this.id,
    required this.titulo,
    required this.paginas,
    required this.dataVisualizacao,
    required this.cadastroIncompleto,
  });

  factory Notificacao.fromMap(Map<String, dynamic> map) {
    final f = MapFields.load(map);
    final paginas = f
        .getList<Map<String, dynamic>>('Corpo', [])
        .map<PaginasNotificacao>((e) => PaginasNotificacao.fromMap(e))
        .toList();
    paginas.sort((a, b) => a.ordem.compareTo(b.ordem));
    return Notificacao(
      id: f.getInt('IDNotificacaoMobile', 0),
      titulo: f.getString('Titulo', ''),
      paginas: paginas,
      dataVisualizacao: f.getDateTimeNullable('DataVisualizacao'),
      cadastroIncompleto: f.getBool('CadastroIncompleto', false),
    );
  }

  String get dataFormatada {
    if (dataVisualizacao == null) return '';
    final data = dataVisualizacao!;
    return '${data.day.toString().padLeft(2, '0')}/${data.month.toString().padLeft(2, '0')}/${data.year.toString().padLeft(4, '0')}';
  }
}

class PaginasNotificacao {
  final int ordem;
  final String conteudo;
  PaginasNotificacao({
    required this.ordem,
    required this.conteudo,
  });

  factory PaginasNotificacao.fromMap(Map<String, dynamic> map) {
    final f = MapFields.load(map);
    return PaginasNotificacao(
      ordem: f.getInt('Ordem', 0),
      conteudo: f.getString('Conteudo', '<html></html>'),
    );
  }
}
