@echo off
setlocal enabledelayedexpansion

REM Script para build iOS com flavors
REM Uso: build_ios.bat [flavor] [build_type]
REM Exemplo: build_ios.bat connect release

set FLAVOR=%1
set BUILD_TYPE=%2

if "%FLAVOR%"=="" set FLAVOR=octalog
if "%BUILD_TYPE%"=="" set BUILD_TYPE=release

echo 🍎 Iniciando build iOS
echo 📱 Flavor: %FLAVOR%
echo 🔧 Build Type: %BUILD_TYPE%
echo.

REM Validar flavor
if "%FLAVOR%"=="octalog" goto flavor_ok
if "%FLAVOR%"=="up360" goto flavor_ok
if "%FLAVOR%"=="connect" goto flavor_ok
if "%FLAVOR%"=="rondolog" goto flavor_ok
if "%FLAVOR%"=="spotlog" goto flavor_ok
if "%FLAVOR%"=="boyviny" goto flavor_ok

echo ❌ Flavor inválido: %FLAVOR%
echo Flavors disponíveis: octalog, up360, connect, rondolog, spotlog, boyviny
exit /b 1

:flavor_ok
echo ✅ Flavor válido: %FLAVOR%

REM Validar build type
if "%BUILD_TYPE%"=="debug" goto build_type_ok
if "%BUILD_TYPE%"=="release" goto build_type_ok
if "%BUILD_TYPE%"=="profile" goto build_type_ok

echo ❌ Build type inválido: %BUILD_TYPE%
echo Build types disponíveis: debug, release, profile
exit /b 1

:build_type_ok
echo ✅ Build type válido: %BUILD_TYPE%
echo.

REM Configurar iOS para o flavor
echo 🔧 Configurando iOS para flavor: %FLAVOR%
call scripts\configure_ios_flavor.bat "%FLAVOR%" "%BUILD_TYPE%"

if errorlevel 1 (
    echo ❌ Erro ao configurar iOS para o flavor: %FLAVOR%
    exit /b 1
)

echo.

REM Gerar ícones para o flavor
echo 🎨 Gerando ícones para o flavor: %FLAVOR%
if exist "scripts\generate_icons.bat" (
    call scripts\generate_icons.bat "%FLAVOR%"
    if errorlevel 1 (
        echo ❌ Erro ao gerar ícones para o flavor: %FLAVOR%
        exit /b 1
    )
) else (
    echo ⚠️  Script de geração de ícones não encontrado, continuando sem gerar ícones...
)

echo.

REM Limpar build anterior
echo 🧹 Limpando build anterior...
flutter clean
flutter pub get

echo.

REM Executar build iOS
echo 🚀 Executando build iOS...
echo Comando: flutter build ios --%BUILD_TYPE% --flavor %FLAVOR% --dart-define=FLAVOR=%FLAVOR%

flutter build ios --%BUILD_TYPE% --flavor "%FLAVOR%" --dart-define=FLAVOR="%FLAVOR%"

if errorlevel 1 (
    echo.
    echo ❌ Erro no build iOS
    exit /b 1
) else (
    echo.
    echo ✅ Build iOS concluído com sucesso!
    echo 📱 Flavor: %FLAVOR%
    echo 🔧 Build Type: %BUILD_TYPE%
    echo.
    echo 📁 Localização do build:
    echo build\ios\iphoneos\Runner.app
    echo.
    echo 🚀 Para abrir no Xcode:
    echo open ios\Runner.xcworkspace
)
