import 'package:octalog/src/models/tipo_agente_uberizado_enum.dart';

class UserLogin {
  final String usuario;
  final String senha;
  final String? token;
  final bool atualizarCadastro;

  UserLogin({
    required this.usuario,
    required this.senha,
    this.token,
    this.atualizarCadastro = true,
  });
}

class UserData extends UserLogin {
  final String nomeCompleto;
  final String telefone;
  final String email;
  final String uf;
  final String foto;
  final String cpf;
  final int idTipoAgenteUberizado;
  final bool permiteTransferenciaMobile;
  final bool loginMocked;
  UserData({
    required this.nomeCompleto,
    required this.telefone,
    required this.email,
    required this.uf,
    required this.foto,
    required this.cpf,
    required this.idTipoAgenteUberizado,
    required UserLogin userLogin,
    required this.permiteTransferenciaMobile,
    this.loginMocked = false,
  }) : super(
          usuario: userLogin.usuario,
          senha: userLogin.senha,
          token: userLogin.token,
          atualizarCadastro: userLogin.atualizarCadastro,
        );

  TipoAgenteUberizado get tipoAgenteUberizado {
    final tipos = TipoAgenteUberizado.values
        .where((t) => t.id == idTipoAgenteUberizado)
        .toList();
    if (tipos.isEmpty) return TipoAgenteUberizado.nenhum;
    return tipos.first;
  }

  UserData copyWith({
    String? nomeCompleto,
    String? telefone,
    String? email,
    String? uf,
    String? foto,
    String? cpf,
    String? usuario,
    String? senha,
    String? token,
    bool? atualizarCadastro,
    int? idTipoAgenteUberizado,
    bool? permiteTransferenciaMobile,
  }) {
    return UserData(
      nomeCompleto: nomeCompleto ?? this.nomeCompleto,
      telefone: telefone ?? this.telefone,
      email: email ?? this.email,
      uf: uf ?? this.uf,
      foto: foto ?? this.foto,
      cpf: cpf ?? this.cpf,
      userLogin: UserLogin(
        usuario: usuario ?? this.usuario,
        senha: senha ?? this.senha,
        token: token ?? this.token,
        atualizarCadastro: atualizarCadastro ?? this.atualizarCadastro,
      ),
      idTipoAgenteUberizado:
          idTipoAgenteUberizado ?? this.idTipoAgenteUberizado,
      permiteTransferenciaMobile:
          permiteTransferenciaMobile ?? this.permiteTransferenciaMobile,
    );
  }
}
