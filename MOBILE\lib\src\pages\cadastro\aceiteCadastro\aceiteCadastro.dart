import 'package:flutter/material.dart';
import 'package:octalog/src/pages/cadastro/cadastro_state.dart';

import '../cadastro_store.dart';
import '../model/contrato_model.dart';

class AceiteCadastro extends StatefulWidget {
  final CadastroStore store;
  const AceiteCadastro({super.key, required this.store});

  @override
  State<AceiteCadastro> createState() => _AceiteCadastroState();
}

class _AceiteCadastroState extends State<AceiteCadastro> {
  ContratoModel? contratoModel;
  bool loadingPage = true;
  bool _aceitouTermos = true;

  @override
  void initState() {
    init();
    super.initState();
  }

  init() {}

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<CadastroState>(
      valueListenable: widget.store.state,
      builder: (BuildContext context, state, _) {
        //final store = widget.store;
        return Padding(
          padding: const EdgeInsets.all(14.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Importante:', textAlign: TextAlign.left, style: TextStyle(fontSize: 25, fontWeight: FontWeight.bold)),
              const SizedBox(height: 24),

              RichText(
                textAlign: TextAlign.left,
                text: const TextSpan(
                  style: TextStyle(fontSize: 20, color: Colors.black),
                  children: [
                    TextSpan(text: 'Octalog não é uma transportadora! ', style: TextStyle(fontWeight: FontWeight.bold)),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              RichText(
                textAlign: TextAlign.left,
                text: const TextSpan(
                  style: TextStyle(fontSize: 20, color: Colors.black),
                  children: [
                    TextSpan(text: 'Trata-se de um aplicativo de gestão e apoio logístico, '),
                    TextSpan(text: 'utilizado por empresas transportadoras', style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(text: ' para facilitar e organizar o processo de entregas.'),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              RichText(
                textAlign: TextAlign.left,
                text: const TextSpan(
                  style: TextStyle(fontSize: 20, color: Colors.black),
                  children: [
                    TextSpan(
                      text:
                          'Conforme a Resolução ANTT nº 4.799/2015 e a Lei nº 11.442/2007, apenas empresas registradas como transportadoras podem realizar transporte rodoviário remunerado de cargas. ',
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              RichText(
                textAlign: TextAlign.left,
                text: const TextSpan(
                  style: TextStyle(fontSize: 20, color: Colors.black),
                  children: [
                    TextSpan(
                      text:
                          'Octalog, portanto, atua apenas como uma ferramenta tecnológica, contratada pela transportadora para apoiar os motoristas parceiros na execução das entregas.',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              Row(
                children: [
                  Checkbox(
                    value: _aceitouTermos,
                    onChanged: (value) {
                      setState(() {
                        _aceitouTermos = value ?? true;
                      });
                    },
                  ),
                  Flexible(child: Text('Eu reconheço que não mantenho vínculo empregatício ou contratual com Octalog.')),
                ],
              ),
              const SizedBox(height: 24),

              // ElevatedButton(
              //   onPressed: _aceitouTermos
              //     ? () {
              //         setState(() {
              //           _mostrarFormulario = true;
              //         });
              //       }
              //     : null,
              //   style: ElevatedButton.styleFrom(
              //     backgroundColor: ThemeColors.customOrange(context),
              //     disabledBackgroundColor: Colors.grey,
              //   ),
              //   child: const Text('Continuar', style: TextStyle(color: Colors.white)),
              //),
            ],
          ),
        );
      },
    );
  }
}
