import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:octalog/src/pages/home/<USER>';
import 'package:octalog/src/pages/mapa_page/mapa_page_state.dart';
import 'package:octalog/src/pages/mapa_page/mapa_page_store.dart';
import 'package:octalog/src/utils/theme_colors.dart';

class MapEntregaWidget extends StatefulWidget {
  final LatLng? positionDaEntreg;
  const MapEntregaWidget({super.key, this.positionDaEntreg});

  @override
  State<MapEntregaWidget> createState() => _MapEntregaWidgetState();
}

class _MapEntregaWidgetState extends State<MapEntregaWidget> {
  final controller = HomeController.instance;
  final mapaPageStorei = MapaPageStore();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<MapaPageState>(
      valueListenable: mapaPageStorei.state,
      builder: (BuildContext context, MapaPageState value, Widget? child) {
        final loc = value.latLng;
        return SizedBox(
          height: 300,
          width: MediaQuery.of(context).size.width,
          child: FlutterMap(
            mapController: value.mapaPageController,
            options: MapOptions(
              rotation: 0,
              center: loc,
              zoom: 13.2,
            ),
            nonRotatedChildren: const [
              Positioned(
                bottom: 5,
                right: 5,
                child: Text(
                  "Octalog",
                ),
              ),
            ],
            children: [
              TileLayer(
                urlTemplate:
                    "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
                subdomains: const ['a', 'b', 'c'],
              ),
              PolylineLayer(
                polylines: [
                  Polyline(
                    points: [
                      LatLng(
                        widget.positionDaEntreg!.latitude,
                        widget.positionDaEntreg!.longitude,
                      ),
                    ],
                    strokeWidth: 4.0,
                    color: Colors.blue,
                  ),
                ],
              ),
              MarkerLayer(
                markers: [
                  Marker(
                    width: 20.0,
                    height: 20.0,
                    point: loc,
                    builder: (ctx) {
                      return Image.asset(
                        'assets/images/map_marker.png',
                        color: ThemeColors.customOrange(context),
                      );
                    },
                  ),
                  Marker(
                    width: 45.0,
                    height: 45.0,
                    point: widget.positionDaEntreg!,
                    builder: (ctx) {
                      return Container(
                        decoration: BoxDecoration(
                          color: Colors.orange.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(100),
                        ),
                      );
                    },
                  ),
                  Marker(
                    width: 25.0,
                    height: 25.0,
                    point: widget.positionDaEntreg!,
                    builder: (ctx) {
                      return Image.asset(
                        'assets/images/packageLs.png',
                        width: 2,
                        height: 2,
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
