import 'package:map_fields/map_fields.dart';

class TransferenciaModel {
  final List<LojasTransferencia> origens;
  final List<LojasTransferencia> destinos;

  TransferenciaModel({
    required this.origens,
    required this.destinos,
  });

  factory TransferenciaModel.fromJson(Map<String, dynamic> json) {
    final l = MapFields.load(json);
    return TransferenciaModel(
      origens: l
          .getList<Map<String, dynamic>>('origens', [])
          .map<LojasTransferencia>((e) => LojasTransferencia.fromJson(e))
          .toList(),
      destinos: l
          .getList<Map<String, dynamic>>('destinos', [])
          .map<LojasTransferencia>((e) => LojasTransferencia.fromJson(e))
          .toList(),
    );
  }
}

class LojasTransferencia {
  final int idLoja;
  final int iDLocalGrupo;
  final String loja;
  final String grupo;
  final double latitude;
  final double longitude;
  final String endereco;
  final double distanciaKM;

  LojasTransferencia({
    required this.idLoja,
    required this.loja,
    required this.grupo,
    required this.latitude,
    required this.longitude,
    required this.endereco,
    required this.distanciaKM,
    required this.iDLocalGrupo,
  });

  factory LojasTransferencia.fromJson(Map<String, dynamic> json) {
    final l = MapFields.load(json);

    return LojasTransferencia(
      idLoja: l.getInt('IDLocal', 0),
      loja: l.getString('Loja', ''),
      grupo: l.getString('Grupo', ''),
      latitude: l.getDouble('Latitude', 0),
      longitude: l.getDouble('Longitude', 0),
      endereco: l.getString('Endereco', ''),
      distanciaKM: l.getDouble('Distancia', 0),
      iDLocalGrupo: l.getInt('IDLocalGrupo'),
    );
  }
}
