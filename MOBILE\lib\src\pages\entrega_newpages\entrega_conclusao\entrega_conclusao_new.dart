import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:octalog/src/components/loading_custom/loading_custom.dart';
import 'package:octalog/src/models/id_status_atividade_enum.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../../components/buttom_custom/button_custom.dart';
import '../../../components/custom_scaffold/custom_scaffold.dart';
import '../../../components/error_widget_custom/error_widget_custom.dart';
import '../../../components/image_perfil/image_perfil.dart';
import '../../../components/reentrega_widget/reentrega_widget.dart';
import '../../home/<USER>';
import '../controller/entrega_new_state.dart';
import '../controller/entrega_new_store.dart';
import 'componentes/stack_custom_new.dart';

class EntregasConclusaoNew extends StatefulWidget {
  final EntregaNewStore store;
  final IdStatusAtividadeEnum idStatusAtividadeEnum;
  final Position? position;

  const EntregasConclusaoNew({super.key, required this.store, required this.idStatusAtividadeEnum, required this.position});

  @override
  State<EntregasConclusaoNew> createState() => _EntregasConclusaoNewState();
}

class _EntregasConclusaoNewState extends State<EntregasConclusaoNew> {
  int _currentIndex = 0;
  bool voltou = false;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: _currentIndex);
  }

  void voltarListagem() {
    Navigator.pushAndRemoveUntil(context, MaterialPageRoute(builder: (context) => const Home(enterAtividade: false)), (route) => false);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ValueListenableBuilder<EntregaNewState>(
        valueListenable: widget.store.state,
        builder: (BuildContext context, EntregaNewState value, Widget? child) {
          final atividade = value.atividade.whereStatus(widget.idStatusAtividadeEnum);

          final positonEnd = value.atividade.latLngEnd;
          List cardList = [StackCustomNew(store: widget.store), ...widget.store.state.value.atividade.fotoConclusao.map((link) => FotosLs(link: link))];

          return CustomScaffold(
            onPop: () => Navigator.pushAndRemoveUntil(context, MaterialPageRoute(builder: (context) => const Home(enterAtividade: false)), (route) => false),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Column(
                          children: [
                            SizedBox(
                              height: 200.0,
                              child: PageView.builder(
                                controller: _pageController,
                                onPageChanged: (index) {
                                  setState(() {
                                    _currentIndex = index;
                                  });
                                },
                                itemCount: cardList.length,
                                itemBuilder: (context, index) {
                                  return cardList[index];
                                },
                              ),
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children:
                                  cardList.asMap().entries.map((indicator) {
                                    return Container(
                                      width: 12,
                                      height: 12,
                                      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: _currentIndex == indicator.key ? ThemeColors.customOrange(context) : ThemeColors.customGrey(context),
                                      ),
                                    );
                                  }).toList(),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: Text(
                            atividade.enderecoFormatado.isEmpty ? 'Sem endereço' : atividade.enderecoFormatado,
                            style: GoogleFonts.roboto(fontSize: 16, fontWeight: FontWeight.w600),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 6,
                          ),
                        ),
                        const SizedBox(height: 20),
                        Visibility(
                          visible: positonEnd > 2,
                          child: const ErrorWidgetCustom(
                            errorMessage: 'Atenção\nEste pedido está sendo confirmado\ndistante do endereço de entrega',
                            typeError: 1,
                          ),
                        ),
                        const SizedBox(height: 5),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 5),
                          child: Column(
                            children: List.generate(atividade.clientes.length, (index) {
                              final cliente = atividade.clientes[index];
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Center(
                                    child: Text(
                                      cliente.statusAtividade,
                                      textAlign: TextAlign.center,
                                      style: GoogleFonts.roboto(fontSize: 16, fontWeight: FontWeight.bold, color: ThemeColors.customOrange(context)),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            Container(
                                              width: 50,
                                              height: 50,
                                              decoration: BoxDecoration(color: Colors.grey[300], borderRadius: BorderRadius.circular(50)),
                                              child: ImagePerfil(url: cliente.logo ?? '', iniciais: cliente.iniciais, fontSize: 25),
                                            ),
                                            const SizedBox(width: 5),
                                            Text(
                                              cliente.nomeCliente,
                                              style: GoogleFonts.roboto(fontSize: 15, fontWeight: FontWeight.bold, color: ThemeColors.customBlack(context)),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 8),
                                        Container(
                                          height: 3,
                                          width: double.infinity,
                                          color: ThemeColors.customGreyLight(context),
                                          child: Row(children: [Container(height: 3, width: 60, color: ThemeColors.customOrange(context))]),
                                        ),
                                        Row(
                                          children: [
                                            Row(
                                              children: [
                                                Icon(Icons.calendar_month, color: ThemeColors.customOrange(context)),
                                                const SizedBox(width: 5),
                                                Text(DateFormat('dd/MM/yyyy').format((cliente.dataTermino ?? DateTime.now()))),
                                              ],
                                            ),
                                            const SizedBox(width: 20),
                                            Row(
                                              children: [
                                                Icon(Icons.watch_later_sharp, color: ThemeColors.customOrange(context)),
                                                const SizedBox(width: 5),
                                                Text(DateFormat('HH:mm').format((cliente.dataTermino ?? DateTime.now()))),
                                              ],
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 5),
                                        Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text('Pedidos: ', style: GoogleFonts.roboto(fontSize: 16, fontWeight: FontWeight.bold)),
                                            Column(
                                              children: List.generate(cliente.volumes.length, (index) {
                                                final codRastreio = cliente.volumes[index];
                                                return Row(
                                                  children: [
                                                    Text(codRastreio.os, style: GoogleFonts.roboto(fontSize: 16, color: ThemeColors.customBlack(context))),
                                                  ],
                                                );
                                              }),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 5),
                                        if (cliente.nomeRecebedor != null)
                                          Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text('Recebedor: ', style: GoogleFonts.roboto(fontSize: 16, fontWeight: FontWeight.bold)),
                                              Text(
                                                cliente.nomeRecebedor.toString(),
                                                style: GoogleFonts.roboto(fontSize: 16, color: ThemeColors.customBlack(context)),
                                              ),
                                            ],
                                          ),
                                        const SizedBox(height: 5),
                                        if (cliente.valorRecebido != null && cliente.valorRecebido != 0)
                                          Text(
                                            'Valor a recebido: R\$ ${cliente.valorRecebido.toString()}',
                                            style: GoogleFonts.roboto(fontSize: 16, fontWeight: FontWeight.bold),
                                          ),
                                        const SizedBox(height: 5),
                                        ReentregaWidget(tags: atividade.tags),
                                      ],
                                    ),
                                  ),
                                  const Divider(),
                                ],
                              );
                            }),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Divider(color: ThemeColors.customGreyLight(context).withOpacity(.15), thickness: 1),
                const SizedBox(height: 10),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 22),
                  child: ButtonLsCustom(
                    text: 'LISTA DE PEDIDOS',
                    onPressed: () {
                      voltarListagem();
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

class FotosLs extends StatelessWidget {
  final String link;

  const FotosLs({super.key, required this.link});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: SizedBox(
        height: 200,
        width: double.infinity,
        child:
            link.isEmpty
                ? const Text('')
                : Card(
                  margin: EdgeInsets.zero,
                  child: SizedBox(
                    height: 200,
                    child: CachedNetworkImage(
                      imageUrl: link,
                      fit: BoxFit.cover,
                      width: double.infinity,
                      placeholder: (context, url) => const Center(child: LoadingLs()),
                      errorWidget: (context, url, error) => const Icon(Icons.error),
                    ),
                  ),
                ),
      ),
    );
  }
}
