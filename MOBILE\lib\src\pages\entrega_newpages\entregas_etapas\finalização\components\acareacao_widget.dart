import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:latlong2/latlong.dart';
import 'package:octalog/src/components/fcm_alert_dailog/components/fcm_porcent.dart';
import 'package:octalog/src/components/image_perfil/image_perfil.dart';
import 'package:octalog/src/components/reentrega_widget/reentrega_widget.dart';
import 'package:octalog/src/helpers/web_connector.dart';
import 'package:octalog/src/models_new/cliente_new.dart';
import 'package:octalog/src/pages/entrega_newpages/controller/entrega_new_state.dart';
import 'package:octalog/src/pages/entrega_newpages/controller/entrega_new_store.dart';
import 'package:octalog/src/pages/entrega_newpages/entregas_etapas/finaliza%C3%A7%C3%A3o/components/assinatura_widget.dart';
import 'package:octalog/src/pages/entrega_newpages/entregas_etapas/finaliza%C3%A7%C3%A3o/components/map_entrega_widget.dart';
import 'package:octalog/src/pages/home/<USER>/widget_acareacao/widget_acareacao.dart';
import 'package:octalog/src/utils/colors.dart';
import 'package:octalog/src/utils/extesion.dart';
import 'package:octalog/src/utils/theme_colors.dart';

class AcareacaoEntragaWidget extends StatefulWidget {
  final ClienteNew clienteEscolhido;
  final TextEditingController entregueParaController;
  final EntregaNewStore store;
  const AcareacaoEntragaWidget({super.key, required this.clienteEscolhido, required this.entregueParaController, required this.store});

  @override
  State<AcareacaoEntragaWidget> createState() => _AcareacaoEntragaWidgetState();
}

class _AcareacaoEntragaWidgetState extends State<AcareacaoEntragaWidget> {
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<EntregaNewState>(
      valueListenable: widget.store.state,
      builder: (_, EntregaNewState value, __) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 170,
                width: MediaQuery.of(context).size.width,
                child: MapEntregaWidget(
                  positionDaEntreg: LatLng(widget.clienteEscolhido.info?.latitudeentrega ?? 0.0, widget.clienteEscolhido.info?.longitudeentrega ?? 0.0),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 10),
                child: Row(
                  children: [
                    SizedBox(
                      width: 70,
                      height: 70,
                      child: ImagePerfil(fontSize: 30, url: widget.clienteEscolhido.logo ?? '', iniciais: widget.clienteEscolhido.iniciais),
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        widget.clienteEscolhido.nomeClienteTratado,
                        style: GoogleFonts.roboto(fontSize: 18, fontWeight: FontWeight.w500, color: ColorsCustom.customBlack),
                      ),
                    ),
                  ],
                ),
              ),
              const FcmPorcent(),
              Row(
                children: [
                   Icon(Icons.calendar_month, color: ThemeColors.customOrange(context)),
                  Text(
                    widget.clienteEscolhido.info?.dataentrega.dataPtBr ?? '',
                    style: GoogleFonts.roboto(fontSize: 16, fontWeight: FontWeight.w500, color: ColorsCustom.customBlack),
                  ),
                  const SizedBox(width: 20),
                   Icon(Icons.access_time_filled, color: ThemeColors.customOrange(context)),
                  Text(
                    widget.clienteEscolhido.info?.dataentrega.dataFormatada ?? '',
                    style: GoogleFonts.roboto(fontSize: 16, fontWeight: FontWeight.w500, color: ColorsCustom.customBlack),
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 10),
                child: RichText(
                  textAlign: TextAlign.start,
                  text: TextSpan(
                    text: 'Recebedor: ',
                    style: GoogleFonts.roboto(fontSize: 16, fontWeight: FontWeight.w800, color: ColorsCustom.customBlack),
                    children: [
                      TextSpan(
                        text: widget.clienteEscolhido.nomeRecebedor,
                        style: GoogleFonts.roboto(fontSize: 16, fontWeight: FontWeight.w500, color: ColorsCustom.customBlack),
                      ),
                    ],
                  ),
                ),
              ),
              ReentregaWidget(scale: 0.8, tags: widget.clienteEscolhido.tags),
              Visibility(
                visible: widget.clienteEscolhido.acareacao,
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 5),
                  child: GestureDetector(
                    onTap: () async {
                      Navigator.push(context, MaterialPageRoute(builder: (context) => AcareacaoWidget(cliente: widget.clienteEscolhido)));
                      // return asuka.Asuka.showDialog(
                      //   builder: (ctx) => AcareacaoWidget(
                      //     cliente: cliente,
                      //   ),
                      // );
                    },
                    child:  Text(
                      'Informações da acareação',
                      style: TextStyle(color: ThemeColors.customOrange(context), fontSize: 15, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ),
              const Text(
                "Nome do recebedor que efetuou a acareação:",
                style: TextStyle(color: ColorsCustom.customBlack, fontSize: 16, fontWeight: FontWeight.w500),
              ),
              TextField(
                controller: widget.entregueParaController,
                onChanged: widget.store.setEntreguePara,
                decoration: const InputDecoration(isDense: true, border: OutlineInputBorder(), suffixText: '(Nome)'),
              ),
              const SizedBox(height: 10),
              GestureDetector(
                onTap: () async {
                  widget.store.seterroAcareacaoRealizadaSucesso(false);
                  widget.store.acareacaoRealizadaSucesso();
                },
                child: Row(
                  children: [
                    Icon(
                      value.acareacaoRealizadaSucesso ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                      color:
                          value.erroAcareacaoRealizadaSucesso
                              ? Colors.red
                              : value.acareacaoRealizadaSucesso
                              ? ColorsCustom.customGreen
                              : ColorsCustom.customGreyLight,
                      size:
                          value.erroAcareacaoRealizadaSucesso
                              ? 25
                              : value.acareacaoRealizadaSucesso
                              ? 22
                              : 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Acareação Realizada - Pedido Entregue',
                      style: GoogleFonts.roboto(
                        fontSize: 16,
                        color:
                            value.erroAcareacaoRealizadaSucesso
                                ? ColorsCustom.customRed
                                : value.acareacaoRealizadaSucesso
                                ? ColorsCustom.customBlack
                                : ColorsCustom.customBlack,
                        fontWeight: value.acareacaoRealizadaSucesso ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 10),
              Row(
                children: [
                  GestureDetector(
                    onTap: () async {
                      final foto = await WebConnector().tirarFoto(context);
                      if (foto == null) return;
                      await widget.store.uploaFotoAcareacao(foto);
                    },
                    child: Row(
                      children: [
                        CircleAvatar(
                          radius: 20,
                          backgroundColor: Colors.grey[200],
                          child: Icon(Icons.camera_alt_rounded, color: value.bytesAcareacao != null ? ThemeColors.customOrange(context) : Colors.grey),
                        ),
                        const SizedBox(width: 5),
                        const Text("TIRE UMA FOTO", style: TextStyle(color: Colors.grey, fontSize: 12, fontWeight: FontWeight.w600)),
                      ],
                    ),
                  ),
                  const Spacer(),
                  Visibility(
                    visible: widget.clienteEscolhido.assinaturaObrigatoria ?? false,
                    child: GestureDetector(
                      onTap: () async {
                        if (value.entreguePara!.length < 6) {
                          return showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return AlertDialog(
                                title: const Text('Atenção'),
                                content: const Text('Informe o nome do recebedor'),
                                actions: [
                                  TextButton(
                                    onPressed: () {
                                      Navigator.pop(context);
                                    },
                                    child:  Text('OK', style: TextStyle(fontWeight: FontWeight.bold, color: ThemeColors.customOrange(context))),
                                  ),
                                ],
                              );
                            },
                          );
                        }

                        final bool? assinatura = await showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return AssinaturaRecebimentoPage(
                              os: widget.clienteEscolhido.volumes.first.os,
                              cliente: widget.clienteEscolhido.nomeClienteTratado,
                              endereco: widget.clienteEscolhido.complemento,
                              nomeRecebedor: widget.entregueParaController.text,
                              store: widget.store,
                            );
                          },
                        );
                        if (assinatura != null && assinatura) {
                          widget.store.setAssinaturaPreenchida(true);
                        }
                      },
                      child: Row(
                        children: [
                          CircleAvatar(
                            radius: 20,
                            backgroundColor: Colors.grey[200],
                            child: Icon(Icons.assignment_rounded, color: value.assinaturaPreenchida ? ThemeColors.customOrange(context) : Colors.grey),
                          ),
                          const SizedBox(width: 5),
                          const Text("ASSINATURA DO CLIENTE", style: TextStyle(color: Colors.grey, fontSize: 12, fontWeight: FontWeight.w600)),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
