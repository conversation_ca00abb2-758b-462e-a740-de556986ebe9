import 'package:flutter/cupertino.dart';
import 'package:hive/hive.dart';
import 'package:octalog/src/pages/search/search_state.dart';

class SearchStore {
  ValueNotifier<SearchState> state = ValueNotifier(
    SearchState(
      search: '',
      searchHistory: [],
    ),
  );

  Future<void> init() async {
    final box = await Hive.openBox<String>('search');
    state.value = state.value.copyWith(
      searchHistory: box.values.toList(),
    );
  }

  Future<void> clearHistory() async {
    final box = await Hive.openBox<String>('search');
    await box.clear();
    state.value = state.value.copyWith(
      searchHistory: [],
    );
  }

  Future<void> setSearch(
    String value, {
    bool addToHistory = false,
  }) async {
    state.value = state.value.copyWith(
      search: value,
    );
    if (!addToHistory) return;
    bool contain = false;
    for (var item in state.value.searchHistory) {
      if (item.toLowerCase().trim() == value.toLowerCase().trim()) {
        contain = true;
      }
    }
    if (!contain) {
      final box = await Hive.openBox<String>('search');
      await box.add(value);
      state.value = state.value.copyWith(
        searchHistory: box.values.toList(),
      );
    }
  }
}
