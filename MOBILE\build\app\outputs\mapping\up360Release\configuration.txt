# The proguard configuration file for the following section is C:\projetos\octa.log\MOBILE\build\app\intermediates\aapt_proguard_file\up360Release\processUp360ReleaseResources\aapt_rules.txt
-keep class android.app.Application { <init>(); }
-keep class androidx.camera.core.impl.MetadataHolderService { <init>(); }
-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.profileinstaller.ProfileInstallReceiver { <init>(); }
-keep class androidx.room.MultiInstanceInvalidationService { <init>(); }
-keep class androidx.startup.InitializationProvider { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.RescheduleReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.SystemAlarmService { <init>(); }
-keep class androidx.work.impl.background.systemjob.SystemJobService { <init>(); }
-keep class androidx.work.impl.diagnostics.DiagnosticsReceiver { <init>(); }
-keep class androidx.work.impl.foreground.SystemForegroundService { <init>(); }
-keep class androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver { <init>(); }
-keep class com.baseflow.geolocator.GeolocatorLocationService { <init>(); }
-keep class com.crazecoder.openfile.FileProvider { <init>(); }
-keep class com.google.android.datatransport.runtime.backends.TransportBackendDiscovery { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService { <init>(); }
-keep class com.google.android.gms.common.api.GoogleApiActivity { <init>(); }
-keep class com.google.android.gms.metadata.ModuleDependencies { <init>(); }
-keep class com.google.android.play.core.common.PlayCoreDialogWrapperActivity { <init>(); }
-keep class com.google.firebase.components.ComponentDiscoveryService { <init>(); }
-keep class com.google.firebase.iid.FirebaseInstanceIdReceiver { <init>(); }
-keep class com.google.firebase.messaging.FirebaseMessagingService { <init>(); }
-keep class com.google.firebase.provider.FirebaseInitProvider { <init>(); }
-keep class com.google.mlkit.common.internal.MlKitComponentDiscoveryService { <init>(); }
-keep class com.google.mlkit.common.internal.MlKitInitProvider { <init>(); }
-keep class com.octalog.MainActivity { <init>(); }
-keep class com.onesignal.NotificationOpenedActivityHMS { <init>(); }
-keep class com.onesignal.core.activities.PermissionsActivity { <init>(); }
-keep class com.onesignal.core.services.SyncJobService { <init>(); }
-keep class com.onesignal.notifications.activities.NotificationOpenedActivity { <init>(); }
-keep class com.onesignal.notifications.activities.NotificationOpenedActivityAndroid22AndOlder { <init>(); }
-keep class com.onesignal.notifications.receivers.BootUpReceiver { <init>(); }
-keep class com.onesignal.notifications.receivers.FCMBroadcastReceiver { <init>(); }
-keep class com.onesignal.notifications.receivers.NotificationDismissReceiver { <init>(); }
-keep class com.onesignal.notifications.receivers.UpgradeReceiver { <init>(); }
-keep class com.onesignal.notifications.services.HmsMessageServiceOneSignal { <init>(); }
-keep class com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentReceiver { <init>(); }
-keep class com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService { <init>(); }
-keep class io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService { <init>(); }
-keep class io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider { <init>(); }
-keep class io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver { <init>(); }
-keep class io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService { <init>(); }
-keep class io.flutter.plugins.imagepicker.ImagePickerFileProvider { <init>(); }
-keep class io.flutter.plugins.urllauncher.WebViewActivity { <init>(); }
-keep class android.widget.Space { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.app.AlertController$RecycleListView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ActionMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ExpandedMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ListMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContainer { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContextView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarOverlayLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActivityChooserView$InnerLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.AlertDialogLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ButtonBarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ContentFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.DialogTitle { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsLinearLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.SearchView$SearchAutoComplete { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.SwitchCompat { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.Toolbar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ViewStubCompat { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.browser.browseractions.BrowserActionsFallbackMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.core.widget.NestedScrollView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.preference.UnPressableLinearLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.preference.internal.PreferenceImageView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.recyclerview.widget.RecyclerView { <init>(android.content.Context, android.util.AttributeSet); }


# End of content from C:\projetos\octa.log\MOBILE\build\app\intermediates\aapt_proguard_file\up360Release\processUp360ReleaseResources\aapt_rules.txt
# The proguard configuration file for the following section is C:\projetos\octa.log\MOBILE\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.0
# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html
#
# Starting with version 2.2 of the Android plugin for Gradle, this file is distributed together with
# the plugin and unpacked at build-time. The files in $ANDROID_HOME are no longer maintained and
# will be ignored by new version of the Android plugin for Gradle.

# Optimizations: If you don't want to optimize, use the proguard-android.txt configuration file
# instead of this one, which turns off the optimization flags.
-allowaccessmodification

# Preserve some attributes that may be required for reflection.
-keepattributes AnnotationDefault,
                EnclosingMethod,
                InnerClasses,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations,
                Signature

-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService
-keep public class com.google.android.vending.licensing.ILicensingService
-dontnote com.android.vending.licensing.ILicensingService
-dontnote com.google.vending.licensing.ILicensingService
-dontnote com.google.android.vending.licensing.ILicensingService

# For native methods, see https://www.guardsquare.com/manual/configuration/examples#native
-keepclasseswithmembernames,includedescriptorclasses class * {
    native <methods>;
}

# Keep setters in Views so that animations can still work.
-keepclassmembers public class * extends android.view.View {
    void set*(***);
    *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick.
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}

# For enumeration classes, see https://www.guardsquare.com/manual/configuration/examples#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

# Preserve annotated Javascript interface methods.
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# The support libraries contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version. We know about them, and they are safe.
-dontnote android.support.**
-dontnote androidx.**
-dontwarn android.support.**
-dontwarn androidx.**

# Understand the @Keep support annotation.
-keep class android.support.annotation.Keep

-keep @android.support.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <init>(...);
}

# These classes are duplicated between android.jar and org.apache.http.legacy.jar.
-dontnote org.apache.http.**
-dontnote android.net.http.**

# These classes are duplicated between android.jar and core-lambda-stubs.jar.
-dontnote java.lang.invoke.**

# End of content from C:\projetos\octa.log\MOBILE\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.0
# The proguard configuration file for the following section is C:\src\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro
# Build the ephemeral app in a module project.
# Prevents: Warning: library class <plugin-package> depends on program class io.flutter.plugin.**
# This is due to plugins (libraries) depending on the embedding (the program jar)
-dontwarn io.flutter.plugin.**

# The android.** package is provided by the OS at runtime.
-dontwarn android.**

# In some cases, R8 is incorrectly stripping plugin classes. Keep
# all implementations of FlutterPlugin until we can determine
# why this is the case.
# See https://github.com/flutter/flutter/issues/154580.
-if class * implements io.flutter.embedding.engine.plugins.FlutterPlugin
-keep,allowshrinking,allowobfuscation class <1>

# End of content from C:\src\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro
# The proguard configuration file for the following section is C:\projetos\octa.log\MOBILE\build\mobile_scanner\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
-keep class com.google.mlkit.* { *; }
-keep class com.google.android.libraries.barhopper.** { *; }
-keep class com.google.photos.* { *; }

-keepclassmembers class * extends java.lang.Enum {
    <fields>;
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# End of content from C:\projetos\octa.log\MOBILE\build\mobile_scanner\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
# The proguard configuration file for the following section is C:\projetos\octa.log\MOBILE\build\file_picker\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
-keep class androidx.lifecycle.DefaultLifecycleObserver
# End of content from C:\projetos\octa.log\MOBILE\build\file_picker\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
# The proguard configuration file for the following section is C:\projetos\octa.log\MOBILE\build\flutter_pdfview\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
-keep class com.shockwave.**
# End of content from C:\projetos\octa.log\MOBILE\build\flutter_pdfview\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
# The proguard configuration file for the following section is C:\projetos\octa.log\MOBILE\build\flutter_plugin_android_lifecycle\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
# The point of this package is to specify that a dependent plugin intends to
# use the AndroidX lifecycle classes. Make sure no R8 heuristics shrink classes
# brought in by the embedding's pom.
#
# This isn't strictly needed since by definition, plugins using Android
# lifecycles should implement DefaultLifecycleObserver and therefore keep it
# from being shrunk. But there seems to be an R8 bug so this needs to stay
# https://issuetracker.google.com/issues/142778206.
-keep class androidx.lifecycle.DefaultLifecycleObserver

# End of content from C:\projetos\octa.log\MOBILE\build\flutter_plugin_android_lifecycle\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
# The proguard configuration file for the following section is C:\projetos\octa.log\MOBILE\build\flutter_webrtc\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
# Flutter WebRTC
-keep class com.cloudwebrtc.webrtc.** { *; }
-keep class org.webrtc.** { *; }

# End of content from C:\projetos\octa.log\MOBILE\build\flutter_webrtc\intermediates\consumer_proguard_dir\release\exportReleaseConsumerProguardFiles\lib0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41ce894b414f4e410f366a4a6ad7589\transformed\jetified-camera-core-1.3.4\proguard.txt
# Copyright (C) 2023 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keeps the quirk classes to prevent them from being merged incorrectly. For more information,
# please see b/278818703.
-keep,allowobfuscation,allowshrinking class ** implements androidx.camera.core.impl.Quirk

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41ce894b414f4e410f366a4a6ad7589\transformed\jetified-camera-core-1.3.4\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a600f391fc985fb6fe77353ae0360c6c\transformed\jetified-camera-camera2-1.3.4\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# This is necessary for default initialization using Camera2Config
-keep public class androidx.camera.camera2.Camera2Config$DefaultProvider { *; }

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a600f391fc985fb6fe77353ae0360c6c\transformed\jetified-camera-camera2-1.3.4\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6fec79dfcef552889e3835cc7bab572a\transformed\preference-1.2.1\proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Preference objects are inflated via reflection
-keep public class androidx.preference.Preference {
    public <init>(android.content.Context, android.util.AttributeSet);
}
-keep public class * extends androidx.preference.Preference {
    public <init>(android.content.Context, android.util.AttributeSet);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6fec79dfcef552889e3835cc7bab572a\transformed\preference-1.2.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d17889e797a41f531d22754be8eceb74\transformed\jetified-barcode-scanning-17.3.0\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.mlkit_vision_barcode_bundled.zzeh {
  <fields>;
}

# This prevents the names of native methods from being obfuscated and prevents
# UnsatisfiedLinkErrors.
-keepclasseswithmembernames class * {
    native <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d17889e797a41f531d22754be8eceb74\transformed\jetified-barcode-scanning-17.3.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137683c38d0641808b56059b5fe37a10\transformed\jetified-face-detection-16.1.7\proguard.txt
# This prevents the names of native methods from being obfuscated and prevents
# UnsatisfiedLinkErrors.
-keepclasseswithmembernames class * {
    native <methods>;
}

# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.mlkit_vision_face_bundled.zzuw {
  <fields>;
}

# Uses reflection to determine if these classes are present and has a graceful
# fallback if they aren't.
-dontwarn dalvik.system.VMStack

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\137683c38d0641808b56059b5fe37a10\transformed\jetified-face-detection-16.1.7\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\proguard.txt
# Annotations are implemented as attributes, so we have to explicitly keep them.
# Catch all which encompasses attributes like RuntimeVisibleParameterAnnotations
# and RuntimeVisibleTypeAnnotations
-keepattributes RuntimeVisible*Annotation*

# JNI is an entry point that's hard to keep track of, so there's
# an annotation to mark fields and methods used by native code.

# Keep the annotations that proguard needs to process.
-keep class com.google.android.apps.common.proguard.UsedBy*

# Just because native code accesses members of a class, does not mean that the
# class itself needs to be annotated - only annotate classes that are
# referenced themselves in native code.
-keep @com.google.android.apps.common.proguard.UsedBy* class * {
  <init>();
}
-keepclassmembers class * {
    @com.google.android.apps.common.proguard.UsedBy* *;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22c70eb4700b3262896da0db5fa32c7\transformed\jetified-in-app-messages-5.1.29\proguard.txt
-dontwarn com.onesignal.iam.**

-keepclassmembers class com.onesignal.inAppMessages.** { *; }
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22c70eb4700b3262896da0db5fa32c7\transformed\jetified-in-app-messages-5.1.29\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\proguard.txt
-dontwarn com.onesignal.notification.**

# These 2 methods are called with reflection.
-keep class com.google.android.gms.common.api.GoogleApiClient {
    void connect();
    void disconnect();
}

# Need to keep as these 2 methods are called with reflection from com.onesignal.PushRegistratorFCM
-keep class com.google.firebase.iid.FirebaseInstanceId {
    static com.google.firebase.iid.FirebaseInstanceId getInstance(com.google.firebase.FirebaseApp);
    java.lang.String getToken(java.lang.String, java.lang.String);
}

-keep class ** implements com.onesignal.notifications.IPermissionObserver{
    void onNotificationPermissionChange(java.lang.Boolean);
}

-keep class ** implements com.onesignal.user.subscriptions.IPushSubscriptionObserver {
    void onPushSubscriptionChange(com.onesignal.user.subscriptions.PushSubscriptionChangedState);
}

-keep class ** implements com.onesignal.user.state.IUserStateObserver {
    void onUserStateChange(com.onesignal.user.state.UserChangedState);
}

-keep class ** implements com.onesignal.notifications.INotificationServiceExtension{
   void onNotificationReceived(com.onesignal.notifications.INotificationReceivedEvent);
}

-keep class com.onesignal.notifications.internal.badges.impl.shortcutbadger.impl.AdwHomeBadger { <init>(...); }
-keep class com.onesignal.notifications.internal.badges.impl.shortcutbadger.impl.ApexHomeBadger { <init>(...); }
-keep class com.onesignal.notifications.internal.badges.impl.shortcutbadger.impl.AsusHomeBadger { <init>(...); }
-keep class com.onesignal.notifications.internal.badges.impl.shortcutbadger.impl.DefaultBadger { <init>(...); }
-keep class com.onesignal.notifications.internal.badges.impl.shortcutbadger.impl.EverythingMeHomeBadger { <init>(...); }
-keep class com.onesignal.notifications.internal.badges.impl.shortcutbadger.impl.HuaweiHomeBadger { <init>(...); }
-keep class com.onesignal.notifications.internal.badges.impl.shortcutbadger.impl.LGHomeBadger { <init>(...); }
-keep class com.onesignal.notifications.internal.badges.impl.shortcutbadger.impl.NewHtcHomeBadger { <init>(...); }
-keep class com.onesignal.notifications.internal.badges.impl.shortcutbadger.impl.NovaHomeBadger { <init>(...); }
-keep class com.onesignal.notifications.internal.badges.impl.shortcutbadger.impl.OPPOHomeBader { <init>(...); }
-keep class com.onesignal.notifications.internal.badges.impl.shortcutbadger.impl.SamsungHomeBadger { <init>(...); }
-keep class com.onesignal.notifications.internal.badges.impl.shortcutbadger.impl.SonyHomeBadger { <init>(...); }
-keep class com.onesignal.notifications.internal.badges.impl.shortcutbadger.impl.VivoHomeBadger { <init>(...); }
-keep class com.onesignal.notifications.internal.badges.impl.shortcutbadger.impl.XiaomiHomeBadger { <init>(...); }
-keep class com.onesignal.notifications.internal.badges.impl.shortcutbadger.impl.ZukHomeBadger { <init>(...); }

-dontwarn com.huawei.**
-dontwarn com.amazon.**

# Proguard ends up removing this class even if it is used in AndroidManifest.xml so force keeping it.
-keep public class com.onesignal.notifications.services.ADMMessageHandler {*;}

-keep public class com.onesignal.notifications.services.ADMMessageHandlerJob {*;}

-keep class com.onesignal.JobIntentService$* {*;}

-keepclassmembers class com.onesignal.notifications.** { *; }
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52f643eeb6fba9363586e00d8d04b656\transformed\jetified-location-5.1.29\proguard.txt
-dontwarn com.onesignal.location.**

-keepclassmembers class com.onesignal.location.** { *; }
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52f643eeb6fba9363586e00d8d04b656\transformed\jetified-location-5.1.29\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\proguard.txt
-dontwarn com.onesignal.**

-dontwarn com.amazon.**

-keepclassmembers class com.onesignal.core.** { *; }

-keepclassmembers class com.onesignal.session.** { *; }

-keepclassmembers class com.onesignal.user.** { *; }

-keepclassmembers class com.onesignal.internal.** { *; }

-keepclassmembers class com.onesignal.debug.** { *; }

-keepclassmembers class com.onesignal.common.** { *; }

-keepclassmembers @com.onesignal.core.internal.minification.KeepStub class * { <init>(...); }

-keep class ** implements com.onesignal.common.modules.IModule { *; }

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdc1fd5a4f8cb18094ee01b408d70e6e\transformed\appcompat-1.6.1\proguard.txt
# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# aapt is not able to read app::actionViewClass and app:actionProviderClass to produce proguard
# keep rules. Add a commonly used SearchView to the keep list until b/109831488 is resolved.
-keep class androidx.appcompat.widget.SearchView { <init>(...); }

# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl* {
  <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdc1fd5a4f8cb18094ee01b408d70e6e\transformed\appcompat-1.6.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07660f349a2fe95c07a6bc7026689012\transformed\jetified-play-services-base-18.5.0\proguard.txt
# b/35135904 Ensure that proguard will not strip the mResultGuardian.
-keepclassmembers class com.google.android.gms.common.api.internal.BasePendingResult {
  com.google.android.gms.common.api.internal.BasePendingResult$ReleasableResultGuardian mResultGuardian;
}



# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07660f349a2fe95c07a6bc7026689012\transformed\jetified-play-services-base-18.5.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\proguard.txt
-dontwarn com.google.firebase.platforminfo.KotlinDetector
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31710a1d11dfb3018b4d584b1dcc524f\transformed\recyclerview-1.0.0\proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# When layoutManager xml attribute is used, RecyclerView inflates
#LayoutManagers' constructors using reflection.
-keep public class * extends androidx.recyclerview.widget.RecyclerView$LayoutManager {
    public <init>(android.content.Context, android.util.AttributeSet, int, int);
    public <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31710a1d11dfb3018b4d584b1dcc524f\transformed\recyclerview-1.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# A rule that will keep classes that implement SidecarInterface$SidecarCallback if Sidecar seems
# be used. See b/157286362 and b/165268619 for details.
# TODO(b/208543178) investigate how to pass header jar to R8 so we don't need this rule
-if class androidx.window.layout.adapter.sidecar.SidecarCompat {
  public *** setExtensionCallback(androidx.window.layout.adapter.sidecar.ExtensionInterfaceCompat$ExtensionCallbackInterface);
}
-keep class androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback,
 androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback {
  public *** onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState);
  public *** onWindowLayoutChanged(android.os.IBinder, androidx.window.sidecar.SidecarWindowLayoutInfo);
}
# Required for window area API reflection guard
-keep interface androidx.window.area.reflectionguard.* {*;}
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e879718b482afd13bd9ed85634ab258\transformed\rules\lib\META-INF\proguard\androidx.datastore_datastore-preferences-core.pro
-keepclassmembers class * extends androidx.datastore.preferences.protobuf.GeneratedMessageLite {
    <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e879718b482afd13bd9ed85634ab258\transformed\rules\lib\META-INF\proguard\androidx.datastore_datastore-preferences-core.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\proguard.txt
-keep class * extends androidx.work.Worker
-keep class * extends androidx.work.InputMerger
# Keep all constructors on ListenableWorker, Worker
-keep public class * extends androidx.work.ListenableWorker {
    public <init>(...);
}
# We need to keep WorkerParameters for the ListenableWorker constructor
-keep class androidx.work.WorkerParameters

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23adfe722c8911b0c72ccd7b6c20c672\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>(androidx.lifecycle.SavedStateHandle);
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application,androidx.lifecycle.SavedStateHandle);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23adfe722c8911b0c72ccd7b6c20c672\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df03071d3ea5a13ca8a701a85da690b\transformed\lifecycle-viewmodel-2.7.0\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>();
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df03071d3ea5a13ca8a701a85da690b\transformed\lifecycle-viewmodel-2.7.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\proguard.txt
# this rule is need to work properly when app is compiled with api 28, see b/142778206
-keepclassmembers class * extends androidx.lifecycle.EmptyActivityLifecycleCallbacks { *; }
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d9ae9a3ba8bfb771c0b1d6be965a5e0\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# When editing this file, update the following files as well:
# - META-INF/proguard/coroutines.pro
# - META-INF/com.android.tools/proguard/coroutines.pro

# Most of volatile fields are updated with AFU and should not be mangled
-keepclassmembers class kotlinx.coroutines.** {
    volatile <fields>;
}

# Same story for the standard library's SafeContinuation that also uses AtomicReferenceFieldUpdater
-keepclassmembers class kotlin.coroutines.SafeContinuation {
    volatile <fields>;
}

# These classes are only required by kotlinx.coroutines.debug.AgentPremain, which is only loaded when
# kotlinx-coroutines-core is used as a Java agent, so these are not needed in contexts where ProGuard is used.
-dontwarn java.lang.instrument.ClassFileTransformer
-dontwarn sun.misc.SignalHandler
-dontwarn java.lang.instrument.Instrumentation
-dontwarn sun.misc.Signal

# Only used in `kotlinx.coroutines.internal.ExceptionsConstructor`.
# The case when it is not available is hidden in a `try`-`catch`, as well as a check for Android.
-dontwarn java.lang.ClassValue

# An annotation used for build tooling, won't be directly accessed.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d9ae9a3ba8bfb771c0b1d6be965a5e0\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e31e08ffbeca131f12df15180c67eea\transformed\jetified-play-services-tasks-18.2.0\proguard.txt


# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e31e08ffbeca131f12df15180c67eea\transformed\jetified-play-services-tasks-18.2.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf636ef2f0738047fe8129f320ef339e\transformed\jetified-play-services-basement-18.4.0\proguard.txt
# Needed when building against pre-Marshmallow SDK.
-dontwarn android.security.NetworkSecurityPolicy

# Needed when building against Marshmallow SDK.
-dontwarn android.app.Notification

# Protobuf has references not on the Android boot classpath
-dontwarn sun.misc.Unsafe
-dontwarn libcore.io.Memory

# Annotations used during internal SDK shrinking.
-dontwarn com.google.android.apps.common.proguard.UsedBy*
-dontwarn com.google.android.apps.common.proguard.SideEffectFree

# Annotations referenced by the SDK but whose definitions are contained in
# non-required dependencies.
-dontwarn javax.annotation.**
-dontwarn org.checkerframework.**
-dontwarn com.google.errorprone.annotations.**
-dontwarn org.jspecify.nullness.NullMarked

# Annotations no longer exist. Suppression prevents ProGuard failures in
# SDKs which depend on earlier versions of play-services-basement.
-dontwarn com.google.android.gms.common.util.VisibleForTesting

# Proguard flags for consumers of the Google Play services SDK
# https://developers.google.com/android/guides/setup#add_google_play_services_to_your_project

# Keep SafeParcelable NULL value, needed for reflection by DowngradeableSafeParcel
-keepclassmembers public class com.google.android.gms.common.internal.safeparcel.SafeParcelable {
    public static final *** NULL;
}

# Needed for Parcelable/SafeParcelable classes & their creators to not get renamed, as they are
# found via reflection.
-keep class com.google.android.gms.common.internal.ReflectedParcelable
-keepnames class * implements com.google.android.gms.common.internal.ReflectedParcelable
-keepclassmembers class * implements android.os.Parcelable {
  public static final *** CREATOR;
}

# Keep the classes/members we need for client functionality.
-keep @interface android.support.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep androidX equivalent of above android.support to allow Jetification.
-keep @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep the names of classes/members we need for client functionality.
-keep @interface com.google.android.gms.common.annotation.KeepName
-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
  @com.google.android.gms.common.annotation.KeepName *;
}

# Keep Dynamite API entry points
-keep @interface com.google.android.gms.common.util.DynamiteApi
-keep @com.google.android.gms.common.util.DynamiteApi public class * {
  public <fields>;
  public <methods>;
}



# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf636ef2f0738047fe8129f320ef339e\transformed\jetified-play-services-basement-18.4.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\504a3b9bc759ca6028567aaea36c5498\transformed\fragment-1.7.1\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The default FragmentFactory creates Fragment instances using reflection
-if public class ** extends androidx.fragment.app.Fragment
-keepclasseswithmembers,allowobfuscation public class <1> {
    public <init>();
}

# FragmentTransition will reflectively lookup:
# androidx.transition.FragmentTransitionSupport
# We should ensure that we keep the constructor if the code using this is alive
-if class androidx.fragment.app.FragmentTransition {
   private static androidx.fragment.app.FragmentTransitionImpl resolveSupportImpl();
}
-keep class androidx.transition.FragmentTransitionSupport {
    public <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\504a3b9bc759ca6028567aaea36c5498\transformed\fragment-1.7.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80812f0e193ab0497f129d7a30001e85\transformed\webkit-1.12.1\proguard.txt
# Copyright 2018 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# We need to avoid obfuscating the support library boundary interface because
# this API is shared with the Android Support Library.
# Note that we only 'keep' the package org.chromium.support_lib_boundary itself,
# any sub-packages of that package can still be obfuscated.
-keep public class org.chromium.support_lib_boundary.* { public *; }

# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Prevent WebViewClientCompat from being renamed, since chromium depends on this name.
-keepnames public class androidx.webkit.WebViewClientCompat

# Prevent ProcessGlobalConfig and member sProcessGlobalConfig from being renamed, since chromium
# depends on this name.
-keepnames public class androidx.webkit.ProcessGlobalConfig {
    private static final *** sProcessGlobalConfig;
}
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80812f0e193ab0497f129d7a30001e85\transformed\webkit-1.12.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c91915f15604457da9254c01d4a29364\transformed\vectordrawable-animated-1.1.0\proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# keep setters in VectorDrawables so that animations can still work.
-keepclassmembers class androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$* {
   void set*(***);
   *** get*();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c91915f15604457da9254c01d4a29364\transformed\vectordrawable-animated-1.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\765cbe31314e92111ddb6ec950f59a6b\transformed\coordinatorlayout-1.0.0\proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# CoordinatorLayout resolves the behaviors of its child components with reflection.
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>();
}

# Make sure we keep annotations for CoordinatorLayout's DefaultBehavior and ViewPager's DecorView
-keepattributes *Annotation*

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\765cbe31314e92111ddb6ec950f59a6b\transformed\coordinatorlayout-1.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c90255c19f9c683385e871d79ee1ace\transformed\transition-1.4.1\proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep a field in transition that is used to keep a reference to weakly-referenced object
-keepclassmembers class androidx.transition.ChangeBounds$* extends android.animation.AnimatorListenerAdapter {
  androidx.transition.ChangeBounds$ViewBounds mViewBounds;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c90255c19f9c683385e871d79ee1ace\transformed\transition-1.4.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\proguard.txt
# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.ViewCompat$Api* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.WindowInsetsCompat$*Impl* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.app.NotificationCompat$*$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.os.UserHandleCompat$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.widget.EdgeEffectCompat$Api*Impl {
  <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07d5ffcb9c6912ffd610dc84a8004380\transformed\lifecycle-runtime-2.7.0\proguard.txt
-keepattributes AnnotationDefault,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations

-keepclassmembers enum androidx.lifecycle.Lifecycle$Event {
    <fields>;
}

-keep class * implements androidx.lifecycle.GeneratedAdapter {
    <init>(...);
}

-keepclassmembers class ** {
    @androidx.lifecycle.OnLifecycleEvent *;
}

# this rule is need to work properly when app is compiled with api 28, see b/142778206
# Also this rule prevents registerIn from being inlined.
-keepclassmembers class androidx.lifecycle.ReportFragment$LifecycleCallbacks { *; }
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07d5ffcb9c6912ffd610dc84a8004380\transformed\lifecycle-runtime-2.7.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f37faca982b225960866422b14b0e48\transformed\jetified-savedstate-1.2.1\proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

-keepclassmembers,allowobfuscation class * implements androidx.savedstate.SavedStateRegistry$AutoRecreated {
    <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f37faca982b225960866422b14b0e48\transformed\jetified-savedstate-1.2.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9022025cb47adfd78d8b0efabb6159af\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# Allow R8 to optimize away the FastServiceLoader.
# Together with ServiceLoader optimization in R8
# this results in direct instantiation when loading Dispatchers.Main
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatcherLoader {
    boolean FAST_SERVICE_LOADER_ENABLED return false;
}

-assumenosideeffects class kotlinx.coroutines.internal.FastServiceLoaderKt {
    boolean ANDROID_DETECTED return true;
}

# Disable support for "Missing Main Dispatcher", since we always have Android main dispatcher
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatchersKt {
    boolean SUPPORT_MISSING return false;
}

# Statically turn off all debugging facilities and assertions
-assumenosideeffects class kotlinx.coroutines.DebugKt {
    boolean getASSERTIONS_ENABLED() return false;
    boolean getDEBUG() return false;
    boolean getRECOVER_STACK_TRACES() return false;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9022025cb47adfd78d8b0efabb6159af\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\160f0e0b2f19cfe5268e794069515506\transformed\jetified-audioswitch-89582c47c9a04c62f90aa5e57251af4800a62c9a\proguard.txt

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\160f0e0b2f19cfe5268e794069515506\transformed\jetified-audioswitch-89582c47c9a04c62f90aa5e57251af4800a62c9a\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4042ec9e8cc7d9cdf61197d11f01375\transformed\jetified-startup-runtime-1.1.1\proguard.txt
# It's important that we preserve initializer names, given they are used in the AndroidManifest.xml.
-keepnames class * extends androidx.startup.Initializer

# These Proguard rules ensures that ComponentInitializers are are neither shrunk nor obfuscated,
# and are a part of the primary dex file. This is because they are discovered and instantiated
# during application startup.
-keep class * extends androidx.startup.Initializer {
    # Keep the public no-argument constructor while allowing other methods to be optimized.
    <init>();
}

-assumenosideeffects class androidx.startup.StartupLogger { public static <methods>; }

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4042ec9e8cc7d9cdf61197d11f01375\transformed\jetified-startup-runtime-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\393754ebd3afdfde0b3ec42e2ddd4b15\transformed\versionedparcelable-1.1.1\proguard.txt
-keep class * implements androidx.versionedparcelable.VersionedParcelable
-keep public class android.support.**Parcelizer { *; }
-keep public class androidx.**Parcelizer { *; }
-keep public class androidx.versionedparcelable.ParcelImpl

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\393754ebd3afdfde0b3ec42e2ddd4b15\transformed\versionedparcelable-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca09e878cbb074545c6b4079b0555c5\transformed\room-runtime-2.5.0\proguard.txt
-keep class * extends androidx.room.RoomDatabase
-dontwarn androidx.room.paging.**

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca09e878cbb074545c6b4079b0555c5\transformed\room-runtime-2.5.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfaf4301d7f10a0abf4fefc082e3f899\transformed\jetified-firebase-components-18.0.0\proguard.txt
-dontwarn com.google.firebase.components.Component$Instantiation
-dontwarn com.google.firebase.components.Component$ComponentType

-keep class * implements com.google.firebase.components.ComponentRegistrar
-keep,allowshrinking interface com.google.firebase.components.ComponentRegistrar

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfaf4301d7f10a0abf4fefc082e3f899\transformed\jetified-firebase-components-18.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d4d3739c32fe5c3f2b5757ded9e26f4\transformed\jetified-transport-api-3.1.0\proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d4d3739c32fe5c3f2b5757ded9e26f4\transformed\jetified-transport-api-3.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5231da36518c5740ff6ec8f636c4adaa\transformed\jetified-core-1.0.0\proguard.txt
# Copyright (C) 2022 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# These interfaces must be kept for the client library to invoke methods in extensions.
-keep interface androidx.window.extensions.core.util.function.Consumer {
  public void accept(***);
}
-keep interface androidx.window.extensions.core.util.function.Predicate {
  public boolean test(***);
}
-keep interface androidx.window.extensions.core.util.function.Function {
  public *** apply(***);
}
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5231da36518c5740ff6ec8f636c4adaa\transformed\jetified-core-1.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6ca5cf3515ee63827c22853024d7c14\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
-keep,allowobfuscation @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

-keepclassmembers,allowobfuscation class * {
  @androidx.annotation.DoNotInline <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6ca5cf3515ee63827c22853024d7c14\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c00cc1fe128424c77f1637fa57ed894b\transformed\rules\lib\META-INF\proguard\okio.pro
# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c00cc1fe128424c77f1637fa57ed894b\transformed\rules\lib\META-INF\proguard\okio.pro
# The proguard configuration file for the following section is <unknown>

# End of content from <unknown>