import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/material.dart';
import 'package:octalog/src/pages/home/<USER>/historico_sac/widget/card_sac.dart';
import 'package:octalog/src/pages/home/<USER>/historico_sac/widget/info_sac.dart';
import 'package:octalog/src/utils/extesion.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../../../components/buttom_ls/button_ls_custom.dart';
import '../../../../components/custom_scaffold/custom_scaffold.dart';
import '../../../../helpers/login/login.dart';
import '../../../../utils/colors.dart';
import 'historico_sac_state.dart';
import 'historico_sac_store.dart';

class HistoricoSac extends StatelessWidget {
  HistoricoSac({super.key});
  final store = HistoricoSacStore();
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<HistoricoSacState>(
      valueListenable: store.state,
      builder: (_, HistoricoSacState state, __) {
        return SafeArea(
          child: CustomScaffold(
            onPop: () {
              Navigator.of(context).pop();
            },
            title: 'Histórico de SAC',
            iconsCamera: Icons.refresh,
            isColorIcon: false,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  GestureDetector(
                    onTap: () async {
                      await showDialog(
                        context: context,
                        builder: (context) {
                          return Dialog(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                            backgroundColor: Colors.transparent,
                            clipBehavior: Clip.antiAlias,
                            child: Material(
                              color: Colors.white,
                              child: SizedBox(
                                width: MediaQuery.of(context).size.width,
                                height: 400,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    CalendarDatePicker2WithActionButtons(
                                      onCancelTapped: () {
                                        Navigator.of(context).pop();
                                      },
                                      onValueChanged: (value) {
                                        store.setDataInicialDataFinal(
                                            value[0] ?? DateTime.now(),
                                            value[1] ?? DateTime.now());
                                        Navigator.of(context).pop();
                                      },
                                      value: [
                                        state.dataInicial,
                                        state.dataFinal,
                                      ],
                                      config:
                                          CalendarDatePicker2WithActionButtonsConfig(
                                        yearTextStyle:  TextStyle(
                                          color: ThemeColors.customOrange(context),
                                          fontSize: 18,
                                        ),
                                        okButton:  Text(
                                          'Ok',
                                          style: TextStyle(
                                            color: ThemeColors.customOrange(context),
                                            fontSize: 18,
                                          ),
                                        ),
                                        cancelButton:  Text(
                                          'Cancelar',
                                          style: TextStyle(
                                            color: ThemeColors.customOrange(context),
                                            fontSize: 18,
                                          ),
                                        ),
                                        buttonPadding:
                                            const EdgeInsets.symmetric(
                                          horizontal: 36.0,
                                        ),
                                        controlsTextStyle:  TextStyle(
                                          color: ThemeColors.customOrange(context),
                                          fontSize: 18,
                                        ),
                                        dayTextStyle: const TextStyle(
                                          // color: ThemeColors.customOrange(context),
                                          fontSize: 17,
                                        ),
                                        selectedYearTextStyle: const TextStyle(
                                          color: ColorsCustom.customWhite,
                                          fontSize: 18,
                                        ),
                                        dayBorderRadius: const BorderRadius.all(
                                          Radius.circular(5),
                                        ),
                                        selectedDayHighlightColor: ColorsCustom
                                            .customOrange
                                            .withOpacity(0.9),
                                        calendarViewMode: CalendarDatePicker2Mode.day,
                                        firstDate: DateTime.now().subtract(
                                            const Duration(days: 365)),
                                        lastDate: DateTime.now(),
                                        calendarType:
                                            CalendarDatePicker2Type.range,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      );
                    },
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          " Nome: ${Login.instance.usuarioLogado?.nomeCompleto ?? ''}",
                          overflow: TextOverflow.ellipsis,
                          softWrap: false,
                          maxLines: 1,
                          style:  TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                              color: ThemeColors.customOrange(context)),
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                        Row(
                          children: [
                            Card(
                              color: Colors.white,
                              elevation: 1,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(5),
                              ),
                              child: Container(
                                decoration: const BoxDecoration(
                                  color: Color.fromARGB(197, 243, 243, 243),
                                ),
                                padding: const EdgeInsets.only(
                                  left: 3,
                                  right: 10,
                                  top: 5,
                                  bottom: 5,
                                ),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                     SizedBox(
                                      width: 10,
                                      child: Icon(
                                        Icons.calendar_today_outlined,
                                        size: 18,
                                        color: ThemeColors.customOrange(context),
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 10,
                                    ),
                                    Text(
                                      "${state.dataInicial.dataPtBr} - ${state.dataFinal.dataPtBr}",
                                      style: const TextStyle(
                                        fontSize: 17,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(
                              width: 10,
                            ),
                            SizedBox(
                              height: 40,
                              child: ButtonLsCustom(
                                text: "Buscar",
                                onPressed: () {
                                  store.buscarPedidos();
                                },
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  _buildList(context),
                  Visibility(
                    visible: state.historicoSac.isNotEmpty,
                    replacement:  Expanded(
                      child: Center(
                        child: Text(
                          'Nenhum registro encontrado',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: ThemeColors.customOrange(context),
                          ),
                        ),
                      ),
                    ),
                    child: Expanded(
                      child: ListView.builder(
                        itemCount: state.historicoSac.length,
                        itemBuilder: (context, index) {
                          final historico = state.historicoSac[index];
                          return HistoricoItem(
                            historico: historico,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) {
                                    return InfoSac(
                                      historico: historico,
                                    );
                                  },
                                ),
                              );
                            },
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildList(BuildContext context) {
    return  Row(
      children: [
        Expanded(
          child: Divider(
            color: ThemeColors.customOrange(context),
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 10),
          child: Text(
            'Histórico de SAC',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: ThemeColors.customOrange(context),
            ),
          ),
        ),
        Expanded(
          child: Divider(
            color: ThemeColors.customOrange(context),
          ),
        ),
      ],
    );
  }
}
