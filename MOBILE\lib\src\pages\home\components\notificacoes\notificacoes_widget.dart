import 'package:flutter/material.dart';
import 'package:octalog/src/components/buttom_ls/button_ls_custom.dart';
import 'package:octalog/src/components/custom_scaffold/custom_scaffold.dart';
import 'package:octalog/src/pages/cadastro/cadastro_page.dart';
import 'package:octalog/src/utils/theme_colors.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../helpers/web_connector.dart';
import './notificacao.dart';

class NotificacoesWidget extends StatefulWidget {
  final Notificacao notificacao;
  const NotificacoesWidget({
    super.key,
    required this.notificacao,
  });

  @override
  State<NotificacoesWidget> createState() => _NotificacoesWidgetState();
}

class _NotificacoesWidgetState extends State<NotificacoesWidget> {
  final pageController = PageController();
  int indexPage = 0;
  bool get isLastPage => indexPage == (widget.notificacao.paginas.length - 1);
  bool loading = false;

  Future endPage() async {
    try {
      final conn = WebConnector();
      await conn.put(
        '/notificacoes/visualizada?IDNotificacaoMobile=${widget.notificacao.id}',
      );
      loading = true;
    } catch (_) {
      loading = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvoked: (bool value) async {
        await endPage();
      },
      child: CustomScaffold(
        child: Scaffold(
          // appBar: AppBar(
          //   automaticallyImplyLeading: false,
          //   title: Text(
          //     widget.notificacao.titulo,
          //     style: const TextStyle(
          //       color: Colors.black87,
          //     ),
          //   ),
          //   centerTitle: true,
          //   elevation: 0,
          //   backgroundColor: Colors.grey[200],
          // ),
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: PageView.builder(
                  reverse: true,
                  physics: const NeverScrollableScrollPhysics(),
                  controller: pageController,
                  onPageChanged: (value) {
                    setState(() => indexPage = value);
                  },
                  itemCount: widget.notificacao.paginas.length,
                  itemBuilder: (context, index) {
                    return WebViewWidget(
                      controller: WebViewController()
                        ..setJavaScriptMode(JavaScriptMode.unrestricted)
                        ..setNavigationDelegate(
                          NavigationDelegate(
                            onNavigationRequest: (NavigationRequest request) {
                              if (request.url.startsWith('http')) {
                                launchUrl(Uri.parse(request.url));
                                return NavigationDecision.prevent;
                              }
                              return NavigationDecision.navigate;
                            },
                          ),
                        )
                        ..loadHtmlString(widget.notificacao.paginas[index].conteudo),
                    );
                  },
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Visibility(
                      visible: indexPage != 0 && isLastPage,
                      child: Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: ButtonLsCustom(
                            colorBackground: ThemeColors.customGreen(context),
                            onPressed: () {
                              pageController.previousPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.ease,
                              );
                            },
                            text: 'Anterior',
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: ButtonLsCustom(
                        isLoading: loading,
                        text: widget.notificacao.cadastroIncompleto
                            ? 'COMPLETE SEU CADASTRO'
                            : isLastPage
                                ? 'Fechar'
                                : 'Próximo',
                        onPressed: () async {
                          if (widget.notificacao.cadastroIncompleto) {
                            Navigator.pushAndRemoveUntil(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        const CadastroAgente()),
                                (route) => false);
                            return;
                          }
                          if (isLastPage) {
                            setState(() => loading = true);
                            await endPage();
                            if (loading) {
                              Navigator.of(context).pop();
                            }
                            setState(() => loading = false);
                          } else {
                            pageController.nextPage(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.ease,
                            );
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
