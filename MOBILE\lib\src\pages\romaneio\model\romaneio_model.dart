import 'package:map_fields/map_fields.dart';

class RomaneioModel {
  final String tipo;
  final int total;
  final int ordemExibicao;
  final List<int> romaneios;
  final List<ResumoRomaneioModel> resumo;

  RomaneioModel({
    required this.tipo,
    required this.total,
    required this.ordemExibicao,
    required this.romaneios,
    required this.resumo,
  });

  factory RomaneioModel.fromJson(Map<String, dynamic> json) {
    final mapFields = MapFields.load(json);
    return RomaneioModel(
      tipo: mapFields.getString('Tipo', ''),
      total: mapFields.getInt('Total', 0),
      ordemExibicao: mapFields.getInt('OrdemExibicao', 0),
      romaneios: mapFields.getList<int>('Romaneios', []),
      resumo: mapFields
          .getList('Resumo', [])
          .map((e) => ResumoRomaneioModel.fromJson(e))
          .toList(),
    );
  }
}

class ResumoRomaneioModel {
  final int qtde;
  final String local;

  ResumoRomaneioModel({
    required this.qtde,
    required this.local,
  });

  factory ResumoRomaneioModel.fromJson(Map<String, dynamic> json) {
    final mapFields = MapFields.load(json);
    return ResumoRomaneioModel(
      qtde: mapFields.getInt('Qtde', 0),
      local: mapFields.getString('Local', ''),
    );
  }
}
