import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../../models_new/cliente_new.dart';
import '../../../controller/entrega_new_store.dart';
import '../../../../../utils/colors-dart';

class WidgetValorReceber extends StatefulWidget {
  final EntregaNewStore store;
  final bool avisoReceber;
  final bool receberValorObrigatorio;
  final ClienteNew? clienteEscolhido;
  const WidgetValorReceber({
    super.key,
    required this.store,
    required this.receberValorObrigatorio,
    this.clienteEscolhido,
    required this.avisoReceber,
  });

  @override
  State<WidgetValorReceber> createState() => _WidgetValorReceberState();
}

class _WidgetValorReceberState extends State<WidgetValorReceber> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Visibility(
          visible: true,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Valor a receber: R\$ ${widget.clienteEscolhido?.valorReceber ?? 0.0}',
                      style: GoogleFonts.roboto(
                        fontSize: 15,
                        color: ThemeColors.customBlack(context),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Visibility(
                      visible: widget.store.valorRecebido != 0,
                      child: Text(
                        'Valor recebido: R\$ ${widget.store.valorRecebido.toStringAsFixed(2).replaceAll('.', ',')}',
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 10),
              Container(
                height: 52,
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                ),
                child: TextField(
                  onChanged: (value) {
                    if (value.isEmpty) {
                      widget.store.setValorRecebido('0');
                      return;
                    }

                    widget.store.setValorRecebido(value);
                  },
                  keyboardType: const TextInputType.numberWithOptions(
                    decimal: true,
                  ),
                  decoration: const InputDecoration(
                    isDense: true,
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(height: 10)
            ],
          ),
        ),
      ],
    );
  }
}
