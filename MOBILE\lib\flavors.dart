enum Flavor {
  octalog,
  up360,
  connect,
  spotlog,
}

class F {
  static late final Flavor appFlavor;

  static String get name => appFlavor.name;

  static String get title {
    switch (appFlavor) {
      case Flavor.octalog:
        return 'Octalog';
      case Flavor.up360:
        return 'UP360';
      case Flavor.connect:
        return 'Connect ExpressLog';
      case Flavor.spotlog:
        return 'SpotLog';
    }
  }

}
