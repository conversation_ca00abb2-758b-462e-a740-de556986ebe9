import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:hive_flutter/hive_flutter.dart';

import '../model/contrato_model.dart';

abstract class ContratoPrefs {
  Future<void> save(ContratoModel contratoModel);
  Future<void> clean();
  Future<ContratoModel?> read();
  static final instance = ContratoPrefsImpl();
}

class ContratoPrefsImpl implements ContratoPrefs {
  @override
  Future<void> save(ContratoModel contratoModel) async {
    final box = await Hive.openBox<String>('contrato_map');
    await box.put('contrato', jsonEncode(contratoModel.toJson()));
  }

  @override
  Future<void> clean() async {
    final box = await Hive.openBox<String>('contrato_map');
    await box.clear();
  }

  @override
  Future<ContratoModel> read() async {
    final box = await Hive.openBox<String>('contrato_map');
    final contrato = box.get('contrato');
    try {
      return ContratoModel.fromJson(jsonDecode(contrato!));
    } catch (e) {
      await clean();
      debugPrint('Erro ao ler contrato: $e');
      return ContratoModel.fromJson(jsonDecode(contrato!));
    }
  }
}
