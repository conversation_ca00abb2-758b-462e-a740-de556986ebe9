// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import 'package:octalog/src/components/buttom_custom/button_custom.dart';
import 'package:octalog/src/components/custom_scaffold/custom_scaffold.dart';
import 'package:octalog/src/database/baixa_fora_do_local/database_fora_do_local.dart';
import 'package:octalog/src/helpers/gps/gps_position.dart';
import 'package:octalog/src/models_new/endereco_new.dart';
import 'package:octalog/src/models_new/position_data_location.dart';
import 'package:octalog/src/pages/entrega_newpages/componentes/card_endereco.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../../../database/config_blob/config_blob_model.dart';
import '../../../../database/config_blob/config_database.dart';
import '../../../../helpers/gps/gps_contract.dart';
import '../../../../helpers/web_connector.dart';
import '../../../entregas/components/entrega_card.dart';
import '../../../home/<USER>';
import '../../controller/entrega_new_etapa.dart';
import '../../controller/entrega_new_state.dart';
import '../../controller/entrega_new_store.dart';
import '../../ocorrencias/entrega_negativa.dart';

class EntregaChegada extends StatefulWidget {
  final EntregaNewStore store;
  const EntregaChegada({super.key, required this.store});

  @override
  State<EntregaChegada> createState() => _EntregaChegadaState();
}

class _EntregaChegadaState extends State<EntregaChegada> {
  bool isColor = true;
  bool loadingBotao = false;

  @override
  void initState() {
    widget.store.timePedidoOld();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ValueListenableBuilder<EntregaNewState>(
        valueListenable: widget.store.state,
        builder: (BuildContext context, EntregaNewState value, Widget? child) {
          final atividade = value.atividade;
          return CustomScaffold(
            isColorIcon: isColor,
            cameraTela: () async {
              final foto = await WebConnector().tirarFoto(context);
              if (foto == null) return;
              widget.store.upLoadFotoDeslocamento(foto, true);
              setState(() {
                isColor = false;
              });
            },
            canPop: false,
            onPopClose: () async {
              Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const Home(enterAtividade: false)));
              return true;
            },
            onPop: () {
              Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const Home(enterAtividade: false)));
            },
            title:
                atividade.clientes.last.acareacao
                    ? '${atividade.volumesLength} Acareação'
                    : '${atividade.volumesLength} ${atividade.volumesLength == 1 ? 'pedido' : 'pedidos'} para entregar',
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CardEndereco(store: widget.store, isColor: isColor),
                const SizedBox(height: 10),
                Expanded(
                  child: SizedBox(
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: value.restantes.length,
                      itemBuilder: (context, index) {
                        final cliente = value.restantes[index];
                        return Column(
                          children: [
                            EntregaCard(
                              atividade: atividade,
                              cliente: cliente,
                              etapa: value.etapa,
                              onTap: () async {
                                final exibir = widget.store.filtrarStatusAtividadesChild(indexClienteRestante: value.indexClienteEscolhido);
                                if (exibir!.isEmpty) return;

                                if (isColor) {
                                  return showDialog(
                                    context: context,
                                    builder:
                                        (context) => AlertDialog(
                                          backgroundColor: Colors.white,
                                          title: const Text('Atenção'),
                                          content: const Text('Você não tirou foto deseja continuar?'),
                                          actions: [
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              mainAxisSize: MainAxisSize.max,
                                              children: [
                                                TextButton(
                                                  onPressed: () {
                                                    Navigator.pop(context);
                                                  },
                                                  child: Text('CANCELAR', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.grey.shade800)),
                                                ),
                                                TextButton(
                                                  onPressed: () {
                                                    Navigator.pop(context);
                                                    showModalBottomSheet(
                                                      shape: const RoundedRectangleBorder(
                                                        borderRadius: BorderRadius.only(topLeft: Radius.circular(30), topRight: Radius.circular(30)),
                                                      ),
                                                      isScrollControlled: true,
                                                      isDismissible: true,
                                                      context: context,
                                                      builder:
                                                          (_) => EntregaNegativa(
                                                            store: widget.store,
                                                            indexClienteRestante: index,
                                                            isOcorrenciaGlobal: false,
                                                            removerAcareacao: atividade.clientes.last.acareacao,
                                                          ),
                                                    );
                                                  },
                                                  child:  Text(
                                                    'CONFIRMAR',
                                                    style: TextStyle(fontWeight: FontWeight.bold, color: ThemeColors.customOrange(context)),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                  );
                                } else {
                                  return showModalBottomSheet(
                                    shape: const RoundedRectangleBorder(
                                      borderRadius: BorderRadius.only(topLeft: Radius.circular(30), topRight: Radius.circular(30)),
                                    ),
                                    isScrollControlled: true,
                                    isDismissible: true,
                                    context: context,
                                    builder:
                                        (_) => EntregaNegativa(
                                          store: widget.store,
                                          indexClienteRestante: index,
                                          isOcorrenciaGlobal: false,
                                          removerAcareacao: atividade.clientes.last.acareacao,
                                        ),
                                  );
                                }
                              },
                            ),
                            const SizedBox(height: 5),
                            Visibility(
                              visible: value.restantes.length > 1 && index != value.restantes.length - 1,
                              child: const Padding(padding: EdgeInsets.symmetric(horizontal: 22), child: Divider(thickness: 0.5, color: Colors.grey)),
                            ),
                            const SizedBox(height: 10),
                          ],
                        );
                      },
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 22),
                  child: Builder(
                    builder: (context) {
                      final isTime = value.timerDataFisicoChegadaNoLocalContador != null && value.timerDataFisicoChegadaNoLocalContador != '';
                      return ButtonLsCustom(
                        text: isTime ? value.timerDataFisicoChegadaNoLocalContador ?? '' : 'CHEGADA NO LOCAL',
                        isLoading: loadingBotao || value.timerDataFisicoChegadaNoLocalContador == '',
                        onPressed:
                            isTime
                                ? null
                                : () async {
                                  PositionDataLocation? position;
                                  try {
                                    position = await GpsHelperContract.instance.updateAndGetLastPosition();
                                  } catch (_) {}

                                  final gravar = GravarBaixaForaDoLocalDatabase.instance;
                                  gravar.deletAll();
                                  await widget.store.finalizarDeslocamento(
                                    EntregaNewEtapa.finalizar,
                                    position == null ? null : LatLng(position.latitude, position.longitude),
                                  );

                                  // await widget.store.baixasNoMesmoLocal();
                                  // if (value.timerDataFisicoChegadaNoLocalContador !=
                                  //         null &&
                                  //     value.timerDataFisicoChegadaNoLocalContador !=
                                  //         '') {
                                  //   return;
                                  // }
                                  // final continuar =
                                  //     await checkDistanciaEntrega(atividade);

                                  // if (isTime) {
                                  //   return;
                                  // }
                                  // if (continuar) {
                                  //   return;
                                  // }
                                  // setState(() => loadingBotao = true);
                                  // PositionDataLocation? position;
                                  // try {
                                  //   position = await GpsHelperContract.instance
                                  //       .updateAndGetLastPosition();
                                  // } catch (_) {}

                                  // final responseConfig =
                                  //     await ConfigDatabase.instance.getConfig();
                                  // try {
                                  //   if (responseConfig.bloqueioProximoCds) {
                                  //     final idDistancia = await checkDistancia(
                                  //         atividade.latitude, atividade.longitude);
                                  //     if (idDistancia) {
                                  //       setState(() => loadingBotao = false);
                                  //       return;
                                  //     }
                                  //   }
                                  // } catch (_) {}

                                  // final distanciaMinima =
                                  //     (responseConfig.distanciaMetrosFotoFaixada ??
                                  //             1000) /
                                  //         1000;
                                  // final distancia = position == null
                                  //     ? 0
                                  //     : atividade.distance(position);
                                  // try {
                                  //   if (atividade.latitude == 0 &&
                                  //       atividade.longitude == 0) {
                                  //     final gravar =
                                  //         GravarBaixaForaDoLocalDatabase.instance;
                                  //     gravar.deletAll();
                                  //     await widget.store.finalizarDeslocamento(
                                  //       EntregaNewEtapa.finalizar,
                                  //       position == null
                                  //           ? null
                                  //           : LatLng(position.latitude,
                                  //               position.longitude),
                                  //     );
                                  //     setState(() => loadingBotao = false);
                                  //     return;
                                  //   }
                                  //   if (distancia > distanciaMinima) {
                                  //     await widget.store.finalizarDeslocamento(
                                  //       EntregaNewEtapa.foto,
                                  //       position == null
                                  //           ? null
                                  //           : LatLng(position.latitude,
                                  //               position.longitude),
                                  //     );
                                  //   } else {
                                  //     final gravar =
                                  //         GravarBaixaForaDoLocalDatabase.instance;
                                  //     gravar.deletAll();
                                  //     await widget.store.finalizarDeslocamento(
                                  //       EntregaNewEtapa.finalizar,
                                  //       position == null
                                  //           ? null
                                  //           : LatLng(position.latitude,
                                  //               position.longitude),
                                  //     );
                                  //   }
                                  // } catch (e) {
                                  //   final gravar =
                                  //       GravarBaixaForaDoLocalDatabase.instance;
                                  //   gravar.deletAll();
                                  //   await widget.store.finalizarDeslocamento(
                                  //     EntregaNewEtapa.foto,
                                  //     position == null ? null : LatLng(0, 0),
                                  //   );
                                  // }
                                  setState(() => loadingBotao = false);
                                },
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Future<bool> checkDistanciaEntrega(EnderecoNew atividade) async {
    if (atividade.distanciaPermitidaParaChegada == 0) {
      return false;
    }
    if (atividade.latitude == 0 && atividade.longitude == 0 || atividade.latitude == 0.0 && atividade.longitude == 0.0) {
      return false;
    }
    final position = GpsHelper.instance;
    final posicaoAgente = await position.receberLocalizacao();
    final distancia = Geolocator.distanceBetween(posicaoAgente.latitude, posicaoAgente.longitude, atividade.latitude, atividade.longitude);
    if (distancia > atividade.distanciaPermitidaParaChegada) {
      final continuar = await showDialog(
        context: context,
        barrierDismissible: false,
        barrierColor: Colors.black.withOpacity(0.5),
        builder:
            (ctx) => AlertDialog(
              title: const Text('Atenção'),
              content: const Text('Entrega deste pedido só poderá ser feito no endereço de entrega. Você ainda não esta no endereço de entrega'),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(ctx).pop(true);
                  },
                  child: Text('OK', style: TextStyle(color: ThemeColors.customOrange(context))),
                ),
              ],
            ),
      );
      return continuar;
    }
    return false;
  }

  Future<bool> checkDistancia(double lat1, double long1) async {
    await GpsHelperContract.instance.updateLoc();
    final position = await GpsHelperContract.instance.updateAndGetLastPosition();

    if (position == null) {
      return false;
    }

    final config = await ConfigDatabase.instance.getConfig();
    final int distanciaMax = config.distanciaMetrosChegadaDeslocamento ~/ 3;
    final List<BloqueiosCDsLatLong> latLongCDsBloqueio = config.bloqueiosCDsLatLong;

    for (final bloqueio in latLongCDsBloqueio) {
      final double distancia = Geolocator.distanceBetween(bloqueio.latitude, bloqueio.longitude, position.latitude, position.longitude);

      if (distancia < distanciaMax) {
        return await showDialog<bool>(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) {
                return AlertDialog(
                  title: const Text('Atenção!'),
                  content: Text('Você está no ${bloqueio.nome}, não poderá seguir com a baixa.\nProcure o setor de acerto.'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(true), // Pass true to indicate action is needed.
                      child: Text('OK', style: TextStyle(color: ThemeColors.customOrange(context))),
                    ),
                  ],
                );
              },
            ) ??
            true;
      }
    }

    return false;
  }
}
