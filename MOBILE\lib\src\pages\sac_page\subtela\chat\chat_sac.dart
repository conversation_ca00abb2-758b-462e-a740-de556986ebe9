import 'dart:async';

import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:octalog/src/helpers/web_connector.dart';
import 'package:octalog/src/pages/entrega_newpages/entregas_etapas/finaliza%C3%A7%C3%A3o/components/preview_fotos.dart';
import 'package:octalog/src/pages/sac_page/model/messagem_chat.dart';
import 'package:octalog/src/pages/sac_page/sac_page.state.dart';
import 'package:octalog/src/pages/sac_page/sac_page_store.dart';
import 'package:octalog/src/pages/sac_page/subtela/chat/chat_sac_widget.dart';
import 'package:octalog/src/utils/theme_colors.dart';
import 'package:speech_to_text/speech_recognition_result.dart';
import 'package:speech_to_text/speech_to_text.dart';

import '../../../../utils/colors.dart';
import '../../enum/sap_page_etapa.dart';

class HomeChatSAC extends StatefulWidget {
  final SacPageStore store;

  const HomeChatSAC({super.key, required this.store});

  @override
  State<HomeChatSAC> createState() => _HomeChatSACState();
}

class _HomeChatSACState extends State<HomeChatSAC> {
  List<MessagemChat> messages = [];
  final controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initSpeech();
  }

  final SpeechToText _speechToText = SpeechToText();
  bool isListening = false;
  bool _buttonVisible = false;
  Timer? _timer;

  void _initSpeech() async {
    isListening = await _speechToText.initialize();
    setState(() {});
  }

  void _toggleListening() {
    if (_speechToText.isNotListening) {
      _startListening();
    } else {
      _stopListening();
    }
  }

  void _startListening() async {
    await _speechToText.listen(
      onResult: _onSpeechResult,
      localeId: 'pt_BR',
    );
    setState(() {
      isListening = true;
      _startBlinking();
    });
  }

  void _stopListening() async {
    await _speechToText.stop();
    setState(() {
      isListening = false;
      _stopBlinking();
    });
  }

  void _onSpeechResult(SpeechRecognitionResult result) {
    setState(() {
      controller.text = result.recognizedWords;
      controller.selection = TextSelection.fromPosition(
        TextPosition(offset: controller.text.length),
      );
    });
  }

  void _startBlinking() {
    _timer ??= Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (_speechToText.isNotListening) {
        _stopBlinking();
      }

      setState(() {
        _buttonVisible = !_buttonVisible;
      });
    });
  }

  void _stopBlinking() {
    _timer?.cancel();
    _timer = null;
    setState(() {
      _buttonVisible = true;
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: Padding(
          padding: const EdgeInsets.all(5.0),
          child: SizedBox(
            width: MediaQuery.of(context).size.width,
            child: GestureDetector(
              child: Container(
                height: 40,
                width: 40,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: ThemeColors.customOrange(context).withOpacity(0.1),
                ),
                child:  Icon(
                  Icons.arrow_back,
                  color: ThemeColors.customOrange(context),
                ),
              ),
              onTap: () async {
                await showDialog<bool>(
                  context: context,
                  builder: (ctx) {
                    return AlertDialog(
                      title: const Text('Deseja sair do Chat?'),
                      content: const Text(
                          'Ao sair do atendimento, você perderá o contato com o atendente.'),
                      actions: [
                        TextButton(
                          onPressed: () {
                            Navigator.pop(ctx, false);
                          },
                          child: const Text('CANCELAR',
                              style: TextStyle(color: Colors.black)),
                        ),
                        TextButton(
                          onPressed: () {
                            Navigator.pop(ctx, true);
                            widget.store.setEtapa(SacPageEtapa.espera);
                          },
                          child:  Text(
                            'SAIR',
                            style: TextStyle(color: ThemeColors.customOrange(ctx)),
                          ),
                        )
                      ],
                    );
                  },
                );
              },
            ),
          ),
        ),
        title: const Text('Sac'),
        centerTitle: true,
        actions: const [
          // IconButton(
          //   onPressed: () {
          //     // Navigator.push(context,
          //     //     MaterialPageRoute(builder: (context) => VideoCallApp()));
          //   },
          //   icon: const Icon(Icons.phone),
          // ),
        ],
      ),
      body: ValueListenableBuilder<SacPageState>(
        valueListenable: widget.store.state,
        builder: (_, SacPageState value, __) {
          return Column(
            children: [
              Expanded(
                child: Visibility(
                  visible: value.messagesChat != null,
                  replacement: Center(
                      child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text('Aguarde um momento'),
                      Text(
                          'O ${value.sacModelFila!.chamado.nomeAtendente} vai te atender em breve!'),
                    ],
                  )),
                  child: ChatSACWidget(
                    messages: value.messagesChat,
                    store: widget.store,
                  ),
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  color: ThemeColors.customOrange(context),
                  borderRadius: BorderRadius.circular(10),
                ),
                width: MediaQuery.of(context).size.width * .98,
                height: 50,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(width: 10),
                    Expanded(
                      child: TextField(
                        autofocus: true,
                        controller: controller,
                        cursorColor: Colors.white,
                        style: const TextStyle(color: Colors.white),
                        decoration: const InputDecoration(
                          hintStyle: TextStyle(color: Colors.white),
                          contentPadding: EdgeInsets.symmetric(horizontal: 0),
                          hintText: 'Digite sua mensagem...',
                          border: OutlineInputBorder(
                            borderSide: BorderSide.none,
                          ),
                        ),
                      ),
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        GestureDetector(
                          onTap: _toggleListening,
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 500),
                            height: 50,
                            width: 50,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(50),
                              color: _buttonVisible
                                  ? ColorsCustom.customWhite.withOpacity(0.1)
                                  : ColorsCustom.customTransparent,
                            ),
                            child: Icon(
                              _speechToText.isNotListening
                                  ? Icons.mic
                                  : Icons.mic,
                              color: _speechToText.isNotListening
                                  ? ColorsCustom.customWhite
                                  : ColorsCustom.customBlack,
                            ),
                          ),
                        ),

                        // camera button
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: GestureDetector(
                            onTap: () async {
                              XFile? foto =
                                  await WebConnector().tirarFoto(context);
                              if (foto == null) return;

                              bool? liberarFoto = await Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => PreviewImageSemRosto(
                                    imageFile: foto,
                                  ),
                                ),
                              );
                              if (liberarFoto == null || !liberarFoto) return;
                              await widget.store.uploaAddFoto(foto);
                            },
                            child: const Icon(
                              Icons.camera_alt_outlined,
                              color: Colors.white,
                            ),
                          ),
                        ),

                        IconButton(
                          onPressed: () {
                            if (controller.text.isEmpty) {
                              return;
                            }
                            widget.store.sendMessage(controller.text);
                            controller.clear();
                          },
                          icon: const Icon(Icons.send, color: Colors.white),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 10),
            ],
          );
        },
      ),
    );
  }
}
