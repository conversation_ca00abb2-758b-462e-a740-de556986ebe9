import 'package:flutter/material.dart';
import 'package:flutter_linkify/flutter_linkify.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../utils/theme_colors.dart';

class MessagemCard extends StatefulWidget {
  final String conteudo;
  final String usuario;
  final String dataMensagem;
  final bool isAgent;
  final String? hyperLink;
  final bool foto;

  const MessagemCard({
    super.key,
    required this.conteudo,
    required this.usuario,
    required this.dataMensagem,
    required this.isAgent,
    required this.foto,
    this.hyperLink,
  });

  @override
  State<MessagemCard> createState() => _MessagemCardState();
}

class _MessagemCardState extends State<MessagemCard> {
  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: widget.isAgent ? Alignment.centerRight : Alignment.centerLeft,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width - 45,
        ),
        child: Card(
          elevation: 0,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          color: widget.isAgent
              ? ThemeColors.customOrange(context).withOpacity(.3)
              : Colors.grey.withOpacity(.2),
          margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
          child: Container(
            constraints: const BoxConstraints(
              minWidth: 100,
            ),
            child: Stack(
              children: [
                widget.isAgent
                    ? const SizedBox()
                    : Padding(
                        padding: const EdgeInsets.only(
                          left: 10,
                          right: 30,
                          top: 5,
                          bottom: 5,
                        ),
                        child: Text(
                          widget.usuario,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                Padding(
                  padding: EdgeInsets.only(
                    left: 10,
                    right: 30,
                    top: widget.isAgent ? 5 : 30,
                    bottom: 20,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Visibility(
                          visible: widget.conteudo.isNotEmpty && !widget.foto,
                          replacement: widget.foto
                              ? Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Image.network(
                                    widget.conteudo,
                                    width: 300,
                                    height: 300,
                                  ),
                                )
                              : const SizedBox(),
                          child: Linkify(
                            onOpen: (link) async {
                              final url = Uri.parse(link.url);
                              final fallbackUrl = Uri.parse(link.url);
                              if (await canLaunchUrl(url)) {
                                await launchUrl(
                                  url,
                                  mode: LaunchMode.externalApplication,
                                );
                              } else {
                                await launchUrl(
                                  fallbackUrl,
                                  mode: LaunchMode.externalApplication,
                                );
                              }
                            },
                            text: widget.conteudo,
                            textAlign: TextAlign.start,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.black,
                            ),
                          )),
                      if (widget.hyperLink != null)
                        Padding(
                          padding: const EdgeInsets.only(
                            top: 5,
                            bottom: 5,
                          ),
                          child: GestureDetector(
                            onTap: () async {
                              final link = widget.hyperLink!;
                              final url = Uri.parse(link);
                              final fallbackUrl = Uri.parse(link);
                              if (await canLaunchUrl(url)) {
                                await launchUrl(
                                  url,
                                  mode: LaunchMode.externalApplication,
                                );
                              } else {
                                await launchUrl(
                                  fallbackUrl,
                                  mode: LaunchMode.externalApplication,
                                );
                              }
                            },
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Image.asset(
                                'assets/images/mapa2.png',
                                scale: 0.11,
                              ),
                            ),
                          ),
                        )
                    ],
                  ),
                ),
                Positioned(
                  bottom: 4,
                  right: 10,
                  child: Row(
                    children: [
                      Text(
                        widget.dataMensagem,
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(
                        width: 5,
                      ),
                      const Icon(
                        Icons.done_all,
                        size: 20,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
