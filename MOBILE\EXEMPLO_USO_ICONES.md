# Exemplo de Uso - Ícones por Flavor

Este documento mostra exemplos práticos de como usar os ícones personalizados por flavor.

## 🎯 Cenários de Uso

### 1. Desenvolvimento Local

```bash
# Gerar ícones para o flavor que você está desenvolvendo
./scripts/generate_icons.sh connect

# Executar o app em debug
flutter run --flavor connect --dart-define=FLAVOR=connect
```

### 2. Build para Produção

```bash
# Método automático (recomendado) - gera ícones + build
./scripts/build_flavors.sh octalog apk

# Método manual
./scripts/generate_icons.sh octalog
flutter build apk --release --flavor octalog --dart-define=FLAVOR=octalog
```

### 3. Build para Todos os Flavors

```bash
# Gerar ícones para todos os flavors
./scripts/generate_icons.sh all

# Build individual para cada flavor
./scripts/build_flavors.sh octalog apk
./scripts/build_flavors.sh arcargo appbundle
./scripts/build_flavors.sh connect apk
./scripts/build_flavors.sh rondolog appbundle
```

## 📱 Resultado Esperado

Após executar os comandos acima, cada flavor terá seu próprio ícone:

### Android
- **octalog**: Logo azul/laranja em `android/app/src/main/res/mipmap-*/launcher_icon.png`
- **arcargo**: Logo verde em `android/app/src/main/res/mipmap-*/launcher_icon.png`
- **connect**: Logo azul em `android/app/src/main/res/mipmap-*/launcher_icon.png`
- **rondolog**: Logo laranja em `android/app/src/main/res/mipmap-*/launcher_icon.png`

### iOS
- **octalog**: Logo em `ios/Runner/Assets.xcassets/AppIcon-octalog.appiconset/`
- **arcargo**: Logo em `ios/Runner/Assets.xcassets/AppIcon-arcargo.appiconset/`
- **connect**: Logo em `ios/Runner/Assets.xcassets/AppIcon-connect.appiconset/`
- **rondolog**: Logo em `ios/Runner/Assets.xcassets/AppIcon-rondolog.appiconset/`

## 🔄 Workflow CI/CD

### GitHub Actions (Exemplo)

```yaml
name: Build Flavors
on: [push]

jobs:
  build-android:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        flavor: [octalog, arcargo, connect, rondolog]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.7.2'
    
    - name: Install dependencies
      run: flutter pub get
    
    - name: Generate icons for ${{ matrix.flavor }}
      run: ./scripts/generate_icons.sh ${{ matrix.flavor }}
    
    - name: Build APK for ${{ matrix.flavor }}
      run: flutter build apk --release --flavor ${{ matrix.flavor }} --dart-define=FLAVOR=${{ matrix.flavor }}
    
    - name: Upload APK
      uses: actions/upload-artifact@v3
      with:
        name: ${{ matrix.flavor }}-apk
        path: build/app/outputs/flutter-apk/app-${{ matrix.flavor }}-release.apk
```

## 🛠️ Personalização Avançada

### Alterando Cores do Adaptive Icon

Edite o arquivo `flutter_launcher_icons-[flavor].yaml`:

```yaml
# Exemplo para arcargo (verde)
flutter_icons:
  adaptive_icon_background: "#4CAF50"  # Verde
  adaptive_icon_foreground: "assets/images/arcargo/logo512.png"
```

### Adicionando Novos Flavors

1. **Criar pasta de assets**:
   ```bash
   mkdir assets/images/novoflavor
   cp assets/images/octalog/logo512.png assets/images/novoflavor/
   ```

2. **Criar configuração YAML**:
   ```bash
   cp flutter_launcher_icons-octalog.yaml flutter_launcher_icons-novoflavor.yaml
   # Editar o arquivo para apontar para o novo logo
   ```

3. **Atualizar scripts**:
   - Adicionar `novoflavor` na lista de flavors válidos
   - Testar: `./scripts/generate_icons.sh novoflavor`

## 📋 Checklist de Verificação

Antes de fazer deploy, verifique:

- [ ] ✅ Ícones gerados para o flavor correto
- [ ] ✅ Build executado com sucesso
- [ ] ✅ APK/AAB instalado e testado em dispositivo
- [ ] ✅ Ícone correto aparece na tela inicial
- [ ] ✅ Nome do app correto (definido no flavor)
- [ ] ✅ Application ID correto

## 🚨 Troubleshooting Comum

### Ícone não muda no dispositivo
```bash
# Limpar cache e reinstalar
flutter clean
./scripts/generate_icons.sh [flavor]
flutter build apk --flavor [flavor] --dart-define=FLAVOR=[flavor]
# Desinstalar app do dispositivo e reinstalar
```

### Erro "Image not found"
```bash
# Verificar se o logo existe
ls -la assets/images/[flavor]/logo512.*

# Verificar configuração YAML
cat flutter_launcher_icons-[flavor].yaml
```

### Script não executa
```bash
# Dar permissão de execução (Linux/Mac)
chmod +x scripts/generate_icons.sh

# Verificar se está no diretório correto
pwd  # Deve estar na raiz do projeto Flutter
```

## 📚 Referências

- [flutter_launcher_icons](https://pub.dev/packages/flutter_launcher_icons)
- [Android App Icons](https://developer.android.com/guide/practices/ui_guidelines/icon_design_launcher)
- [iOS App Icons](https://developer.apple.com/design/human-interface-guidelines/app-icons)
- [Documentação dos Flavors](FLAVORS_README.md)
- [Documentação dos Ícones](ICONS_README.md)
