import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../../../components/image_perfil/image_perfil.dart';

class CardPerfilAcareacao extends StatelessWidget {
  final String referencia;
  final String complemento;
  final String urlFoto;
  final String? iniciais;
  final String pedido;

  const CardPerfilAcareacao({
    super.key,
    required this.referencia,
    required this.complemento,
    required this.urlFoto,
    required this.pedido,
    this.iniciais,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SizedBox(
          width: 70,
          height: 70,
          child: ImagePerfil(
            fontSize: 30,
            url: urlFoto,
            iniciais: iniciais ?? '',
          ),
        ),
        const SizedBox(width: 10),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Visibility(
              visible: complemento.isNotEmpty,
              child: Text(
                complemento,
                textAlign: TextAlign.start,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
                style: GoogleFonts.roboto(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: ThemeColors.customOrange(context),
                ),
              ),
            ),
            Text(
              pedido,
              textAlign: TextAlign.start,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
              style: GoogleFonts.roboto(
                fontSize: 12,
                fontWeight: FontWeight.w800,
                color: ThemeColors.customBlack(context),
              ),
            ),
            Visibility(
              visible: referencia.isNotEmpty,
              child: Text(
                'Ponto de referência:',
                textAlign: TextAlign.start,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: GoogleFonts.roboto(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: ThemeColors.customBlack(context).withOpacity(0.8),
                ),
              ),
            ),
            Visibility(
              visible: referencia.isNotEmpty,
              child: Text(
                referencia,
                textAlign: TextAlign.start,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
                style: GoogleFonts.roboto(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: ThemeColors.customBlack(context).withOpacity(0.5),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
