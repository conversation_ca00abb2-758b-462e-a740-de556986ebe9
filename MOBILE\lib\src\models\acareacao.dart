import 'package:map_fields/map_fields.dart';

class AcareacaoInfoModel {
  final int idos;
  final int idcliente;
  final String nomecliente;
  final String enderecocliente;
  final double latitudecliente;
  final double longitudecliente;
  final String nomeloja;
  final String enderecoloja;
  final String logoloja;
  final double latitudeentrega;
  final double longitudeentrega;
  final List<String> fotos;
  final List<String> pedidos;
  final DateTime dataentrega;
  final String nomerecebedor;
  final List<String> mensagens;
  final String referencia;
  final String complemento;
  AcareacaoInfoModel({
    required this.idos,
    required this.idcliente,
    required this.nomecliente,
    required this.enderecocliente,
    required this.latitudecliente,
    required this.longitudecliente,
    required this.nomeloja,
    required this.enderecoloja,
    required this.logoloja,
    required this.latitudeentrega,
    required this.longitudeentrega,
    required this.fotos,
    required this.pedidos,
    required this.dataentrega,
    required this.nomerecebedor,
    required this.mensagens,
    required this.referencia,
    required this.complemento,
  });

  factory AcareacaoInfoModel.fromJson(Map<String, dynamic> json) {
    final MapFields a = MapFields.load(json);
    return AcareacaoInfoModel(
      idos: a.getInt('IDOS', 0),
      idcliente: a.getInt('IDCliente', 0),
      nomecliente: a.getString('NomeCliente', ''),
      enderecocliente: a.getString('EnderecoCliente', 'Endereço na etiqueta'),
      latitudecliente: a.getDouble('LatitudeCliente', 0),
      longitudecliente: a.getDouble('LongitudeCliente', 0),
      nomeloja: a.getString('NomeLoja', ''),
      enderecoloja: a.getString('EnderecoLoja', ''),
      logoloja: a.getString('LogoLoja', ''),
      latitudeentrega: a.getDouble('LatitudeEntrega', 0),
      longitudeentrega: a.getDouble('LongitudeEntrega', 0),
      dataentrega: a.getDateTime('DataEntrega', DateTime.now()),
      nomerecebedor: a.getString('NomeRecebedor', ''),
      fotos: a.getList<String>('Fotos', []),
      pedidos: a.getList<String>('Pedidos', []),
      mensagens: a.getList<String>('Mensagens', []),
      referencia: a.getString('PontoReferencia', ''),
      complemento: a.getString('Complemento', ''),
    );
  }

  factory AcareacaoInfoModel.fromHiveMap(Map<String, dynamic> json) {
    final MapFields a = MapFields.load(json);

    return AcareacaoInfoModel(
      idos: a.getInt('idos', 0),
      idcliente: a.getInt('idcliente', 0),
      nomecliente: a.getString('nomecliente', ''),
      enderecocliente: a.getString('enderecocliente', 'Endereço na etiqueta'),
      latitudecliente: a.getDouble('latitudecliente', 0),
      longitudecliente: a.getDouble('longitudecliente', 0),
      nomeloja: a.getString('nomeloja', ''),
      enderecoloja: a.getString('enderecoloja', 'Sem endereço'),
      logoloja: a.getString('logoloja', ''),
      latitudeentrega: a.getDouble('latitudeentrega', 0),
      longitudeentrega: a.getDouble('longitudeentrega', 0),
      dataentrega: a.getDateTime('dataentrega', DateTime.now()),
      nomerecebedor: a.getString('nomerecebedor', ''),
      fotos: a.getList<String>('fotos', []),
      pedidos: a.getList<String>('pedidos', []),
      mensagens: a.getList<String>('mensagens', []),
      referencia: a.getString('pontoReferencia', ''),
      complemento: a.getString('complemento', ''),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'IDOS': idos,
      'IDCliente': idcliente,
      'NomeCliente': nomecliente,
      'EnderecoCliente': enderecocliente,
      'LatitudeCliente': latitudecliente,
      'LongitudeCliente': longitudecliente,
      'NomeLoja': nomeloja,
      'EnderecoLoja': enderecoloja,
      'LogoLoja': logoloja,
      'LatitudeEntrega': latitudeentrega,
      'LongitudeEntrega': longitudeentrega,
      'Fotos': fotos,
      'Pedidos': pedidos,
      'DataEntrega': dataentrega.toIso8601String(),
      'NomeRecebedor': nomerecebedor,
      'Mensagens': mensagens,
      'PontoReferencia': referencia,
      'Complemento': complemento,
    };
  }

  Map<String, dynamic> toHiveMap() {
    final ret = {
      'idos': idos,
      'idcliente': idcliente,
      'nomecliente': nomecliente,
      'enderecocliente': enderecocliente,
      'latitudecliente': latitudecliente,
      'longitudecliente': longitudecliente,
      'nomeloja': nomeloja,
      'enderecoloja': enderecoloja,
      'logoloja': logoloja,
      'latitudeentrega': latitudeentrega,
      'longitudeentrega': longitudeentrega,
      'fotos': fotos,
      'pedidos': pedidos,
      'dataentrega': dataentrega.toIso8601String(),
      'nomerecebedor': nomerecebedor,
      'mensagens': mensagens,
      'pontoReferencia': referencia,
      'complemento': complemento,
    };
    return ret;
  }
}
