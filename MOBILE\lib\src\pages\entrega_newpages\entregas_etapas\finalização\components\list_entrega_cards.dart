import 'package:flutter/material.dart';

import '../../../../../models_new/cliente_new.dart';
import '../../../../../models_new/endereco_new.dart';
import '../../../../entregas/components/entrega_card.dart';
import '../../../controller/entrega_new_etapa.dart';

class EntregaCards extends StatelessWidget {
  final List<ClienteNew> clientesRestantes;
  final EnderecoNew atividade;

  final EntregaNewEtapa etapa;
  final Function(int) onTap;

  const EntregaCards({
    super.key,
    required this.clientesRestantes,
    required this.atividade,
    required this.onTap,
    required this.etapa,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(
        clientesRestantes.length,
        (index) {
          final cliente = clientesRestantes[index];
          return Column(
            children: [
              EntregaCard(
                atividade: atividade,
                etapa: etapa,
                cliente: cliente,
                onTap: () => onTap(index),
              ),
              Visibility(
                visible: clientesRestantes.length > 1 &&
                    index != clientesRestantes.length - 1,
                child: const Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: 22,
                    vertical: 20,
                  ),
                  child: Divider(
                    thickness: 0.5,
                    color: Colors.grey,
                  ),
                ),
              ),
              const SizedBox(
                height: 10,
              ),
            ],
          );
        },
      ).toList(),
    );
  }
}
