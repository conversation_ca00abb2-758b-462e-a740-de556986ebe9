import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../../../components/mapa_widget/map_widget.dart';
import '../../../../models/id_status_atividade_enum.dart';
import '../../controller/entrega_new_state.dart';
import '../../controller/entrega_new_store.dart';

class StackCustomNew extends StatefulWidget {
  final EntregaNewStore store;
  const StackCustomNew({super.key, required this.store});

  @override
  State<StackCustomNew> createState() => _StackCustomNewState();
}

class _StackCustomNewState extends State<StackCustomNew> {
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<EntregaNewState>(
      valueListenable: widget.store.state,
      builder: (BuildContext context, EntregaNewState value, Widget? child) {
        final atividade = value.atividade;
        return Column(
          children: [
            Stack(
              children: [
                const SizedBox(
                  child: AbsorbPointer(
                    absorbing: true,
                    child: SizedBox(height: 200, width: double.infinity, child: Card(margin: EdgeInsets.zero, child: Center(child: MapWidget()))),
                  ),
                ),
                SizedBox(
                  child: ValueListenableBuilder<EntregaNewState>(
                    valueListenable: widget.store.state,
                    builder: (BuildContext context, EntregaNewState value, Widget? child) {
                      if (atividade.status.contains(IdStatusAtividadeEnum.entregue)) {
                        return Container(
                          height: 100,
                          width: 340,
                          padding: const EdgeInsets.only(left: 30, top: 28),
                          child: Card(
                            color: Colors.white60,
                            elevation: 15,
                            child: Container(
                              decoration: BoxDecoration(border: Border(left: BorderSide(color: ThemeColors.secondary(context), width: 5))),
                              child: ListTile(
                                title: Text('Parabéns!', style: GoogleFonts.roboto(fontSize: 14, fontWeight: FontWeight.w500, color: ThemeColors.customBlack(context))),
                                subtitle: Text('Entregas concluídas.', style: GoogleFonts.roboto(fontSize: 14, fontWeight: FontWeight.w500)),
                              ),
                            ),
                          ),
                        );
                      } else {
                        return Container(
                          height: 90,
                          padding: const EdgeInsets.only(left: 10, top: 20),
                          child: Card(
                            color: Colors.white60,
                            elevation: 15,
                            child: Container(
                              decoration: BoxDecoration(border: Border(left: BorderSide(color: ThemeColors.customRed(context), width: 5))),
                              child: ListTile(
                                title: Text('Uma pena', style: GoogleFonts.roboto(fontSize: 14, fontWeight: FontWeight.w500, color: ThemeColors.customBlack(context))),
                                subtitle: Text(
                                  'você não conclui a entrega deste endereço',
                                  style: GoogleFonts.roboto(fontSize: 14, fontWeight: FontWeight.w500),
                                ),
                              ),
                            ),
                          ),
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}
