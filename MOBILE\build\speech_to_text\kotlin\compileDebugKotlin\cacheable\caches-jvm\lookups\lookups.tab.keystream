  Manifest android  R android  
permission android.Manifest  BLUETOOTH_CONNECT android.Manifest.permission  RECORD_AUDIO android.Manifest.permission  attr 	android.R  data android.R.attr  	TargetApi android.annotation  Activity android.app  	RESULT_OK android.app.Activity  BluetoothAdapter android.bluetooth  BluetoothDevice android.bluetooth  BluetoothHeadset android.bluetooth  BluetoothProfile android.bluetooth  getBondedDevices "android.bluetooth.BluetoothAdapter  getDefaultAdapter "android.bluetooth.BluetoothAdapter  getISEnabled "android.bluetooth.BluetoothAdapter  getIsEnabled "android.bluetooth.BluetoothAdapter  getProfileProxy "android.bluetooth.BluetoothAdapter  	isEnabled "android.bluetooth.BluetoothAdapter  
setEnabled "android.bluetooth.BluetoothAdapter  getTOString "android.bluetooth.BluetoothHeadset  getToString "android.bluetooth.BluetoothHeadset  startVoiceRecognition "android.bluetooth.BluetoothHeadset  stopVoiceRecognition "android.bluetooth.BluetoothHeadset  toString "android.bluetooth.BluetoothHeadset  HEADSET "android.bluetooth.BluetoothProfile  ServiceListener "android.bluetooth.BluetoothProfile  BroadcastReceiver android.content  
ComponentName android.content  Context android.content  Intent android.content  	ArrayList !android.content.BroadcastReceiver  Boolean !android.content.BroadcastReceiver  Context !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  List !android.content.BroadcastReceiver  Locale !android.content.BroadcastReceiver  Log !android.content.BroadcastReceiver  RecognizerIntent !android.content.BroadcastReceiver  Result !android.content.BroadcastReceiver  String !android.content.BroadcastReceiver  buildIdNameForLocale !android.content.BroadcastReceiver  createResponse !android.content.BroadcastReceiver  debugLog !android.content.BroadcastReceiver  getResultExtras !android.content.BroadcastReceiver  replace !android.content.BroadcastReceiver  
ComponentName android.content.Context  Intent android.content.Context  RecognitionService android.content.Context  applicationInfo android.content.Context  debugLog android.content.Context  equals android.content.Context  findComponentName android.content.Context  firstOrNull android.content.Context  getAPPLICATIONInfo android.content.Context  getApplicationInfo android.content.Context  getDEBUGLog android.content.Context  getDebugLog android.content.Context  getFINDComponentName android.content.Context  getFIRSTOrNull android.content.Context  getFindComponentName android.content.Context  getFirstOrNull android.content.Context  getLET android.content.Context  getLet android.content.Context  getPACKAGEManager android.content.Context  getPackageManager android.content.Context  let android.content.Context  packageManager android.content.Context  sendOrderedBroadcast android.content.Context  setApplicationInfo android.content.Context  setPackageManager android.content.Context  Locale android.content.Intent  RecognizerIntent android.content.Intent  apply android.content.Intent  debugLog android.content.Intent  getAPPLY android.content.Intent  getApply android.content.Intent  getDEBUGLog android.content.Intent  getDebugLog android.content.Intent  getPLUGINContext android.content.Intent  getPluginContext android.content.Intent  
pluginContext android.content.Intent  putExtra android.content.Intent  
setPackage android.content.Intent  PackageManager android.content.pm  ResolveInfo android.content.pm  ServiceInfo android.content.pm  packageName "android.content.pm.ApplicationInfo  let  android.content.pm.ComponentInfo  let "android.content.pm.PackageItemInfo  PERMISSION_GRANTED !android.content.pm.PackageManager  queryIntentServices !android.content.pm.PackageManager  serviceInfo android.content.pm.ResolveInfo  getLET android.content.pm.ServiceInfo  getLet android.content.pm.ServiceInfo  let android.content.pm.ServiceInfo  name android.content.pm.ServiceInfo  packageName android.content.pm.ServiceInfo  Uri android.net  Build 
android.os  Bundle 
android.os  Handler 
android.os  Looper 
android.os  containsKey android.os.BaseBundle  
getFloatArray android.os.BaseBundle  	getString android.os.BaseBundle  getStringArrayList android.os.BaseBundle  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  containsKey android.os.Bundle  
getFloatArray android.os.Bundle  	getString android.os.Bundle  getStringArrayList android.os.Bundle  post android.os.Handler  postDelayed android.os.Handler  
getMainLooper android.os.Looper  Activity android.speech  ActivityCompat android.speech  	ArrayList android.speech  BluetoothAdapter android.speech  BluetoothProfile android.speech  Build android.speech  ChannelResultWrapper android.speech  
ComponentName android.speech  
ContextCompat android.speech  	Exception android.speech  	Executors android.speech  Handler android.speech  Intent android.speech  	JSONArray android.speech  
JSONObject android.speech  LanguageDetailsChecker android.speech  
ListenMode android.speech  Locale android.speech  Log android.speech  Looper android.speech  Manifest android.speech  
MethodChannel android.speech  PackageManager android.speech  RecognitionListener android.speech  RecognitionService android.speech  RecognitionSupport android.speech  RecognitionSupportCallback android.speech  RecognizerIntent android.speech  SpeechRecognizer android.speech  SpeechToTextCallbackMethods android.speech  SpeechToTextErrors android.speech  SpeechToTextStatus android.speech  System android.speech  android android.speech  apply android.speech  arrayOf android.speech  bluetoothHeadset android.speech  createOnDeviceSpeechRecognizer android.speech  createSpeechRecognizer android.speech  debugLog android.speech  debugLogging android.speech  firstOrNull android.speech  forEach android.speech  isEmpty android.speech  
isNotEmpty android.speech  let android.speech  pluginChannelName android.speech  
pluginContext android.speech  plus android.speech  replace android.speech  run android.speech  toString android.speech  SERVICE_INTERFACE !android.speech.RecognitionService  getSUPPORTEDOnDeviceLanguages !android.speech.RecognitionSupport  getSupportedOnDeviceLanguages !android.speech.RecognitionSupport  setSupportedOnDeviceLanguages !android.speech.RecognitionSupport  supportedOnDeviceLanguages !android.speech.RecognitionSupport  ACTION_GET_LANGUAGE_DETAILS android.speech.RecognizerIntent  ACTION_RECOGNIZE_SPEECH android.speech.RecognizerIntent  EXTRA_CALLING_PACKAGE android.speech.RecognizerIntent  EXTRA_LANGUAGE android.speech.RecognizerIntent  EXTRA_LANGUAGE_MODEL android.speech.RecognizerIntent  EXTRA_LANGUAGE_PREFERENCE android.speech.RecognizerIntent  EXTRA_MAX_RESULTS android.speech.RecognizerIntent  EXTRA_PARTIAL_RESULTS android.speech.RecognizerIntent  EXTRA_PREFER_OFFLINE android.speech.RecognizerIntent  EXTRA_SUPPORTED_LANGUAGES android.speech.RecognizerIntent  LANGUAGE_MODEL_FREE_FORM android.speech.RecognizerIntent  getVoiceDetailsIntent android.speech.RecognizerIntent  CONFIDENCE_SCORES android.speech.SpeechRecognizer  ERROR_AUDIO android.speech.SpeechRecognizer  ERROR_CLIENT android.speech.SpeechRecognizer  ERROR_INSUFFICIENT_PERMISSIONS android.speech.SpeechRecognizer  ERROR_LANGUAGE_NOT_SUPPORTED android.speech.SpeechRecognizer  ERROR_LANGUAGE_UNAVAILABLE android.speech.SpeechRecognizer  
ERROR_NETWORK android.speech.SpeechRecognizer  ERROR_NETWORK_TIMEOUT android.speech.SpeechRecognizer  ERROR_NO_MATCH android.speech.SpeechRecognizer  ERROR_RECOGNIZER_BUSY android.speech.SpeechRecognizer  ERROR_SERVER android.speech.SpeechRecognizer  ERROR_SERVER_DISCONNECTED android.speech.SpeechRecognizer  ERROR_SPEECH_TIMEOUT android.speech.SpeechRecognizer  ERROR_TOO_MANY_REQUESTS android.speech.SpeechRecognizer  RESULTS_RECOGNITION android.speech.SpeechRecognizer  apply android.speech.SpeechRecognizer  cancel android.speech.SpeechRecognizer  checkRecognitionSupport android.speech.SpeechRecognizer  createOnDeviceSpeechRecognizer android.speech.SpeechRecognizer  createSpeechRecognizer android.speech.SpeechRecognizer  debugLog android.speech.SpeechRecognizer  destroy android.speech.SpeechRecognizer  getAPPLY android.speech.SpeechRecognizer  getApply android.speech.SpeechRecognizer  getDEBUGLog android.speech.SpeechRecognizer  getDebugLog android.speech.SpeechRecognizer  isOnDeviceRecognitionAvailable android.speech.SpeechRecognizer  isRecognitionAvailable android.speech.SpeechRecognizer  setRecognitionListener android.speech.SpeechRecognizer  startListening android.speech.SpeechRecognizer  
stopListening android.speech.SpeechRecognizer  Log android.util  d android.util.Log  e android.util.Log  NonNull androidx.annotation  ActivityCompat androidx.core.app  requestPermissions  androidx.core.app.ActivityCompat  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  Activity com.csdcorp.speech_to_text  ActivityCompat com.csdcorp.speech_to_text  Any com.csdcorp.speech_to_text  Array com.csdcorp.speech_to_text  	ArrayList com.csdcorp.speech_to_text  BluetoothAdapter com.csdcorp.speech_to_text  BluetoothProfile com.csdcorp.speech_to_text  Boolean com.csdcorp.speech_to_text  Build com.csdcorp.speech_to_text  	ByteArray com.csdcorp.speech_to_text  ChannelResultWrapper com.csdcorp.speech_to_text  
ComponentName com.csdcorp.speech_to_text  
ContextCompat com.csdcorp.speech_to_text  Double com.csdcorp.speech_to_text  	Exception com.csdcorp.speech_to_text  	Executors com.csdcorp.speech_to_text  Float com.csdcorp.speech_to_text  Handler com.csdcorp.speech_to_text  Int com.csdcorp.speech_to_text  IntArray com.csdcorp.speech_to_text  Intent com.csdcorp.speech_to_text  	JSONArray com.csdcorp.speech_to_text  
JSONObject com.csdcorp.speech_to_text  LanguageDetailsChecker com.csdcorp.speech_to_text  List com.csdcorp.speech_to_text  
ListenMode com.csdcorp.speech_to_text  Locale com.csdcorp.speech_to_text  Log com.csdcorp.speech_to_text  Long com.csdcorp.speech_to_text  Looper com.csdcorp.speech_to_text  Manifest com.csdcorp.speech_to_text  
MethodChannel com.csdcorp.speech_to_text  PackageManager com.csdcorp.speech_to_text  RecognitionListener com.csdcorp.speech_to_text  RecognitionService com.csdcorp.speech_to_text  RecognitionSupport com.csdcorp.speech_to_text  RecognitionSupportCallback com.csdcorp.speech_to_text  RecognizerIntent com.csdcorp.speech_to_text  Set com.csdcorp.speech_to_text  SpeechRecognizer com.csdcorp.speech_to_text  SpeechToTextCallbackMethods com.csdcorp.speech_to_text  SpeechToTextErrors com.csdcorp.speech_to_text  SpeechToTextPlugin com.csdcorp.speech_to_text  SpeechToTextStatus com.csdcorp.speech_to_text  String com.csdcorp.speech_to_text  System com.csdcorp.speech_to_text  android com.csdcorp.speech_to_text  apply com.csdcorp.speech_to_text  arrayOf com.csdcorp.speech_to_text  bluetoothHeadset com.csdcorp.speech_to_text  createOnDeviceSpeechRecognizer com.csdcorp.speech_to_text  createSpeechRecognizer com.csdcorp.speech_to_text  debugLog com.csdcorp.speech_to_text  debugLogging com.csdcorp.speech_to_text  firstOrNull com.csdcorp.speech_to_text  forEach com.csdcorp.speech_to_text  isEmpty com.csdcorp.speech_to_text  
isNotEmpty com.csdcorp.speech_to_text  let com.csdcorp.speech_to_text  pluginChannelName com.csdcorp.speech_to_text  
pluginContext com.csdcorp.speech_to_text  plus com.csdcorp.speech_to_text  replace com.csdcorp.speech_to_text  run com.csdcorp.speech_to_text  toString com.csdcorp.speech_to_text  Any /com.csdcorp.speech_to_text.ChannelResultWrapper  Handler /com.csdcorp.speech_to_text.ChannelResultWrapper  Looper /com.csdcorp.speech_to_text.ChannelResultWrapper  Result /com.csdcorp.speech_to_text.ChannelResultWrapper  String /com.csdcorp.speech_to_text.ChannelResultWrapper  error /com.csdcorp.speech_to_text.ChannelResultWrapper  getRUN /com.csdcorp.speech_to_text.ChannelResultWrapper  getRun /com.csdcorp.speech_to_text.ChannelResultWrapper  handler /com.csdcorp.speech_to_text.ChannelResultWrapper  notImplemented /com.csdcorp.speech_to_text.ChannelResultWrapper  result /com.csdcorp.speech_to_text.ChannelResultWrapper  run /com.csdcorp.speech_to_text.ChannelResultWrapper  	ArrayList 1com.csdcorp.speech_to_text.LanguageDetailsChecker  Boolean 1com.csdcorp.speech_to_text.LanguageDetailsChecker  Context 1com.csdcorp.speech_to_text.LanguageDetailsChecker  Intent 1com.csdcorp.speech_to_text.LanguageDetailsChecker  List 1com.csdcorp.speech_to_text.LanguageDetailsChecker  Locale 1com.csdcorp.speech_to_text.LanguageDetailsChecker  Log 1com.csdcorp.speech_to_text.LanguageDetailsChecker  RecognizerIntent 1com.csdcorp.speech_to_text.LanguageDetailsChecker  Result 1com.csdcorp.speech_to_text.LanguageDetailsChecker  String 1com.csdcorp.speech_to_text.LanguageDetailsChecker  buildIdNameForLocale 1com.csdcorp.speech_to_text.LanguageDetailsChecker  createResponse 1com.csdcorp.speech_to_text.LanguageDetailsChecker  debugLog 1com.csdcorp.speech_to_text.LanguageDetailsChecker  debugLogging 1com.csdcorp.speech_to_text.LanguageDetailsChecker  
getREPLACE 1com.csdcorp.speech_to_text.LanguageDetailsChecker  
getReplace 1com.csdcorp.speech_to_text.LanguageDetailsChecker  getResultExtras 1com.csdcorp.speech_to_text.LanguageDetailsChecker  languagePreference 1com.csdcorp.speech_to_text.LanguageDetailsChecker  logTag 1com.csdcorp.speech_to_text.LanguageDetailsChecker  replace 1com.csdcorp.speech_to_text.LanguageDetailsChecker  result 1com.csdcorp.speech_to_text.LanguageDetailsChecker  supportedLanguages 1com.csdcorp.speech_to_text.LanguageDetailsChecker  
deviceDefault %com.csdcorp.speech_to_text.ListenMode  	dictation %com.csdcorp.speech_to_text.ListenMode  equals %com.csdcorp.speech_to_text.ListenMode  ordinal %com.csdcorp.speech_to_text.ListenMode  name 6com.csdcorp.speech_to_text.SpeechToTextCallbackMethods  notifyError 6com.csdcorp.speech_to_text.SpeechToTextCallbackMethods  notifyStatus 6com.csdcorp.speech_to_text.SpeechToTextCallbackMethods  soundLevelChange 6com.csdcorp.speech_to_text.SpeechToTextCallbackMethods  textRecognition 6com.csdcorp.speech_to_text.SpeechToTextCallbackMethods  missingContext -com.csdcorp.speech_to_text.SpeechToTextErrors  missingOrInvalidArg -com.csdcorp.speech_to_text.SpeechToTextErrors  multipleRequests -com.csdcorp.speech_to_text.SpeechToTextErrors  name -com.csdcorp.speech_to_text.SpeechToTextErrors  recognizerNotAvailable -com.csdcorp.speech_to_text.SpeechToTextErrors  unknown -com.csdcorp.speech_to_text.SpeechToTextErrors  Activity -com.csdcorp.speech_to_text.SpeechToTextPlugin  ActivityCompat -com.csdcorp.speech_to_text.SpeechToTextPlugin  ActivityPluginBinding -com.csdcorp.speech_to_text.SpeechToTextPlugin  Array -com.csdcorp.speech_to_text.SpeechToTextPlugin  BinaryMessenger -com.csdcorp.speech_to_text.SpeechToTextPlugin  BluetoothAdapter -com.csdcorp.speech_to_text.SpeechToTextPlugin  BluetoothHeadset -com.csdcorp.speech_to_text.SpeechToTextPlugin  BluetoothProfile -com.csdcorp.speech_to_text.SpeechToTextPlugin  Boolean -com.csdcorp.speech_to_text.SpeechToTextPlugin  Build -com.csdcorp.speech_to_text.SpeechToTextPlugin  Bundle -com.csdcorp.speech_to_text.SpeechToTextPlugin  	ByteArray -com.csdcorp.speech_to_text.SpeechToTextPlugin  ChannelResultWrapper -com.csdcorp.speech_to_text.SpeechToTextPlugin  
ComponentName -com.csdcorp.speech_to_text.SpeechToTextPlugin  Context -com.csdcorp.speech_to_text.SpeechToTextPlugin  
ContextCompat -com.csdcorp.speech_to_text.SpeechToTextPlugin  Double -com.csdcorp.speech_to_text.SpeechToTextPlugin  	Exception -com.csdcorp.speech_to_text.SpeechToTextPlugin  	Executors -com.csdcorp.speech_to_text.SpeechToTextPlugin  Float -com.csdcorp.speech_to_text.SpeechToTextPlugin  
FlutterPlugin -com.csdcorp.speech_to_text.SpeechToTextPlugin  Handler -com.csdcorp.speech_to_text.SpeechToTextPlugin  Int -com.csdcorp.speech_to_text.SpeechToTextPlugin  IntArray -com.csdcorp.speech_to_text.SpeechToTextPlugin  Intent -com.csdcorp.speech_to_text.SpeechToTextPlugin  	JSONArray -com.csdcorp.speech_to_text.SpeechToTextPlugin  
JSONObject -com.csdcorp.speech_to_text.SpeechToTextPlugin  LanguageDetailsChecker -com.csdcorp.speech_to_text.SpeechToTextPlugin  List -com.csdcorp.speech_to_text.SpeechToTextPlugin  
ListenMode -com.csdcorp.speech_to_text.SpeechToTextPlugin  Locale -com.csdcorp.speech_to_text.SpeechToTextPlugin  Log -com.csdcorp.speech_to_text.SpeechToTextPlugin  Long -com.csdcorp.speech_to_text.SpeechToTextPlugin  Looper -com.csdcorp.speech_to_text.SpeechToTextPlugin  Manifest -com.csdcorp.speech_to_text.SpeechToTextPlugin  
MethodCall -com.csdcorp.speech_to_text.SpeechToTextPlugin  
MethodChannel -com.csdcorp.speech_to_text.SpeechToTextPlugin  NonNull -com.csdcorp.speech_to_text.SpeechToTextPlugin  PackageManager -com.csdcorp.speech_to_text.SpeechToTextPlugin  RecognitionService -com.csdcorp.speech_to_text.SpeechToTextPlugin  RecognitionSupport -com.csdcorp.speech_to_text.SpeechToTextPlugin  RecognitionSupportCallback -com.csdcorp.speech_to_text.SpeechToTextPlugin  RecognizerIntent -com.csdcorp.speech_to_text.SpeechToTextPlugin  ResolveInfo -com.csdcorp.speech_to_text.SpeechToTextPlugin  Result -com.csdcorp.speech_to_text.SpeechToTextPlugin  Set -com.csdcorp.speech_to_text.SpeechToTextPlugin  SpeechRecognizer -com.csdcorp.speech_to_text.SpeechToTextPlugin  SpeechToTextCallbackMethods -com.csdcorp.speech_to_text.SpeechToTextPlugin  SpeechToTextErrors -com.csdcorp.speech_to_text.SpeechToTextPlugin  SpeechToTextStatus -com.csdcorp.speech_to_text.SpeechToTextPlugin  String -com.csdcorp.speech_to_text.SpeechToTextPlugin  System -com.csdcorp.speech_to_text.SpeechToTextPlugin  activeBluetooth -com.csdcorp.speech_to_text.SpeechToTextPlugin  activeResult -com.csdcorp.speech_to_text.SpeechToTextPlugin  
alwaysUseStop -com.csdcorp.speech_to_text.SpeechToTextPlugin  android -com.csdcorp.speech_to_text.SpeechToTextPlugin  apply -com.csdcorp.speech_to_text.SpeechToTextPlugin  arrayOf -com.csdcorp.speech_to_text.SpeechToTextPlugin  bluetoothAdapter -com.csdcorp.speech_to_text.SpeechToTextPlugin  bluetoothDisabled -com.csdcorp.speech_to_text.SpeechToTextPlugin  bluetoothHeadset -com.csdcorp.speech_to_text.SpeechToTextPlugin  
brokenStopSdk -com.csdcorp.speech_to_text.SpeechToTextPlugin  cancelListening -com.csdcorp.speech_to_text.SpeechToTextPlugin  channel -com.csdcorp.speech_to_text.SpeechToTextPlugin  completeInitialize -com.csdcorp.speech_to_text.SpeechToTextPlugin  createOnDeviceSpeechRecognizer -com.csdcorp.speech_to_text.SpeechToTextPlugin  createRecognizer -com.csdcorp.speech_to_text.SpeechToTextPlugin  createSpeechRecognizer -com.csdcorp.speech_to_text.SpeechToTextPlugin  currentActivity -com.csdcorp.speech_to_text.SpeechToTextPlugin  debugLog -com.csdcorp.speech_to_text.SpeechToTextPlugin  debugLogging -com.csdcorp.speech_to_text.SpeechToTextPlugin  defaultLanguageTag -com.csdcorp.speech_to_text.SpeechToTextPlugin  destroyRecognizer -com.csdcorp.speech_to_text.SpeechToTextPlugin  findComponentName -com.csdcorp.speech_to_text.SpeechToTextPlugin  firstOrNull -com.csdcorp.speech_to_text.SpeechToTextPlugin  getAPPLY -com.csdcorp.speech_to_text.SpeechToTextPlugin  
getARRAYOf -com.csdcorp.speech_to_text.SpeechToTextPlugin  getApply -com.csdcorp.speech_to_text.SpeechToTextPlugin  
getArrayOf -com.csdcorp.speech_to_text.SpeechToTextPlugin  !getCREATEOnDeviceSpeechRecognizer -com.csdcorp.speech_to_text.SpeechToTextPlugin  getCREATESpeechRecognizer -com.csdcorp.speech_to_text.SpeechToTextPlugin  !getCreateOnDeviceSpeechRecognizer -com.csdcorp.speech_to_text.SpeechToTextPlugin  getCreateSpeechRecognizer -com.csdcorp.speech_to_text.SpeechToTextPlugin  getFIRSTOrNull -com.csdcorp.speech_to_text.SpeechToTextPlugin  getFirstOrNull -com.csdcorp.speech_to_text.SpeechToTextPlugin  
getISEmpty -com.csdcorp.speech_to_text.SpeechToTextPlugin  
getISNotEmpty -com.csdcorp.speech_to_text.SpeechToTextPlugin  
getIsEmpty -com.csdcorp.speech_to_text.SpeechToTextPlugin  
getIsNotEmpty -com.csdcorp.speech_to_text.SpeechToTextPlugin  getLET -com.csdcorp.speech_to_text.SpeechToTextPlugin  getLet -com.csdcorp.speech_to_text.SpeechToTextPlugin  getPLUGINChannelName -com.csdcorp.speech_to_text.SpeechToTextPlugin  getPLUS -com.csdcorp.speech_to_text.SpeechToTextPlugin  getPluginChannelName -com.csdcorp.speech_to_text.SpeechToTextPlugin  getPlus -com.csdcorp.speech_to_text.SpeechToTextPlugin  
getREPLACE -com.csdcorp.speech_to_text.SpeechToTextPlugin  getRUN -com.csdcorp.speech_to_text.SpeechToTextPlugin  
getReplace -com.csdcorp.speech_to_text.SpeechToTextPlugin  getRun -com.csdcorp.speech_to_text.SpeechToTextPlugin  getTOString -com.csdcorp.speech_to_text.SpeechToTextPlugin  getToString -com.csdcorp.speech_to_text.SpeechToTextPlugin  handler -com.csdcorp.speech_to_text.SpeechToTextPlugin  
hasPermission -com.csdcorp.speech_to_text.SpeechToTextPlugin  
initialize -com.csdcorp.speech_to_text.SpeechToTextPlugin  initializeIfPermitted -com.csdcorp.speech_to_text.SpeechToTextPlugin  initializedSuccessfully -com.csdcorp.speech_to_text.SpeechToTextPlugin  intentLookup -com.csdcorp.speech_to_text.SpeechToTextPlugin  isDuplicateFinal -com.csdcorp.speech_to_text.SpeechToTextPlugin  isEmpty -com.csdcorp.speech_to_text.SpeechToTextPlugin  isListening -com.csdcorp.speech_to_text.SpeechToTextPlugin  
isNotEmpty -com.csdcorp.speech_to_text.SpeechToTextPlugin  isNotInitialized -com.csdcorp.speech_to_text.SpeechToTextPlugin  isNotListening -com.csdcorp.speech_to_text.SpeechToTextPlugin  
lastFinalTime -com.csdcorp.speech_to_text.SpeechToTextPlugin  lastOnDevice -com.csdcorp.speech_to_text.SpeechToTextPlugin  let -com.csdcorp.speech_to_text.SpeechToTextPlugin  	listening -com.csdcorp.speech_to_text.SpeechToTextPlugin  locales -com.csdcorp.speech_to_text.SpeechToTextPlugin  logTag -com.csdcorp.speech_to_text.SpeechToTextPlugin  maxRms -com.csdcorp.speech_to_text.SpeechToTextPlugin  minRms -com.csdcorp.speech_to_text.SpeechToTextPlugin  minSdkForSpeechSupport -com.csdcorp.speech_to_text.SpeechToTextPlugin  missingConfidence -com.csdcorp.speech_to_text.SpeechToTextPlugin  noBluetoothOpt -com.csdcorp.speech_to_text.SpeechToTextPlugin  notifyListening -com.csdcorp.speech_to_text.SpeechToTextPlugin  onAttachedToEngine -com.csdcorp.speech_to_text.SpeechToTextPlugin  optionallyStartBluetooth -com.csdcorp.speech_to_text.SpeechToTextPlugin  optionallyStopBluetooth -com.csdcorp.speech_to_text.SpeechToTextPlugin  
pairedDevices -com.csdcorp.speech_to_text.SpeechToTextPlugin  permissionToRecordAudio -com.csdcorp.speech_to_text.SpeechToTextPlugin  pluginChannelName -com.csdcorp.speech_to_text.SpeechToTextPlugin  
pluginContext -com.csdcorp.speech_to_text.SpeechToTextPlugin  plus -com.csdcorp.speech_to_text.SpeechToTextPlugin  previousListenMode -com.csdcorp.speech_to_text.SpeechToTextPlugin  previousPartialResults -com.csdcorp.speech_to_text.SpeechToTextPlugin  previousRecognizerLang -com.csdcorp.speech_to_text.SpeechToTextPlugin  recognizerIntent -com.csdcorp.speech_to_text.SpeechToTextPlugin  recognizerStops -com.csdcorp.speech_to_text.SpeechToTextPlugin  replace -com.csdcorp.speech_to_text.SpeechToTextPlugin  
resultSent -com.csdcorp.speech_to_text.SpeechToTextPlugin  run -com.csdcorp.speech_to_text.SpeechToTextPlugin  sdkVersionTooLow -com.csdcorp.speech_to_text.SpeechToTextPlugin  	sendError -com.csdcorp.speech_to_text.SpeechToTextPlugin  setupBluetooth -com.csdcorp.speech_to_text.SpeechToTextPlugin  setupRecognizerIntent -com.csdcorp.speech_to_text.SpeechToTextPlugin  speechRecognizer -com.csdcorp.speech_to_text.SpeechToTextPlugin  speechStartTime -com.csdcorp.speech_to_text.SpeechToTextPlugin  speechThresholdRms -com.csdcorp.speech_to_text.SpeechToTextPlugin  speechToTextPermissionCode -com.csdcorp.speech_to_text.SpeechToTextPlugin  startListening -com.csdcorp.speech_to_text.SpeechToTextPlugin  
stopListening -com.csdcorp.speech_to_text.SpeechToTextPlugin  toString -com.csdcorp.speech_to_text.SpeechToTextPlugin  
updateResults -com.csdcorp.speech_to_text.SpeechToTextPlugin  getDEBUGLog Hcom.csdcorp.speech_to_text.SpeechToTextPlugin.locales.<no name provided>  getDEBUGLogging Hcom.csdcorp.speech_to_text.SpeechToTextPlugin.locales.<no name provided>  getDebugLog Hcom.csdcorp.speech_to_text.SpeechToTextPlugin.locales.<no name provided>  getDebugLogging Hcom.csdcorp.speech_to_text.SpeechToTextPlugin.locales.<no name provided>  getBLUETOOTHHeadset Ocom.csdcorp.speech_to_text.SpeechToTextPlugin.setupBluetooth.<no name provided>  getBluetoothHeadset Ocom.csdcorp.speech_to_text.SpeechToTextPlugin.setupBluetooth.<no name provided>  getDEBUGLog Ocom.csdcorp.speech_to_text.SpeechToTextPlugin.setupBluetooth.<no name provided>  getDebugLog Ocom.csdcorp.speech_to_text.SpeechToTextPlugin.setupBluetooth.<no name provided>  getTOString Ocom.csdcorp.speech_to_text.SpeechToTextPlugin.setupBluetooth.<no name provided>  getToString Ocom.csdcorp.speech_to_text.SpeechToTextPlugin.setupBluetooth.<no name provided>  done -com.csdcorp.speech_to_text.SpeechToTextStatus  doneNoResult -com.csdcorp.speech_to_text.SpeechToTextStatus  	listening -com.csdcorp.speech_to_text.SpeechToTextStatus  name -com.csdcorp.speech_to_text.SpeechToTextStatus  notListening -com.csdcorp.speech_to_text.SpeechToTextStatus  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  getApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  #addRequestPermissionsResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  getACTIVITY Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  getActivity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  setActivity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  PluginRegistry io.flutter.plugin.common  argument #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  invokeMethod &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result   RequestPermissionsResultListener 'io.flutter.plugin.common.PluginRegistry  Activity 	java.lang  ActivityCompat 	java.lang  	ArrayList 	java.lang  BluetoothAdapter 	java.lang  BluetoothProfile 	java.lang  Build 	java.lang  ChannelResultWrapper 	java.lang  
ComponentName 	java.lang  
ContextCompat 	java.lang  	Exception 	java.lang  	Executors 	java.lang  Handler 	java.lang  Intent 	java.lang  	JSONArray 	java.lang  
JSONObject 	java.lang  LanguageDetailsChecker 	java.lang  
ListenMode 	java.lang  Locale 	java.lang  Log 	java.lang  Looper 	java.lang  Manifest 	java.lang  
MethodChannel 	java.lang  PackageManager 	java.lang  RecognitionService 	java.lang  RecognizerIntent 	java.lang  SpeechRecognizer 	java.lang  SpeechToTextCallbackMethods 	java.lang  SpeechToTextErrors 	java.lang  SpeechToTextStatus 	java.lang  System 	java.lang  android 	java.lang  apply 	java.lang  arrayOf 	java.lang  bluetoothHeadset 	java.lang  createOnDeviceSpeechRecognizer 	java.lang  createSpeechRecognizer 	java.lang  debugLog 	java.lang  debugLogging 	java.lang  firstOrNull 	java.lang  forEach 	java.lang  isEmpty 	java.lang  
isNotEmpty 	java.lang  let 	java.lang  pluginChannelName 	java.lang  
pluginContext 	java.lang  plus 	java.lang  replace 	java.lang  run 	java.lang  toString 	java.lang  getLOCALIZEDMessage java.lang.Exception  getLocalizedMessage java.lang.Exception  localizedMessage java.lang.Exception  setLocalizedMessage java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  currentTimeMillis java.lang.System  Activity 	java.util  ActivityCompat 	java.util  	ArrayList 	java.util  BluetoothAdapter 	java.util  BluetoothProfile 	java.util  Build 	java.util  ChannelResultWrapper 	java.util  
ComponentName 	java.util  
ContextCompat 	java.util  	Exception 	java.util  	Executors 	java.util  Handler 	java.util  Intent 	java.util  	JSONArray 	java.util  
JSONObject 	java.util  LanguageDetailsChecker 	java.util  
ListenMode 	java.util  Locale 	java.util  Log 	java.util  Looper 	java.util  Manifest 	java.util  
MethodChannel 	java.util  PackageManager 	java.util  RecognitionListener 	java.util  RecognitionService 	java.util  RecognitionSupport 	java.util  RecognitionSupportCallback 	java.util  RecognizerIntent 	java.util  SpeechRecognizer 	java.util  SpeechToTextCallbackMethods 	java.util  SpeechToTextErrors 	java.util  SpeechToTextStatus 	java.util  System 	java.util  android 	java.util  apply 	java.util  arrayOf 	java.util  bluetoothHeadset 	java.util  createOnDeviceSpeechRecognizer 	java.util  createSpeechRecognizer 	java.util  debugLog 	java.util  debugLogging 	java.util  firstOrNull 	java.util  forEach 	java.util  isEmpty 	java.util  
isNotEmpty 	java.util  let 	java.util  pluginChannelName 	java.util  
pluginContext 	java.util  plus 	java.util  replace 	java.util  run 	java.util  toString 	java.util  add java.util.AbstractCollection  get java.util.AbstractCollection  
isNotEmpty java.util.AbstractCollection  add java.util.AbstractList  get java.util.AbstractList  
isNotEmpty java.util.AbstractList  add java.util.ArrayList  get java.util.ArrayList  
getISNotEmpty java.util.ArrayList  
getIsNotEmpty java.util.ArrayList  
isNotEmpty java.util.ArrayList  size java.util.ArrayList  country java.util.Locale  displayName java.util.Locale  forLanguageTag java.util.Locale  
getCOUNTRY java.util.Locale  
getCountry java.util.Locale  getDISPLAYName java.util.Locale  
getDefault java.util.Locale  getDisplayName java.util.Locale  getLANGUAGE java.util.Locale  getLanguage java.util.Locale  language java.util.Locale  
setCountry java.util.Locale  setDisplayName java.util.Locale  setLanguage java.util.Locale  
toLanguageTag java.util.Locale  ExecutorService java.util.concurrent  	Executors java.util.concurrent  newSingleThreadExecutor java.util.concurrent.Executors  Activity kotlin  ActivityCompat kotlin  Any kotlin  Array kotlin  	ArrayList kotlin  BluetoothAdapter kotlin  BluetoothProfile kotlin  Boolean kotlin  Build kotlin  	ByteArray kotlin  ChannelResultWrapper kotlin  Char kotlin  
ComponentName kotlin  
ContextCompat kotlin  Double kotlin  	Exception kotlin  	Executors kotlin  Float kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  Handler kotlin  Int kotlin  IntArray kotlin  Intent kotlin  	JSONArray kotlin  
JSONObject kotlin  LanguageDetailsChecker kotlin  
ListenMode kotlin  Locale kotlin  Log kotlin  Long kotlin  Looper kotlin  Manifest kotlin  
MethodChannel kotlin  Nothing kotlin  PackageManager kotlin  RecognitionService kotlin  RecognizerIntent kotlin  SpeechRecognizer kotlin  SpeechToTextCallbackMethods kotlin  SpeechToTextErrors kotlin  SpeechToTextStatus kotlin  String kotlin  System kotlin  Unit kotlin  android kotlin  apply kotlin  arrayOf kotlin  bluetoothHeadset kotlin  createOnDeviceSpeechRecognizer kotlin  createSpeechRecognizer kotlin  debugLog kotlin  debugLogging kotlin  firstOrNull kotlin  forEach kotlin  isEmpty kotlin  
isNotEmpty kotlin  let kotlin  pluginChannelName kotlin  
pluginContext kotlin  plus kotlin  replace kotlin  run kotlin  toString kotlin  getPLUS kotlin.Array  getPlus kotlin.Array  
getISEmpty kotlin.IntArray  
getISNotEmpty kotlin.IntArray  
getIsEmpty kotlin.IntArray  
getIsNotEmpty kotlin.IntArray  isEmpty kotlin.IntArray  
isNotEmpty kotlin.IntArray  
getREPLACE 
kotlin.String  
getReplace 
kotlin.String  Activity kotlin.annotation  ActivityCompat kotlin.annotation  	ArrayList kotlin.annotation  BluetoothAdapter kotlin.annotation  BluetoothProfile kotlin.annotation  Build kotlin.annotation  ChannelResultWrapper kotlin.annotation  
ComponentName kotlin.annotation  
ContextCompat kotlin.annotation  	Exception kotlin.annotation  	Executors kotlin.annotation  Handler kotlin.annotation  Intent kotlin.annotation  	JSONArray kotlin.annotation  
JSONObject kotlin.annotation  LanguageDetailsChecker kotlin.annotation  
ListenMode kotlin.annotation  Locale kotlin.annotation  Log kotlin.annotation  Looper kotlin.annotation  Manifest kotlin.annotation  
MethodChannel kotlin.annotation  PackageManager kotlin.annotation  RecognitionService kotlin.annotation  RecognizerIntent kotlin.annotation  SpeechRecognizer kotlin.annotation  SpeechToTextCallbackMethods kotlin.annotation  SpeechToTextErrors kotlin.annotation  SpeechToTextStatus kotlin.annotation  System kotlin.annotation  android kotlin.annotation  apply kotlin.annotation  arrayOf kotlin.annotation  bluetoothHeadset kotlin.annotation  createOnDeviceSpeechRecognizer kotlin.annotation  createSpeechRecognizer kotlin.annotation  debugLog kotlin.annotation  debugLogging kotlin.annotation  firstOrNull kotlin.annotation  forEach kotlin.annotation  isEmpty kotlin.annotation  
isNotEmpty kotlin.annotation  let kotlin.annotation  pluginChannelName kotlin.annotation  
pluginContext kotlin.annotation  plus kotlin.annotation  replace kotlin.annotation  run kotlin.annotation  toString kotlin.annotation  Activity kotlin.collections  ActivityCompat kotlin.collections  	ArrayList kotlin.collections  BluetoothAdapter kotlin.collections  BluetoothProfile kotlin.collections  Build kotlin.collections  ChannelResultWrapper kotlin.collections  
ComponentName kotlin.collections  
ContextCompat kotlin.collections  	Exception kotlin.collections  	Executors kotlin.collections  Handler kotlin.collections  Intent kotlin.collections  	JSONArray kotlin.collections  
JSONObject kotlin.collections  LanguageDetailsChecker kotlin.collections  List kotlin.collections  
ListenMode kotlin.collections  Locale kotlin.collections  Log kotlin.collections  Looper kotlin.collections  Manifest kotlin.collections  
MethodChannel kotlin.collections  MutableList kotlin.collections  
MutableSet kotlin.collections  PackageManager kotlin.collections  RecognitionService kotlin.collections  RecognizerIntent kotlin.collections  Set kotlin.collections  SpeechRecognizer kotlin.collections  SpeechToTextCallbackMethods kotlin.collections  SpeechToTextErrors kotlin.collections  SpeechToTextStatus kotlin.collections  System kotlin.collections  android kotlin.collections  apply kotlin.collections  arrayOf kotlin.collections  bluetoothHeadset kotlin.collections  createOnDeviceSpeechRecognizer kotlin.collections  createSpeechRecognizer kotlin.collections  debugLog kotlin.collections  debugLogging kotlin.collections  firstOrNull kotlin.collections  forEach kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  let kotlin.collections  pluginChannelName kotlin.collections  
pluginContext kotlin.collections  plus kotlin.collections  replace kotlin.collections  run kotlin.collections  toString kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  getFIRSTOrNull kotlin.collections.List  getFirstOrNull kotlin.collections.List  Activity kotlin.comparisons  ActivityCompat kotlin.comparisons  	ArrayList kotlin.comparisons  BluetoothAdapter kotlin.comparisons  BluetoothProfile kotlin.comparisons  Build kotlin.comparisons  ChannelResultWrapper kotlin.comparisons  
ComponentName kotlin.comparisons  
ContextCompat kotlin.comparisons  	Exception kotlin.comparisons  	Executors kotlin.comparisons  Handler kotlin.comparisons  Intent kotlin.comparisons  	JSONArray kotlin.comparisons  
JSONObject kotlin.comparisons  LanguageDetailsChecker kotlin.comparisons  
ListenMode kotlin.comparisons  Locale kotlin.comparisons  Log kotlin.comparisons  Looper kotlin.comparisons  Manifest kotlin.comparisons  
MethodChannel kotlin.comparisons  PackageManager kotlin.comparisons  RecognitionService kotlin.comparisons  RecognizerIntent kotlin.comparisons  SpeechRecognizer kotlin.comparisons  SpeechToTextCallbackMethods kotlin.comparisons  SpeechToTextErrors kotlin.comparisons  SpeechToTextStatus kotlin.comparisons  System kotlin.comparisons  android kotlin.comparisons  apply kotlin.comparisons  arrayOf kotlin.comparisons  bluetoothHeadset kotlin.comparisons  createOnDeviceSpeechRecognizer kotlin.comparisons  createSpeechRecognizer kotlin.comparisons  debugLog kotlin.comparisons  debugLogging kotlin.comparisons  firstOrNull kotlin.comparisons  forEach kotlin.comparisons  isEmpty kotlin.comparisons  
isNotEmpty kotlin.comparisons  let kotlin.comparisons  pluginChannelName kotlin.comparisons  
pluginContext kotlin.comparisons  plus kotlin.comparisons  replace kotlin.comparisons  run kotlin.comparisons  toString kotlin.comparisons  Activity 	kotlin.io  ActivityCompat 	kotlin.io  	ArrayList 	kotlin.io  BluetoothAdapter 	kotlin.io  BluetoothProfile 	kotlin.io  Build 	kotlin.io  ChannelResultWrapper 	kotlin.io  
ComponentName 	kotlin.io  
ContextCompat 	kotlin.io  	Exception 	kotlin.io  	Executors 	kotlin.io  Handler 	kotlin.io  Intent 	kotlin.io  	JSONArray 	kotlin.io  
JSONObject 	kotlin.io  LanguageDetailsChecker 	kotlin.io  
ListenMode 	kotlin.io  Locale 	kotlin.io  Log 	kotlin.io  Looper 	kotlin.io  Manifest 	kotlin.io  
MethodChannel 	kotlin.io  PackageManager 	kotlin.io  RecognitionService 	kotlin.io  RecognizerIntent 	kotlin.io  SpeechRecognizer 	kotlin.io  SpeechToTextCallbackMethods 	kotlin.io  SpeechToTextErrors 	kotlin.io  SpeechToTextStatus 	kotlin.io  System 	kotlin.io  android 	kotlin.io  apply 	kotlin.io  arrayOf 	kotlin.io  bluetoothHeadset 	kotlin.io  createOnDeviceSpeechRecognizer 	kotlin.io  createSpeechRecognizer 	kotlin.io  debugLog 	kotlin.io  debugLogging 	kotlin.io  firstOrNull 	kotlin.io  forEach 	kotlin.io  isEmpty 	kotlin.io  
isNotEmpty 	kotlin.io  let 	kotlin.io  pluginChannelName 	kotlin.io  
pluginContext 	kotlin.io  plus 	kotlin.io  replace 	kotlin.io  run 	kotlin.io  toString 	kotlin.io  Activity 
kotlin.jvm  ActivityCompat 
kotlin.jvm  	ArrayList 
kotlin.jvm  BluetoothAdapter 
kotlin.jvm  BluetoothProfile 
kotlin.jvm  Build 
kotlin.jvm  ChannelResultWrapper 
kotlin.jvm  
ComponentName 
kotlin.jvm  
ContextCompat 
kotlin.jvm  	Exception 
kotlin.jvm  	Executors 
kotlin.jvm  Handler 
kotlin.jvm  Intent 
kotlin.jvm  	JSONArray 
kotlin.jvm  
JSONObject 
kotlin.jvm  LanguageDetailsChecker 
kotlin.jvm  
ListenMode 
kotlin.jvm  Locale 
kotlin.jvm  Log 
kotlin.jvm  Looper 
kotlin.jvm  Manifest 
kotlin.jvm  
MethodChannel 
kotlin.jvm  PackageManager 
kotlin.jvm  RecognitionService 
kotlin.jvm  RecognizerIntent 
kotlin.jvm  SpeechRecognizer 
kotlin.jvm  SpeechToTextCallbackMethods 
kotlin.jvm  SpeechToTextErrors 
kotlin.jvm  SpeechToTextStatus 
kotlin.jvm  System 
kotlin.jvm  android 
kotlin.jvm  apply 
kotlin.jvm  arrayOf 
kotlin.jvm  bluetoothHeadset 
kotlin.jvm  createOnDeviceSpeechRecognizer 
kotlin.jvm  createSpeechRecognizer 
kotlin.jvm  debugLog 
kotlin.jvm  debugLogging 
kotlin.jvm  firstOrNull 
kotlin.jvm  forEach 
kotlin.jvm  isEmpty 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  let 
kotlin.jvm  pluginChannelName 
kotlin.jvm  
pluginContext 
kotlin.jvm  plus 
kotlin.jvm  replace 
kotlin.jvm  run 
kotlin.jvm  toString 
kotlin.jvm  Activity 
kotlin.ranges  ActivityCompat 
kotlin.ranges  	ArrayList 
kotlin.ranges  BluetoothAdapter 
kotlin.ranges  BluetoothProfile 
kotlin.ranges  Build 
kotlin.ranges  ChannelResultWrapper 
kotlin.ranges  
ComponentName 
kotlin.ranges  
ContextCompat 
kotlin.ranges  	Exception 
kotlin.ranges  	Executors 
kotlin.ranges  Handler 
kotlin.ranges  IntRange 
kotlin.ranges  Intent 
kotlin.ranges  	JSONArray 
kotlin.ranges  
JSONObject 
kotlin.ranges  LanguageDetailsChecker 
kotlin.ranges  
ListenMode 
kotlin.ranges  Locale 
kotlin.ranges  Log 
kotlin.ranges  Looper 
kotlin.ranges  Manifest 
kotlin.ranges  
MethodChannel 
kotlin.ranges  PackageManager 
kotlin.ranges  RecognitionService 
kotlin.ranges  RecognizerIntent 
kotlin.ranges  SpeechRecognizer 
kotlin.ranges  SpeechToTextCallbackMethods 
kotlin.ranges  SpeechToTextErrors 
kotlin.ranges  SpeechToTextStatus 
kotlin.ranges  System 
kotlin.ranges  android 
kotlin.ranges  apply 
kotlin.ranges  arrayOf 
kotlin.ranges  bluetoothHeadset 
kotlin.ranges  createOnDeviceSpeechRecognizer 
kotlin.ranges  createSpeechRecognizer 
kotlin.ranges  debugLog 
kotlin.ranges  debugLogging 
kotlin.ranges  firstOrNull 
kotlin.ranges  forEach 
kotlin.ranges  isEmpty 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  let 
kotlin.ranges  pluginChannelName 
kotlin.ranges  
pluginContext 
kotlin.ranges  plus 
kotlin.ranges  replace 
kotlin.ranges  run 
kotlin.ranges  toString 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  Activity kotlin.sequences  ActivityCompat kotlin.sequences  	ArrayList kotlin.sequences  BluetoothAdapter kotlin.sequences  BluetoothProfile kotlin.sequences  Build kotlin.sequences  ChannelResultWrapper kotlin.sequences  
ComponentName kotlin.sequences  
ContextCompat kotlin.sequences  	Exception kotlin.sequences  	Executors kotlin.sequences  Handler kotlin.sequences  Intent kotlin.sequences  	JSONArray kotlin.sequences  
JSONObject kotlin.sequences  LanguageDetailsChecker kotlin.sequences  
ListenMode kotlin.sequences  Locale kotlin.sequences  Log kotlin.sequences  Looper kotlin.sequences  Manifest kotlin.sequences  
MethodChannel kotlin.sequences  PackageManager kotlin.sequences  RecognitionService kotlin.sequences  RecognizerIntent kotlin.sequences  SpeechRecognizer kotlin.sequences  SpeechToTextCallbackMethods kotlin.sequences  SpeechToTextErrors kotlin.sequences  SpeechToTextStatus kotlin.sequences  System kotlin.sequences  android kotlin.sequences  apply kotlin.sequences  arrayOf kotlin.sequences  bluetoothHeadset kotlin.sequences  createOnDeviceSpeechRecognizer kotlin.sequences  createSpeechRecognizer kotlin.sequences  debugLog kotlin.sequences  debugLogging kotlin.sequences  firstOrNull kotlin.sequences  forEach kotlin.sequences  isEmpty kotlin.sequences  
isNotEmpty kotlin.sequences  let kotlin.sequences  pluginChannelName kotlin.sequences  
pluginContext kotlin.sequences  plus kotlin.sequences  replace kotlin.sequences  run kotlin.sequences  toString kotlin.sequences  Activity kotlin.text  ActivityCompat kotlin.text  	ArrayList kotlin.text  BluetoothAdapter kotlin.text  BluetoothProfile kotlin.text  Build kotlin.text  ChannelResultWrapper kotlin.text  
ComponentName kotlin.text  
ContextCompat kotlin.text  	Exception kotlin.text  	Executors kotlin.text  Handler kotlin.text  Intent kotlin.text  	JSONArray kotlin.text  
JSONObject kotlin.text  LanguageDetailsChecker kotlin.text  
ListenMode kotlin.text  Locale kotlin.text  Log kotlin.text  Looper kotlin.text  Manifest kotlin.text  
MethodChannel kotlin.text  PackageManager kotlin.text  RecognitionService kotlin.text  RecognizerIntent kotlin.text  SpeechRecognizer kotlin.text  SpeechToTextCallbackMethods kotlin.text  SpeechToTextErrors kotlin.text  SpeechToTextStatus kotlin.text  System kotlin.text  android kotlin.text  apply kotlin.text  arrayOf kotlin.text  bluetoothHeadset kotlin.text  createOnDeviceSpeechRecognizer kotlin.text  createSpeechRecognizer kotlin.text  debugLog kotlin.text  debugLogging kotlin.text  firstOrNull kotlin.text  forEach kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  let kotlin.text  pluginChannelName kotlin.text  
pluginContext kotlin.text  plus kotlin.text  replace kotlin.text  run kotlin.text  toString kotlin.text  	JSONArray org.json  
JSONObject org.json  put org.json.JSONArray  put org.json.JSONObject  toString org.json.JSONObject                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          