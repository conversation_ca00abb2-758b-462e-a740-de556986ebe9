import 'package:flutter/cupertino.dart';
import 'package:octalog/src/pages/esqueceu_senha/esqueceu_senha_state.dart';

import '../../helpers/login/login.dart';

class EsqueceuSenhaStore {
  ValueNotifier<EsqueceuSenhaState> state = ValueNotifier(EsqueceuSenhaState());

  Future<void> esqueceuSenha(String telefone) async {
    state.value = state.value.copyWith(
      loading: true,
      error: false,
      success: false,
    );
    final response = await Login.instance.esqueceuSenha(telefone);
    state.value = state.value.copyWith(
      loading: false,
      error: !response,
      success: response,
    );
    if (response) {
      state.value = state.value.copyWith(
        success: true,
      );
    } else {
      state.value = state.value.copyWith(
        error: true,
      );
    }
  }
}
