import 'dart:async';
import 'dart:convert';

import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:octalog/src/helpers/api_ls.dart';

class RemoteConfigWrapper {
  RemoteConfigWrapper._();

  static RemoteConfigWrapper i = RemoteConfigWrapper._();
  final ValueNotifier<String?> urlApiEntregadores = ValueNotifier(null);
  final ValueNotifier<String?> urlApiEntregadoresQA = ValueNotifier(null);
  final ValueNotifier<Map<String, String>?> urlApiEntregadoresServicos =
      ValueNotifier(null);
  final ValueNotifier<Map<String, String>?> urlApiEntregadoresQAServicos =
      ValueNotifier(null);

  String get urlApi {
    if (ApiLs.instance.development) {
      return urlApiEntregadoresQA.value ?? ApiLs.instance.api;
    }
    return urlApiEntregadores.value ?? ApiLs.instance.api;
  }

  Map<String, String> get urlApiServicos {
    if (ApiLs.instance.development) {
      return urlApiEntregadoresQAServicos.value ?? _configBaseUrlProd;
    }
    return urlApiEntregadoresServicos.value ?? _configBaseUrlProd;
  }

  late final StreamSubscription? subscription;

  Future<void> init({required FirebaseRemoteConfig remoteConfig}) async {
    remoteConfig.setConfigSettings(
      RemoteConfigSettings(
        fetchTimeout: const Duration(seconds: 10),
        minimumFetchInterval: const Duration(minutes: 1),
      ),
    );
    remoteConfig.setDefaults(<String, dynamic>{
      'urlApiEntregadores': 'https://apimobile.octalog.com.br',
      'urlApiEntregadoresQA':
          'https://api-entregadores-qa-linux.azurewebsites.net',
      'urlApiEntregadoresServicos': jsonEncode(_configBaseUrlProd),
      'urlApiEntregadoresQAServicos': jsonEncode(_configBaseUrlProd),
    });
    await remoteConfig.fetchAndActivate();
    _updateData(remoteConfig);
    subscription = remoteConfig.onConfigUpdated.listen((snapshot) async {
      await remoteConfig.fetchAndActivate();
      _updateData(remoteConfig);
    });
  }

  void _updateData(FirebaseRemoteConfig remoteConfig) {
    urlApiEntregadores.value = remoteConfig.getString('urlApiEntregadores');
    urlApiEntregadoresQA.value = remoteConfig.getString('urlApiEntregadoresQA');
    try {
      urlApiEntregadoresServicos.value =
          (jsonDecode(remoteConfig.getString('urlApiEntregadoresServicos'))
                  as Map)
              .cast<String, String>();
      urlApiEntregadoresQAServicos.value =
          (jsonDecode(remoteConfig.getString('urlApiEntregadoresQAServicos'))
                  as Map)
              .cast<String, String>();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  final _configBaseUrlProd = {
    "/agente/":
        "https://api-mobile-entregadores-v3-sac-b0c3dff5agfdhpfj.brazilsouth-01.azurewebsites.net",
    "/config/":
        "https://api-mobile-entregadores-v3-config-efgtg7fmepd6hzgm.brazilsouth-01.azurewebsites.net",
    "/deslocamento/":
        "https://api-mobile-entregadores-v3-deslocamento-bkd3anauabcae6f4.brazilsouth-01.azurewebsites.net",
    "/sac/":
        "https://api-mobile-entregadores-v3-sac-b0c3dff5agfdhpfj.brazilsouth-01.azurewebsites.net",
    "/atividades/":
        "https://api-mobile-entregadores-v3-atividades-cve4bshvdcf9cga4.brazilsouth-01.azurewebsites.net",
    "/servicos/":
        "https://api-mobile-entregadores-v3-servicos-e9d6b9g7ckfse5cj.brazilsouth-01.azurewebsites.net",
    "/entrega/":
        "https://api-mobile-entregadores-v3-servicos-cve4bshvdcf9cga4.brazilsouth-01.azurewebsites.net",
    "/maps/":
        "https://api-mobile-entregadores-v3-relatorio-f3fecmgcepdbf5hu.brazilsouth-01.azurewebsites.net",
    "/notiticacoes/":
        "https://api-mobile-entregadores-v3-relatorio-hqdmg0d7d5bfg9ar.brazilsouth-01.azurewebsites.net",
    "/relatorio/":
        "https://api-mobile-entregadores-v3-relatorio-hgc4aub7afdzemhp.brazilsouth-01.azurewebsites.net",
  };
}
