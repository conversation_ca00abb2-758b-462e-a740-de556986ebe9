# Configuração de Ícones por Flavor - Octalog

Este documento explica como configurar e gerar ícones personalizados para cada flavor do projeto.

## 📱 Visão Geral

Cada flavor (cliente) possui seu próprio ícone que será exibido no dispositivo quando o app for instalado:

- **octalog** - Logo azul/laranja (cliente padrão)
- **arcargo** - <PERSON>go verde (ArCargo)
- **connect** - <PERSON><PERSON> azul (Connect)
- **rondolog** - Logo laranja (RondoLog)

## 📁 Estrutura de Arquivos

### Configurações YAML
```
flutter_launcher_icons-octalog.yaml
flutter_launcher_icons-arcargo.yaml
flutter_launcher_icons-connect.yaml
flutter_launcher_icons-rondolog.yaml
```

### Logos dos Clientes
```
assets/images/
├── octalog/logo512.png
├── arcargo/logo512.pdn
├── connect/logo512.png
└── rondolog/logo512.png
```

### Scripts de Geração
```
scripts/
├── generate_icons.sh    # Linux/Mac
└── generate_icons.bat   # Windows
```

## 🚀 Como Usar

### 1. Geração Automática (Recomendado)

#### Linux/Mac
```bash
# Gerar ícones para um flavor específico
./scripts/generate_icons.sh octalog

# Gerar ícones para todos os flavors
./scripts/generate_icons.sh all
```

#### Windows
```cmd
# Gerar ícones para um flavor específico
scripts\generate_icons.bat octalog

# Gerar ícones para todos os flavors
scripts\generate_icons.bat all
```

### 2. Geração Manual

```bash
# Para cada flavor individualmente
flutter pub run flutter_launcher_icons:main -f flutter_launcher_icons-octalog.yaml
flutter pub run flutter_launcher_icons:main -f flutter_launcher_icons-arcargo.yaml
flutter pub run flutter_launcher_icons:main -f flutter_launcher_icons-connect.yaml
flutter pub run flutter_launcher_icons:main -f flutter_launcher_icons-rondolog.yaml
```

## 🔄 Workflow de Desenvolvimento

### Antes de Buildar um Flavor
```bash
# 1. Gerar ícones do flavor
./scripts/generate_icons.sh octalog

# 2. Buildar o flavor
flutter build apk --release --flavor octalog --dart-define=FLAVOR=octalog
```

### Durante o Desenvolvimento
```bash
# 1. Gerar ícones do flavor que está testando
./scripts/generate_icons.sh connect

# 2. Executar em debug
flutter run --flavor connect --dart-define=FLAVOR=connect
```

## 📂 Onde os Ícones são Gerados

### Android
- **Localização**: `android/app/src/main/res/mipmap-*/`
- **Formatos**: hdpi, mdpi, xhdpi, xxhdpi, xxxhdpi, anydpi-v26
- **Adaptive Icons**: Suporte para Android 8.0+

### iOS
- **Localização**: `ios/Runner/Assets.xcassets/AppIcon.appiconset/`
- **Formatos**: Todas as resoluções necessárias para iOS
- **Compatibilidade**: iPhone, iPad, Apple Watch, etc.

### Web (Opcional)
- **Localização**: `web/icons/`
- **Formatos**: PNG em várias resoluções
- **Favicon**: Atualizado automaticamente

## 🎨 Personalizando Ícones

### Para Alterar o Ícone de um Flavor

1. **Substitua o arquivo de logo**:
   ```
   assets/images/[flavor]/logo512.png
   ```

2. **Gere os novos ícones**:
   ```bash
   ./scripts/generate_icons.sh [flavor]
   ```

3. **Faça o build**:
   ```bash
   flutter build apk --flavor [flavor] --dart-define=FLAVOR=[flavor]
   ```

### Requisitos da Imagem
- **Formato**: PNG (ou PDN para arcargo)
- **Resolução**: 512x512 pixels (mínimo)
- **Qualidade**: Alta resolução para melhor resultado
- **Transparência**: Suportada (removida automaticamente no iOS)

## ⚙️ Configuração Avançada

### Personalizando as Configurações YAML

Cada arquivo `flutter_launcher_icons-[flavor].yaml` pode ser personalizado:

```yaml
flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/[flavor]/logo512.png"
  remove_alpha_ios: true
  
  # Adaptive Icons (Android 8.0+)
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/images/[flavor]/logo512.png"
  
  # iOS específico
  ios_content_mode: "scaleAspectFit"
  
  # Web (opcional)
  web:
    generate: true
    image_path: "assets/images/[flavor]/logo512.png"
    background_color: "#FFFFFF"
    theme_color: "#FF6B35"
```

### Cores por Flavor
- **octalog**: `#FF6B35` (laranja)
- **arcargo**: `#4CAF50` (verde)
- **connect**: `#2196F3` (azul)
- **rondolog**: `#FF9800` (laranja)

## 🔧 Troubleshooting

### Erro: "Arquivo de configuração não encontrado"
```bash
# Verificar se os arquivos YAML existem
ls flutter_launcher_icons-*.yaml
```

### Erro: "Imagem não encontrada"
```bash
# Verificar se os logos existem
ls assets/images/*/logo512.*
```

### Erro: "flutter_launcher_icons não instalado"
```bash
# Instalar dependências
flutter pub get
```

### Ícones não aparecem no dispositivo
1. **Limpar build**:
   ```bash
   flutter clean
   flutter pub get
   ```

2. **Regenerar ícones**:
   ```bash
   ./scripts/generate_icons.sh [flavor]
   ```

3. **Rebuild completo**:
   ```bash
   flutter build apk --flavor [flavor] --dart-define=FLAVOR=[flavor]
   ```

### Problemas específicos do arcargo
- O arcargo usa arquivos `.pdn` (Paint.NET)
- Certifique-se de que o flutter_launcher_icons suporta este formato
- Se necessário, converta para `.png`

## 📝 Notas Importantes

1. **Não versionar ícones gerados**: Os diretórios de ícones estão no `.gitignore`
2. **Gerar antes do build**: Sempre gere os ícones antes de fazer o build de um flavor
3. **Testar em dispositivo real**: Ícones podem aparecer diferentes no emulador vs dispositivo
4. **Backup dos logos**: Mantenha backup dos arquivos originais de logo
5. **Compatibilidade**: Os ícones são compatíveis com `flutter run --flavor` e `flutter build --flavor`

## 🆘 Suporte

Para problemas relacionados aos ícones:
1. Verifique este README
2. Consulte a documentação do [flutter_launcher_icons](https://pub.dev/packages/flutter_launcher_icons)
3. Verifique os logs de erro ao executar os scripts
