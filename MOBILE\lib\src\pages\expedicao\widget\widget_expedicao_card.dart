import 'package:flutter/material.dart';
import 'package:octalog/src/pages/expedicao/model/expedicao_model.dart';
import 'package:octalog/src/utils/extesion.dart';

import '../../../utils/theme_colors.dart';

class ExpedicaoCard extends StatefulWidget {
  final EntregasListaModel expedicao;
  const ExpedicaoCard({
    super.key,
    required this.expedicao,
  });

  @override
  State<ExpedicaoCard> createState() => _ExpedicaoCardState();
}

class _ExpedicaoCardState extends State<ExpedicaoCard> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 8,
          vertical: 2,
        ),
        child: Column(
          children: [
            Theme(
              data: ThemeData().copyWith(dividerColor: Colors.transparent),
              child: ExpansionTile(
                iconColor: ThemeColors.customBlack(context),
                textColor: ThemeColors.customBlack(context),
                tilePadding: EdgeInsets.zero,
                title: Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: ThemeColors.customOrange(context),
                        borderRadius: BorderRadius.circular(50),
                      ),
                      padding: const EdgeInsets.all(1),
                      child: CircleAvatar(
                        backgroundColor: ThemeColors.customWhite(context),
                        child: Text(
                          "${widget.expedicao.data.day}/${widget.expedicao.data.month}",
                          style: TextStyle(
                            color: ThemeColors.customOrange(context),
                            fontWeight: FontWeight.bold,
                            fontSize: 13,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${widget.expedicao.entregaConcluidas} Entregas concluídas',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        Text('${widget.expedicao.negativas} negativas'),
                      ],
                    ),
                  ],
                ),
                children: [
                  SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Column(
                      children: [
                        ...List.generate(
                          widget.expedicao.enderecos.length,
                          (index) {
                            final item = widget.expedicao.enderecos[index];
                            return Column(
                              children: [
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      item.dataTermino.dataFormatada,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 15,
                                      ),
                                    ),
                                    const SizedBox(width: 3),
                                    const Center(
                                      child: SizedBox(
                                        width: 12,
                                        // child: Divider(
                                        //   color: Colors.black,
                                        // ),
                                      ),
                                    ),
                                    const SizedBox(width: 3),
                                    Expanded(
                                      child: Text(
                                        item.endereco.toUpperCase(),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 15,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                  ],
                                ),
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 5,
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      ...List.generate(
                                        item.pedidos.length,
                                        (index) {
                                          final entrega = item.pedidos[index];
                                          return Row(
                                            children: [
                                              const SizedBox(width: 44),
                                              Container(
                                                width: 1,
                                                height: 20,
                                                color: Colors.black,
                                              ),
                                              const SizedBox(width: 10),
                                              Expanded(
                                                child: Text(
                                                  '${entrega.os} - ${entrega.nomeCliente} - ${entrega.local} - ${entrega.volumes} VOLUMES'
                                                      .toUpperCase(),
                                                  maxLines: 1,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  style: const TextStyle(
                                                    fontSize: 15,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
