import 'package:flutter/material.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import "../../../utils/colors.dart";
import '../fcm_alert_dialog_store.dart';
import '../models/fcm_alert_dados.dart';

class CardPedidoDeletCustom extends StatefulWidget {
  final FcmAlertDialogStore store;
  final String nomeCliente;
  final String endereco;
  final List<FcmPedido> pedidos;
  final int coletados;
  const CardPedidoDeletCustom({
    super.key,
    required this.store,
    required this.nomeCliente,
    required this.endereco,
    required this.pedidos,
    required this.coletados,
  });

  @override
  State<CardPedidoDeletCustom> createState() => _CardPedidoDeletCustommState();
}

class _CardPedidoDeletCustommState extends State<CardPedidoDeletCustom> {
  bool onExpansion = false;
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      color: ColorsCustom.customBlue,
      shape:  RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(0),
        ),
        side: BorderSide(
          width: 2,
          color: ThemeColors.customOrange(context),
        ),
      ),
      child: Theme(
        data: ThemeData(
          dividerColor: Colors.transparent,
          colorScheme: ColorScheme.fromSwatch().copyWith(),
        ).copyWith(
          dividerColor: Colors.transparent,
        ),
        child: ExpansionTile(
          onExpansionChanged: (value) {
            setState(() {
              onExpansion = value;
            });
          },
          trailing: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                decoration: BoxDecoration(
                  //color: const Color.fromARGB(255, 253, 203, 178),
                  borderRadius: BorderRadius.circular(5),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 5,
                  vertical: 2,
                ),
                child: Text(
                  widget.coletados > 1
                      ? '${widget.coletados} Pedidos'
                      : '${widget.coletados} Pedido',
                  style: const TextStyle(
                    fontSize: 10,
                    color: Color(0xFF757474),
                    fontWeight: FontWeight.w800,
                  ),
                ),
              ),
              Icon(
                onExpansion ? Icons.expand_less : Icons.expand_more,
                color: ThemeColors.customOrange(context),
              )
            ],
          ),
          title: Text(
            widget.nomeCliente,
            style:  TextStyle(
              fontSize: 16.0,
              fontWeight: FontWeight.w700,
              color: ThemeColors.customOrange(context),
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 8,
              ),
              Text(
                widget.endereco,
                style: TextStyle(
                  fontSize: 14.0,
                  fontWeight: FontWeight.w500,
                  color: ColorsCustom.customBlack.withOpacity(0.6),
                ),
              ),
            ],
          ),
          children: <Widget>[
            const Padding(
              padding: EdgeInsets.symmetric(
                horizontal: 15.0,
              ),
              child: SizedBox(
                width: double.infinity,
                child: Divider(
                  color: ColorsCustom.customGrey,
                  thickness: 0.5,
                ),
              ),
            ),
            if (widget.pedidos.length > 1)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15.0),
                child: GestureDetector(
                  onTap: () async {
                    for (var i = 0; i < widget.pedidos.length; i++) {
                      widget.store.deletarPedidoCodigo(
                        widget.pedidos[i],
                      );
                    }
                  },
                  child: const SizedBox(
                    height: 30,
                    child: Row(
                      children: [
                        SizedBox(
                            width: 30,
                            child: Icon(
                              Icons.delete,
                              color: Color.fromARGB(255, 247, 91, 125),
                              size: 23,
                            )),
                        Text(
                          'Excluir todos',
                          style: TextStyle(
                            fontSize: 15,
                            letterSpacing: 0.5,
                            color: Color(0xFF534845),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ...List.generate(
              widget.pedidos.length,
              (index) => Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 15.0,
                  vertical: 3.0,
                ),
                child: GestureDetector(
                  onTap: () async {
                    widget.store.deletarPedidoCodigo(
                      widget.pedidos[index],
                    );
                  },
                  child: Row(
                    children: [
                      const SizedBox(
                        width: 30,
                        child: Icon(
                          Icons.delete,
                          color: Color.fromARGB(255, 247, 91, 125),
                          size: 23,
                        ),
                      ),
                      Text(
                        widget.pedidos[index].os,
                        style: const TextStyle(
                          fontSize: 16.0,
                          fontWeight: FontWeight.w500,
                          color: ColorsCustom.customBlack,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            )
          ],
        ),
      ),
    );
  }
}
