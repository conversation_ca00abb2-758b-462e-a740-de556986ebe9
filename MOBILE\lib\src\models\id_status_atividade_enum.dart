enum IdStatusAtividadeEnum {
  entrega(
    title: '',
    id: 4,
    isEntrega: true,
    headTitle: 'para entrega',
    headTitlePlu: 'para entrega',
  ),
  negativa(
    title: 'Negativas',
    id: 5,
    isEntrega: true,
    headTitle: 'Negativa',
    headTitlePlu: 'Negativa',
  ),
  entregue(
    title: 'Entregue',
    id: 10,
    isEntrega: false,
    headTitle: 'entregue',
    headTitlePlu: 'entregues',
  ),
  cancelada(
    title: 'Cancelada',
    id: 7,
    isEntrega: false,
    headTitle: 'cancelado',
    headTitlePlu: 'cancelados',
  );

  final String title;
  final int id;
  final bool isEntrega;
  final String headTitle;
  final String headTitlePlu;

  const IdStatusAtividadeEnum({
    required this.title,
    required this.id,
    required this.isEntrega,
    required this.headTitle,
    required this.headTitlePlu,
  });
}

bool get isEntrega => IdStatusAtividadeEnum.entrega.isEntrega;

String getIdStatusAtividadeEnumName(int index) {
  switch (index) {
    case 0:
      return "Entregas";
    case 1:
      return "Negativas";
    case 2:
      return "Entregues";
    case 3:
      return "Canceladas";
    default:
      return "";
  }
}

IdStatusAtividadeEnum getIdStatusAtividadeEnum(int idStatusAtividade) {
  if ([4, 21, 28, 29].contains(idStatusAtividade)) {
    return IdStatusAtividadeEnum.entrega;
  } else if ([5, 6, 13, 14, 16, 20].contains(idStatusAtividade)) {
    return IdStatusAtividadeEnum.negativa;
  } else if ([10, 11, 22, 24, 35].contains(idStatusAtividade)) {
    return IdStatusAtividadeEnum.entregue;
  } else if ([7, 8, 9, 12, 15, 19, 31, 32, 33, 34]
      .contains(idStatusAtividade)) {
    return IdStatusAtividadeEnum.cancelada;
  } else {
    return IdStatusAtividadeEnum.entrega;
  }
}

String getNameSituacao(int id) {
  switch (id) {
    case 7:
      return 'Pedido Danificado';
    case 9:
      return 'Pedido Extraviado';
    case 14:
      return 'Endereço Não Localizado';
    case 20:
      return 'Número não localizado';
    case 15:
      return 'Cancelado pelo cliente';
    case 6:
      return 'Cliente Ausente';
    case 16:
      return 'Cliente Inexistente';
    case 19:
      return 'Devolvido';
    case 13:
      return 'Estabelecimento Fechado';
    case 12:
      return 'Pedido Recusado';
    default:
      return 'Situação não encontrada';
  }
}
