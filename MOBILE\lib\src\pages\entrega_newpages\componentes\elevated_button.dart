import 'package:asuka/asuka.dart' as asuka;
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/utils/theme_colors.dart';
class ElevatedLsButton extends StatelessWidget {
  final String text;
  final Function()? onPressed;
  final bool isLoading;
  final String? message;

  const ElevatedLsButton(
      {super.key,
      required this.text,
      this.onPressed,
      required this.isLoading,
      this.message});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 56,
      child: ElevatedButton(
        style: ButtonStyle(
          // backgroundColor: WidgetStateProperty.all<Color>(
          //   ThemeColors.customOrange(context),
          // ),
          backgroundColor:
              WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey;
            }
            return ThemeColors.customOrange(context);
          }),
          // shape: WidgetStateProperty.all<OutlinedBorder>(
          //   RoundedRectangleBorder(
          //     borderRadius: BorderRadius.circular(8),
          //   ),
          // ),
          shape: WidgetStateProperty.all<OutlinedBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        onPressed: funcIfDisabled(
          context,
          message,
          isLoading,
          onPressed,
        ),
        child: SizedBox(
          height: 45,
          width: MediaQuery.of(context).size.width * 0.75,
          child: Center(
            child: Text(
              text,
              style: GoogleFonts.roboto(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: ThemeColors.customWhite(context),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

Function()? funcIfDisabled(
    context, String? message, bool isLoading, Function()? onPressed) {
  if (isLoading) {
    return null;
  }
  if (message != null) {
    return () {
      // ignore: deprecated_member_use
      asuka.Asuka.showDialog(
        barrierColor: Colors.black.withOpacity(0.8),
        builder: (context) => AlertDialog(
          title: const Text('Atenção'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'CONFIRMAR',
                style: TextStyle(color: ThemeColors.customOrange(context)),
              ),
            )
          ],
        ),
      );
    };
  }
  return onPressed;
}
