<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\android\app\src\main\res"><file name="launch_background" path="C:\projetos\octa.log\MOBILE\android\app\src\main\res\drawable\launch_background.xml" qualifiers="" type="drawable"/><file name="launch_background" path="C:\projetos\octa.log\MOBILE\android\app\src\main\res\drawable-v21\launch_background.xml" qualifiers="v21" type="drawable"/><file name="ic_launcher" path="C:\projetos\octa.log\MOBILE\android\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="launcher_icon" path="C:\projetos\octa.log\MOBILE\android\app\src\main\res\mipmap-hdpi\launcher_icon.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\projetos\octa.log\MOBILE\android\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="launcher_icon" path="C:\projetos\octa.log\MOBILE\android\app\src\main\res\mipmap-mdpi\launcher_icon.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\projetos\octa.log\MOBILE\android\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="launcher_icon" path="C:\projetos\octa.log\MOBILE\android\app\src\main\res\mipmap-xhdpi\launcher_icon.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\projetos\octa.log\MOBILE\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="launcher_icon" path="C:\projetos\octa.log\MOBILE\android\app\src\main\res\mipmap-xxhdpi\launcher_icon.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\projetos\octa.log\MOBILE\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="launcher_icon" path="C:\projetos\octa.log\MOBILE\android\app\src\main\res\mipmap-xxxhdpi\launcher_icon.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\projetos\octa.log\MOBILE\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file path="C:\projetos\octa.log\MOBILE\android\app\src\main\res\values-night\styles.xml" qualifiers="night-v8"><style name="LaunchTheme" parent="@android:style/Theme.Black.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="spotlog$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\android\app\src\spotlog\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="spotlog" generated-set="spotlog$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\android\app\src\spotlog\res"><file name="ic_launcher_foreground" path="C:\projetos\octa.log\MOBILE\android\app\src\spotlog\res\drawable-hdpi\ic_launcher_foreground.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_launcher_foreground" path="C:\projetos\octa.log\MOBILE\android\app\src\spotlog\res\drawable-mdpi\ic_launcher_foreground.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_launcher_foreground" path="C:\projetos\octa.log\MOBILE\android\app\src\spotlog\res\drawable-xhdpi\ic_launcher_foreground.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_launcher_foreground" path="C:\projetos\octa.log\MOBILE\android\app\src\spotlog\res\drawable-xxhdpi\ic_launcher_foreground.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_launcher_foreground" path="C:\projetos\octa.log\MOBILE\android\app\src\spotlog\res\drawable-xxxhdpi\ic_launcher_foreground.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="launcher_icon" path="C:\projetos\octa.log\MOBILE\android\app\src\spotlog\res\mipmap-anydpi-v26\launcher_icon.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="launcher_icon" path="C:\projetos\octa.log\MOBILE\android\app\src\spotlog\res\mipmap-hdpi\launcher_icon.png" qualifiers="hdpi-v4" type="mipmap"/><file name="launcher_icon" path="C:\projetos\octa.log\MOBILE\android\app\src\spotlog\res\mipmap-mdpi\launcher_icon.png" qualifiers="mdpi-v4" type="mipmap"/><file name="launcher_icon" path="C:\projetos\octa.log\MOBILE\android\app\src\spotlog\res\mipmap-xhdpi\launcher_icon.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="launcher_icon" path="C:\projetos\octa.log\MOBILE\android\app\src\spotlog\res\mipmap-xxhdpi\launcher_icon.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="launcher_icon" path="C:\projetos\octa.log\MOBILE\android\app\src\spotlog\res\mipmap-xxxhdpi\launcher_icon.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\projetos\octa.log\MOBILE\android\app\src\spotlog\res\values\colors.xml" qualifiers=""><color name="ic_launcher_background">#FFFFFF</color></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\app\generated\res\resValues\spotlog\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\build\app\generated\res\resValues\spotlog\debug"><file path="C:\projetos\octa.log\MOBILE\build\app\generated\res\resValues\spotlog\debug\values\gradleResValues.xml" qualifiers=""><string name="app_name" translatable="false">Spot Log</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="variant$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\android\app\src\spotlogDebug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="variant" generated-set="variant$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\android\app\src\spotlogDebug\res"/></dataSet><mergedItems/></merger>