import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/database/excluir_atividade/excluir_atividade_repository.dart';
import 'package:octalog/src/models_new/endereco_new.dart';
import 'package:octalog/src/utils/theme_colors.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../database/atividades_endereco/atividades_endereco_excluir.dart';
import '../../../utils/theme_colors.dart';
import '../../entrega_newpages/controller/entrega_new_store.dart';
import '../home_controller.dart';

class ShowModalHome extends StatefulWidget {
  final EnderecoNew atividade;
  final HomeController controller;
  final EntregaNewStore storeEntrega;

  const ShowModalHome(
      {super.key,
      required this.atividade,
      required this.controller,
      required this.storeEntrega});

  @override
  State<ShowModalHome> createState() => _ShowModalHomeState();
}

class _ShowModalHomeState extends State<ShowModalHome> {
  List<EnderecoNew> enderecosList = <EnderecoNew>[];
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: SizedBox(
        height: MediaQuery.of(context).size.height * 0.55,
        width: MediaQuery.of(context).size.width,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(
              height: 8,
            ),
            Container(
              decoration: const BoxDecoration(
                color: Colors.grey,
                borderRadius: BorderRadius.all(
                  Radius.circular(50),
                ),
              ),
              height: 5,
              width: 50,
            ),
            const SizedBox(
              height: 30,
            ),
            Padding(
              padding: const EdgeInsets.only(
                left: 30,
                right: 30,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.atividade.enderecoFormatado,
                    style: GoogleFonts.roboto(
                      fontSize: 18,
                      color: ThemeColors.customBlack(context),
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 7,
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Divider(
                    color: Colors.grey.shade400,
                    thickness: 1,
                  ),
                  TextButton.icon(
                    label: Text(
                      'Endereço principal',
                      style: GoogleFonts.roboto(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    icon: const Icon(
                      Icons.location_on_rounded,
                      color: Colors.orange,
                      size: 25,
                    ),
                    onPressed: () async {
                      final dest = [widget.atividade.enderecoCompleto];
                      final url =
                          Uri.parse('google.search:q=${dest.join(',')}');
                      final fallbackUrl = Uri.parse(
                          'https://www.google.com/search?q=${dest.join(',')}');
                      if (await canLaunchUrl(url)) {
                        await launchUrl(url);
                      } else {
                        await launchUrl(fallbackUrl);
                      }
                    },
                  ),
                  widget.atividade.excluir == false
                      ? const SizedBox()
                      : TextButton.icon(
                          label: Text(
                            'Excluir',
                            style: GoogleFonts.roboto(
                              fontSize: 15,
                              color: ThemeColors.customBlack(context),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          icon: const Icon(
                            Icons.delete,
                            color: Colors.red,
                            size: 25,
                          ),
                          onPressed: () {
                            showDialog(
                                context: context,
                                builder: (context) {
                                  return AlertDialog(
                                    backgroundColor:
                                        const Color.fromRGBO(230, 230, 230, 1),
                                    title: const Text('Excluir endereço'),
                                    content: const Text(
                                      'Você deseja excluir esse endereço?',
                                    ),
                                    actions: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          TextButton(
                                            onPressed: () {
                                              Navigator.of(context).pop();
                                            },
                                            child: Text(
                                              'DESISTIR',
                                              style: TextStyle(
                                                color: Colors.grey.shade800,
                                              ),
                                            ),
                                          ),
                                          TextButton(
                                            onPressed: () {
                                              if (widget.atividade.excluir ==
                                                  true) {
                                                _deleteMeuEndereco(
                                                        widget.atividade)
                                                    .then((value) {
                                                  widget.storeEntrega
                                                      .limparBanco();
                                                });
                                                widget.controller
                                                    .fetchAtividades();
                                              }
                                              Navigator.of(context).pop();
                                            },
                                            child: const Text(
                                              'CONTINUAR',
                                              style: TextStyle(
                                                color: Colors.orange,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  );
                                });
                            Navigator.of(context).pop();
                          },
                        ),
                ],
              ),
            ),
            widget.atividade.excluir == true
                ? const SizedBox()
                : const SizedBox(
                    height: 20,
                  ),
            // Padding(
            //   padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 30),
            //   child: SizedBox(
            //     child: ButtonLsCustom(
            //       onPressed: () async {
            //         Navigator.of(context).pop();
            //         final responseConfig =
            //             await ConfigDatabase.instance.getConfig();
            //         final fone = responseConfig.foneSac;
            //         launchUrl(Uri.parse('tel://$fone'));
            //       },
            //       text: 'SAC',
            //       colorBackground: Colors.blue,
            //     ),
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  Future _deleteMeuEndereco(EnderecoNew enderecoNew) async {
    getAtividadeEndereco().then((value) {
      setState(() {
        final int indexOf = enderecosList.indexOf(enderecoNew);
        enderecosList.removeAt(indexOf);
      });
    });

    final response =
        await DeleteAtividade().delete(enderecoNew.idEnderecoCliente);
    return response;
  }
}
