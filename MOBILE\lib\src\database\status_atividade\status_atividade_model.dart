import 'package:map_fields/map_fields.dart';

import '../../models/status_atividades.dart';

class StatusAtividadesModelHive {
  final List<StatusAtividadesChildModelHive> ocorrenciaInicio;
  final List<StatusAtividadesChildModelHive> ocorrenciaChegada;
  final List<StatusAtividadesChildModelHive> problemaEntrega;

  StatusAtividadesModelHive({
    required this.ocorrenciaInicio,
    required this.ocorrenciaChegada,
    required this.problemaEntrega,
  });

  factory StatusAtividadesModelHive.fromJson(Map<String, dynamic> json) {
    final f = MapFields.load(json);
    return StatusAtividadesModelHive(
      ocorrenciaInicio: f
          .getList<Map<String, dynamic>>('OcorrenciaInicio')
          .map((e) => StatusAtividadesChildModelHive.fromJson(e))
          .toList(),
      ocorrenciaChegada: f
          .getList<Map<String, dynamic>>('OcorrenciaChegada')
          .map((e) => StatusAtividadesChildModelHive.fromJson(e))
          .toList(),
      problemaEntrega: f
          .getList<Map<String, dynamic>>('ProblemaEntrega')
          .map((e) => StatusAtividadesChildModelHive.fromJson(e))
          .toList(),
    );
  }

  StatusAtividades toStatusAtividade() {
    return StatusAtividades(
      ocorrenciasInicio:
          ocorrenciaInicio.map((e) => e.toStatusAtividade()).toList(),
      ocorrenciasChegada:
          ocorrenciaChegada.map((e) => e.toStatusAtividade()).toList(),
      problemasEntregas:
          problemaEntrega.map((e) => e.toStatusAtividade()).toList(),
    );
  }

  factory StatusAtividadesModelHive.fromHiveMap(Map<String, dynamic> map) {
    final m = MapFields.load(map);
    return StatusAtividadesModelHive(
      ocorrenciaChegada: m
          .getList<Map<String, dynamic>>('ocorrenciaChegada')
          .map((e) => StatusAtividadesChildModelHive.fromHiveMap(e))
          .toList(),
      ocorrenciaInicio: m
          .getList<Map<String, dynamic>>('ocorrenciaInicio')
          .map((e) => StatusAtividadesChildModelHive.fromHiveMap(e))
          .toList(),
      problemaEntrega: m
          .getList<Map<String, dynamic>>('problemaEntrega')
          .map((e) => StatusAtividadesChildModelHive.fromHiveMap(e))
          .toList(),
    );
  }

  Map<String, dynamic> toHiveMap() {
    return {
      'ocorrenciaInicio': ocorrenciaInicio.map((e) => e.toHiveMap()).toList(),
      'ocorrenciaChegada': ocorrenciaChegada.map((e) => e.toHiveMap()).toList(),
      'problemaEntrega': problemaEntrega.map((e) => e.toHiveMap()).toList(),
    };
  }
}

class StatusAtividadesChildModelHive {
  final int id;
  final String nome;
  final bool finalizador;
  final bool receita;
  final bool acareacao;
  final bool sacObrigatorio;

  StatusAtividadesChildModelHive({
    required this.id,
    required this.nome,
    required this.finalizador,
    required this.receita,
    required this.acareacao,
    required this.sacObrigatorio,
  });

  factory StatusAtividadesChildModelHive.fromJson(Map<String, dynamic> json) {
    final f = MapFields.load(json);

    return StatusAtividadesChildModelHive(
      id: f.getInt('IDStatusAtividade', -1),
      nome: f.getString('Nome', ''),
      finalizador: f.getBool('Finalizador', false),
      receita: f.getBool('Receita', false),
      acareacao: f.getBool('Acareacao', false),
      sacObrigatorio: f.getBool('SacObrigatorio', false),
    );
  }

  StatusAtividadesChild toStatusAtividade() {
    return StatusAtividadesChild(
      idStatusAtividade: id,
      nome: nome,
      finalizador: finalizador,
      receita: receita,
      acareacao: acareacao,
      sacObrigatorio: sacObrigatorio,
    );
  }

  factory StatusAtividadesChildModelHive.fromHiveMap(Map<String, dynamic> map) {
    final m = MapFields.load(map);
    return StatusAtividadesChildModelHive(
      id: m.getInt('id', -1),
      nome: m.getString('nome', ''),
      finalizador: m.getBool('finalizador', false),
      receita: m.getBool('receita', false),
      acareacao: m.getBool('acareacao', false),
      sacObrigatorio: m.getBool('sacObrigatorio', false),
    );
  }

  Map<String, dynamic> toHiveMap() {
    return {
      'id': id,
      'nome': nome,
      'finalizador': finalizador,
      'receita': receita,
      'acareacao': acareacao,
      'sacObrigatorio': sacObrigatorio,
    };
  }
}
