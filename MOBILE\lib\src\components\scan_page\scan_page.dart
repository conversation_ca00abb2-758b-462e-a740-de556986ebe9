import 'package:flutter/material.dart';
//import 'package:flutter_beep/flutter_beep.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:octalog/src/utils/theme_colors.dart';

class ScanPage extends StatefulWidget {
  final String? title;
  const ScanPage({super.key, this.title});
  @override
  State<ScanPage> createState() => _ScanPageState();
}

class _ScanPageState extends State<ScanPage> {
  static const int maxLeituras = 10;
  String codigoLido = '';
  int lidos = 0;

  void leitura(String codigo) {
    if (codigoLido == codigo) {
      lidos++;
      if (lidos == maxLeituras) {
        copiarCodigo();
      }
      setState(() {});
    } else {
      lidos = 1;
      codigoLido = codigo;
    }
  }

  Future<void> copiarCodigo() async {
    final codigoLido = this.codigoLido;

    //FlutterBeep.beep();
    if (Navigator.of(context).canPop()) {
      controller.stop();
      Navigator.pop(context, codigoLido.toString());
    }
  }

  String mensagem = 'Nenhum código copiado';

  bool pause = false;

  MobileScannerController controller = MobileScannerController();
  bool isFlashOn = false;
  @override
  void initState() {
    if (widget.title != null) {
      showSnackBar(widget.title!);
    }
    controller.start();

    super.initState();
  }

  showSnackBar(String mensagem) {
    Future.delayed(const Duration(seconds: 1), () {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(duration: const Duration(seconds: 5), content: Text(mensagem), action: SnackBarAction(label: 'OK', onPressed: () {})));
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Faça a leitura do código de barras'),
        centerTitle: true,
            backgroundColor: ThemeColors.customOrange(context),
        actions: [
          IconButton(
            color: Colors.white,
            icon: Builder(
              builder: (context) {
                final icon = isFlashOn ? Icons.flash_on : Icons.flash_off;
                return Icon(icon);
              },
            ),
            iconSize: 32.0,
            onPressed: () => controller.toggleTorch(),
          ),
        ],
      ),
      body: Stack(
        children: [
          MobileScanner(
            controller: controller,
            fit: BoxFit.cover,
            onDetect: (barcode) async {
              if (pause) {
                return;
              }

              leitura(barcode.barcodes.first.rawValue ?? '');
            },
          ),
          detailsCam(),
          const Animation2(),
        ],
      ),
    );
  }
}

class Animation2 extends StatefulWidget {
  const Animation2({super.key});

  @override
  State<Animation2> createState() => _Animation2State();
}

class _Animation2State extends State<Animation2> {
  bool subindo = false;
  static const int duration = 2;

  Future<void> loop() async {
    while (true) {
      setState(() {
        subindo = !subindo;
      });
      await Future.delayed(const Duration(seconds: duration));
    }
  }

  @override
  void initState() {
    //loop();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      color: Colors.red,
      duration: const Duration(seconds: duration),
      curve: Curves.linear,
      width: 2,
      height: MediaQuery.of(context).size.height - 16,
      margin: EdgeInsets.only(left: subindo ? MediaQuery.of(context).size.width - 16 : 16, top: 16, bottom: 16),
    );
  }
}

Widget detailsCam() {
  return const Stack(
    children: [
      Positioned(top: 0, left: 6.5, child: SizedBox(width: 30, child: Divider(color: Colors.red, thickness: 3))),
      Positioned(top: 9, child: SizedBox(height: 30, child: VerticalDivider(color: Colors.red, thickness: 3))),
      Positioned(top: 0, right: 6.5, child: SizedBox(width: 30, child: Divider(color: Colors.red, thickness: 3))),
      Positioned(top: 9, right: 0, child: SizedBox(height: 30, child: VerticalDivider(color: Colors.red, thickness: 3))),
      Positioned(bottom: 0, left: 6.5, child: SizedBox(width: 30, child: Divider(color: Colors.red, thickness: 3))),
      Positioned(bottom: 9, child: SizedBox(height: 30, child: VerticalDivider(color: Colors.red, thickness: 3))),
      Positioned(bottom: 0, right: 6.5, child: SizedBox(width: 30, child: Divider(color: Colors.red, thickness: 3))),
      Positioned(bottom: 9, right: 0, child: SizedBox(height: 30, child: VerticalDivider(color: Colors.red, thickness: 3))),
    ],
  );
}
