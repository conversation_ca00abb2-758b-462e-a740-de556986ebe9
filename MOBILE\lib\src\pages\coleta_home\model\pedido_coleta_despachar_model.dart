import 'package:map_fields/map_fields.dart';

class PedidoColetaDespachar {
  final int idos;
  final String os;
  late String cliente;
  final String enderecoCompleto;

  PedidoColetaDespachar({
    required this.idos,
    required this.os,
    required this.cliente,
    required this.enderecoCompleto,
  });

  factory PedidoColetaDespachar.fromMapKlev(Map<String, dynamic> map) {
    final f = MapFields.load(map);
    return PedidoColetaDespachar(
      idos: f.getInt('IDOS', 0),
      os: f.getString('OS', '...'),
      cliente: f.getString('Cliente', 'Consumidor final'),
      enderecoCompleto: f.getString('EnderecoCompleto', 'Endereço na etiqueta'),
    );
  }
}
