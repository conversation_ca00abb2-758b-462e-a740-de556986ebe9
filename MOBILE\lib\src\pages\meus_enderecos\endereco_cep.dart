import 'package:asuka/asuka.dart' as asuka;
import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/database/meus_enderecos/buscar_cep.dart';
import 'package:octalog/src/database/meus_enderecos/novo_endereco_repository.dart';
import 'package:octalog/src/models/novo_endereco_model.dart';
import 'package:octalog/src/pages/home/<USER>/custom_enum_navigation.dart';
import 'package:octalog/src/pages/meus_enderecos/textfield_cep.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../components/buttom_custom/button_custom.dart';
import '../../components/fcm_alert_dailog/components/fcm_appbar_custom.dart';

import '../home/<USER>';

class EnderecoCep extends StatefulWidget {
  final HomeController controller;
  const EnderecoCep({super.key, required this.controller});

  @override
  State<EnderecoCep> createState() => _EnderecoCepState();
}

class _EnderecoCepState extends State<EnderecoCep> {
  final _formKey = GlobalKey<FormState>();
  bool _enableField = true;
  String _cep = '';
  int lTd = 0;
  int lgt = 0;
  late FocusNode myFocusNode;
  final data = DateTime.now().toString();

  final _cepController = TextEditingController();
  final _logradouroController = TextEditingController();
  final _bairroController = TextEditingController();
  final _cidadeController = TextEditingController();
  final _estadoController = TextEditingController();
  final _numeroController = TextEditingController();
  final _apelidoController = TextEditingController();

  @override
  void initState() {
    myFocusNode = FocusNode();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    myFocusNode.dispose();
    _cepController.clear();
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardVisibilityBuilder(
      builder: (context, isKeyboardVisible) {
        return SafeArea(
          child: Scaffold(
            backgroundColor: const Color.fromARGB(255, 247, 247, 247),
            floatingActionButton: _estadoController.text.isNotEmpty
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: MediaQuery.of(context).size.width / 14,
                      ),
                      SizedBox(
                        width: MediaQuery.of(context).size.width / 1.4,
                        child: ButtonLsCustom(
                          text: 'SALVAR',
                          onPressed: () async {
                            if (_formKey.currentState!.validate()) {
                              await NovoEnderecoRepository().sendNovoEndereco(
                                NovoEnderecoModel(
                                  apelido: _apelidoController.text,
                                  endereco: _logradouroController.text,
                                  numero: _numeroController.text,
                                  bairro: _bairroController.text,
                                  cidade: _cidadeController.text,
                                  cep: _cepController.text,
                                  uf: _estadoController.text,
                                ),
                              );
                              Navigator.pop(context);
                              widget.controller.setPageSelected(
                                HomeNavigationEnum.routing,
                              );
                            }
                          },
                        ),
                      ),
                    ],
                  )
                : null,
            body: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: <Widget>[
                  const Padding(
                    padding: EdgeInsets.all(10.0),
                    child: FcmAppBarCustom(
                      title: 'Novo Endereço',
                      isBack: true,
                    ),
                  ),
                  SizedBox(
                    height: 80,
                    width: MediaQuery.of(context).size.width * 0.80,
                    child: TextFormField(
                      maxLength: 8,
                      onChanged: ((value) async {
                        if (_cepController.text.length == 8) {
                          await _buscarCep();
                        }
                      }),
                      controller: _cepController,
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      autofocus: true,
                      autocorrect: false,
                      textInputAction: TextInputAction.next,
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsets.fromLTRB(5, 22, 0, 0),
                        hintText: 'Digite o endereço ou CEP',
                        hintStyle: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                        ),
                        focusedBorder:  UnderlineInputBorder(
                          borderSide:
                              BorderSide(color: ThemeColors.customGrey(context)),
                        ),
                        suffixIcon: IconButton(
                          icon: Icon(
                            _cepController.text.length == 8
                                ? Icons.clear
                                : Icons.location_pin,
                            color: ThemeColors.customOrange(context),
                          ),
                          onPressed: () {
                            if (_cepController.text.length == 8) {
                              setState(() {
                                _cepController.clear();
                              });
                            }
                          },
                        ),
                      ),
                    ),
                  ),
                  _buildResultForm(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _buscando(bool enable) {
    setState(() {
      _cep = enable ? '' : _cep;
      _enableField = !enable;
    });
  }

  Future _buscarCep() async {
    try {
      _buscando(true);

      final cep = _cepController.text;

      final response = await BuscarCep().fetchMeusEnd(cep);

      setState(() {
        _logradouroController.text = response.logradouro;
        _bairroController.text = response.bairro;
        _cidadeController.text = response.cidade;
        _estadoController.text = response.estado;
      });
      _buscando(false);
      FocusScope.of(context).requestFocus(FocusNode());
    } catch (_) {
      FocusScope.of(context).requestFocus(FocusNode());
      asuka.Asuka.showDialog(
        builder: (context) => AlertDialog(
          title: const Text('CEP informado não e válido'),
          content:
              const Text('Verifique se digitou o cep completo e\ncorretamente'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('OK'),
            ),
          ],
        ),
      );
      _buscando(false);
    }
  }

  Widget _buildResultForm() {
    return Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
        padding: const EdgeInsets.only(left: 20, top: 20, right: 20),
        child: Builder(
          builder: (_) {
            if (_estadoController.text.isEmpty) {
              return Container();
            }
            return Form(
              key: _formKey,
              child: Column(
                children: [
                  card('Endereço:', _logradouroController.text),
                  const Divider(
                    height: 10,
                  ),
                  card('Bairro:', _bairroController.text),
                  const Divider(
                    height: 10,
                  ),
                  card('Cidade:', _cidadeController.text),
                  const Divider(
                    height: 10,
                  ),
                  card('Estado:', _estadoController.text),
                  const Divider(
                    height: 10,
                  ),
                  Row(
                    children: [
                      TextFiledCustom(
                        focusNode: myFocusNode,
                        enabled: _enableField,
                        controller: _numeroController,
                        keyboardType: TextInputType.number,
                        label: 'Número',
                        validator: (String? value) {
                          if (value == null || value.isEmpty) {
                            return 'Campo obrigatório';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Row(
                    children: [
                      TextFiledCustom(
                        controller: _apelidoController,
                        keyboardType: TextInputType.text,
                        label: 'EX: Casa, Trabalho, etc',
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget card(String titule, String subtitle) {
    return Padding(
      padding: const EdgeInsets.only(top: 10, bottom: 10),
      child: Row(
        children: [
          Text(
            titule,
            style: GoogleFonts.roboto(
              fontSize: 20,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(
            width: 10,
          ),
          Text(
            subtitle,
            style: GoogleFonts.roboto(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }
}
