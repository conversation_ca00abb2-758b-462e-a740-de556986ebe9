@echo off
setlocal enabledelayedexpansion

REM Script para configurar flavors no iOS
REM Uso: configure_ios_flavor.bat [flavor] [build_mode]
REM Exemplo: configure_ios_flavor.bat up360 debug

set FLAVOR=%1
set BUILD_MODE=%2

if "%FLAVOR%"=="" set FLAVOR=octalog
if "%BUILD_MODE%"=="" set BUILD_MODE=debug

echo 🍎 Configurando iOS para flavor: %FLAVOR%
echo 📱 Modo de build: %BUILD_MODE%

REM Validar flavor
if "%FLAVOR%"=="octalog" goto flavor_ok
if "%FLAVOR%"=="up360" goto flavor_ok
if "%FLAVOR%"=="connect" goto flavor_ok
if "%FLAVOR%"=="rondolog" goto flavor_ok
if "%FLAVOR%"=="spotlog" goto flavor_ok
if "%FLAVOR%"=="boyviny" goto flavor_ok

echo ❌ Flavor inválido: %FLAVOR%
echo Flavors disponíveis: octalog, up360, connect, rondolog, spotlog, boyviny
exit /b 1

:flavor_ok
echo ✅ Flavor válido: %FLAVOR%

REM Definir configurações por flavor
if "%FLAVOR%"=="octalog" (
    set BUNDLE_ID=com.octalog
    set DISPLAY_NAME=Octalog
)
if "%FLAVOR%"=="up360" (
    set BUNDLE_ID=com.octalog.up360
    set DISPLAY_NAME=UP360
)
if "%FLAVOR%"=="connect" (
    set BUNDLE_ID=com.octalog.connect
    set DISPLAY_NAME=Connect
)
if "%FLAVOR%"=="rondolog" (
    set BUNDLE_ID=com.octalog.rondolog
    set DISPLAY_NAME=RondoLog
)
if "%FLAVOR%"=="spotlog" (
    set BUNDLE_ID=com.octalog.spotlog
    set DISPLAY_NAME=SpotLog
)
if "%FLAVOR%"=="boyviny" (
    set BUNDLE_ID=com.octalog.boyviny
    set DISPLAY_NAME=Boy Viny
)

REM Definir arquivo de configuração baseado no modo de build
if "%BUILD_MODE%"=="debug" (
    set CONFIG_FILE=ios\Flutter\Flavor-Debug.xcconfig
    set INCLUDE_FILE=Debug.xcconfig
)
if "%BUILD_MODE%"=="release" (
    set CONFIG_FILE=ios\Flutter\Flavor-Release.xcconfig
    set INCLUDE_FILE=Release.xcconfig
)
if "%BUILD_MODE%"=="profile" (
    set CONFIG_FILE=ios\Flutter\Flavor-Profile.xcconfig
    set INCLUDE_FILE=Release.xcconfig
)

if "%CONFIG_FILE%"=="" (
    echo ❌ Modo de build inválido: %BUILD_MODE%
    echo Modos disponíveis: debug, release, profile
    exit /b 1
)

echo 📝 Atualizando arquivo de configuração: %CONFIG_FILE%
echo 🆔 Bundle ID: %BUNDLE_ID%
echo 📱 Nome do App: %DISPLAY_NAME%

REM Criar conteúdo do arquivo de configuração
(
echo #include "%INCLUDE_FILE%"
echo.
echo // Flavor configuration for iOS - %FLAVOR%
echo // Generated automatically by configure_ios_flavor.bat
echo.
echo FLAVOR_BUNDLE_IDENTIFIER = %BUNDLE_ID%
echo FLAVOR_DISPLAY_NAME = %DISPLAY_NAME%
) > "%CONFIG_FILE%"

echo ✅ Configuração iOS atualizada com sucesso!
echo 📄 Arquivo gerado: %CONFIG_FILE%
echo.
echo 🚀 Para buildar o iOS agora, use:
echo flutter build ios --release --flavor %FLAVOR% --dart-define=FLAVOR=%FLAVOR%
