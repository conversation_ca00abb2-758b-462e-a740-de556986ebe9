
//import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:octalog/one_signal_wrapper.dart';

class FmcExternalLogin {
  // static String _fcmKey = '';

  // static Future<void> loadFCMKey() async {
  //   try {
  //     final value = await FirebaseMessaging.instance.getToken();
  //     _fcmKey = value ?? 'Sem token';
  //   } catch (e) {
  //     _fcmKey = 'Erro: $e';
  //   }
  // }

  static String get fcmKey => OneSignalService().userId ?? '';
  //static String get fcmKey => _fcmKey;
}
