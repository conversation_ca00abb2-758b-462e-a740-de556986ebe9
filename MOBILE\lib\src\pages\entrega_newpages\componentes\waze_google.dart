// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:octalog/src/components/flavor_image/flavor_image.dart';
import 'package:octalog/src/models_new/endereco_new.dart';
import 'package:url_launcher/url_launcher.dart';

class WazeGoogle extends StatelessWidget {
  final EnderecoNew atividade;
  const WazeGoogle({super.key, required this.atividade});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: () async {
                  final dest = [atividade.latitude, atividade.longitude];
                  final url = Uri.parse('google.navigation:q=${dest.join(',')}');
                  final fallbackUrl = Uri.parse('https://www.google.com/maps/dir/?api=1&destination=${dest.join(',')}');
                  if (await canLaunchUrl(url)) {
                    await launchUrl(url, mode: LaunchMode.externalApplication);
                  } else {
                    await launchUrl(fallbackUrl, mode: LaunchMode.externalApplication);
                  }
                },
                child: FlavorImage(assetName: 'google_maps.png', height: 45, width: 45, fit: BoxFit.fill),
              ),
              const SizedBox(width: 50),
              GestureDetector(
                onTap: () async {
                  final dest = [atividade.latitude, atividade.longitude];
                  final url = Uri.parse('waze://?ll=${dest.join(',')}');
                  final fallbackUrl = Uri.parse('https://www.waze.com/ul?ll=${dest.join(',')}&navigate=yes');
                  if (await canLaunchUrl(url)) {
                    await launchUrl(url, mode: LaunchMode.externalApplication);
                  } else {
                    await launchUrl(fallbackUrl, mode: LaunchMode.externalApplication);
                  }
                },
                child: Image.asset('assets/images/waze_2.png', height: 55, width: 55, fit: BoxFit.fill),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
