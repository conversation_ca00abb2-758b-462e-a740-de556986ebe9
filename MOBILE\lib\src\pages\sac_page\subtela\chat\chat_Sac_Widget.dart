import 'dart:async';

import 'package:flutter/material.dart';
import 'package:octalog/src/pages/sac_page/enum/message_type.dart';
import 'package:octalog/src/pages/sac_page/model/messagem_chat.dart';
import 'package:octalog/src/pages/sac_page/sac_page_store.dart';
import 'package:octalog/src/pages/sac_page/subtela/chat/messagem_card.dart';
//import 'package:octalog/src/utils/colors.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../../../utils/extesion.dart';

class ChatSACWidget extends StatefulWidget {
  final List<MessagemChat>? messages;
  final SacPageStore store;

  const ChatSACWidget({
    super.key,
    required this.messages,
    required this.store,
  });

  @override
  State<ChatSACWidget> createState() => _ChatSACWidgetState();
}

class _ChatSACWidgetState extends State<ChatSACWidget> {
  late Timer timer;

  SacPageStore get store => widget.store;

  @override
  void initState() {
    super.initState();
    timer = Timer.periodic(
      const Duration(seconds: 30),
      (Timer t) {
        setState(() {});
      },
    );
  }

  @override
  void dispose() {
    timer.cancel();
    super.dispose();
  }

  String atualizarMensagem(
    DateTime data,
  ) {
    return DateTimeExt.exibirTempoDecorridoAPartirDoTimestamp(data);
  }

  @override
  Widget build(BuildContext context) {
    return RawScrollbar(
      thumbColor: ThemeColors.customOrange(context),
      thumbVisibility: true,
      child: SingleChildScrollView(
        reverse: true,
        physics: const BouncingScrollPhysics(),
        child: ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.all(2),
          itemCount: widget.messages!.length,
          itemBuilder: (context, index) {
            final message = widget.messages![index];

            return MessagemCard(
              conteudo: store.removerHyperLink(message.conteudo),
              hyperLink:
                  store.verificarHyperLinkDestinoDoGoogleMpas(message.conteudo),
              foto: store.verificarHyperLinkDeFoto(message.conteudo),
              usuario: message.usuario,
              dataMensagem:
                  atualizarMensagem(message.dateInclusao ?? DateTime.now()),
              isAgent: message.origem == MessagemTipo.agente,
            );
          },
        ),
      ),
    );
  }
}
