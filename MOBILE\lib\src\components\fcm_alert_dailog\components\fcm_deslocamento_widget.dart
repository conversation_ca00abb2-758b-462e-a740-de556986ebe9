import 'package:flutter/material.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../../utils/colors.dart';
import 'fcm_anim.dart';

class FcmDeslocamentoWidget extends StatelessWidget {
  final String local;
  final String endereco;
  const FcmDeslocamentoWidget(
      {super.key, required this.local, required this.endereco});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 10, left: 20, right: 10),
      child: SizedBox(
        //height: 120,
        child: Row(
          children: [
            //icons(),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: [
                  RichText(
                    overflow: TextOverflow.clip,
                    textAlign: TextAlign.start,
                    textScaler: const TextScaler.linear(1.2),
                    text: TextSpan(
                      style: TextStyle(
                        color: ThemeColors.customOrange(context),
                        fontWeight: FontWeight.w600,
                      ),
                      text: local.toUpperCase(),
                    ),
                  ),
                  //const Spacer(),
                  Padding(
                    padding: const EdgeInsets.only(bottom: 10),
                    child: RichText(
                      overflow: TextOverflow.clip,
                      textAlign: TextAlign.start,
                      text: TextSpan(
                        style: const TextStyle(
                          color: Colors.black,
                        ),
                        text: endereco,
                      ),
                    //  textScaler: const TextScaler.linear(1),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget icons() {
    return Column(
      children: [
        SizedBox(
          height: 30,
          child: Image.asset(
            'assets/images/localization.png',
            height: 80,
          ),
        ),
        // ...diverVertical(),
        const FcmAnimTest(),
        const Icon(
          Icons.circle_outlined,
          color: ColorsCustom.customOrange,
          size: 40,
        )
      ],
    );
  }
}
