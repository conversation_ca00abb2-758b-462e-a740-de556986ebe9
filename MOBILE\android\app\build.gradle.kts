import java.util.Properties

plugins {
    id("com.android.application")
    // START: FlutterFire Configuration
    // id("com.google.gms.google-services") // Temporariamente desabilitado para testes de flavors
    // END: FlutterFire Configuration
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

val keystorePropertiesFile = rootProject.file("key.properties")
val keystoreProperties = Properties()
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(keystorePropertiesFile.inputStream())
}

android {
    namespace = "com.octalog"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    buildFeatures {
        buildConfig = true
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.octalog"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        ndkVersion = "27.0.12077973" // Add this line to specify the NDK version
    }

    flavorDimensions += "client"

    productFlavors {
        create("octalog") {
            dimension = "client"
            applicationId = "com.octalog"
            resValue("string", "app_name", "Octalog")
            buildConfigField("String", "FLAVOR_NAME", "\"octalog\"")
        }

        create("arcargo") {
            dimension = "client"
            applicationId = "com.octalog.arcargo"
            resValue("string", "app_name", "ArCargo")
            buildConfigField("String", "FLAVOR_NAME", "\"arcargo\"")
        }

        create("connect") {
            dimension = "client"
            applicationId = "com.octalog.connect"
            resValue("string", "app_name", "Connect")
            buildConfigField("String", "FLAVOR_NAME", "\"connect\"")
        }

        create("rondolog") {
            dimension = "client"
            applicationId = "com.octalog.rondolog"
            resValue("string", "app_name", "RondoLog")
            buildConfigField("String", "FLAVOR_NAME", "\"rondolog\"")
        }
    }

    signingConfigs {
        create("release") {
            if (keystorePropertiesFile.exists()) {
                storeFile = file(keystoreProperties["storeFile"] as String)
                storePassword = keystoreProperties["storePassword"] as String
                keyAlias = keystoreProperties["keyAlias"] as String
                keyPassword = keystoreProperties["keyPassword"] as String
            }
        }
    }    

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.getByName("release")
        }
    }
}

flutter {
    source = "../.."
}
