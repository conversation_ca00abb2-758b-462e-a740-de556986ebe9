import 'dart:convert';

import 'package:asuka/asuka.dart' as asuka;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:octalog/src/components/custom_scaffold/custom_scaffold.dart';
import 'package:octalog/src/components/loading_ls/loading_ls.dart';
import 'package:octalog/src/database/log_database/log_database.dart';
import 'package:octalog/src/helpers/login/login.dart';
import 'package:octalog/src/helpers/smtp_email_envio.dart';
import 'package:octalog/src/pages/home/<USER>';
import 'package:octalog/src/utils/extesion.dart';

import '../../components/buttom_ls/button_ls_custom.dart';
import '../../database/offline_request/offline_request_database.dart';

class LogPage extends StatefulWidget {
  const LogPage({super.key});

  @override
  State<LogPage> createState() => _LogPageState();
}

class _LogPageState extends State<LogPage> {
  List<LogCustom> logs = [];
  bool loading = false;
  bool logsEnviados = false;
  bool enviandoLogs = false;

  Future<void> loadLogs() async {
    setState(() => loading = true);
    logs = (await LogDatabase.instance.getLogs()).reversed.toList();
    setState(() => loading = false);
  }

  Future<void> enviarLogs() async {
    setState(() {
      enviandoLogs = true;
    });

    final enderecos = jsonEncode(
      HomeController.instance.state.value.atividadesCompletas
          .toList()
          .map((e) => e.toHiveMap())
          .toList(),
    );
    final sincronismo = jsonEncode(
      (await OfflineRequestDatabase.instance.getRestanteValues())
          .map((e) => e.toHiveMap())
          .toList(),
    );
    final logJson = jsonEncode(logs.map((e) => e.toMap()).toList());

    final usuario = Login.instance.usuarioLogado?.usuario ?? 'Sem login';
    final nome = Login.instance.usuarioLogado?.nomeCompleto ?? 'Sem login';
    final anexos = [
      AnexoEmail(
        nome: '$usuario-enderecos.json',
        dados: enderecos,
      ),
      AnexoEmail(
        nome: '$usuario-sincronismo.json',
        dados: sincronismo,
      ),
      AnexoEmail(
        nome: '$usuario-logs.json',
        dados: logJson,
      ),
    ];

    final sucesso = await SmtpEmailEnvio.instance.enviarEmailSuporte(
      assunto: 'Logs do agente $usuario',
      mensagem: 'Logs do agente $usuario \n Nome do agente: $nome',
      anexos: anexos,
    );

    if (sucesso) {
      asuka.AsukaSnackbar.success(
        'Logs enviados com sucesso!',
      ).show();
      setState(() {
        enviandoLogs = false;
        logsEnviados = true;
      });
    } else {
      asuka.AsukaSnackbar.warning(
        'Erro ao enviar logs!',
      ).show();
      setState(() {
        enviandoLogs = false;
        logsEnviados = false;
      });
    }
  }

  @override
  void initState() {
    loadLogs();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      onPop: () => Navigator.pop(context),
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Builder(builder: (context) {
            String? message;
            if (logsEnviados) {
              message = 'Logs enviados com sucesso!';
            } else if (enviandoLogs) {
              message = 'Enviando logs...';
            }
            return Padding(
              padding: const EdgeInsets.only(right: 20, left: 45),
              child: ButtonLsCustom(
                text: 'ENVIAR LOGS',
                isLoading: enviandoLogs,
                onPressed: message == null ? enviarLogs : null,
              ),
            );
          }),
        ],
      ),
      cameraTela: loadLogs,
      iconsCamera: Icons.refresh,
      isColorIcon: false,
      title: 'Logs',
      child: Builder(builder: (context) {
        if (loading) {
          return const Center(
            child: LoadingLs(),
          );
        }
        if (logs.isEmpty) {
          return const Center(
            child: Text('Nenhum log encontrado'),
          );
        }
        return ListView.builder(
          itemCount: logs.length,
          itemBuilder: (_, index) {
            final log = logs[index];
            return ListTile(
              onTap: () {
                final complementosKeys = log.complementos.keys.toList();
                if (complementosKeys.isEmpty) return;
                showDialog(
                  context: context,
                  builder: (context) {
                    return AlertDialog(
                      title: Text(log.message),
                      content: SizedBox(
                        height: MediaQuery.of(context).size.height * .8,
                        width: MediaQuery.of(context).size.width * .8,
                        child: ListView.builder(
                          itemCount: complementosKeys.length,
                          itemBuilder: (_, index) {
                            final key = complementosKeys[index];
                            final value = log.complementos[key];
                            return ExpansionTile(
                              title: Text(key),
                              children: [
                                Text(value.toString()),
                              ],
                            );
                          },
                        ),
                      ),
                      actions: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            TextButton(
                              onPressed: () {
                                Clipboard.setData(
                                  ClipboardData(text: log.toJson()),
                                );
                                Navigator.pop(context);
                                asuka.AsukaSnackbar.info(
                                  'Log copiado com sucesso!',
                                ).show();
                              },
                              child: const Text('COPIAR',
                                  style: TextStyle(color: Colors.orange)),
                            ),
                            TextButton(
                              onPressed: () => Navigator.pop(context),
                              child: const Text('FECHAR',
                                  style: TextStyle(color: Colors.orange)),
                            ),
                          ],
                        ),
                      ],
                    );
                  },
                );
              },
              leading: Builder(
                builder: (context) {
                  switch (log.type) {
                    case LogType.info:
                      return const Icon(Icons.info, color: Colors.blue);
                    case LogType.success:
                      return const Icon(Icons.check, color: Colors.greenAccent);
                    case LogType.error:
                      return const Icon(Icons.error, color: Colors.red);
                    case LogType.warn:
                      return const Icon(Icons.dangerous, color: Colors.orange);
                    default:
                      return const Icon(Icons.info);
                  }
                },
              ),
              title: Text(log.message),
              subtitle: Text('${log.tag}\n${log.subTag}'),
              trailing: Text(
                '${log.dateTime.dataPtBr}\n${log.dateTime.horaPtBr}',
                textAlign: TextAlign.end,
              ),
            );
          },
        );
      }),
    );
  }
}
