import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../../utils/theme_colors.dart';

class MensagemClienteEscolhidoWidget extends StatelessWidget {
  final String? mensagem;

  const MensagemClienteEscolhidoWidget({super.key, this.mensagem});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Visibility(
        visible: (mensagem ?? '').isNotEmpty,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(5),
          ),
          padding: const EdgeInsets.all(5),
          width: double.infinity,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                mensagem ?? '',
                style: GoogleFonts.roboto(
                  fontSize: 12,
                  color: ThemeColors.customBlack(context),
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 5,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
