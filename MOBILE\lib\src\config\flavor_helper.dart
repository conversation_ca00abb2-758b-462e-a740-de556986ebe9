import 'package:flutter/services.dart';
import 'flavor_config.dart';

class FlavorHelper {
  static const MethodChannel _channel = MethodChannel('com.octalog/flavor');

  static FlavorType getCurrentFlavor() {
    // Primeiro, tenta usar a variável de ambiente FLAVOR (funciona para debug e release)
    const String flavorName = String.fromEnvironment('FLAVOR');
    if (flavorName.isNotEmpty) {
      return _parseFlavorFromString(flavorName);
    }

    // Se não encontrou via environment, tenta detectar pelo build
    return _detectFlavorFromBuild();
  }

  static FlavorType _parseFlavorFromString(String flavorName) {
    switch (flavorName.toLowerCase()) {
      case 'arcargo':
        return FlavorType.arcargo;
      case 'connect':
        return FlavorType.connect;
      case 'rondolog':
        return FlavorType.rondolog;
      case 'spotlog':
        return FlavorType.spotlog;
      case 'octalog':
      default:
        return FlavorType.octalog;
    }
  }

  static FlavorType _detectFlavorFromBuild() {
    // Para builds release, retorna o padrão e será detectado via async
    return FlavorType.octalog;
  }

  static Future<FlavorType> _detectFlavorFromPlatform() async {
    try {
      final String flavorName = await _channel.invokeMethod('getFlavor');
      return _parseFlavorFromString(flavorName);
    } catch (e) {
      // Se falhar (ex: iOS), retorna o padrão
      return FlavorType.octalog;
    }
  }

  static void initializeFlavor() {
    final flavor = getCurrentFlavor();
    FlavorConfig.initialize(flavor);

    // Tenta detectar via platform de forma assíncrona e atualiza se necessário
    _detectFlavorFromPlatform().then((detectedFlavor) {
      if (detectedFlavor != flavor) {
        FlavorConfig.initialize(detectedFlavor);
      }
    }).catchError((e) {
      // Ignora erros silenciosamente
    });
  }
}
