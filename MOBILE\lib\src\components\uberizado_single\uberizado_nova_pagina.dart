// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:octalog/src/components/loading_ls/loading_ls.dart';
import 'package:octalog/src/utils/extesion.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../database/log_database/log_database.dart';
import '../../pages/home/<USER>';
import '../buttom_ls/button_ls_custom.dart';
import '../fcm_alert_dailog/components/card_pedido_padrao.dart';
import '../fcm_alert_dailog/components/fcm_appbar_custom.dart';
import '../fcm_alert_dailog/fcm_alert_dialog_store.dart';

import 'uberizado_loja_card.dart';

class UberizadoNovaPagina extends StatefulWidget {
  final FcmAlertDialogStore store;
  const UberizadoNovaPagina({
    super.key,
    required this.store,
  });

  @override
  State<UberizadoNovaPagina> createState() => _UberizadoNovaPaginaState();
}

class _UberizadoNovaPaginaState extends State<UberizadoNovaPagina> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: KeyboardVisibilityBuilder(
          builder: (context, isKeyboardVisible) {
            return ValueListenableBuilder(
              valueListenable: widget.store.state,
              builder: (context, state, child) {
                final dados = state.fcmAlertDados;
                return SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: FcmAppBarCustom(
                          title: 'Pedidos para coletar',
                          isBack: true,
                          isLoadgin: true,
                          onPressed: () {
                            state.isLoading
                                ? null
                                : widget.store.getDadosPedidos(
                                    context,
                                  );
                          },
                        ),
                      ),
                      UberizadoLojaCard(
                        endereco: state.fcmAlertDados.endereco,
                        foto: state.fcmAlertDados.logo ?? "",
                        iniciais: state.fcmAlertDados.titulo.iniciais,
                        titulo: state.fcmAlertDados.titulo,
                      ),
                      const SizedBox(height: 5),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                           Expanded(
                            child: Divider(
                              color: ThemeColors.customOrange(context),
                              thickness: 2,
                              indent: 20,
                              endIndent: 20,
                            ),
                          ),
                          Builder(builder: (context) {
                            final int lenco = state.naoColetados.length;
                            return Text(
                              // '$lenco ${state.coletados.toSet().length == 1 ? ' pedido coletado' : ' pedidos coletados'}',
                              '$lenco ${lenco == 1 ? ' pedido coletado' : ' pedidos coletados'}',
                              style:  TextStyle(
                                color: ThemeColors.customOrange(context),
                                fontWeight: FontWeight.w500,
                                fontSize: 17,
                              ),
                            );
                          }),
                           Expanded(
                            child: Divider(
                              color: ThemeColors.customOrange(context),
                              thickness: 2,
                              indent: 20,
                              endIndent: 20,
                            ),
                          ),
                        ],
                      ),
                      Builder(
                        builder: (context) {
                          if (state.isLoading) {
                            return SizedBox(
                              height: MediaQuery.of(context).size.height / 2,
                              child: const Center(
                                child: SizedBox(
                                  width: 200,
                                  height: 200,
                                  child: LoadingLs(),
                                ),
                              ),
                            );
                          }
                          if (dados.pedidos.isEmpty) {
                            return SizedBox(
                              height: MediaQuery.of(context).size.height / 2,
                              child:  Center(
                                child: Text(
                                  'Clique no botão para atualizar a lista de pedidos...',
                                  style: TextStyle(
                                    color: ThemeColors.customOrange(context),
                                  ),
                                ),
                              ),
                            );
                          }
                          return Container(
                            constraints: BoxConstraints(
                              minHeight: MediaQuery.of(context).size.height / 2,
                            ),
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 10),
                              child: ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: state.clientesUnicosFiltrados.length,
                                itemBuilder: (context, index) {
                                  final pedido =
                                      state.clientesNaoColetados[index];
                                  final pedidos = state
                                      .pedidosTotaisCliente(pedido.cliente);
                                  final qtdPedido = pedidos.length;
                                  return Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: CardPedidoPadrao(
                                      nomeCliente: pedido.cliente,
                                      endereco: pedido.title,
                                      status: pedido.status,
                                      pedidos: pedidos,
                                      coletados: qtdPedido,
                                    ),
                                  );
                                },
                              ),
                            ),
                          );
                        },
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SizedBox(
                              height: 54,
                              width: double.infinity,
                              child: TextField(
                                style: const TextStyle(
                                  fontSize: 20,
                                  height: 1,
                                ),
                                onChanged: widget.store.setLiberadoPor,
                                textInputAction: TextInputAction.search,
                                decoration: InputDecoration(
                                  hintText: 'Liberado na loja por',
                                  hintStyle: const TextStyle(
                                    color: Colors.grey,
                                    fontSize: 18,
                                  ),
                                  prefixIcon: Icon(
                                    Icons.supervisor_account_sharp,
                                    color: Colors.grey.shade600,
                                  ),
                                  filled: true,
                                  fillColor:
                                      const Color.fromARGB(255, 238, 238, 238),
                                  border: const OutlineInputBorder(
                                    borderSide: BorderSide.none,
                                    borderRadius: BorderRadius.all(
                                      Radius.circular(8),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            ButtonLsCustom(
                              isLoading: state.isLoadingButton,
                              onPressed: () async {
                                if (widget
                                        .store.state.value.liberadoPor.length >
                                    4) {
                                  widget.store.setLoadingButton(true);

                                  if (await widget.store.verificarDistancia()) {
                                    widget.store.setLoadingButton(false);
                                    return;
                                  }

                                  try {
                                    await widget.store.finalizeColeta(
                                      context,
                                      finalizarTodosPedidos: true,
                                    );
                                  } catch (e) {
                                    await LogDatabase.instance.logError(
                                      '/deslocamento/pedidoscoletados',
                                      '',
                                      'finalizeColeta',
                                      {
                                        'error': e.toString(),
                                        'error_type': e.runtimeType.toString(),
                                      },
                                    );
                                  }

                                  Navigator.pushAndRemoveUntil(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => const Home(
                                        enterAtividade: false,
                                      ),
                                    ),
                                    (route) => false,
                                  );
                                } else {
                                  widget.store.setLoadingButton(false);
                                  showDialog(
                                    context: context,
                                    builder: (ctx) {
                                      return AlertDialog(
                                        title: const Text('Atenção'),
                                        content: const Text(
                                            'Informe o nome de quem liberou a mercadoria na loja.'),
                                        actions: [
                                          TextButton(
                                            onPressed: () {
                                              Navigator.pop(ctx);
                                            },
                                            child: const Text(
                                              'Ok',
                                              style: TextStyle(
                                                  color: Colors.orange),
                                            ),
                                          )
                                        ],
                                      );
                                    },
                                  );
                                }
                              },
                              text: 'FINALIZAR COLETA',
                              colorBackground: ThemeColors.customOrange(context),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}
