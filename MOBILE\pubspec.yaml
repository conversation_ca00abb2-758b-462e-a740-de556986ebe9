name: octalog
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.2.4+31

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter


# não aceitou
  # airplane_mode_checker: ^2.1.0
  # battery_info: ^1.1.1
  # flutter_android_developer_mode: ^1.0.0
  # flutter_beep: ^1.0.0
  # flutter_jailbreak_detection: ^1.10.0
  # flutter_local_notifications: ^17.2.1+2
  # location2: ^6.0.4
  # webview_flutter_plus: ^0.3.0+2

  # geolocator: ^12.0.0
  # speech_to_text: ^6.4.0


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  onesignal_flutter: ^5.2.0
  cupertino_icons: ^1.0.8
  firebase_core: ^4.0.0
  firebase_messaging: ^16.0.0
  firebase_remote_config: ^6.0.0
  mailer: ^6.4.1
  signalr_core: ^1.1.1
  flutter_pdfview: ^1.4.0
  pdfrx: ^1.0.98
  open_file: ^3.5.7
  android_id: ^0.4.0
  asuka: ^2.2.1
  azstore: ^1.0.3
  cached_network_image: ^3.3.1
  calendar_date_picker2: ^2.0.0
  camera: ^0.10.5+9
  camera_camera: ^3.0.0
  connectivity_plus: ^6.1.3
  device_info_plus: ^11.3.3
  dio: ^5.8.0+1
  dotted_decoration: ^2.0.0
  drift: ^2.26.0
  file_picker: ^10.2.0
  flutter_activity_recognition: ^4.0.0
  flutter_keyboard_visibility: ^6.0.0
  flutter_linkify: ^6.0.0
  flutter_map: ^4.0.0
  flutter_ringtone_player: ^4.0.0+4
  signature: ^5.5.0
  sqlite3_flutter_libs: ^0.5.32
  system_info_plus: ^0.0.6
  url_launcher: ^6.3.1
  uuid: ^4.5.1
  google_fonts: ^4.0.4
  google_mlkit_face_detection: ^0.13.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  logger: ^2.5.0
  map_fields: ^0.0.5
  mime: ^2.0.0
  mobile_scanner: ^7.0.1
  ntp: ^2.0.0
  package_info_plus: ^8.3.0
  path: ^1.9.1
  path_provider: ^2.1.5
  permission_handler: ^12.0.1
  pub_semver: ^2.2.0
  rive: ^0.13.20
  screenshot: ^3.0.0
  image_picker: ^1.1.2
  in_app_review: ^2.0.10
  intl: any
  shared_preferences: ^2.5.2
  shorebird_code_push: ^2.0.3
  latlong2: ^0.8.2
  iconsax: ^0.0.8
  flutter_webrtc: ^1.0.0
  speech_to_text: ^7.0.0
  airplane_mode_checker: ^3.1.0
  geolocator: ^14.0.2
  webview_flutter: ^4.0.0
  flutter_flavorizr: ^2.2.1
  # webview_flutter_plus: ^0.4.10

dev_dependencies:
  flutter_test:
    sdk: flutter

  build_runner: ^2.3.2
  change_app_package_name: ^1.4.0
  drift_dev: ^2.22.1
  flutter_launcher_icons: ^0.14.3
  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/images/octalog/
    - assets/images/up360/
    - assets/images/connect/
    - assets/images/rondolog/
    - assets/images/spotlog/
    - assets/images/boyviny/
    - assets/sac/
    - assets/animations/
    - assets/notification/

flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/octalog/logo512.png"
  remove_alpha_ios: true

# Configuração de ícones por flavor (será usado com scripts de build)
flavor_icons:
  octalog:
    android: "launcher_icon"
    ios: true
    image_path: "assets/images/octalog/logo512.png"
  up360:
    android: "launcher_icon"
    ios: true
    image_path: "assets/images/up360/logo512.png"
  connect:
    android: "launcher_icon"
    ios: true
    image_path: "assets/images/connect/logo512.png"
  rondolog:
    android: "launcher_icon"
    ios: true
    image_path: "assets/images/rondolog/logo512.png"
  spotlog:
    android: "launcher_icon"
    ios: true
    image_path: "assets/images/spotlog/logo512.png"
  boyviny:
    android: "launcher_icon"
    ios: true
    image_path: "assets/images/boyviny/logo512.png"
