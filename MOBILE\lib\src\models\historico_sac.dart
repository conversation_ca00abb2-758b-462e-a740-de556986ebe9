import 'package:map_fields/map_fields.dart';

class HistoricoSacModel {
  final int idSacAtendimento;
  final String os;
  final String lojaNome;
  final String lojaLogo;
  final String enderecoDoCliente;
  final String nomeCliente;
  final String nomeAtendente;
  final int idStatusAtividade;
  final String statusAtividade;
  final String mensagemAgente;
  final DateTime dataContatoAgente;
  final String mensagemAtendente;
  final DateTime dataAtendimentoFinalizado;
  final double latitudeAgente;
  final double longitudeAgente;
  final int tempoAtendimentoMinutos;
  final List<String>? fotos;
  HistoricoSacModel({
    required this.idSacAtendimento,
    required this.os,
    required this.enderecoDoCliente,
    required this.lojaNome,
    required this.lojaLogo,
    required this.nomeCliente,
    required this.nomeAtendente,
    required this.idStatusAtividade,
    required this.statusAtividade,
    required this.mensagemAgente,
    required this.dataContatoAgente,
    required this.mensagemAtendente,
    required this.dataAtendimentoFinalizado,
    required this.latitudeAgente,
    required this.longitudeAgente,
    required this.tempoAtendimentoMinutos,
    required this.fotos,
  });

  factory HistoricoSacModel.fromMap(Map<String, dynamic> map) {
    final MapFields mapFields = MapFields.load(map);
    return HistoricoSacModel(
      idSacAtendimento: mapFields.getInt('IDSacAtendimento', 0),
      os: mapFields.getString('OS', ''),
      lojaNome: mapFields.getString('LojaNome', ''),
      lojaLogo: mapFields.getString('LojaLogo', ''),
      enderecoDoCliente:
          mapFields.getString('EnderecoDoCliente', 'Sem endereço'),
      nomeCliente: mapFields.getString('NomeCliente', ''),
      nomeAtendente: mapFields.getString('NomeAtendente', ''),
      idStatusAtividade: mapFields.getInt('IDStatusAtividade', 0),
      statusAtividade: mapFields.getString('StatusAtividade', ''),
      mensagemAgente: mapFields.getString('MensagemAgente', ''),
      mensagemAtendente: mapFields.getString('MensagemAtendente', ''),
      dataContatoAgente:
          mapFields.getDateTime('DataContatoAgente', DateTime.now()),
      dataAtendimentoFinalizado:
          mapFields.getDateTime('DataAtendimentoFinalizado', DateTime.now()),
      latitudeAgente: mapFields.getDouble('LatitudeAgente', 0),
      longitudeAgente: mapFields.getDouble('LongitudeAgente', 0),
      tempoAtendimentoMinutos: mapFields.getInt('TempoAtendimentoMinutos', 0),
      fotos: mapFields.getListNullable<String>('Fotos'),
    );
  }

  static fromJsonList(data) {
    return List<HistoricoSacModel>.from(
        data.map((item) => HistoricoSacModel.fromMap(item)));
  }
}
