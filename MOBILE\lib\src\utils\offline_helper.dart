import 'package:flutter/material.dart';

final offlineStore = OfflineStore._();

class OfflineStore {
  OfflineStore._();
  final ValueNotifier<bool> offline = ValueNotifier<bool>(false);
  final ValueNotifier<bool> forceFakeOnline = ValueNotifier<bool>(false);

  bool get isOffline => offline.value;

  void setOffline(bool value) {
    if (forceFakeOnline.value) {
      offline.value = false;
      return;
    }
    offline.value = value;
  }

  void setForceFakeOnline(bool value) => forceFakeOnline.value = value;
}
