<variant
    name="up360Release"
    package="com.octalog"
    minSdkVersion="21"
    targetSdkVersion="35"
    shrinking="true"
    mergedManifest="C:\projetos\octa.log\MOBILE\build\app\intermediates\merged_manifest\up360Release\processUp360ReleaseMainManifest\AndroidManifest.xml"
    manifestMergeReport="C:\projetos\octa.log\MOBILE\build\app\outputs\logs\manifest-merger-up360-release-report.txt"
    proguardFiles="C:\projetos\octa.log\MOBILE\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.0;C:\src\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro;proguard-rules.pro"
    partialResultsDir="C:\projetos\octa.log\MOBILE\build\app\intermediates\lint_vital_partial_results\up360Release\lintVitalAnalyzeUp360Release\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\up360\java;src\release\java;src\up360Release\java;src\main\kotlin;src\up360\kotlin;src\release\kotlin;src\up360Release\kotlin"
        resDirectories="src\main\res;src\up360\res;src\release\res;src\up360Release\res"
        assetsDirectories="src\main\assets;src\up360\assets;src\release\assets;src\up360Release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <resValues>
    <resValue
        type="string"
        name="app_name"
        value="UP360" />
  </resValues>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <artifact
      classOutputs="C:\projetos\octa.log\MOBILE\build\app\intermediates\javac\up360Release\compileUp360ReleaseJavaWithJavac\classes;C:\projetos\octa.log\MOBILE\build\app\tmp\kotlin-classes\up360Release;C:\projetos\octa.log\MOBILE\build\app\kotlinToolingMetadata;C:\projetos\octa.log\MOBILE\build\app\intermediates\compile_and_runtime_not_namespaced_r_class_jar\up360Release\processUp360ReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.octalog.up360"
      generatedSourceFolders="C:\projetos\octa.log\MOBILE\build\app\generated\ap_generated_sources\up360Release\out;C:\projetos\octa.log\MOBILE\build\app\generated\source\buildConfig\up360\release"
      generatedResourceFolders="C:\projetos\octa.log\MOBILE\build\app\generated\res\resValues\up360\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6acd1316a909a3b9467814415d8b5421\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
