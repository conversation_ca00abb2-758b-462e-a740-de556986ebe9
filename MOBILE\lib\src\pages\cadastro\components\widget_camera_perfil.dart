import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/components/buttom_ls/button_ls_custom.dart';
import 'package:octalog/src/components/loading_ls/loading_ls.dart';

import '../../../utils/colors-dart';

late List<CameraDescription> _cameras;

class WidgetCameraPerfil extends StatefulWidget {
  const WidgetCameraPerfil({super.key});

  @override
  State<WidgetCameraPerfil> createState() => _WidgetCameraPerfilState();
}

class _WidgetCameraPerfilState extends State<WidgetCameraPerfil> {
  late CameraController controller;
  bool isCameraReady = false;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    _cameras = await availableCameras();
    controller = CameraController(
      _cameras[1],
      ResolutionPreset.max,
      imageFormatGroup: ImageFormatGroup.bgra8888,
      enableAudio: false,
    );
    controller.initialize().then((_) {
      if (!mounted) {
        return;
      }
      setState(() {
        isCameraReady = true;
      });
    }).catchError((Object e) {
      if (e is CameraException) {
        switch (e.code) {
          case 'CameraAccessDenied':
            break;
          default:
            break;
        }
      }
    });
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!isCameraReady || !controller.value.isInitialized) {
      return const LoadingLs();
    }
    return Scaffold(
      body: SizedBox(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        child: Stack(
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              child: CameraPreview(controller),
            ),
            IgnorePointer(
              child: ClipPath(
                clipper: InvertedCircleClipper(),
                child: Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  color: ThemeColors.customBlack(context).withOpacity(0.8),
                ),
              ),
            ),
            // icone para trocar de camera
            Positioned(
              top: 40,
              right: 20,
              child: IconButton(
                onPressed: () {
                  if (controller.description.lensDirection ==
                      CameraLensDirection.front) {
                    controller = CameraController(
                      _cameras[0],
                      ResolutionPreset.medium,
                      imageFormatGroup: ImageFormatGroup.bgra8888,
                    );

                    controller.initialize().then((_) {
                      if (!mounted) {
                        return;
                      }
                      setState(() {
                        isCameraReady = true;
                      });
                    }).catchError((Object e) {
                      if (e is CameraException) {
                        switch (e.code) {
                          case 'CameraAccessDenied':
                            break;
                          default:
                            break;
                        }
                      }
                    });
                  } else {
                    controller =
                        CameraController(_cameras[1], ResolutionPreset.max);
                    controller.initialize().then((_) {
                      if (!mounted) {
                        return;
                      }
                      setState(() {
                        isCameraReady = true;
                      });
                    }).catchError((Object e) {
                      if (e is CameraException) {
                        switch (e.code) {
                          case 'CameraAccessDenied':
                            break;
                          default:
                            break;
                        }
                      }
                    });
                  }
                },
                icon: const Icon(
                  Icons.flip_camera_ios,
                  color: ThemeColors.customWhite(context),
                  size: 35,
                ),
              ),
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  alignment: Alignment.center,
                  margin:
                      const EdgeInsets.only(bottom: 10, left: 20, right: 20),
                  child: Text(
                    'Posicione seu rosto precisamente no centro da tela. Esta foto será exibida ao cliente. Vamos garantir a melhor apresentação possível.',
                    textAlign: TextAlign.center,
                    style: GoogleFonts.roboto(
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                      color: ThemeColors.customWhite(context),
                    ),
                  ),
                ),
                Container(
                  alignment: Alignment.bottomCenter,
                  margin: const EdgeInsets.only(
                    bottom: 10,
                    left: 50,
                    right: 50,
                  ),
                  child: ButtonLsCustom(
                    text: 'TIRAR FOTO',
                    isLoading: isLoading,
                    onPressed: () async {
                      setState(() {
                        isLoading = true;
                      });
                      await controller
                          .takePicture()
                          .then((value) => Navigator.pop(context, value));
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class InvertedCircleClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final Path path = Path()
      ..addRect(Rect.fromLTWH(0, 0, size.width, size.height))
      ..addRRect(RRect.fromRectAndRadius(
          Rect.fromCenter(
              center: Offset(size.width / 2, size.height / 2.27),
              width: size.width / 1.2,
              height: size.height / 1.4),
          const Radius.circular(0)))
      ..fillType = PathFillType.evenOdd;
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
