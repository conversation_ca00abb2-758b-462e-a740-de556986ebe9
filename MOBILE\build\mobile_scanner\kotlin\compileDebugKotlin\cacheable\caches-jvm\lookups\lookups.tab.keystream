  Manifest android  
permission android.Manifest  CAMERA android.Manifest.permission  Activity android.app  applicationContext android.app.Activity  display android.app.Activity  getAPPLICATIONContext android.app.Activity  getApplicationContext android.app.Activity  
getDISPLAY android.app.Activity  
getDisplay android.app.Activity  setApplicationContext android.app.Activity  
setDisplay android.app.Activity  Context android.content  DISPLAY_SERVICE android.content.Context  WINDOW_SERVICE android.content.Context  getSystemService android.content.Context  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  Bitmap android.graphics  ImageFormat android.graphics  Matrix android.graphics  Point android.graphics  Rect android.graphics  SurfaceTexture android.graphics  YuvImage android.graphics  CompressFormat android.graphics.Bitmap  Config android.graphics.Bitmap  compress android.graphics.Bitmap  createBitmap android.graphics.Bitmap  	getHEIGHT android.graphics.Bitmap  	getHeight android.graphics.Bitmap  getWIDTH android.graphics.Bitmap  getWidth android.graphics.Bitmap  height android.graphics.Bitmap  recycle android.graphics.Bitmap  	setHeight android.graphics.Bitmap  setWidth android.graphics.Bitmap  width android.graphics.Bitmap  PNG &android.graphics.Bitmap.CompressFormat  	ARGB_8888 android.graphics.Bitmap.Config  NV21 android.graphics.ImageFormat  YUV_420_888 android.graphics.ImageFormat  
postRotate android.graphics.Matrix  data android.graphics.Point  getDATA android.graphics.Point  getData android.graphics.Point  getMAPOf android.graphics.Point  getMapOf android.graphics.Point  getTO android.graphics.Point  getTo android.graphics.Point  mapOf android.graphics.Point  to android.graphics.Point  x android.graphics.Point  y android.graphics.Point  bottom android.graphics.Rect  contains android.graphics.Rect  emptyMap android.graphics.Rect  getEMPTYMap android.graphics.Rect  getEmptyMap android.graphics.Rect  getMAPOf android.graphics.Rect  getMapOf android.graphics.Rect  getSIZE android.graphics.Rect  getSize android.graphics.Rect  getTO android.graphics.Rect  getTo android.graphics.Rect  height android.graphics.Rect  left android.graphics.Rect  mapOf android.graphics.Rect  right android.graphics.Rect  size android.graphics.Rect  to android.graphics.Rect  top android.graphics.Rect  width android.graphics.Rect  setDefaultBufferSize android.graphics.SurfaceTexture  compressToJpeg android.graphics.YuvImage  	getHEIGHT android.graphics.YuvImage  	getHeight android.graphics.YuvImage  getWIDTH android.graphics.YuvImage  getWidth android.graphics.YuvImage  height android.graphics.YuvImage  	setHeight android.graphics.YuvImage  setWidth android.graphics.YuvImage  width android.graphics.YuvImage  DisplayManager android.hardware.display  DisplayListener 'android.hardware.display.DisplayManager  registerDisplayListener 'android.hardware.display.DisplayManager  unregisterDisplayListener 'android.hardware.display.DisplayManager  equals 7android.hardware.display.DisplayManager.DisplayListener  Image 
android.media  	ByteArray android.media.Image  ByteArrayOutputStream android.media.Image  ImageFormat android.media.Image  Plane android.media.Image  Rect android.media.Image  YuvImage android.media.Image  	getHEIGHT android.media.Image  	getHeight android.media.Image  	getPLANES android.media.Image  	getPlanes android.media.Image  getWIDTH android.media.Image  getWidth android.media.Image  height android.media.Image  planes android.media.Image  	setHeight android.media.Image  	setPlanes android.media.Image  setWidth android.media.Image  width android.media.Image  buffer android.media.Image.Plane  	getBUFFER android.media.Image.Plane  	getBuffer android.media.Image.Plane  getPIXELStride android.media.Image.Plane  getPixelStride android.media.Image.Plane  getROWStride android.media.Image.Plane  getRowStride android.media.Image.Plane  pixelStride android.media.Image.Plane  	rowStride android.media.Image.Plane  	setBuffer android.media.Image.Plane  setPixelStride android.media.Image.Plane  setRowStride android.media.Image.Plane  Uri android.net  fromFile android.net.Uri  Build 
android.os  Handler 
android.os  Looper 
android.os  VERSION android.os.Build  SDK_INT android.os.Build.VERSION  post android.os.Handler  postDelayed android.os.Handler  
getMainLooper android.os.Looper  
Allocation android.renderscript  Element android.renderscript  RenderScript android.renderscript  ScriptIntrinsicYuvToRGB android.renderscript  Type android.renderscript  USAGE_SCRIPT android.renderscript.Allocation  copyFrom android.renderscript.Allocation  copyTo android.renderscript.Allocation  createTyped android.renderscript.Allocation  destroy android.renderscript.Allocation  getTYPE android.renderscript.Allocation  getType android.renderscript.Allocation  setType android.renderscript.Allocation  type android.renderscript.Allocation  copyFrom android.renderscript.BaseObj  copyTo android.renderscript.BaseObj  destroy android.renderscript.BaseObj  forEach android.renderscript.BaseObj  setInput android.renderscript.BaseObj  	RGBA_8888 android.renderscript.Element  U8 android.renderscript.Element  U8_4 android.renderscript.Element  create !android.renderscript.RenderScript  destroy !android.renderscript.RenderScript  destroy android.renderscript.Script  forEach android.renderscript.Script  setInput android.renderscript.Script  destroy $android.renderscript.ScriptIntrinsic  forEach $android.renderscript.ScriptIntrinsic  setInput $android.renderscript.ScriptIntrinsic  create ,android.renderscript.ScriptIntrinsicYuvToRGB  destroy ,android.renderscript.ScriptIntrinsicYuvToRGB  forEach ,android.renderscript.ScriptIntrinsicYuvToRGB  setInput ,android.renderscript.ScriptIntrinsicYuvToRGB  Builder android.renderscript.Type  getX android.renderscript.Type  getY android.renderscript.Type  getYUV android.renderscript.Type  getYuv android.renderscript.Type  setX android.renderscript.Type  setY android.renderscript.Type  setYuv android.renderscript.Type  x android.renderscript.Type  y android.renderscript.Type  yuv android.renderscript.Type  create !android.renderscript.Type.Builder  setX !android.renderscript.Type.Builder  setY !android.renderscript.Type.Builder  setYuvFormat !android.renderscript.Type.Builder  Size android.util  equals android.util.Size  	getHEIGHT android.util.Size  	getHeight android.util.Size  getWIDTH android.util.Size  getWidth android.util.Size  height android.util.Size  	setHeight android.util.Size  setWidth android.util.Size  width android.util.Size  Display android.view  Surface android.view  
WindowManager android.view  getROTATION android.view.Display  getRotation android.view.Display  rotation android.view.Display  setRotation android.view.Display  
ROTATION_0 android.view.Surface  ROTATION_180 android.view.Surface  defaultDisplay android.view.WindowManager  getDEFAULTDisplay android.view.WindowManager  getDefaultDisplay android.view.WindowManager  setDefaultDisplay android.view.WindowManager  IntDef androidx.annotation  VisibleForTesting androidx.annotation  Camera androidx.camera.core  
CameraInfo androidx.camera.core  CameraSelector androidx.camera.core  ExperimentalGetImage androidx.camera.core  
ImageAnalysis androidx.camera.core  
ImageProxy androidx.camera.core  Preview androidx.camera.core  ResolutionInfo androidx.camera.core  SurfaceRequest androidx.camera.core  
TorchState androidx.camera.core  	ZoomState androidx.camera.core  
cameraControl androidx.camera.core.Camera  
cameraInfo androidx.camera.core.Camera  equals androidx.camera.core.Camera  getCAMERAControl androidx.camera.core.Camera  
getCAMERAInfo androidx.camera.core.Camera  getCameraControl androidx.camera.core.Camera  
getCameraInfo androidx.camera.core.Camera  getLET androidx.camera.core.Camera  getLet androidx.camera.core.Camera  let androidx.camera.core.Camera  setCameraControl androidx.camera.core.Camera  
setCameraInfo androidx.camera.core.Camera  enableTorch "androidx.camera.core.CameraControl  
setLinearZoom "androidx.camera.core.CameraControl  setZoomRatio "androidx.camera.core.CameraControl  cameraState androidx.camera.core.CameraInfo  equals androidx.camera.core.CameraInfo  getCAMERAState androidx.camera.core.CameraInfo  getCameraState androidx.camera.core.CameraInfo  getLET androidx.camera.core.CameraInfo  getLet androidx.camera.core.CameraInfo  getSENSORRotationDegrees androidx.camera.core.CameraInfo  getSensorRotationDegrees androidx.camera.core.CameraInfo  
getTORCHState androidx.camera.core.CameraInfo  
getTorchState androidx.camera.core.CameraInfo  getZOOMState androidx.camera.core.CameraInfo  getZoomState androidx.camera.core.CameraInfo  hasFlashUnit androidx.camera.core.CameraInfo  let androidx.camera.core.CameraInfo  sensorRotationDegrees androidx.camera.core.CameraInfo  setCameraState androidx.camera.core.CameraInfo  setSensorRotationDegrees androidx.camera.core.CameraInfo  
setTorchState androidx.camera.core.CameraInfo  setZoomState androidx.camera.core.CameraInfo  
torchState androidx.camera.core.CameraInfo  	zoomState androidx.camera.core.CameraInfo  DEFAULT_BACK_CAMERA #androidx.camera.core.CameraSelector  DEFAULT_FRONT_CAMERA #androidx.camera.core.CameraSelector  getLET #androidx.camera.core.CameraSelector  getLet #androidx.camera.core.CameraSelector  let #androidx.camera.core.CameraSelector  Analyzer "androidx.camera.core.ImageAnalysis  Builder "androidx.camera.core.ImageAnalysis  STRATEGY_KEEP_ONLY_LATEST "androidx.camera.core.ImageAnalysis  apply "androidx.camera.core.ImageAnalysis  
captureOutput "androidx.camera.core.ImageAnalysis  getAPPLY "androidx.camera.core.ImageAnalysis  getApply "androidx.camera.core.ImageAnalysis  getCAPTUREOutput "androidx.camera.core.ImageAnalysis  getCaptureOutput "androidx.camera.core.ImageAnalysis  getRESOLUTIONInfo "androidx.camera.core.ImageAnalysis  getResolutionInfo "androidx.camera.core.ImageAnalysis  resolutionInfo "androidx.camera.core.ImageAnalysis  setAnalyzer "androidx.camera.core.ImageAnalysis  setResolutionInfo "androidx.camera.core.ImageAnalysis  <SAM-CONSTRUCTOR> +androidx.camera.core.ImageAnalysis.Analyzer  build *androidx.camera.core.ImageAnalysis.Builder  setBackpressureStrategy *androidx.camera.core.ImageAnalysis.Builder  setResolutionSelector *androidx.camera.core.ImageAnalysis.Builder  setTargetResolution *androidx.camera.core.ImageAnalysis.Builder  getROTATIONDegrees androidx.camera.core.ImageInfo  getRotationDegrees androidx.camera.core.ImageInfo  rotationDegrees androidx.camera.core.ImageInfo  setRotationDegrees androidx.camera.core.ImageInfo  close androidx.camera.core.ImageProxy  	getHEIGHT androidx.camera.core.ImageProxy  	getHeight androidx.camera.core.ImageProxy  getIMAGE androidx.camera.core.ImageProxy  getIMAGEInfo androidx.camera.core.ImageProxy  getImage androidx.camera.core.ImageProxy  getImageInfo androidx.camera.core.ImageProxy  getWIDTH androidx.camera.core.ImageProxy  getWidth androidx.camera.core.ImageProxy  height androidx.camera.core.ImageProxy  image androidx.camera.core.ImageProxy  	imageInfo androidx.camera.core.ImageProxy  	setHeight androidx.camera.core.ImageProxy  setImage androidx.camera.core.ImageProxy  setImageInfo androidx.camera.core.ImageProxy  setWidth androidx.camera.core.ImageProxy  width androidx.camera.core.ImageProxy  Builder androidx.camera.core.Preview  SurfaceProvider androidx.camera.core.Preview  apply androidx.camera.core.Preview  equals androidx.camera.core.Preview  getAPPLY androidx.camera.core.Preview  getApply androidx.camera.core.Preview  setSurfaceProvider androidx.camera.core.Preview  build $androidx.camera.core.Preview.Builder  <SAM-CONSTRUCTOR> ,androidx.camera.core.Preview.SurfaceProvider  
getRESOLUTION #androidx.camera.core.ResolutionInfo  
getResolution #androidx.camera.core.ResolutionInfo  
resolution #androidx.camera.core.ResolutionInfo  
setResolution #androidx.camera.core.ResolutionInfo  Result #androidx.camera.core.SurfaceRequest  getPROVIDESurface #androidx.camera.core.SurfaceRequest  getProvideSurface #androidx.camera.core.SurfaceRequest  
getRESOLUTION #androidx.camera.core.SurfaceRequest  
getResolution #androidx.camera.core.SurfaceRequest  provideSurface #androidx.camera.core.SurfaceRequest  
resolution #androidx.camera.core.SurfaceRequest  
setResolution #androidx.camera.core.SurfaceRequest  OFF androidx.camera.core.TorchState  ON androidx.camera.core.TorchState  apply androidx.camera.core.UseCase  setAnalyzer androidx.camera.core.UseCase  setSurfaceProvider androidx.camera.core.UseCase  
getLINEARZoom androidx.camera.core.ZoomState  
getLinearZoom androidx.camera.core.ZoomState  
linearZoom androidx.camera.core.ZoomState  
setLinearZoom androidx.camera.core.ZoomState  ResolutionSelector 'androidx.camera.core.resolutionselector  ResolutionStrategy 'androidx.camera.core.resolutionselector  Builder :androidx.camera.core.resolutionselector.ResolutionSelector  build Bandroidx.camera.core.resolutionselector.ResolutionSelector.Builder  setResolutionStrategy Bandroidx.camera.core.resolutionselector.ResolutionSelector.Builder  'FALLBACK_RULE_CLOSEST_HIGHER_THEN_LOWER :androidx.camera.core.resolutionselector.ResolutionStrategy  ProcessCameraProvider androidx.camera.lifecycle  availableCameraInfos /androidx.camera.lifecycle.ProcessCameraProvider  bindToLifecycle /androidx.camera.lifecycle.ProcessCameraProvider  equals /androidx.camera.lifecycle.ProcessCameraProvider  getAVAILABLECameraInfos /androidx.camera.lifecycle.ProcessCameraProvider  getAvailableCameraInfos /androidx.camera.lifecycle.ProcessCameraProvider  getInstance /androidx.camera.lifecycle.ProcessCameraProvider  getLET /androidx.camera.lifecycle.ProcessCameraProvider  getLet /androidx.camera.lifecycle.ProcessCameraProvider  let /androidx.camera.lifecycle.ProcessCameraProvider  setAvailableCameraInfos /androidx.camera.lifecycle.ProcessCameraProvider  	unbindAll /androidx.camera.lifecycle.ProcessCameraProvider  ActivityCompat androidx.core.app  requestPermissions  androidx.core.app.ActivityCompat  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  getMainExecutor #androidx.core.content.ContextCompat  <SAM-CONSTRUCTOR> androidx.core.util.Consumer  LifecycleOwner androidx.lifecycle  getVALUE androidx.lifecycle.LiveData  getValue androidx.lifecycle.LiveData  observe androidx.lifecycle.LiveData  removeObservers androidx.lifecycle.LiveData  setValue androidx.lifecycle.LiveData  value androidx.lifecycle.LiveData  <SAM-CONSTRUCTOR> androidx.lifecycle.Observer  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> /com.google.android.gms.tasks.OnCompleteListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnCompleteListener !com.google.android.gms.tasks.Task  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  ListenableFuture !com.google.common.util.concurrent  addListener 2com.google.common.util.concurrent.ListenableFuture  get 2com.google.common.util.concurrent.ListenableFuture  getADDListener 2com.google.common.util.concurrent.ListenableFuture  getAddListener 2com.google.common.util.concurrent.ListenableFuture  BarcodeScanner com.google.mlkit.vision.barcode  BarcodeScannerOptions com.google.mlkit.vision.barcode  BarcodeScanning com.google.mlkit.vision.barcode  close .com.google.mlkit.vision.barcode.BarcodeScanner  getLET .com.google.mlkit.vision.barcode.BarcodeScanner  getLet .com.google.mlkit.vision.barcode.BarcodeScanner  let .com.google.mlkit.vision.barcode.BarcodeScanner  process .com.google.mlkit.vision.barcode.BarcodeScanner  Builder 5com.google.mlkit.vision.barcode.BarcodeScannerOptions  equals 5com.google.mlkit.vision.barcode.BarcodeScannerOptions  build =com.google.mlkit.vision.barcode.BarcodeScannerOptions.Builder  setBarcodeFormats =com.google.mlkit.vision.barcode.BarcodeScannerOptions.Builder  	getClient /com.google.mlkit.vision.barcode.BarcodeScanning  Barcode &com.google.mlkit.vision.barcode.common  Address .com.google.mlkit.vision.barcode.common.Barcode  CalendarDateTime .com.google.mlkit.vision.barcode.common.Barcode  
CalendarEvent .com.google.mlkit.vision.barcode.common.Barcode  ContactInfo .com.google.mlkit.vision.barcode.common.Barcode  
DriverLicense .com.google.mlkit.vision.barcode.common.Barcode  Email .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_ALL_FORMATS .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_AZTEC .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_CODABAR .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_CODE_128 .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_CODE_39 .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_CODE_93 .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_DATA_MATRIX .com.google.mlkit.vision.barcode.common.Barcode  
FORMAT_EAN_13 .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_EAN_8 .com.google.mlkit.vision.barcode.common.Barcode  
FORMAT_ITF .com.google.mlkit.vision.barcode.common.Barcode  
FORMAT_PDF417 .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_QR_CODE .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_UNKNOWN .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_UPC_A .com.google.mlkit.vision.barcode.common.Barcode  FORMAT_UPC_E .com.google.mlkit.vision.barcode.common.Barcode  GeoPoint .com.google.mlkit.vision.barcode.common.Barcode  
PersonName .com.google.mlkit.vision.barcode.common.Barcode  Phone .com.google.mlkit.vision.barcode.common.Barcode  Sms .com.google.mlkit.vision.barcode.common.Barcode  UrlBookmark .com.google.mlkit.vision.barcode.common.Barcode  WiFi .com.google.mlkit.vision.barcode.common.Barcode  boundingBox .com.google.mlkit.vision.barcode.common.Barcode  
calendarEvent .com.google.mlkit.vision.barcode.common.Barcode  contactInfo .com.google.mlkit.vision.barcode.common.Barcode  cornerPoints .com.google.mlkit.vision.barcode.common.Barcode  data .com.google.mlkit.vision.barcode.common.Barcode  displayValue .com.google.mlkit.vision.barcode.common.Barcode  
driverLicense .com.google.mlkit.vision.barcode.common.Barcode  email .com.google.mlkit.vision.barcode.common.Barcode  format .com.google.mlkit.vision.barcode.common.Barcode  geoPoint .com.google.mlkit.vision.barcode.common.Barcode  getBOUNDINGBox .com.google.mlkit.vision.barcode.common.Barcode  getBoundingBox .com.google.mlkit.vision.barcode.common.Barcode  getCALENDAREvent .com.google.mlkit.vision.barcode.common.Barcode  getCONTACTInfo .com.google.mlkit.vision.barcode.common.Barcode  getCORNERPoints .com.google.mlkit.vision.barcode.common.Barcode  getCalendarEvent .com.google.mlkit.vision.barcode.common.Barcode  getContactInfo .com.google.mlkit.vision.barcode.common.Barcode  getCornerPoints .com.google.mlkit.vision.barcode.common.Barcode  getDATA .com.google.mlkit.vision.barcode.common.Barcode  getDISPLAYValue .com.google.mlkit.vision.barcode.common.Barcode  getDRIVERLicense .com.google.mlkit.vision.barcode.common.Barcode  getData .com.google.mlkit.vision.barcode.common.Barcode  getDisplayValue .com.google.mlkit.vision.barcode.common.Barcode  getDriverLicense .com.google.mlkit.vision.barcode.common.Barcode  getEMAIL .com.google.mlkit.vision.barcode.common.Barcode  getEmail .com.google.mlkit.vision.barcode.common.Barcode  	getFORMAT .com.google.mlkit.vision.barcode.common.Barcode  	getFormat .com.google.mlkit.vision.barcode.common.Barcode  getGEOPoint .com.google.mlkit.vision.barcode.common.Barcode  getGeoPoint .com.google.mlkit.vision.barcode.common.Barcode  getMAP .com.google.mlkit.vision.barcode.common.Barcode  getMAPOf .com.google.mlkit.vision.barcode.common.Barcode  getMap .com.google.mlkit.vision.barcode.common.Barcode  getMapOf .com.google.mlkit.vision.barcode.common.Barcode  getPHONE .com.google.mlkit.vision.barcode.common.Barcode  getPhone .com.google.mlkit.vision.barcode.common.Barcode  getRAWBytes .com.google.mlkit.vision.barcode.common.Barcode  getRAWValue .com.google.mlkit.vision.barcode.common.Barcode  getRawBytes .com.google.mlkit.vision.barcode.common.Barcode  getRawValue .com.google.mlkit.vision.barcode.common.Barcode  getSMS .com.google.mlkit.vision.barcode.common.Barcode  getSms .com.google.mlkit.vision.barcode.common.Barcode  getTO .com.google.mlkit.vision.barcode.common.Barcode  getTo .com.google.mlkit.vision.barcode.common.Barcode  getURL .com.google.mlkit.vision.barcode.common.Barcode  getUrl .com.google.mlkit.vision.barcode.common.Barcode  getVALUEType .com.google.mlkit.vision.barcode.common.Barcode  getValueType .com.google.mlkit.vision.barcode.common.Barcode  getWIFI .com.google.mlkit.vision.barcode.common.Barcode  getWifi .com.google.mlkit.vision.barcode.common.Barcode  map .com.google.mlkit.vision.barcode.common.Barcode  mapOf .com.google.mlkit.vision.barcode.common.Barcode  phone .com.google.mlkit.vision.barcode.common.Barcode  rawBytes .com.google.mlkit.vision.barcode.common.Barcode  rawValue .com.google.mlkit.vision.barcode.common.Barcode  setBoundingBox .com.google.mlkit.vision.barcode.common.Barcode  setCalendarEvent .com.google.mlkit.vision.barcode.common.Barcode  setContactInfo .com.google.mlkit.vision.barcode.common.Barcode  setCornerPoints .com.google.mlkit.vision.barcode.common.Barcode  setDisplayValue .com.google.mlkit.vision.barcode.common.Barcode  setDriverLicense .com.google.mlkit.vision.barcode.common.Barcode  setEmail .com.google.mlkit.vision.barcode.common.Barcode  	setFormat .com.google.mlkit.vision.barcode.common.Barcode  setGeoPoint .com.google.mlkit.vision.barcode.common.Barcode  setPhone .com.google.mlkit.vision.barcode.common.Barcode  setRawBytes .com.google.mlkit.vision.barcode.common.Barcode  setRawValue .com.google.mlkit.vision.barcode.common.Barcode  setSms .com.google.mlkit.vision.barcode.common.Barcode  setUrl .com.google.mlkit.vision.barcode.common.Barcode  setValueType .com.google.mlkit.vision.barcode.common.Barcode  setWifi .com.google.mlkit.vision.barcode.common.Barcode  size .com.google.mlkit.vision.barcode.common.Barcode  sms .com.google.mlkit.vision.barcode.common.Barcode  to .com.google.mlkit.vision.barcode.common.Barcode  url .com.google.mlkit.vision.barcode.common.Barcode  	valueType .com.google.mlkit.vision.barcode.common.Barcode  wifi .com.google.mlkit.vision.barcode.common.Barcode  addressLines 6com.google.mlkit.vision.barcode.common.Barcode.Address  data 6com.google.mlkit.vision.barcode.common.Barcode.Address  getADDRESSLines 6com.google.mlkit.vision.barcode.common.Barcode.Address  getAddressLines 6com.google.mlkit.vision.barcode.common.Barcode.Address  getDATA 6com.google.mlkit.vision.barcode.common.Barcode.Address  getData 6com.google.mlkit.vision.barcode.common.Barcode.Address  getMAP 6com.google.mlkit.vision.barcode.common.Barcode.Address  getMAPOf 6com.google.mlkit.vision.barcode.common.Barcode.Address  getMap 6com.google.mlkit.vision.barcode.common.Barcode.Address  getMapOf 6com.google.mlkit.vision.barcode.common.Barcode.Address  getTO 6com.google.mlkit.vision.barcode.common.Barcode.Address  getTYPE 6com.google.mlkit.vision.barcode.common.Barcode.Address  getTo 6com.google.mlkit.vision.barcode.common.Barcode.Address  getType 6com.google.mlkit.vision.barcode.common.Barcode.Address  map 6com.google.mlkit.vision.barcode.common.Barcode.Address  mapOf 6com.google.mlkit.vision.barcode.common.Barcode.Address  setAddressLines 6com.google.mlkit.vision.barcode.common.Barcode.Address  setType 6com.google.mlkit.vision.barcode.common.Barcode.Address  to 6com.google.mlkit.vision.barcode.common.Barcode.Address  type 6com.google.mlkit.vision.barcode.common.Barcode.Address  getRAWValue ?com.google.mlkit.vision.barcode.common.Barcode.CalendarDateTime  getRawValue ?com.google.mlkit.vision.barcode.common.Barcode.CalendarDateTime  rawValue ?com.google.mlkit.vision.barcode.common.Barcode.CalendarDateTime  setRawValue ?com.google.mlkit.vision.barcode.common.Barcode.CalendarDateTime  data <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  description <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  end <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  getDATA <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  getDESCRIPTION <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  getData <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  getDescription <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  getEND <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  getEnd <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  getLOCATION <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  getLocation <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  getMAPOf <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  getMapOf <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  getORGANIZER <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  getOrganizer <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  getSTART <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  	getSTATUS <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  
getSUMMARY <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  getStart <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  	getStatus <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  
getSummary <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  getTO <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  getTo <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  location <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  mapOf <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  	organizer <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  setDescription <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  setEnd <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  setLocation <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  setOrganizer <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  setStart <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  	setStatus <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  
setSummary <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  start <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  status <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  summary <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  to <com.google.mlkit.vision.barcode.common.Barcode.CalendarEvent  	addresses :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  data :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  emails :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  getADDRESSES :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  getAddresses :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  getDATA :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  getData :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  	getEMAILS :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  	getEmails :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  getMAP :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  getMAPOf :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  getMap :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  getMapOf :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  getNAME :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  getName :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  getORGANIZATION :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  getOrganization :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  	getPHONES :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  	getPhones :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  getTITLE :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  getTO :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  getTitle :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  getTo :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  getURLS :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  getUrls :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  map :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  mapOf :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  name :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  organization :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  phones :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  setAddresses :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  	setEmails :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  setName :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  setOrganization :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  	setPhones :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  setTitle :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  setUrls :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  title :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  to :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  urls :com.google.mlkit.vision.barcode.common.Barcode.ContactInfo  addressCity <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  addressState <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  
addressStreet <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  
addressZip <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  	birthDate <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  data <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  documentType <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  
expiryDate <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  	firstName <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  gender <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getADDRESSCity <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getADDRESSState <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getADDRESSStreet <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  
getADDRESSZip <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getAddressCity <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getAddressState <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getAddressStreet <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  
getAddressZip <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getBIRTHDate <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getBirthDate <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getDATA <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getDOCUMENTType <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getData <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getDocumentType <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  
getEXPIRYDate <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  
getExpiryDate <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getFIRSTName <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getFirstName <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  	getGENDER <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  	getGender <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getISSUEDate <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getISSUINGCountry <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getIssueDate <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getIssuingCountry <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getLASTName <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getLICENSENumber <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getLastName <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getLicenseNumber <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getMAPOf <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  
getMIDDLEName <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getMapOf <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  
getMiddleName <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getTO <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  getTo <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  	issueDate <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  issuingCountry <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  lastName <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  
licenseNumber <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  mapOf <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  
middleName <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  setAddressCity <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  setAddressState <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  setAddressStreet <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  
setAddressZip <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  setBirthDate <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  setDocumentType <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  
setExpiryDate <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  setFirstName <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  	setGender <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  setIssueDate <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  setIssuingCountry <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  setLastName <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  setLicenseNumber <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  
setMiddleName <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  to <com.google.mlkit.vision.barcode.common.Barcode.DriverLicense  address 4com.google.mlkit.vision.barcode.common.Barcode.Email  body 4com.google.mlkit.vision.barcode.common.Barcode.Email  data 4com.google.mlkit.vision.barcode.common.Barcode.Email  
getADDRESS 4com.google.mlkit.vision.barcode.common.Barcode.Email  
getAddress 4com.google.mlkit.vision.barcode.common.Barcode.Email  getBODY 4com.google.mlkit.vision.barcode.common.Barcode.Email  getBody 4com.google.mlkit.vision.barcode.common.Barcode.Email  getDATA 4com.google.mlkit.vision.barcode.common.Barcode.Email  getData 4com.google.mlkit.vision.barcode.common.Barcode.Email  getMAPOf 4com.google.mlkit.vision.barcode.common.Barcode.Email  getMapOf 4com.google.mlkit.vision.barcode.common.Barcode.Email  
getSUBJECT 4com.google.mlkit.vision.barcode.common.Barcode.Email  
getSubject 4com.google.mlkit.vision.barcode.common.Barcode.Email  getTO 4com.google.mlkit.vision.barcode.common.Barcode.Email  getTYPE 4com.google.mlkit.vision.barcode.common.Barcode.Email  getTo 4com.google.mlkit.vision.barcode.common.Barcode.Email  getType 4com.google.mlkit.vision.barcode.common.Barcode.Email  mapOf 4com.google.mlkit.vision.barcode.common.Barcode.Email  
setAddress 4com.google.mlkit.vision.barcode.common.Barcode.Email  setBody 4com.google.mlkit.vision.barcode.common.Barcode.Email  
setSubject 4com.google.mlkit.vision.barcode.common.Barcode.Email  setType 4com.google.mlkit.vision.barcode.common.Barcode.Email  subject 4com.google.mlkit.vision.barcode.common.Barcode.Email  to 4com.google.mlkit.vision.barcode.common.Barcode.Email  type 4com.google.mlkit.vision.barcode.common.Barcode.Email  data 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  getDATA 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  getData 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  getLAT 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  getLNG 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  getLat 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  getLng 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  getMAPOf 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  getMapOf 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  getTO 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  getTo 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  lat 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  lng 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  mapOf 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  setLat 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  setLng 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  to 7com.google.mlkit.vision.barcode.common.Barcode.GeoPoint  data 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  first 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  
formattedName 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  getDATA 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  getData 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  getFIRST 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  getFORMATTEDName 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  getFirst 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  getFormattedName 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  getLAST 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  getLast 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  getMAPOf 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  	getMIDDLE 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  getMapOf 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  	getMiddle 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  	getPREFIX 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  getPRONUNCIATION 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  	getPrefix 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  getPronunciation 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  	getSUFFIX 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  	getSuffix 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  getTO 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  getTo 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  last 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  mapOf 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  middle 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  prefix 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  
pronunciation 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  setFirst 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  setFormattedName 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  setLast 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  	setMiddle 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  	setPrefix 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  setPronunciation 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  	setSuffix 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  suffix 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  to 9com.google.mlkit.vision.barcode.common.Barcode.PersonName  data 4com.google.mlkit.vision.barcode.common.Barcode.Phone  getDATA 4com.google.mlkit.vision.barcode.common.Barcode.Phone  getData 4com.google.mlkit.vision.barcode.common.Barcode.Phone  getMAPOf 4com.google.mlkit.vision.barcode.common.Barcode.Phone  getMapOf 4com.google.mlkit.vision.barcode.common.Barcode.Phone  	getNUMBER 4com.google.mlkit.vision.barcode.common.Barcode.Phone  	getNumber 4com.google.mlkit.vision.barcode.common.Barcode.Phone  getTO 4com.google.mlkit.vision.barcode.common.Barcode.Phone  getTYPE 4com.google.mlkit.vision.barcode.common.Barcode.Phone  getTo 4com.google.mlkit.vision.barcode.common.Barcode.Phone  getType 4com.google.mlkit.vision.barcode.common.Barcode.Phone  mapOf 4com.google.mlkit.vision.barcode.common.Barcode.Phone  number 4com.google.mlkit.vision.barcode.common.Barcode.Phone  	setNumber 4com.google.mlkit.vision.barcode.common.Barcode.Phone  setType 4com.google.mlkit.vision.barcode.common.Barcode.Phone  to 4com.google.mlkit.vision.barcode.common.Barcode.Phone  type 4com.google.mlkit.vision.barcode.common.Barcode.Phone  data 2com.google.mlkit.vision.barcode.common.Barcode.Sms  getDATA 2com.google.mlkit.vision.barcode.common.Barcode.Sms  getData 2com.google.mlkit.vision.barcode.common.Barcode.Sms  getMAPOf 2com.google.mlkit.vision.barcode.common.Barcode.Sms  
getMESSAGE 2com.google.mlkit.vision.barcode.common.Barcode.Sms  getMapOf 2com.google.mlkit.vision.barcode.common.Barcode.Sms  
getMessage 2com.google.mlkit.vision.barcode.common.Barcode.Sms  getPHONENumber 2com.google.mlkit.vision.barcode.common.Barcode.Sms  getPhoneNumber 2com.google.mlkit.vision.barcode.common.Barcode.Sms  getTO 2com.google.mlkit.vision.barcode.common.Barcode.Sms  getTo 2com.google.mlkit.vision.barcode.common.Barcode.Sms  mapOf 2com.google.mlkit.vision.barcode.common.Barcode.Sms  message 2com.google.mlkit.vision.barcode.common.Barcode.Sms  phoneNumber 2com.google.mlkit.vision.barcode.common.Barcode.Sms  
setMessage 2com.google.mlkit.vision.barcode.common.Barcode.Sms  setPhoneNumber 2com.google.mlkit.vision.barcode.common.Barcode.Sms  to 2com.google.mlkit.vision.barcode.common.Barcode.Sms  data :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  getDATA :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  getData :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  getMAPOf :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  getMapOf :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  getTITLE :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  getTO :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  getTitle :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  getTo :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  getURL :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  getUrl :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  mapOf :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  setTitle :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  setUrl :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  title :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  to :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  url :com.google.mlkit.vision.barcode.common.Barcode.UrlBookmark  data 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  encryptionType 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  getDATA 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  getData 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  getENCRYPTIONType 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  getEncryptionType 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  getMAPOf 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  getMapOf 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  getPASSWORD 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  getPassword 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  getSSID 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  getSsid 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  getTO 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  getTo 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  mapOf 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  password 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  setEncryptionType 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  setPassword 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  setSsid 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  ssid 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  to 3com.google.mlkit.vision.barcode.common.Barcode.WiFi  
InputImage com.google.mlkit.vision.common  fromFilePath )com.google.mlkit.vision.common.InputImage  fromMediaImage )com.google.mlkit.vision.common.InputImage  ActivityCompat dev.steenbakker.mobile_scanner  
AlreadyPaused dev.steenbakker.mobile_scanner  AlreadyStarted dev.steenbakker.mobile_scanner  AlreadyStopped dev.steenbakker.mobile_scanner  AnalyzerErrorCallback dev.steenbakker.mobile_scanner  AnalyzerSuccessCallback dev.steenbakker.mobile_scanner  Any dev.steenbakker.mobile_scanner  Array dev.steenbakker.mobile_scanner  BarcodeFormats dev.steenbakker.mobile_scanner  BarcodeHandler dev.steenbakker.mobile_scanner  BarcodeScannerOptions dev.steenbakker.mobile_scanner  BarcodeScanning dev.steenbakker.mobile_scanner  Bitmap dev.steenbakker.mobile_scanner  Boolean dev.steenbakker.mobile_scanner  Build dev.steenbakker.mobile_scanner  	ByteArray dev.steenbakker.mobile_scanner  ByteArrayOutputStream dev.steenbakker.mobile_scanner  CameraError dev.steenbakker.mobile_scanner  CameraSelector dev.steenbakker.mobile_scanner  Context dev.steenbakker.mobile_scanner  
ContextCompat dev.steenbakker.mobile_scanner  CoroutineScope dev.steenbakker.mobile_scanner  DetectionSpeed dev.steenbakker.mobile_scanner  Dispatchers dev.steenbakker.mobile_scanner  Double dev.steenbakker.mobile_scanner  EventChannel dev.steenbakker.mobile_scanner  	Exception dev.steenbakker.mobile_scanner  File dev.steenbakker.mobile_scanner  Float dev.steenbakker.mobile_scanner  Handler dev.steenbakker.mobile_scanner  IllegalArgumentException dev.steenbakker.mobile_scanner  
ImageAnalysis dev.steenbakker.mobile_scanner  ImageFormat dev.steenbakker.mobile_scanner  
InputImage dev.steenbakker.mobile_scanner  Int dev.steenbakker.mobile_scanner  IntArray dev.steenbakker.mobile_scanner  List dev.steenbakker.mobile_scanner  Long dev.steenbakker.mobile_scanner  Looper dev.steenbakker.mobile_scanner  Map dev.steenbakker.mobile_scanner  Matrix dev.steenbakker.mobile_scanner  
MethodChannel dev.steenbakker.mobile_scanner  
MobileScanner dev.steenbakker.mobile_scanner  MobileScannerCallback dev.steenbakker.mobile_scanner  MobileScannerErrorCallback dev.steenbakker.mobile_scanner  MobileScannerErrorCodes dev.steenbakker.mobile_scanner  MobileScannerHandler dev.steenbakker.mobile_scanner  MobileScannerPermissions dev.steenbakker.mobile_scanner   MobileScannerPermissionsListener dev.steenbakker.mobile_scanner  MobileScannerPlugin dev.steenbakker.mobile_scanner  MobileScannerStartParameters dev.steenbakker.mobile_scanner  MobileScannerStartedCallback dev.steenbakker.mobile_scanner  MutableList dev.steenbakker.mobile_scanner  NoCamera dev.steenbakker.mobile_scanner  PackageManager dev.steenbakker.mobile_scanner  Preview dev.steenbakker.mobile_scanner  ProcessCameraProvider dev.steenbakker.mobile_scanner  REQUEST_CODE dev.steenbakker.mobile_scanner  Rect dev.steenbakker.mobile_scanner  ResolutionSelector dev.steenbakker.mobile_scanner  ResolutionStrategy dev.steenbakker.mobile_scanner  Size dev.steenbakker.mobile_scanner  String dev.steenbakker.mobile_scanner  Suppress dev.steenbakker.mobile_scanner  Surface dev.steenbakker.mobile_scanner  
TorchState dev.steenbakker.mobile_scanner  TorchStateCallback dev.steenbakker.mobile_scanner  Unit dev.steenbakker.mobile_scanner  Uri dev.steenbakker.mobile_scanner  YuvImage dev.steenbakker.mobile_scanner  YuvToRgbConverter dev.steenbakker.mobile_scanner  ZoomNotInRange dev.steenbakker.mobile_scanner  ZoomScaleStateCallback dev.steenbakker.mobile_scanner  ZoomWhenStopped dev.steenbakker.mobile_scanner  activity dev.steenbakker.mobile_scanner  addListener dev.steenbakker.mobile_scanner  apply dev.steenbakker.mobile_scanner  arrayOf dev.steenbakker.mobile_scanner  camera dev.steenbakker.mobile_scanner  
captureOutput dev.steenbakker.mobile_scanner  data dev.steenbakker.mobile_scanner  emptyMap dev.steenbakker.mobile_scanner  first dev.steenbakker.mobile_scanner  
getResolution dev.steenbakker.mobile_scanner  invoke dev.steenbakker.mobile_scanner  isEmpty dev.steenbakker.mobile_scanner  
isNotEmpty dev.steenbakker.mobile_scanner  launch dev.steenbakker.mobile_scanner  let dev.steenbakker.mobile_scanner  listener dev.steenbakker.mobile_scanner  map dev.steenbakker.mobile_scanner  
mapNotNull dev.steenbakker.mobile_scanner  mapOf dev.steenbakker.mobile_scanner  mobileScannerCallback dev.steenbakker.mobile_scanner  
mutableListOf dev.steenbakker.mobile_scanner  ongoing dev.steenbakker.mobile_scanner  
permission dev.steenbakker.mobile_scanner  provideSurface dev.steenbakker.mobile_scanner  rotateBitmap dev.steenbakker.mobile_scanner  
roundToInt dev.steenbakker.mobile_scanner  size dev.steenbakker.mobile_scanner  sorted dev.steenbakker.mobile_scanner  to dev.steenbakker.mobile_scanner  toByteArray dev.steenbakker.mobile_scanner  
toIntArray dev.steenbakker.mobile_scanner  Any -dev.steenbakker.mobile_scanner.BarcodeHandler  BinaryMessenger -dev.steenbakker.mobile_scanner.BarcodeHandler  EventChannel -dev.steenbakker.mobile_scanner.BarcodeHandler  Handler -dev.steenbakker.mobile_scanner.BarcodeHandler  Looper -dev.steenbakker.mobile_scanner.BarcodeHandler  Map -dev.steenbakker.mobile_scanner.BarcodeHandler  String -dev.steenbakker.mobile_scanner.BarcodeHandler  eventChannel -dev.steenbakker.mobile_scanner.BarcodeHandler  	eventSink -dev.steenbakker.mobile_scanner.BarcodeHandler  publishError -dev.steenbakker.mobile_scanner.BarcodeHandler  publishEvent -dev.steenbakker.mobile_scanner.BarcodeHandler  Activity ,dev.steenbakker.mobile_scanner.MobileScanner  
AlreadyPaused ,dev.steenbakker.mobile_scanner.MobileScanner  AlreadyStarted ,dev.steenbakker.mobile_scanner.MobileScanner  AlreadyStopped ,dev.steenbakker.mobile_scanner.MobileScanner  AnalyzerErrorCallback ,dev.steenbakker.mobile_scanner.MobileScanner  AnalyzerSuccessCallback ,dev.steenbakker.mobile_scanner.MobileScanner  Any ,dev.steenbakker.mobile_scanner.MobileScanner  Barcode ,dev.steenbakker.mobile_scanner.MobileScanner  BarcodeScanner ,dev.steenbakker.mobile_scanner.MobileScanner  BarcodeScannerOptions ,dev.steenbakker.mobile_scanner.MobileScanner  BarcodeScanning ,dev.steenbakker.mobile_scanner.MobileScanner  Bitmap ,dev.steenbakker.mobile_scanner.MobileScanner  Boolean ,dev.steenbakker.mobile_scanner.MobileScanner  Build ,dev.steenbakker.mobile_scanner.MobileScanner  ByteArrayOutputStream ,dev.steenbakker.mobile_scanner.MobileScanner  Camera ,dev.steenbakker.mobile_scanner.MobileScanner  CameraError ,dev.steenbakker.mobile_scanner.MobileScanner  CameraSelector ,dev.steenbakker.mobile_scanner.MobileScanner  Context ,dev.steenbakker.mobile_scanner.MobileScanner  
ContextCompat ,dev.steenbakker.mobile_scanner.MobileScanner  CoroutineScope ,dev.steenbakker.mobile_scanner.MobileScanner  DetectionSpeed ,dev.steenbakker.mobile_scanner.MobileScanner  Dispatchers ,dev.steenbakker.mobile_scanner.MobileScanner  DisplayManager ,dev.steenbakker.mobile_scanner.MobileScanner  Double ,dev.steenbakker.mobile_scanner.MobileScanner  	Exception ,dev.steenbakker.mobile_scanner.MobileScanner  ExperimentalGetImage ,dev.steenbakker.mobile_scanner.MobileScanner  Float ,dev.steenbakker.mobile_scanner.MobileScanner  Handler ,dev.steenbakker.mobile_scanner.MobileScanner  IllegalArgumentException ,dev.steenbakker.mobile_scanner.MobileScanner  
ImageAnalysis ,dev.steenbakker.mobile_scanner.MobileScanner  
ImageProxy ,dev.steenbakker.mobile_scanner.MobileScanner  
InputImage ,dev.steenbakker.mobile_scanner.MobileScanner  Int ,dev.steenbakker.mobile_scanner.MobileScanner  LifecycleOwner ,dev.steenbakker.mobile_scanner.MobileScanner  List ,dev.steenbakker.mobile_scanner.MobileScanner  Long ,dev.steenbakker.mobile_scanner.MobileScanner  Looper ,dev.steenbakker.mobile_scanner.MobileScanner  Map ,dev.steenbakker.mobile_scanner.MobileScanner  Matrix ,dev.steenbakker.mobile_scanner.MobileScanner  MobileScannerCallback ,dev.steenbakker.mobile_scanner.MobileScanner  MobileScannerErrorCallback ,dev.steenbakker.mobile_scanner.MobileScanner  MobileScannerStartParameters ,dev.steenbakker.mobile_scanner.MobileScanner  MobileScannerStartedCallback ,dev.steenbakker.mobile_scanner.MobileScanner  MutableList ,dev.steenbakker.mobile_scanner.MobileScanner  NoCamera ,dev.steenbakker.mobile_scanner.MobileScanner  Preview ,dev.steenbakker.mobile_scanner.MobileScanner  ProcessCameraProvider ,dev.steenbakker.mobile_scanner.MobileScanner  Rect ,dev.steenbakker.mobile_scanner.MobileScanner  ResolutionSelector ,dev.steenbakker.mobile_scanner.MobileScanner  ResolutionStrategy ,dev.steenbakker.mobile_scanner.MobileScanner  Size ,dev.steenbakker.mobile_scanner.MobileScanner  String ,dev.steenbakker.mobile_scanner.MobileScanner  Suppress ,dev.steenbakker.mobile_scanner.MobileScanner  Surface ,dev.steenbakker.mobile_scanner.MobileScanner  TextureRegistry ,dev.steenbakker.mobile_scanner.MobileScanner  
TorchState ,dev.steenbakker.mobile_scanner.MobileScanner  TorchStateCallback ,dev.steenbakker.mobile_scanner.MobileScanner  Unit ,dev.steenbakker.mobile_scanner.MobileScanner  Uri ,dev.steenbakker.mobile_scanner.MobileScanner  VisibleForTesting ,dev.steenbakker.mobile_scanner.MobileScanner  
WindowManager ,dev.steenbakker.mobile_scanner.MobileScanner  YuvToRgbConverter ,dev.steenbakker.mobile_scanner.MobileScanner  ZoomNotInRange ,dev.steenbakker.mobile_scanner.MobileScanner  ZoomScaleStateCallback ,dev.steenbakker.mobile_scanner.MobileScanner  ZoomWhenStopped ,dev.steenbakker.mobile_scanner.MobileScanner  activity ,dev.steenbakker.mobile_scanner.MobileScanner  addListener ,dev.steenbakker.mobile_scanner.MobileScanner  analyzeImage ,dev.steenbakker.mobile_scanner.MobileScanner  apply ,dev.steenbakker.mobile_scanner.MobileScanner  barcodeScannerFactory ,dev.steenbakker.mobile_scanner.MobileScanner  camera ,dev.steenbakker.mobile_scanner.MobileScanner  cameraProvider ,dev.steenbakker.mobile_scanner.MobileScanner  cameraSelector ,dev.steenbakker.mobile_scanner.MobileScanner  
captureOutput ,dev.steenbakker.mobile_scanner.MobileScanner  data ,dev.steenbakker.mobile_scanner.MobileScanner  defaultBarcodeScannerFactory ,dev.steenbakker.mobile_scanner.MobileScanner  detectionSpeed ,dev.steenbakker.mobile_scanner.MobileScanner  detectionTimeout ,dev.steenbakker.mobile_scanner.MobileScanner  displayListener ,dev.steenbakker.mobile_scanner.MobileScanner  dispose ,dev.steenbakker.mobile_scanner.MobileScanner  getADDListener ,dev.steenbakker.mobile_scanner.MobileScanner  getAPPLY ,dev.steenbakker.mobile_scanner.MobileScanner  getAddListener ,dev.steenbakker.mobile_scanner.MobileScanner  getApply ,dev.steenbakker.mobile_scanner.MobileScanner  
getISNotEmpty ,dev.steenbakker.mobile_scanner.MobileScanner  
getIsNotEmpty ,dev.steenbakker.mobile_scanner.MobileScanner  	getLAUNCH ,dev.steenbakker.mobile_scanner.MobileScanner  getLET ,dev.steenbakker.mobile_scanner.MobileScanner  	getLaunch ,dev.steenbakker.mobile_scanner.MobileScanner  getLet ,dev.steenbakker.mobile_scanner.MobileScanner  getMAP ,dev.steenbakker.mobile_scanner.MobileScanner  
getMAPNotNull ,dev.steenbakker.mobile_scanner.MobileScanner  getMUTABLEListOf ,dev.steenbakker.mobile_scanner.MobileScanner  getMap ,dev.steenbakker.mobile_scanner.MobileScanner  
getMapNotNull ,dev.steenbakker.mobile_scanner.MobileScanner  getMutableListOf ,dev.steenbakker.mobile_scanner.MobileScanner  getPROVIDESurface ,dev.steenbakker.mobile_scanner.MobileScanner  getProvideSurface ,dev.steenbakker.mobile_scanner.MobileScanner  
getROUNDToInt ,dev.steenbakker.mobile_scanner.MobileScanner  
getResolution ,dev.steenbakker.mobile_scanner.MobileScanner  
getRoundToInt ,dev.steenbakker.mobile_scanner.MobileScanner  	getSORTED ,dev.steenbakker.mobile_scanner.MobileScanner  	getSorted ,dev.steenbakker.mobile_scanner.MobileScanner  invoke ,dev.steenbakker.mobile_scanner.MobileScanner  isBarcodeInScanWindow ,dev.steenbakker.mobile_scanner.MobileScanner  
isNotEmpty ,dev.steenbakker.mobile_scanner.MobileScanner  isPaused ,dev.steenbakker.mobile_scanner.MobileScanner  	isStopped ,dev.steenbakker.mobile_scanner.MobileScanner  lastScanned ,dev.steenbakker.mobile_scanner.MobileScanner  launch ,dev.steenbakker.mobile_scanner.MobileScanner  let ,dev.steenbakker.mobile_scanner.MobileScanner  map ,dev.steenbakker.mobile_scanner.MobileScanner  
mapNotNull ,dev.steenbakker.mobile_scanner.MobileScanner  mobileScannerCallback ,dev.steenbakker.mobile_scanner.MobileScanner  mobileScannerErrorCallback ,dev.steenbakker.mobile_scanner.MobileScanner  
mutableListOf ,dev.steenbakker.mobile_scanner.MobileScanner  pause ,dev.steenbakker.mobile_scanner.MobileScanner  pauseCamera ,dev.steenbakker.mobile_scanner.MobileScanner  preview ,dev.steenbakker.mobile_scanner.MobileScanner  provideSurface ,dev.steenbakker.mobile_scanner.MobileScanner  
releaseCamera ,dev.steenbakker.mobile_scanner.MobileScanner  
resetScale ,dev.steenbakker.mobile_scanner.MobileScanner  returnImage ,dev.steenbakker.mobile_scanner.MobileScanner  rotateBitmap ,dev.steenbakker.mobile_scanner.MobileScanner  
roundToInt ,dev.steenbakker.mobile_scanner.MobileScanner  
scanWindow ,dev.steenbakker.mobile_scanner.MobileScanner  scanner ,dev.steenbakker.mobile_scanner.MobileScanner  scannerTimeout ,dev.steenbakker.mobile_scanner.MobileScanner  setScale ,dev.steenbakker.mobile_scanner.MobileScanner  sorted ,dev.steenbakker.mobile_scanner.MobileScanner  start ,dev.steenbakker.mobile_scanner.MobileScanner  stop ,dev.steenbakker.mobile_scanner.MobileScanner  textureEntry ,dev.steenbakker.mobile_scanner.MobileScanner  textureRegistry ,dev.steenbakker.mobile_scanner.MobileScanner  toggleTorch ,dev.steenbakker.mobile_scanner.MobileScanner  Activity 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
AlreadyPaused 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  AlreadyStarted 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  AlreadyStopped 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  AnalyzerErrorCallback 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  AnalyzerSuccessCallback 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Any 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Barcode 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  BarcodeScanner 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  BarcodeScannerOptions 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  BarcodeScanning 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Bitmap 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Boolean 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Build 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  ByteArrayOutputStream 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Camera 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  CameraError 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  CameraSelector 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Context 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
ContextCompat 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  CoroutineScope 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  DetectionSpeed 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Dispatchers 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  DisplayManager 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Double 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  	Exception 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  ExperimentalGetImage 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Float 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Handler 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  IllegalArgumentException 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
ImageAnalysis 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
ImageProxy 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
InputImage 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Int 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  LifecycleOwner 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  List 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Long 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Looper 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Map 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Matrix 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  MobileScannerCallback 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  MobileScannerErrorCallback 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  MobileScannerStartParameters 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  MobileScannerStartedCallback 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  MutableList 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  NoCamera 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Preview 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  ProcessCameraProvider 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Rect 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  ResolutionSelector 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  ResolutionStrategy 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Size 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  String 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Suppress 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Surface 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  TextureRegistry 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
TorchState 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  TorchStateCallback 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Unit 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  Uri 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  VisibleForTesting 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
WindowManager 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  YuvToRgbConverter 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  ZoomNotInRange 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  ZoomScaleStateCallback 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  ZoomWhenStopped 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  activity 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  addListener 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  apply 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  camera 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
captureOutput 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  data 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  defaultBarcodeScannerFactory 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  getADDListener 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  getAPPLY 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  getAddListener 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  getApply 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
getISNotEmpty 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
getIsNotEmpty 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  	getLAUNCH 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  getLET 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  	getLaunch 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  getLet 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  getMAP 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
getMAPNotNull 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  getMUTABLEListOf 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  getMap 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
getMapNotNull 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  getMutableListOf 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  getPROVIDESurface 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  getProvideSurface 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
getROUNDToInt 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
getResolution 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
getRoundToInt 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  	getSORTED 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  	getSorted 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  invoke 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
isNotEmpty 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  launch 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  let 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  map 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
mapNotNull 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  mobileScannerCallback 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
mutableListOf 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  provideSurface 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  rotateBitmap 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  
roundToInt 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  sorted 6dev.steenbakker.mobile_scanner.MobileScanner.Companion  getGETResolution Qdev.steenbakker.mobile_scanner.MobileScanner.start.<anonymous>.<no name provided>  getGetResolution Qdev.steenbakker.mobile_scanner.MobileScanner.start.<anonymous>.<no name provided>  Activity 3dev.steenbakker.mobile_scanner.MobileScannerHandler  ActivityPluginBinding 3dev.steenbakker.mobile_scanner.MobileScannerHandler  
AlreadyPaused 3dev.steenbakker.mobile_scanner.MobileScannerHandler  AlreadyStarted 3dev.steenbakker.mobile_scanner.MobileScannerHandler  AlreadyStopped 3dev.steenbakker.mobile_scanner.MobileScannerHandler  AnalyzerErrorCallback 3dev.steenbakker.mobile_scanner.MobileScannerHandler  AnalyzerSuccessCallback 3dev.steenbakker.mobile_scanner.MobileScannerHandler  Any 3dev.steenbakker.mobile_scanner.MobileScannerHandler  BarcodeFormats 3dev.steenbakker.mobile_scanner.MobileScannerHandler  BarcodeHandler 3dev.steenbakker.mobile_scanner.MobileScannerHandler  BarcodeScannerOptions 3dev.steenbakker.mobile_scanner.MobileScannerHandler  BinaryMessenger 3dev.steenbakker.mobile_scanner.MobileScannerHandler  Boolean 3dev.steenbakker.mobile_scanner.MobileScannerHandler  	ByteArray 3dev.steenbakker.mobile_scanner.MobileScannerHandler  CameraError 3dev.steenbakker.mobile_scanner.MobileScannerHandler  CameraSelector 3dev.steenbakker.mobile_scanner.MobileScannerHandler  DetectionSpeed 3dev.steenbakker.mobile_scanner.MobileScannerHandler  Double 3dev.steenbakker.mobile_scanner.MobileScannerHandler  	Exception 3dev.steenbakker.mobile_scanner.MobileScannerHandler  ExperimentalGetImage 3dev.steenbakker.mobile_scanner.MobileScannerHandler  File 3dev.steenbakker.mobile_scanner.MobileScannerHandler  Float 3dev.steenbakker.mobile_scanner.MobileScannerHandler  Handler 3dev.steenbakker.mobile_scanner.MobileScannerHandler  Int 3dev.steenbakker.mobile_scanner.MobileScannerHandler  List 3dev.steenbakker.mobile_scanner.MobileScannerHandler  Looper 3dev.steenbakker.mobile_scanner.MobileScannerHandler  Map 3dev.steenbakker.mobile_scanner.MobileScannerHandler  
MethodCall 3dev.steenbakker.mobile_scanner.MobileScannerHandler  
MethodChannel 3dev.steenbakker.mobile_scanner.MobileScannerHandler  
MobileScanner 3dev.steenbakker.mobile_scanner.MobileScannerHandler  MobileScannerCallback 3dev.steenbakker.mobile_scanner.MobileScannerHandler  MobileScannerErrorCallback 3dev.steenbakker.mobile_scanner.MobileScannerHandler  MobileScannerErrorCodes 3dev.steenbakker.mobile_scanner.MobileScannerHandler  MobileScannerPermissions 3dev.steenbakker.mobile_scanner.MobileScannerHandler  MutableList 3dev.steenbakker.mobile_scanner.MobileScannerHandler  NoCamera 3dev.steenbakker.mobile_scanner.MobileScannerHandler   RequestPermissionsResultListener 3dev.steenbakker.mobile_scanner.MobileScannerHandler  Size 3dev.steenbakker.mobile_scanner.MobileScannerHandler  String 3dev.steenbakker.mobile_scanner.MobileScannerHandler  TextureRegistry 3dev.steenbakker.mobile_scanner.MobileScannerHandler  TorchStateCallback 3dev.steenbakker.mobile_scanner.MobileScannerHandler  Unit 3dev.steenbakker.mobile_scanner.MobileScannerHandler  Uri 3dev.steenbakker.mobile_scanner.MobileScannerHandler  ZoomNotInRange 3dev.steenbakker.mobile_scanner.MobileScannerHandler  ZoomScaleStateCallback 3dev.steenbakker.mobile_scanner.MobileScannerHandler  ZoomWhenStopped 3dev.steenbakker.mobile_scanner.MobileScannerHandler  activity 3dev.steenbakker.mobile_scanner.MobileScannerHandler  addPermissionListener 3dev.steenbakker.mobile_scanner.MobileScannerHandler  analyzeImage 3dev.steenbakker.mobile_scanner.MobileScannerHandler  analyzeImageErrorCallback 3dev.steenbakker.mobile_scanner.MobileScannerHandler  analyzeImageSuccessCallback 3dev.steenbakker.mobile_scanner.MobileScannerHandler  analyzerResult 3dev.steenbakker.mobile_scanner.MobileScannerHandler  barcodeHandler 3dev.steenbakker.mobile_scanner.MobileScannerHandler  buildBarcodeScannerOptions 3dev.steenbakker.mobile_scanner.MobileScannerHandler  callback 3dev.steenbakker.mobile_scanner.MobileScannerHandler  dispose 3dev.steenbakker.mobile_scanner.MobileScannerHandler  
errorCallback 3dev.steenbakker.mobile_scanner.MobileScannerHandler  first 3dev.steenbakker.mobile_scanner.MobileScannerHandler  getFIRST 3dev.steenbakker.mobile_scanner.MobileScannerHandler  getFirst 3dev.steenbakker.mobile_scanner.MobileScannerHandler  getMAPOf 3dev.steenbakker.mobile_scanner.MobileScannerHandler  getMUTABLEListOf 3dev.steenbakker.mobile_scanner.MobileScannerHandler  getMapOf 3dev.steenbakker.mobile_scanner.MobileScannerHandler  getMutableListOf 3dev.steenbakker.mobile_scanner.MobileScannerHandler  getTO 3dev.steenbakker.mobile_scanner.MobileScannerHandler  
getTOIntArray 3dev.steenbakker.mobile_scanner.MobileScannerHandler  getTo 3dev.steenbakker.mobile_scanner.MobileScannerHandler  
getToIntArray 3dev.steenbakker.mobile_scanner.MobileScannerHandler  invoke 3dev.steenbakker.mobile_scanner.MobileScannerHandler  mapOf 3dev.steenbakker.mobile_scanner.MobileScannerHandler  
methodChannel 3dev.steenbakker.mobile_scanner.MobileScannerHandler  
mobileScanner 3dev.steenbakker.mobile_scanner.MobileScannerHandler  
mutableListOf 3dev.steenbakker.mobile_scanner.MobileScannerHandler  pause 3dev.steenbakker.mobile_scanner.MobileScannerHandler  permissions 3dev.steenbakker.mobile_scanner.MobileScannerHandler  
resetScale 3dev.steenbakker.mobile_scanner.MobileScannerHandler  setScale 3dev.steenbakker.mobile_scanner.MobileScannerHandler  start 3dev.steenbakker.mobile_scanner.MobileScannerHandler  stop 3dev.steenbakker.mobile_scanner.MobileScannerHandler  to 3dev.steenbakker.mobile_scanner.MobileScannerHandler  
toIntArray 3dev.steenbakker.mobile_scanner.MobileScannerHandler  toggleTorch 3dev.steenbakker.mobile_scanner.MobileScannerHandler  torchStateCallback 3dev.steenbakker.mobile_scanner.MobileScannerHandler  updateScanWindow 3dev.steenbakker.mobile_scanner.MobileScannerHandler  zoomScaleStateCallback 3dev.steenbakker.mobile_scanner.MobileScannerHandler  Activity 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  ActivityCompat 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  Boolean 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  
ContextCompat 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  Int 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  MobileScannerErrorCodes 7dev.steenbakker.mobile_scanner.MobileScannerPermissions   MobileScannerPermissionsListener 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  PackageManager 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  REQUEST_CODE 7dev.steenbakker.mobile_scanner.MobileScannerPermissions   RequestPermissionsResultListener 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  ResultCallback 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  String 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  Unit 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  arrayOf 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  
getARRAYOf 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  
getArrayOf 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  getLET 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  getLet 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  
getPERMISSION 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  
getPermission 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  getPermissionListener 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  hasCameraPermission 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  let 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  listener 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  ongoing 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  
permission 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  requestPermission 7dev.steenbakker.mobile_scanner.MobileScannerPermissions  Activity Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  ActivityCompat Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  Boolean Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  
ContextCompat Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  Int Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  MobileScannerErrorCodes Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion   MobileScannerPermissionsListener Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  PackageManager Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  REQUEST_CODE Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion   RequestPermissionsResultListener Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  String Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  Unit Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  arrayOf Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  
getARRAYOf Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  
getArrayOf Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  getLET Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  getLet Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  
getPERMISSION Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  
getPermission Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  invoke Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  let Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  listener Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  ongoing Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  
permission Adev.steenbakker.mobile_scanner.MobileScannerPermissions.Companion  String Fdev.steenbakker.mobile_scanner.MobileScannerPermissions.ResultCallback  onResult Fdev.steenbakker.mobile_scanner.MobileScannerPermissions.ResultCallback  getLISTENER \dev.steenbakker.mobile_scanner.MobileScannerPermissions.requestPermission.<no name provided>  getListener \dev.steenbakker.mobile_scanner.MobileScannerPermissions.requestPermission.<no name provided>  
getONGOING \dev.steenbakker.mobile_scanner.MobileScannerPermissions.requestPermission.<no name provided>  
getOngoing \dev.steenbakker.mobile_scanner.MobileScannerPermissions.requestPermission.<no name provided>  Array ?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener  Boolean ?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener  Int ?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener  IntArray ?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener  MobileScannerErrorCodes ?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener  MobileScannerPermissions ?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener  PackageManager ?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener  String ?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener  
alreadyCalled ?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener  
getISEmpty ?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener  
getIsEmpty ?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener  getLET ?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener  getLet ?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener  isEmpty ?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener  let ?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener  resultCallback ?dev.steenbakker.mobile_scanner.MobileScannerPermissionsListener  ActivityPluginBinding 2dev.steenbakker.mobile_scanner.MobileScannerPlugin  BarcodeHandler 2dev.steenbakker.mobile_scanner.MobileScannerPlugin  
FlutterPlugin 2dev.steenbakker.mobile_scanner.MobileScannerPlugin  MobileScannerHandler 2dev.steenbakker.mobile_scanner.MobileScannerPlugin  MobileScannerPermissions 2dev.steenbakker.mobile_scanner.MobileScannerPlugin  activityPluginBinding 2dev.steenbakker.mobile_scanner.MobileScannerPlugin  flutterPluginBinding 2dev.steenbakker.mobile_scanner.MobileScannerPlugin  invoke 2dev.steenbakker.mobile_scanner.MobileScannerPlugin  methodCallHandler 2dev.steenbakker.mobile_scanner.MobileScannerPlugin  onAttachedToActivity 2dev.steenbakker.mobile_scanner.MobileScannerPlugin  onDetachedFromActivity 2dev.steenbakker.mobile_scanner.MobileScannerPlugin  ALL_FORMATS &dev.steenbakker.mobile_scanner.objects  AZTEC &dev.steenbakker.mobile_scanner.objects  BarcodeFormats &dev.steenbakker.mobile_scanner.objects  CODABAR &dev.steenbakker.mobile_scanner.objects  CODE_128 &dev.steenbakker.mobile_scanner.objects  CODE_39 &dev.steenbakker.mobile_scanner.objects  CODE_93 &dev.steenbakker.mobile_scanner.objects  DATA_MATRIX &dev.steenbakker.mobile_scanner.objects  DetectionSpeed &dev.steenbakker.mobile_scanner.objects  Double &dev.steenbakker.mobile_scanner.objects  EAN_13 &dev.steenbakker.mobile_scanner.objects  EAN_8 &dev.steenbakker.mobile_scanner.objects  ITF &dev.steenbakker.mobile_scanner.objects  Int &dev.steenbakker.mobile_scanner.objects  Long &dev.steenbakker.mobile_scanner.objects  MobileScannerErrorCodes &dev.steenbakker.mobile_scanner.objects  MobileScannerStartParameters &dev.steenbakker.mobile_scanner.objects  PDF417 &dev.steenbakker.mobile_scanner.objects  QR_CODE &dev.steenbakker.mobile_scanner.objects  UNKNOWN &dev.steenbakker.mobile_scanner.objects  UPC_A &dev.steenbakker.mobile_scanner.objects  UPC_E &dev.steenbakker.mobile_scanner.objects  com &dev.steenbakker.mobile_scanner.objects  ALL_FORMATS 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  AZTEC 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  BarcodeFormats 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  CODABAR 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  CODE_128 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  CODE_39 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  CODE_93 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  DATA_MATRIX 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  EAN_13 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  EAN_8 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  ITF 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  Int 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  PDF417 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  QR_CODE 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  UNKNOWN 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  UPC_A 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  UPC_E 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  com 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  fromRawValue 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  intValue 5dev.steenbakker.mobile_scanner.objects.BarcodeFormats  com Adev.steenbakker.mobile_scanner.objects.BarcodeFormats.ALL_FORMATS  com ;dev.steenbakker.mobile_scanner.objects.BarcodeFormats.AZTEC  com =dev.steenbakker.mobile_scanner.objects.BarcodeFormats.CODABAR  com >dev.steenbakker.mobile_scanner.objects.BarcodeFormats.CODE_128  com =dev.steenbakker.mobile_scanner.objects.BarcodeFormats.CODE_39  com =dev.steenbakker.mobile_scanner.objects.BarcodeFormats.CODE_93  ALL_FORMATS ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  AZTEC ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  BarcodeFormats ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  CODABAR ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  CODE_128 ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  CODE_39 ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  CODE_93 ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  DATA_MATRIX ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  EAN_13 ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  EAN_8 ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  ITF ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  Int ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  PDF417 ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  QR_CODE ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  UNKNOWN ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  UPC_A ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  UPC_E ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  com ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  fromRawValue ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  getCOM ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  getCom ?dev.steenbakker.mobile_scanner.objects.BarcodeFormats.Companion  com Adev.steenbakker.mobile_scanner.objects.BarcodeFormats.DATA_MATRIX  com <dev.steenbakker.mobile_scanner.objects.BarcodeFormats.EAN_13  com ;dev.steenbakker.mobile_scanner.objects.BarcodeFormats.EAN_8  com 9dev.steenbakker.mobile_scanner.objects.BarcodeFormats.ITF  com <dev.steenbakker.mobile_scanner.objects.BarcodeFormats.PDF417  com =dev.steenbakker.mobile_scanner.objects.BarcodeFormats.QR_CODE  com =dev.steenbakker.mobile_scanner.objects.BarcodeFormats.UNKNOWN  com ;dev.steenbakker.mobile_scanner.objects.BarcodeFormats.UPC_A  com ;dev.steenbakker.mobile_scanner.objects.BarcodeFormats.UPC_E  DetectionSpeed 5dev.steenbakker.mobile_scanner.objects.DetectionSpeed  Int 5dev.steenbakker.mobile_scanner.objects.DetectionSpeed  NORMAL 5dev.steenbakker.mobile_scanner.objects.DetectionSpeed  
NO_DUPLICATES 5dev.steenbakker.mobile_scanner.objects.DetectionSpeed  UNRESTRICTED 5dev.steenbakker.mobile_scanner.objects.DetectionSpeed  equals 5dev.steenbakker.mobile_scanner.objects.DetectionSpeed  ALREADY_STARTED_ERROR >dev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes  ALREADY_STARTED_ERROR_MESSAGE >dev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes  
BARCODE_ERROR >dev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes  CAMERA_ACCESS_DENIED >dev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes  CAMERA_ERROR >dev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes  CAMERA_ERROR_MESSAGE >dev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes  "CAMERA_PERMISSIONS_REQUEST_ONGOING >dev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes  *CAMERA_PERMISSIONS_REQUEST_ONGOING_MESSAGE >dev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes  
GENERIC_ERROR >dev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes  GENERIC_ERROR_MESSAGE >dev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes   INVALID_ZOOM_SCALE_ERROR_MESSAGE >dev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes  NO_CAMERA_ERROR >dev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes  NO_CAMERA_ERROR_MESSAGE >dev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes  SET_SCALE_WHEN_STOPPED_ERROR >dev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes  $SET_SCALE_WHEN_STOPPED_ERROR_MESSAGE >dev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes  ALREADY_STARTED_ERROR Hdev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes.Companion  ALREADY_STARTED_ERROR_MESSAGE Hdev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes.Companion  
BARCODE_ERROR Hdev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes.Companion  CAMERA_ACCESS_DENIED Hdev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes.Companion  CAMERA_ERROR Hdev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes.Companion  CAMERA_ERROR_MESSAGE Hdev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes.Companion  "CAMERA_PERMISSIONS_REQUEST_ONGOING Hdev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes.Companion  *CAMERA_PERMISSIONS_REQUEST_ONGOING_MESSAGE Hdev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes.Companion  
GENERIC_ERROR Hdev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes.Companion  GENERIC_ERROR_MESSAGE Hdev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes.Companion   INVALID_ZOOM_SCALE_ERROR_MESSAGE Hdev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes.Companion  NO_CAMERA_ERROR Hdev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes.Companion  NO_CAMERA_ERROR_MESSAGE Hdev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes.Companion  SET_SCALE_WHEN_STOPPED_ERROR Hdev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes.Companion  $SET_SCALE_WHEN_STOPPED_ERROR_MESSAGE Hdev.steenbakker.mobile_scanner.objects.MobileScannerErrorCodes.Companion  Double Cdev.steenbakker.mobile_scanner.objects.MobileScannerStartParameters  Int Cdev.steenbakker.mobile_scanner.objects.MobileScannerStartParameters  Long Cdev.steenbakker.mobile_scanner.objects.MobileScannerStartParameters  currentTorchState Cdev.steenbakker.mobile_scanner.objects.MobileScannerStartParameters  height Cdev.steenbakker.mobile_scanner.objects.MobileScannerStartParameters  id Cdev.steenbakker.mobile_scanner.objects.MobileScannerStartParameters  numberOfCameras Cdev.steenbakker.mobile_scanner.objects.MobileScannerStartParameters  width Cdev.steenbakker.mobile_scanner.objects.MobileScannerStartParameters  
Allocation $dev.steenbakker.mobile_scanner.utils  AnnotationRetention $dev.steenbakker.mobile_scanner.utils  Boolean $dev.steenbakker.mobile_scanner.utils  	ByteArray $dev.steenbakker.mobile_scanner.utils  
ByteBuffer $dev.steenbakker.mobile_scanner.utils  Element $dev.steenbakker.mobile_scanner.utils  	Exception $dev.steenbakker.mobile_scanner.utils  IllegalStateException $dev.steenbakker.mobile_scanner.utils  ImageFormat $dev.steenbakker.mobile_scanner.utils  ImageWrapper $dev.steenbakker.mobile_scanner.utils  Int $dev.steenbakker.mobile_scanner.utils  PlaneWrapper $dev.steenbakker.mobile_scanner.utils  RenderScript $dev.steenbakker.mobile_scanner.utils  ScriptIntrinsicYuvToRGB $dev.steenbakker.mobile_scanner.utils  Suppress $dev.steenbakker.mobile_scanner.utils  Synchronized $dev.steenbakker.mobile_scanner.utils  Type $dev.steenbakker.mobile_scanner.utils  
YuvByteBuffer $dev.steenbakker.mobile_scanner.utils  YuvToRgbConverter $dev.steenbakker.mobile_scanner.utils  YuvType $dev.steenbakker.mobile_scanner.utils  forEach $dev.steenbakker.mobile_scanner.utils  kotlin $dev.steenbakker.mobile_scanner.utils  require $dev.steenbakker.mobile_scanner.utils  until $dev.steenbakker.mobile_scanner.utils  
ByteBuffer 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  Image 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  ImageFormat 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  ImageWrapper 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  Int 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  PlaneWrapper 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  YuvType 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  buffer 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  
clipBuffer 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  
getREQUIRE 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  
getRequire 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  getUNTIL 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  getUntil 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  
removePadding 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  removePaddingCompact 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  removePaddingNotCompact 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  require 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  type 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  until 2dev.steenbakker.mobile_scanner.utils.YuvByteBuffer  Image ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.ImageWrapper  PlaneWrapper ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.ImageWrapper  
getREQUIRE ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.ImageWrapper  
getRequire ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.ImageWrapper  height ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.ImageWrapper  require ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.ImageWrapper  u ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.ImageWrapper  v ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.ImageWrapper  width ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.ImageWrapper  y ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.ImageWrapper  
ByteBuffer ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.PlaneWrapper  Image ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.PlaneWrapper  Int ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.PlaneWrapper  buffer ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.PlaneWrapper  height ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.PlaneWrapper  pixelStride ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.PlaneWrapper  	rowStride ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.PlaneWrapper  width ?dev.steenbakker.mobile_scanner.utils.YuvByteBuffer.PlaneWrapper  
Allocation 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  Bitmap 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  Boolean 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  	ByteArray 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  
ByteBuffer 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  Context 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  Element 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  	Exception 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  IllegalStateException 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  Image 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  RenderScript 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  ScriptIntrinsicYuvToRGB 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  Suppress 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  Synchronized 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  Type 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  
YuvByteBuffer 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  bytes 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  createAllocations 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  inputAllocation 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  needCreateAllocations 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  outputAllocation 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  release 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  rs 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  scriptYuvToRgb 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  yuvBits 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  yuvToRgb 6dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBINARYMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getTEXTURERegistry Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getTextureRegistry Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setTextureRegistry Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  textureRegistry Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  #addRequestPermissionsResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  getACTIVITY Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  getActivity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  &removeRequestPermissionsResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  setActivity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BinaryMessenger io.flutter.plugin.common  EventChannel io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  PluginRegistry io.flutter.plugin.common  	EventSink %io.flutter.plugin.common.EventChannel  
StreamHandler %io.flutter.plugin.common.EventChannel  setStreamHandler %io.flutter.plugin.common.EventChannel  error /io.flutter.plugin.common.EventChannel.EventSink  success /io.flutter.plugin.common.EventChannel.EventSink  argument #io.flutter.plugin.common.MethodCall  	arguments #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result   RequestPermissionsResultListener 'io.flutter.plugin.common.PluginRegistry  <SAM-CONSTRUCTOR> Hio.flutter.plugin.common.PluginRegistry.RequestPermissionsResultListener  equals Hio.flutter.plugin.common.PluginRegistry.RequestPermissionsResultListener  getLET Hio.flutter.plugin.common.PluginRegistry.RequestPermissionsResultListener  getLet Hio.flutter.plugin.common.PluginRegistry.RequestPermissionsResultListener  let Hio.flutter.plugin.common.PluginRegistry.RequestPermissionsResultListener  TextureRegistry io.flutter.view  SurfaceTextureEntry io.flutter.view.TextureRegistry  createSurfaceTexture io.flutter.view.TextureRegistry  equals 3io.flutter.view.TextureRegistry.SurfaceTextureEntry  id 3io.flutter.view.TextureRegistry.SurfaceTextureEntry  release 3io.flutter.view.TextureRegistry.SurfaceTextureEntry  surfaceTexture 3io.flutter.view.TextureRegistry.SurfaceTextureEntry  ByteArrayOutputStream java.io  File java.io  toByteArray java.io.ByteArrayOutputStream  toByteArray java.io.OutputStream  ALL_FORMATS 	java.lang  AZTEC 	java.lang  ActivityCompat 	java.lang  
Allocation 	java.lang  
AlreadyPaused 	java.lang  AlreadyStarted 	java.lang  AlreadyStopped 	java.lang  AnnotationRetention 	java.lang  BarcodeFormats 	java.lang  BarcodeHandler 	java.lang  BarcodeScannerOptions 	java.lang  BarcodeScanning 	java.lang  Bitmap 	java.lang  Build 	java.lang  	ByteArray 	java.lang  ByteArrayOutputStream 	java.lang  
ByteBuffer 	java.lang  CODABAR 	java.lang  CODE_128 	java.lang  CODE_39 	java.lang  CODE_93 	java.lang  CameraError 	java.lang  CameraSelector 	java.lang  Context 	java.lang  
ContextCompat 	java.lang  CoroutineScope 	java.lang  DATA_MATRIX 	java.lang  DetectionSpeed 	java.lang  Dispatchers 	java.lang  EAN_13 	java.lang  EAN_8 	java.lang  Element 	java.lang  EventChannel 	java.lang  	Exception 	java.lang  File 	java.lang  Handler 	java.lang  ITF 	java.lang  IllegalStateException 	java.lang  
ImageAnalysis 	java.lang  ImageFormat 	java.lang  ImageWrapper 	java.lang  
InputImage 	java.lang  Looper 	java.lang  Matrix 	java.lang  
MethodChannel 	java.lang  
MobileScanner 	java.lang  MobileScannerErrorCodes 	java.lang  MobileScannerHandler 	java.lang  MobileScannerPermissions 	java.lang   MobileScannerPermissionsListener 	java.lang  MobileScannerStartParameters 	java.lang  NoCamera 	java.lang  PDF417 	java.lang  PackageManager 	java.lang  PlaneWrapper 	java.lang  Preview 	java.lang  ProcessCameraProvider 	java.lang  QR_CODE 	java.lang  REQUEST_CODE 	java.lang  Rect 	java.lang  RenderScript 	java.lang  ResolutionSelector 	java.lang  ResolutionStrategy 	java.lang  ScriptIntrinsicYuvToRGB 	java.lang  Size 	java.lang  Surface 	java.lang  
TorchState 	java.lang  Type 	java.lang  UNKNOWN 	java.lang  UPC_A 	java.lang  UPC_E 	java.lang  Uri 	java.lang  Void 	java.lang  
YuvByteBuffer 	java.lang  YuvImage 	java.lang  YuvToRgbConverter 	java.lang  ZoomNotInRange 	java.lang  ZoomWhenStopped 	java.lang  activity 	java.lang  addListener 	java.lang  apply 	java.lang  arrayOf 	java.lang  camera 	java.lang  
captureOutput 	java.lang  com 	java.lang  emptyMap 	java.lang  first 	java.lang  forEach 	java.lang  
getResolution 	java.lang  isEmpty 	java.lang  
isNotEmpty 	java.lang  kotlin 	java.lang  launch 	java.lang  let 	java.lang  listener 	java.lang  map 	java.lang  
mapNotNull 	java.lang  mapOf 	java.lang  mobileScannerCallback 	java.lang  
mutableListOf 	java.lang  ongoing 	java.lang  
permission 	java.lang  provideSurface 	java.lang  require 	java.lang  rotateBitmap 	java.lang  
roundToInt 	java.lang  sorted 	java.lang  to 	java.lang  
toIntArray 	java.lang  until 	java.lang  getLOCALIZEDMessage java.lang.Exception  getLocalizedMessage java.lang.Exception  localizedMessage java.lang.Exception  setLocalizedMessage java.lang.Exception  toString java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  Buffer java.nio  
ByteBuffer java.nio  capacity java.nio.Buffer  	duplicate java.nio.Buffer  get java.nio.Buffer  limit java.nio.Buffer  position java.nio.Buffer  put java.nio.Buffer  	remaining java.nio.Buffer  rewind java.nio.Buffer  slice java.nio.Buffer  allocateDirect java.nio.ByteBuffer  capacity java.nio.ByteBuffer  	duplicate java.nio.ByteBuffer  equals java.nio.ByteBuffer  get java.nio.ByteBuffer  getISDirect java.nio.ByteBuffer  
getISReadOnly java.nio.ByteBuffer  getIsDirect java.nio.ByteBuffer  
getIsReadOnly java.nio.ByteBuffer  isDirect java.nio.ByteBuffer  
isReadOnly java.nio.ByteBuffer  limit java.nio.ByteBuffer  position java.nio.ByteBuffer  put java.nio.ByteBuffer  	remaining java.nio.ByteBuffer  rewind java.nio.ByteBuffer  	setDirect java.nio.ByteBuffer  setReadOnly java.nio.ByteBuffer  slice java.nio.ByteBuffer  Executor java.util.concurrent  ALL_FORMATS kotlin  AZTEC kotlin  ActivityCompat kotlin  
Allocation kotlin  
AlreadyPaused kotlin  AlreadyStarted kotlin  AlreadyStopped kotlin  AnnotationRetention kotlin  Any kotlin  Array kotlin  BarcodeFormats kotlin  BarcodeHandler kotlin  BarcodeScannerOptions kotlin  BarcodeScanning kotlin  Bitmap kotlin  Boolean kotlin  Build kotlin  Byte kotlin  	ByteArray kotlin  ByteArrayOutputStream kotlin  
ByteBuffer kotlin  CODABAR kotlin  CODE_128 kotlin  CODE_39 kotlin  CODE_93 kotlin  CameraError kotlin  CameraSelector kotlin  Context kotlin  
ContextCompat kotlin  CoroutineScope kotlin  DATA_MATRIX kotlin  DetectionSpeed kotlin  Dispatchers kotlin  Double kotlin  EAN_13 kotlin  EAN_8 kotlin  Element kotlin  EventChannel kotlin  	Exception kotlin  File kotlin  Float kotlin  	Function0 kotlin  	Function1 kotlin  	Function4 kotlin  Handler kotlin  ITF kotlin  IllegalArgumentException kotlin  IllegalStateException kotlin  
ImageAnalysis kotlin  ImageFormat kotlin  ImageWrapper kotlin  
InputImage kotlin  Int kotlin  IntArray kotlin  Long kotlin  Looper kotlin  Matrix kotlin  
MethodChannel kotlin  
MobileScanner kotlin  MobileScannerErrorCodes kotlin  MobileScannerHandler kotlin  MobileScannerPermissions kotlin   MobileScannerPermissionsListener kotlin  MobileScannerStartParameters kotlin  NoCamera kotlin  Nothing kotlin  PDF417 kotlin  PackageManager kotlin  Pair kotlin  PlaneWrapper kotlin  Preview kotlin  ProcessCameraProvider kotlin  QR_CODE kotlin  REQUEST_CODE kotlin  Rect kotlin  RenderScript kotlin  ResolutionSelector kotlin  ResolutionStrategy kotlin  ScriptIntrinsicYuvToRGB kotlin  Size kotlin  String kotlin  Suppress kotlin  Surface kotlin  Synchronized kotlin  
TorchState kotlin  Type kotlin  UNKNOWN kotlin  UPC_A kotlin  UPC_E kotlin  Unit kotlin  Uri kotlin  
YuvByteBuffer kotlin  YuvImage kotlin  YuvToRgbConverter kotlin  ZoomNotInRange kotlin  ZoomWhenStopped kotlin  activity kotlin  addListener kotlin  apply kotlin  arrayOf kotlin  camera kotlin  
captureOutput kotlin  com kotlin  emptyMap kotlin  first kotlin  forEach kotlin  
getResolution kotlin  isEmpty kotlin  
isNotEmpty kotlin  kotlin kotlin  launch kotlin  let kotlin  listener kotlin  map kotlin  
mapNotNull kotlin  mapOf kotlin  mobileScannerCallback kotlin  
mutableListOf kotlin  ongoing kotlin  
permission kotlin  provideSurface kotlin  require kotlin  rotateBitmap kotlin  
roundToInt kotlin  sorted kotlin  to kotlin  
toIntArray kotlin  until kotlin  getMAP kotlin.Array  getMap kotlin.Array  getCOM kotlin.Enum.Companion  getCom kotlin.Enum.Companion  
getROUNDToInt kotlin.Float  
getRoundToInt kotlin.Float  getUNTIL 
kotlin.Int  getUntil 
kotlin.Int  
getISEmpty kotlin.IntArray  
getIsEmpty kotlin.IntArray  isEmpty kotlin.IntArray  getTO 
kotlin.String  getTo 
kotlin.String  ALL_FORMATS kotlin.annotation  AZTEC kotlin.annotation  ActivityCompat kotlin.annotation  
Allocation kotlin.annotation  
AlreadyPaused kotlin.annotation  AlreadyStarted kotlin.annotation  AlreadyStopped kotlin.annotation  AnnotationRetention kotlin.annotation  BarcodeFormats kotlin.annotation  BarcodeHandler kotlin.annotation  BarcodeScannerOptions kotlin.annotation  BarcodeScanning kotlin.annotation  Bitmap kotlin.annotation  Build kotlin.annotation  	ByteArray kotlin.annotation  ByteArrayOutputStream kotlin.annotation  
ByteBuffer kotlin.annotation  CODABAR kotlin.annotation  CODE_128 kotlin.annotation  CODE_39 kotlin.annotation  CODE_93 kotlin.annotation  CameraError kotlin.annotation  CameraSelector kotlin.annotation  Context kotlin.annotation  
ContextCompat kotlin.annotation  CoroutineScope kotlin.annotation  DATA_MATRIX kotlin.annotation  DetectionSpeed kotlin.annotation  Dispatchers kotlin.annotation  EAN_13 kotlin.annotation  EAN_8 kotlin.annotation  Element kotlin.annotation  EventChannel kotlin.annotation  	Exception kotlin.annotation  File kotlin.annotation  Handler kotlin.annotation  ITF kotlin.annotation  IllegalArgumentException kotlin.annotation  IllegalStateException kotlin.annotation  
ImageAnalysis kotlin.annotation  ImageFormat kotlin.annotation  ImageWrapper kotlin.annotation  
InputImage kotlin.annotation  Looper kotlin.annotation  Matrix kotlin.annotation  
MethodChannel kotlin.annotation  
MobileScanner kotlin.annotation  MobileScannerErrorCodes kotlin.annotation  MobileScannerHandler kotlin.annotation  MobileScannerPermissions kotlin.annotation   MobileScannerPermissionsListener kotlin.annotation  MobileScannerStartParameters kotlin.annotation  NoCamera kotlin.annotation  PDF417 kotlin.annotation  PackageManager kotlin.annotation  PlaneWrapper kotlin.annotation  Preview kotlin.annotation  ProcessCameraProvider kotlin.annotation  QR_CODE kotlin.annotation  REQUEST_CODE kotlin.annotation  Rect kotlin.annotation  RenderScript kotlin.annotation  ResolutionSelector kotlin.annotation  ResolutionStrategy kotlin.annotation  	Retention kotlin.annotation  ScriptIntrinsicYuvToRGB kotlin.annotation  Size kotlin.annotation  Surface kotlin.annotation  Synchronized kotlin.annotation  
TorchState kotlin.annotation  Type kotlin.annotation  UNKNOWN kotlin.annotation  UPC_A kotlin.annotation  UPC_E kotlin.annotation  Uri kotlin.annotation  
YuvByteBuffer kotlin.annotation  YuvImage kotlin.annotation  YuvToRgbConverter kotlin.annotation  ZoomNotInRange kotlin.annotation  ZoomWhenStopped kotlin.annotation  activity kotlin.annotation  addListener kotlin.annotation  apply kotlin.annotation  arrayOf kotlin.annotation  camera kotlin.annotation  
captureOutput kotlin.annotation  com kotlin.annotation  emptyMap kotlin.annotation  first kotlin.annotation  forEach kotlin.annotation  
getResolution kotlin.annotation  isEmpty kotlin.annotation  
isNotEmpty kotlin.annotation  kotlin kotlin.annotation  launch kotlin.annotation  let kotlin.annotation  listener kotlin.annotation  map kotlin.annotation  
mapNotNull kotlin.annotation  mapOf kotlin.annotation  mobileScannerCallback kotlin.annotation  
mutableListOf kotlin.annotation  ongoing kotlin.annotation  
permission kotlin.annotation  provideSurface kotlin.annotation  require kotlin.annotation  rotateBitmap kotlin.annotation  
roundToInt kotlin.annotation  sorted kotlin.annotation  to kotlin.annotation  
toIntArray kotlin.annotation  until kotlin.annotation  SOURCE %kotlin.annotation.AnnotationRetention  ALL_FORMATS kotlin.collections  AZTEC kotlin.collections  ActivityCompat kotlin.collections  
Allocation kotlin.collections  
AlreadyPaused kotlin.collections  AlreadyStarted kotlin.collections  AlreadyStopped kotlin.collections  AnnotationRetention kotlin.collections  BarcodeFormats kotlin.collections  BarcodeHandler kotlin.collections  BarcodeScannerOptions kotlin.collections  BarcodeScanning kotlin.collections  Bitmap kotlin.collections  Build kotlin.collections  	ByteArray kotlin.collections  ByteArrayOutputStream kotlin.collections  
ByteBuffer kotlin.collections  CODABAR kotlin.collections  CODE_128 kotlin.collections  CODE_39 kotlin.collections  CODE_93 kotlin.collections  CameraError kotlin.collections  CameraSelector kotlin.collections  Context kotlin.collections  
ContextCompat kotlin.collections  CoroutineScope kotlin.collections  DATA_MATRIX kotlin.collections  DetectionSpeed kotlin.collections  Dispatchers kotlin.collections  EAN_13 kotlin.collections  EAN_8 kotlin.collections  Element kotlin.collections  EventChannel kotlin.collections  	Exception kotlin.collections  File kotlin.collections  Handler kotlin.collections  ITF kotlin.collections  IllegalArgumentException kotlin.collections  IllegalStateException kotlin.collections  
ImageAnalysis kotlin.collections  ImageFormat kotlin.collections  ImageWrapper kotlin.collections  
InputImage kotlin.collections  List kotlin.collections  Looper kotlin.collections  Map kotlin.collections  Matrix kotlin.collections  
MethodChannel kotlin.collections  
MobileScanner kotlin.collections  MobileScannerErrorCodes kotlin.collections  MobileScannerHandler kotlin.collections  MobileScannerPermissions kotlin.collections   MobileScannerPermissionsListener kotlin.collections  MobileScannerStartParameters kotlin.collections  MutableList kotlin.collections  NoCamera kotlin.collections  PDF417 kotlin.collections  PackageManager kotlin.collections  PlaneWrapper kotlin.collections  Preview kotlin.collections  ProcessCameraProvider kotlin.collections  QR_CODE kotlin.collections  REQUEST_CODE kotlin.collections  Rect kotlin.collections  RenderScript kotlin.collections  ResolutionSelector kotlin.collections  ResolutionStrategy kotlin.collections  ScriptIntrinsicYuvToRGB kotlin.collections  Size kotlin.collections  Surface kotlin.collections  Synchronized kotlin.collections  
TorchState kotlin.collections  Type kotlin.collections  UNKNOWN kotlin.collections  UPC_A kotlin.collections  UPC_E kotlin.collections  Uri kotlin.collections  
YuvByteBuffer kotlin.collections  YuvImage kotlin.collections  YuvToRgbConverter kotlin.collections  ZoomNotInRange kotlin.collections  ZoomWhenStopped kotlin.collections  activity kotlin.collections  addListener kotlin.collections  apply kotlin.collections  arrayOf kotlin.collections  camera kotlin.collections  
captureOutput kotlin.collections  com kotlin.collections  emptyMap kotlin.collections  first kotlin.collections  forEach kotlin.collections  
getResolution kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  kotlin kotlin.collections  launch kotlin.collections  let kotlin.collections  listener kotlin.collections  map kotlin.collections  
mapNotNull kotlin.collections  mapOf kotlin.collections  mobileScannerCallback kotlin.collections  
mutableListOf kotlin.collections  ongoing kotlin.collections  
permission kotlin.collections  provideSurface kotlin.collections  require kotlin.collections  rotateBitmap kotlin.collections  
roundToInt kotlin.collections  sorted kotlin.collections  to kotlin.collections  
toIntArray kotlin.collections  until kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  
getISNotEmpty kotlin.collections.List  
getIsNotEmpty kotlin.collections.List  	getSORTED kotlin.collections.List  	getSorted kotlin.collections.List  
isNotEmpty kotlin.collections.List  getFIRST kotlin.collections.MutableList  getFirst kotlin.collections.MutableList  getMAP kotlin.collections.MutableList  
getMAPNotNull kotlin.collections.MutableList  getMap kotlin.collections.MutableList  
getMapNotNull kotlin.collections.MutableList  
getTOIntArray kotlin.collections.MutableList  
getToIntArray kotlin.collections.MutableList  ALL_FORMATS kotlin.comparisons  AZTEC kotlin.comparisons  ActivityCompat kotlin.comparisons  
Allocation kotlin.comparisons  
AlreadyPaused kotlin.comparisons  AlreadyStarted kotlin.comparisons  AlreadyStopped kotlin.comparisons  AnnotationRetention kotlin.comparisons  BarcodeFormats kotlin.comparisons  BarcodeHandler kotlin.comparisons  BarcodeScannerOptions kotlin.comparisons  BarcodeScanning kotlin.comparisons  Bitmap kotlin.comparisons  Build kotlin.comparisons  	ByteArray kotlin.comparisons  ByteArrayOutputStream kotlin.comparisons  
ByteBuffer kotlin.comparisons  CODABAR kotlin.comparisons  CODE_128 kotlin.comparisons  CODE_39 kotlin.comparisons  CODE_93 kotlin.comparisons  CameraError kotlin.comparisons  CameraSelector kotlin.comparisons  Context kotlin.comparisons  
ContextCompat kotlin.comparisons  CoroutineScope kotlin.comparisons  DATA_MATRIX kotlin.comparisons  DetectionSpeed kotlin.comparisons  Dispatchers kotlin.comparisons  EAN_13 kotlin.comparisons  EAN_8 kotlin.comparisons  Element kotlin.comparisons  EventChannel kotlin.comparisons  	Exception kotlin.comparisons  File kotlin.comparisons  Handler kotlin.comparisons  ITF kotlin.comparisons  IllegalArgumentException kotlin.comparisons  IllegalStateException kotlin.comparisons  
ImageAnalysis kotlin.comparisons  ImageFormat kotlin.comparisons  ImageWrapper kotlin.comparisons  
InputImage kotlin.comparisons  Looper kotlin.comparisons  Matrix kotlin.comparisons  
MethodChannel kotlin.comparisons  
MobileScanner kotlin.comparisons  MobileScannerErrorCodes kotlin.comparisons  MobileScannerHandler kotlin.comparisons  MobileScannerPermissions kotlin.comparisons   MobileScannerPermissionsListener kotlin.comparisons  MobileScannerStartParameters kotlin.comparisons  NoCamera kotlin.comparisons  PDF417 kotlin.comparisons  PackageManager kotlin.comparisons  PlaneWrapper kotlin.comparisons  Preview kotlin.comparisons  ProcessCameraProvider kotlin.comparisons  QR_CODE kotlin.comparisons  REQUEST_CODE kotlin.comparisons  Rect kotlin.comparisons  RenderScript kotlin.comparisons  ResolutionSelector kotlin.comparisons  ResolutionStrategy kotlin.comparisons  ScriptIntrinsicYuvToRGB kotlin.comparisons  Size kotlin.comparisons  Surface kotlin.comparisons  Synchronized kotlin.comparisons  
TorchState kotlin.comparisons  Type kotlin.comparisons  UNKNOWN kotlin.comparisons  UPC_A kotlin.comparisons  UPC_E kotlin.comparisons  Uri kotlin.comparisons  
YuvByteBuffer kotlin.comparisons  YuvImage kotlin.comparisons  YuvToRgbConverter kotlin.comparisons  ZoomNotInRange kotlin.comparisons  ZoomWhenStopped kotlin.comparisons  activity kotlin.comparisons  addListener kotlin.comparisons  apply kotlin.comparisons  arrayOf kotlin.comparisons  camera kotlin.comparisons  
captureOutput kotlin.comparisons  com kotlin.comparisons  emptyMap kotlin.comparisons  first kotlin.comparisons  forEach kotlin.comparisons  
getResolution kotlin.comparisons  isEmpty kotlin.comparisons  
isNotEmpty kotlin.comparisons  kotlin kotlin.comparisons  launch kotlin.comparisons  let kotlin.comparisons  listener kotlin.comparisons  map kotlin.comparisons  
mapNotNull kotlin.comparisons  mapOf kotlin.comparisons  mobileScannerCallback kotlin.comparisons  
mutableListOf kotlin.comparisons  ongoing kotlin.comparisons  
permission kotlin.comparisons  provideSurface kotlin.comparisons  require kotlin.comparisons  rotateBitmap kotlin.comparisons  
roundToInt kotlin.comparisons  sorted kotlin.comparisons  to kotlin.comparisons  
toIntArray kotlin.comparisons  until kotlin.comparisons  SuspendFunction1 kotlin.coroutines  ALL_FORMATS 	kotlin.io  AZTEC 	kotlin.io  ActivityCompat 	kotlin.io  
Allocation 	kotlin.io  
AlreadyPaused 	kotlin.io  AlreadyStarted 	kotlin.io  AlreadyStopped 	kotlin.io  AnnotationRetention 	kotlin.io  BarcodeFormats 	kotlin.io  BarcodeHandler 	kotlin.io  BarcodeScannerOptions 	kotlin.io  BarcodeScanning 	kotlin.io  Bitmap 	kotlin.io  Build 	kotlin.io  	ByteArray 	kotlin.io  ByteArrayOutputStream 	kotlin.io  
ByteBuffer 	kotlin.io  CODABAR 	kotlin.io  CODE_128 	kotlin.io  CODE_39 	kotlin.io  CODE_93 	kotlin.io  CameraError 	kotlin.io  CameraSelector 	kotlin.io  Context 	kotlin.io  
ContextCompat 	kotlin.io  CoroutineScope 	kotlin.io  DATA_MATRIX 	kotlin.io  DetectionSpeed 	kotlin.io  Dispatchers 	kotlin.io  EAN_13 	kotlin.io  EAN_8 	kotlin.io  Element 	kotlin.io  EventChannel 	kotlin.io  	Exception 	kotlin.io  File 	kotlin.io  Handler 	kotlin.io  ITF 	kotlin.io  IllegalArgumentException 	kotlin.io  IllegalStateException 	kotlin.io  
ImageAnalysis 	kotlin.io  ImageFormat 	kotlin.io  ImageWrapper 	kotlin.io  
InputImage 	kotlin.io  Looper 	kotlin.io  Matrix 	kotlin.io  
MethodChannel 	kotlin.io  
MobileScanner 	kotlin.io  MobileScannerErrorCodes 	kotlin.io  MobileScannerHandler 	kotlin.io  MobileScannerPermissions 	kotlin.io   MobileScannerPermissionsListener 	kotlin.io  MobileScannerStartParameters 	kotlin.io  NoCamera 	kotlin.io  PDF417 	kotlin.io  PackageManager 	kotlin.io  PlaneWrapper 	kotlin.io  Preview 	kotlin.io  ProcessCameraProvider 	kotlin.io  QR_CODE 	kotlin.io  REQUEST_CODE 	kotlin.io  Rect 	kotlin.io  RenderScript 	kotlin.io  ResolutionSelector 	kotlin.io  ResolutionStrategy 	kotlin.io  ScriptIntrinsicYuvToRGB 	kotlin.io  Size 	kotlin.io  Surface 	kotlin.io  Synchronized 	kotlin.io  
TorchState 	kotlin.io  Type 	kotlin.io  UNKNOWN 	kotlin.io  UPC_A 	kotlin.io  UPC_E 	kotlin.io  Uri 	kotlin.io  
YuvByteBuffer 	kotlin.io  YuvImage 	kotlin.io  YuvToRgbConverter 	kotlin.io  ZoomNotInRange 	kotlin.io  ZoomWhenStopped 	kotlin.io  activity 	kotlin.io  addListener 	kotlin.io  apply 	kotlin.io  arrayOf 	kotlin.io  camera 	kotlin.io  
captureOutput 	kotlin.io  com 	kotlin.io  emptyMap 	kotlin.io  first 	kotlin.io  forEach 	kotlin.io  
getResolution 	kotlin.io  isEmpty 	kotlin.io  
isNotEmpty 	kotlin.io  kotlin 	kotlin.io  launch 	kotlin.io  let 	kotlin.io  listener 	kotlin.io  map 	kotlin.io  
mapNotNull 	kotlin.io  mapOf 	kotlin.io  mobileScannerCallback 	kotlin.io  
mutableListOf 	kotlin.io  ongoing 	kotlin.io  
permission 	kotlin.io  provideSurface 	kotlin.io  require 	kotlin.io  rotateBitmap 	kotlin.io  
roundToInt 	kotlin.io  sorted 	kotlin.io  to 	kotlin.io  
toIntArray 	kotlin.io  until 	kotlin.io  ALL_FORMATS 
kotlin.jvm  AZTEC 
kotlin.jvm  ActivityCompat 
kotlin.jvm  
Allocation 
kotlin.jvm  
AlreadyPaused 
kotlin.jvm  AlreadyStarted 
kotlin.jvm  AlreadyStopped 
kotlin.jvm  AnnotationRetention 
kotlin.jvm  BarcodeFormats 
kotlin.jvm  BarcodeHandler 
kotlin.jvm  BarcodeScannerOptions 
kotlin.jvm  BarcodeScanning 
kotlin.jvm  Bitmap 
kotlin.jvm  Build 
kotlin.jvm  	ByteArray 
kotlin.jvm  ByteArrayOutputStream 
kotlin.jvm  
ByteBuffer 
kotlin.jvm  CODABAR 
kotlin.jvm  CODE_128 
kotlin.jvm  CODE_39 
kotlin.jvm  CODE_93 
kotlin.jvm  CameraError 
kotlin.jvm  CameraSelector 
kotlin.jvm  Context 
kotlin.jvm  
ContextCompat 
kotlin.jvm  CoroutineScope 
kotlin.jvm  DATA_MATRIX 
kotlin.jvm  DetectionSpeed 
kotlin.jvm  Dispatchers 
kotlin.jvm  EAN_13 
kotlin.jvm  EAN_8 
kotlin.jvm  Element 
kotlin.jvm  EventChannel 
kotlin.jvm  	Exception 
kotlin.jvm  File 
kotlin.jvm  Handler 
kotlin.jvm  ITF 
kotlin.jvm  IllegalArgumentException 
kotlin.jvm  IllegalStateException 
kotlin.jvm  
ImageAnalysis 
kotlin.jvm  ImageFormat 
kotlin.jvm  ImageWrapper 
kotlin.jvm  
InputImage 
kotlin.jvm  Looper 
kotlin.jvm  Matrix 
kotlin.jvm  
MethodChannel 
kotlin.jvm  
MobileScanner 
kotlin.jvm  MobileScannerErrorCodes 
kotlin.jvm  MobileScannerHandler 
kotlin.jvm  MobileScannerPermissions 
kotlin.jvm   MobileScannerPermissionsListener 
kotlin.jvm  MobileScannerStartParameters 
kotlin.jvm  NoCamera 
kotlin.jvm  PDF417 
kotlin.jvm  PackageManager 
kotlin.jvm  PlaneWrapper 
kotlin.jvm  Preview 
kotlin.jvm  ProcessCameraProvider 
kotlin.jvm  QR_CODE 
kotlin.jvm  REQUEST_CODE 
kotlin.jvm  Rect 
kotlin.jvm  RenderScript 
kotlin.jvm  ResolutionSelector 
kotlin.jvm  ResolutionStrategy 
kotlin.jvm  ScriptIntrinsicYuvToRGB 
kotlin.jvm  Size 
kotlin.jvm  Surface 
kotlin.jvm  Synchronized 
kotlin.jvm  
TorchState 
kotlin.jvm  Type 
kotlin.jvm  UNKNOWN 
kotlin.jvm  UPC_A 
kotlin.jvm  UPC_E 
kotlin.jvm  Uri 
kotlin.jvm  
YuvByteBuffer 
kotlin.jvm  YuvImage 
kotlin.jvm  YuvToRgbConverter 
kotlin.jvm  ZoomNotInRange 
kotlin.jvm  ZoomWhenStopped 
kotlin.jvm  activity 
kotlin.jvm  addListener 
kotlin.jvm  apply 
kotlin.jvm  arrayOf 
kotlin.jvm  camera 
kotlin.jvm  
captureOutput 
kotlin.jvm  com 
kotlin.jvm  emptyMap 
kotlin.jvm  first 
kotlin.jvm  forEach 
kotlin.jvm  
getResolution 
kotlin.jvm  isEmpty 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  kotlin 
kotlin.jvm  launch 
kotlin.jvm  let 
kotlin.jvm  listener 
kotlin.jvm  map 
kotlin.jvm  
mapNotNull 
kotlin.jvm  mapOf 
kotlin.jvm  mobileScannerCallback 
kotlin.jvm  
mutableListOf 
kotlin.jvm  ongoing 
kotlin.jvm  
permission 
kotlin.jvm  provideSurface 
kotlin.jvm  require 
kotlin.jvm  rotateBitmap 
kotlin.jvm  
roundToInt 
kotlin.jvm  sorted 
kotlin.jvm  to 
kotlin.jvm  
toIntArray 
kotlin.jvm  until 
kotlin.jvm  
roundToInt kotlin.math  ALL_FORMATS 
kotlin.ranges  AZTEC 
kotlin.ranges  ActivityCompat 
kotlin.ranges  
Allocation 
kotlin.ranges  
AlreadyPaused 
kotlin.ranges  AlreadyStarted 
kotlin.ranges  AlreadyStopped 
kotlin.ranges  AnnotationRetention 
kotlin.ranges  BarcodeFormats 
kotlin.ranges  BarcodeHandler 
kotlin.ranges  BarcodeScannerOptions 
kotlin.ranges  BarcodeScanning 
kotlin.ranges  Bitmap 
kotlin.ranges  Build 
kotlin.ranges  	ByteArray 
kotlin.ranges  ByteArrayOutputStream 
kotlin.ranges  
ByteBuffer 
kotlin.ranges  CODABAR 
kotlin.ranges  CODE_128 
kotlin.ranges  CODE_39 
kotlin.ranges  CODE_93 
kotlin.ranges  CameraError 
kotlin.ranges  CameraSelector 
kotlin.ranges  Context 
kotlin.ranges  
ContextCompat 
kotlin.ranges  CoroutineScope 
kotlin.ranges  DATA_MATRIX 
kotlin.ranges  DetectionSpeed 
kotlin.ranges  Dispatchers 
kotlin.ranges  EAN_13 
kotlin.ranges  EAN_8 
kotlin.ranges  Element 
kotlin.ranges  EventChannel 
kotlin.ranges  	Exception 
kotlin.ranges  File 
kotlin.ranges  Handler 
kotlin.ranges  ITF 
kotlin.ranges  IllegalArgumentException 
kotlin.ranges  IllegalStateException 
kotlin.ranges  
ImageAnalysis 
kotlin.ranges  ImageFormat 
kotlin.ranges  ImageWrapper 
kotlin.ranges  
InputImage 
kotlin.ranges  IntRange 
kotlin.ranges  Looper 
kotlin.ranges  Matrix 
kotlin.ranges  
MethodChannel 
kotlin.ranges  
MobileScanner 
kotlin.ranges  MobileScannerErrorCodes 
kotlin.ranges  MobileScannerHandler 
kotlin.ranges  MobileScannerPermissions 
kotlin.ranges   MobileScannerPermissionsListener 
kotlin.ranges  MobileScannerStartParameters 
kotlin.ranges  NoCamera 
kotlin.ranges  PDF417 
kotlin.ranges  PackageManager 
kotlin.ranges  PlaneWrapper 
kotlin.ranges  Preview 
kotlin.ranges  ProcessCameraProvider 
kotlin.ranges  QR_CODE 
kotlin.ranges  REQUEST_CODE 
kotlin.ranges  Rect 
kotlin.ranges  RenderScript 
kotlin.ranges  ResolutionSelector 
kotlin.ranges  ResolutionStrategy 
kotlin.ranges  ScriptIntrinsicYuvToRGB 
kotlin.ranges  Size 
kotlin.ranges  Surface 
kotlin.ranges  Synchronized 
kotlin.ranges  
TorchState 
kotlin.ranges  Type 
kotlin.ranges  UNKNOWN 
kotlin.ranges  UPC_A 
kotlin.ranges  UPC_E 
kotlin.ranges  Uri 
kotlin.ranges  
YuvByteBuffer 
kotlin.ranges  YuvImage 
kotlin.ranges  YuvToRgbConverter 
kotlin.ranges  ZoomNotInRange 
kotlin.ranges  ZoomWhenStopped 
kotlin.ranges  activity 
kotlin.ranges  addListener 
kotlin.ranges  apply 
kotlin.ranges  arrayOf 
kotlin.ranges  camera 
kotlin.ranges  
captureOutput 
kotlin.ranges  com 
kotlin.ranges  emptyMap 
kotlin.ranges  first 
kotlin.ranges  forEach 
kotlin.ranges  
getResolution 
kotlin.ranges  isEmpty 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  kotlin 
kotlin.ranges  launch 
kotlin.ranges  let 
kotlin.ranges  listener 
kotlin.ranges  map 
kotlin.ranges  
mapNotNull 
kotlin.ranges  mapOf 
kotlin.ranges  mobileScannerCallback 
kotlin.ranges  
mutableListOf 
kotlin.ranges  ongoing 
kotlin.ranges  
permission 
kotlin.ranges  provideSurface 
kotlin.ranges  require 
kotlin.ranges  rotateBitmap 
kotlin.ranges  
roundToInt 
kotlin.ranges  sorted 
kotlin.ranges  to 
kotlin.ranges  
toIntArray 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  
KFunction1 kotlin.reflect  ALL_FORMATS kotlin.sequences  AZTEC kotlin.sequences  ActivityCompat kotlin.sequences  
Allocation kotlin.sequences  
AlreadyPaused kotlin.sequences  AlreadyStarted kotlin.sequences  AlreadyStopped kotlin.sequences  AnnotationRetention kotlin.sequences  BarcodeFormats kotlin.sequences  BarcodeHandler kotlin.sequences  BarcodeScannerOptions kotlin.sequences  BarcodeScanning kotlin.sequences  Bitmap kotlin.sequences  Build kotlin.sequences  	ByteArray kotlin.sequences  ByteArrayOutputStream kotlin.sequences  
ByteBuffer kotlin.sequences  CODABAR kotlin.sequences  CODE_128 kotlin.sequences  CODE_39 kotlin.sequences  CODE_93 kotlin.sequences  CameraError kotlin.sequences  CameraSelector kotlin.sequences  Context kotlin.sequences  
ContextCompat kotlin.sequences  CoroutineScope kotlin.sequences  DATA_MATRIX kotlin.sequences  DetectionSpeed kotlin.sequences  Dispatchers kotlin.sequences  EAN_13 kotlin.sequences  EAN_8 kotlin.sequences  Element kotlin.sequences  EventChannel kotlin.sequences  	Exception kotlin.sequences  File kotlin.sequences  Handler kotlin.sequences  ITF kotlin.sequences  IllegalArgumentException kotlin.sequences  IllegalStateException kotlin.sequences  
ImageAnalysis kotlin.sequences  ImageFormat kotlin.sequences  ImageWrapper kotlin.sequences  
InputImage kotlin.sequences  Looper kotlin.sequences  Matrix kotlin.sequences  
MethodChannel kotlin.sequences  
MobileScanner kotlin.sequences  MobileScannerErrorCodes kotlin.sequences  MobileScannerHandler kotlin.sequences  MobileScannerPermissions kotlin.sequences   MobileScannerPermissionsListener kotlin.sequences  MobileScannerStartParameters kotlin.sequences  NoCamera kotlin.sequences  PDF417 kotlin.sequences  PackageManager kotlin.sequences  PlaneWrapper kotlin.sequences  Preview kotlin.sequences  ProcessCameraProvider kotlin.sequences  QR_CODE kotlin.sequences  REQUEST_CODE kotlin.sequences  Rect kotlin.sequences  RenderScript kotlin.sequences  ResolutionSelector kotlin.sequences  ResolutionStrategy kotlin.sequences  ScriptIntrinsicYuvToRGB kotlin.sequences  Size kotlin.sequences  Surface kotlin.sequences  Synchronized kotlin.sequences  
TorchState kotlin.sequences  Type kotlin.sequences  UNKNOWN kotlin.sequences  UPC_A kotlin.sequences  UPC_E kotlin.sequences  Uri kotlin.sequences  
YuvByteBuffer kotlin.sequences  YuvImage kotlin.sequences  YuvToRgbConverter kotlin.sequences  ZoomNotInRange kotlin.sequences  ZoomWhenStopped kotlin.sequences  activity kotlin.sequences  addListener kotlin.sequences  apply kotlin.sequences  arrayOf kotlin.sequences  camera kotlin.sequences  
captureOutput kotlin.sequences  com kotlin.sequences  emptyMap kotlin.sequences  first kotlin.sequences  forEach kotlin.sequences  
getResolution kotlin.sequences  isEmpty kotlin.sequences  
isNotEmpty kotlin.sequences  kotlin kotlin.sequences  launch kotlin.sequences  let kotlin.sequences  listener kotlin.sequences  map kotlin.sequences  
mapNotNull kotlin.sequences  mapOf kotlin.sequences  mobileScannerCallback kotlin.sequences  
mutableListOf kotlin.sequences  ongoing kotlin.sequences  
permission kotlin.sequences  provideSurface kotlin.sequences  require kotlin.sequences  rotateBitmap kotlin.sequences  
roundToInt kotlin.sequences  sorted kotlin.sequences  to kotlin.sequences  
toIntArray kotlin.sequences  until kotlin.sequences  ALL_FORMATS kotlin.text  AZTEC kotlin.text  ActivityCompat kotlin.text  
Allocation kotlin.text  
AlreadyPaused kotlin.text  AlreadyStarted kotlin.text  AlreadyStopped kotlin.text  AnnotationRetention kotlin.text  BarcodeFormats kotlin.text  BarcodeHandler kotlin.text  BarcodeScannerOptions kotlin.text  BarcodeScanning kotlin.text  Bitmap kotlin.text  Build kotlin.text  	ByteArray kotlin.text  ByteArrayOutputStream kotlin.text  
ByteBuffer kotlin.text  CODABAR kotlin.text  CODE_128 kotlin.text  CODE_39 kotlin.text  CODE_93 kotlin.text  CameraError kotlin.text  CameraSelector kotlin.text  Context kotlin.text  
ContextCompat kotlin.text  CoroutineScope kotlin.text  DATA_MATRIX kotlin.text  DetectionSpeed kotlin.text  Dispatchers kotlin.text  EAN_13 kotlin.text  EAN_8 kotlin.text  Element kotlin.text  EventChannel kotlin.text  	Exception kotlin.text  File kotlin.text  Handler kotlin.text  ITF kotlin.text  IllegalArgumentException kotlin.text  IllegalStateException kotlin.text  
ImageAnalysis kotlin.text  ImageFormat kotlin.text  ImageWrapper kotlin.text  
InputImage kotlin.text  Looper kotlin.text  Matrix kotlin.text  
MethodChannel kotlin.text  
MobileScanner kotlin.text  MobileScannerErrorCodes kotlin.text  MobileScannerHandler kotlin.text  MobileScannerPermissions kotlin.text   MobileScannerPermissionsListener kotlin.text  MobileScannerStartParameters kotlin.text  NoCamera kotlin.text  PDF417 kotlin.text  PackageManager kotlin.text  PlaneWrapper kotlin.text  Preview kotlin.text  ProcessCameraProvider kotlin.text  QR_CODE kotlin.text  REQUEST_CODE kotlin.text  Rect kotlin.text  RenderScript kotlin.text  ResolutionSelector kotlin.text  ResolutionStrategy kotlin.text  ScriptIntrinsicYuvToRGB kotlin.text  Size kotlin.text  Surface kotlin.text  Synchronized kotlin.text  
TorchState kotlin.text  Type kotlin.text  UNKNOWN kotlin.text  UPC_A kotlin.text  UPC_E kotlin.text  Uri kotlin.text  
YuvByteBuffer kotlin.text  YuvImage kotlin.text  YuvToRgbConverter kotlin.text  ZoomNotInRange kotlin.text  ZoomWhenStopped kotlin.text  activity kotlin.text  addListener kotlin.text  apply kotlin.text  arrayOf kotlin.text  camera kotlin.text  
captureOutput kotlin.text  com kotlin.text  emptyMap kotlin.text  first kotlin.text  forEach kotlin.text  
getResolution kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  kotlin kotlin.text  launch kotlin.text  let kotlin.text  listener kotlin.text  map kotlin.text  
mapNotNull kotlin.text  mapOf kotlin.text  mobileScannerCallback kotlin.text  
mutableListOf kotlin.text  ongoing kotlin.text  
permission kotlin.text  provideSurface kotlin.text  require kotlin.text  rotateBitmap kotlin.text  
roundToInt kotlin.text  sorted kotlin.text  to kotlin.text  
toIntArray kotlin.text  until kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  coroutineScope kotlinx.coroutines  launch kotlinx.coroutines  Bitmap !kotlinx.coroutines.CoroutineScope  ByteArrayOutputStream !kotlinx.coroutines.CoroutineScope  YuvToRgbConverter !kotlinx.coroutines.CoroutineScope  activity !kotlinx.coroutines.CoroutineScope  camera !kotlinx.coroutines.CoroutineScope  getACTIVITY !kotlinx.coroutines.CoroutineScope  getActivity !kotlinx.coroutines.CoroutineScope  	getCAMERA !kotlinx.coroutines.CoroutineScope  	getCamera !kotlinx.coroutines.CoroutineScope  	getLAUNCH !kotlinx.coroutines.CoroutineScope  	getLaunch !kotlinx.coroutines.CoroutineScope  getMOBILEScannerCallback !kotlinx.coroutines.CoroutineScope  getMobileScannerCallback !kotlinx.coroutines.CoroutineScope  getROTATEBitmap !kotlinx.coroutines.CoroutineScope  getRotateBitmap !kotlinx.coroutines.CoroutineScope  invoke !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  mobileScannerCallback !kotlinx.coroutines.CoroutineScope  rotateBitmap !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      