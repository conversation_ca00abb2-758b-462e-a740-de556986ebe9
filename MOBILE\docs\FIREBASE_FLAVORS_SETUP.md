# Configuração do Firebase para Flavors

## Problema
O erro `No matching client found for package name 'com.arcargo'` ocorre porque o Firebase está configurado apenas para `com.octalog`.

## Soluções

### Opção 1: Configurar Firebase para cada flavor (Recomendado)

1. **Criar projetos Firebase separados** para cada cliente:
   - Projeto Firebase para ArCargo (com package `com.arcargo`)
   - Projeto Firebase para Connect (com package `com.connect`)
   - Projeto Firebase para RondoLog (com package `com.rondolog`)

2. **Baixar google-services.json para cada projeto** e organizá-los:
```
android/app/src/
├── octalog/
│   └── google-services.json
├── arcargo/
│   └── google-services.json
├── connect/
│   └── google-services.json
└── rondolog/
    └── google-services.json
```

3. **Configurar build.gradle** para usar o arquivo correto por flavor:
```kotlin
android {
    // ... outras configurações
    
    productFlavors {
        create("octalog") {
            // ... configurações existentes
        }
        create("arcargo") {
            // ... configurações existentes
        }
        // ... outros flavors
    }
}

// Aplicar plugin do Google Services após as configurações
apply(plugin = "com.google.gms.google-services")
```

### Opção 2: Usar mesmo projeto Firebase (Mais simples)

1. **No Console Firebase**, adicionar todos os package names ao mesmo projeto:
   - `com.octalog`
   - `com.arcargo`
   - `com.connect`
   - `com.rondolog`

2. **Baixar novo google-services.json** que inclui todos os packages

### Opção 3: Desabilitar Firebase temporariamente (Para testes)

Comentar o plugin do Firebase no `build.gradle.kts`:

```kotlin
plugins {
    id("com.android.application")
    // id("com.google.gms.google-services") // Comentar esta linha
    id("kotlin-android")
    id("dev.flutter.flutter-gradle-plugin")
}
```

E comentar as inicializações do Firebase no código Dart.

## Implementação Rápida (Opção 2)

Para resolver rapidamente, siga estes passos:

1. Acesse o [Console Firebase](https://console.firebase.google.com/)
2. Selecione seu projeto atual
3. Vá em "Configurações do projeto" > "Seus apps"
4. Clique em "Adicionar app" > "Android"
5. Adicione cada package name:
   - `com.octalog.arcargo`
   - `com.octalog.connect`
   - `com.octalog.rondolog`
6. Baixe o novo `google-services.json`
7. Substitua o arquivo em `android/app/google-services.json`

## Teste após configuração

```bash
# Testar cada flavor
flutter build apk --debug --flavor octalog --dart-define=FLAVOR=octalog
flutter build apk --debug --flavor arcargo --dart-define=FLAVOR=arcargo
flutter build apk --debug --flavor connect --dart-define=FLAVOR=connect
flutter build apk --debug --flavor rondolog --dart-define=FLAVOR=rondolog
```
