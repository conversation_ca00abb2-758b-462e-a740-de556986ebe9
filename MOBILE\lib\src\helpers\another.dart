import 'package:map_fields/map_fields.dart';

class Another<S> {
  const Another();

  T fold<T>(
    T Function(S success) onSuccess,
    T Function(ExceptionLs error) onError,
  ) {
    if (this is Error) {
      return onError((this as Error).value);
    } else {
      return onSuccess((this as Success).value);
    }
  }

  static Another<S> run<S>(S Function() func) {
    try {
      return Success(func());
    } catch (e) {
      return Error(e);
    }
  }

  static Future<Another<S>> asyncRun<S>(Future<S> Function() func) async {
    try {
      final result = await func();
      return Success(result);
    } catch (e) {
      return Error(e);
    }
  }
}

class Success<S> extends Another<S> {
  final S _r;
  const Success(this._r);
  S get value => _r;
}

class Error<S> extends Another<S> {
  final dynamic _l;
  const Error(this._l);
  ExceptionLs get value => ExceptionLs.fromException(_l);
}

class ExceptionLs {
  final String message;
  const ExceptionLs(this.message);

  factory ExceptionLs.fromException(dynamic e) {
    if (e is ExceptionLs) {
      return e;
    }
    if (e is MapFieldsError) {
      return ExceptionLs('Erro conversão json => ${e.toString()}');
    }
    return ExceptionLs(e.toString());
  }

  @override
  String toString() => message;
}

// class AnotherTest {
//   Future<List<int>> getIds(bool error) async {
//     if (error) {
//       MapFieldsSettings.instance.setLanguage(MapFieldsLanguages.ptBr);
//       throw InvalidMapStringObjectError();
//     }
//     return [1, 2, 3];
//   }

//   Future<Another<List<int>>> teste(int id) async {
//     final isError = id % 2 == 0;
//     final response = await Another.asyncRun(
//       () async => await getIds(isError),
//     );
//     return response;
//   }
// }
