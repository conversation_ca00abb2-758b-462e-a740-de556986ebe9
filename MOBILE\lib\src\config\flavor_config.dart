import 'package:flutter/material.dart';

enum FlavorType {
  octalog,
  up360,
  connect,
  rondolog,
  spotlog,
  boyviny,
}

class FlavorConfig {
  final FlavorType flavor;
  final String name;
  final String applicationId;
  final ThemeData theme;
  final String assetPath;
  final Map<String, String> assetOverrides;

  FlavorConfig._({
    required this.flavor,
    required this.name,
    required this.applicationId,
    required this.theme,
    required this.assetPath,
    required this.assetOverrides,
  });

  static FlavorConfig? _instance;

  static FlavorConfig get instance {
    return _instance ?? _createDefault();
  }

  static void initialize(FlavorType flavor) {
    _instance = _createFlavor(flavor);
  }

  static FlavorConfig _createDefault() {
    return _createFlavor(FlavorType.octalog);
  }

  static FlavorConfig _createFlavor(FlavorType flavor) {
    switch (flavor) {
      case FlavorType.octalog:
        return FlavorConfig._(
          flavor: flavor,
          name: 'Octalog',
          applicationId: 'com.octalog',
          theme: _createOctalogTheme(),
          assetPath: 'assets/images/octalog/',
          assetOverrides: _getOctalogAssets(),
        );
      case FlavorType.up360:
        return FlavorConfig._(
          flavor: flavor,
          name: 'UP360',
          applicationId: 'com.octalog.up360',
          theme: _createUp360Theme(),
          assetPath: 'assets/images/up360/',
          assetOverrides: _getUp360Assets(),
        );
      case FlavorType.connect:
        return FlavorConfig._(
          flavor: flavor,
          name: 'Connect',
          applicationId: 'com.octalog.connect',
          theme: _createConnectTheme(),
          assetPath: 'assets/images/connect/',
          assetOverrides: _getConnectAssets(),
        );
      case FlavorType.rondolog:
        return FlavorConfig._(
          flavor: flavor,
          name: 'RondoLog',
          applicationId: 'com.octalog.rondolog',
          theme: _createRondologTheme(),
          assetPath: 'assets/images/rondolog/',
          assetOverrides: _getRondologAssets(),
        );
        
      case FlavorType.spotlog:
        return FlavorConfig._(
          flavor: flavor,
          name: 'SpotLog',
          applicationId: 'com.octalog.spotlog',
          theme: _createSpotlogTheme(),
          assetPath: 'assets/images/spotlog/',
          assetOverrides: _getSpotlogAssets(),
        );

      case FlavorType.boyviny:
        return FlavorConfig._(
          flavor: flavor,
          name: 'Boy Viny',
          applicationId: 'com.octalog.boyviny',
          theme: _createBoyvinyTheme(),
          assetPath: 'assets/images/boyviny/',
          assetOverrides: _getBoyvinyAssets(),
        );
    }
  }

  // Método para obter asset específico do flavor
  String getAsset(String assetName) {
    if (assetOverrides.containsKey(assetName)) {
      return assetOverrides[assetName]!;
    }
    return '$assetPath$assetName';
  }

  // Temas específicos por flavor
  static ThemeData _createOctalogTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color.fromRGBO(35, 87, 132, 1), // customOrange atual
        primary: const Color.fromRGBO(35, 87, 132, 1),
        secondary: const Color(0xFF3498db), // customGreen atual
        surface: const Color(0xFFFFFFFF),
        onSurface: const Color(0xFF000000),
        error: const Color.fromRGBO(255, 0, 0, 1),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Color.fromRGBO(35, 87, 132, 1),
        foregroundColor: Colors.white,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color.fromRGBO(35, 87, 132, 1),
          foregroundColor: Colors.white,
        ),
      ),
    );
  }

  static ThemeData _createUp360Theme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF063466), //
        primary: const Color(0xFF063466),
        secondary: const Color(0xFF3F81A0),
        surface: const Color(0xFFFFFFFF),
        onSurface: const Color(0xFF000000),
        error: const Color.fromRGBO(255, 0, 0, 1),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFF2E7D32),
        foregroundColor: Colors.white,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF2E7D32),
          foregroundColor: Colors.white,
        ),
      ),
    );
  }

  static ThemeData _createConnectTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF47A91B), // Azul Connect
        primary: const Color(0xFF47A91B),
        secondary: const Color(0xFFF7560C),
        surface: const Color(0xFFFFFFFF),
        onSurface: const Color(0xFF000000),
        error: const Color.fromRGBO(255, 0, 0, 1),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFF1976D2),
        foregroundColor: Colors.white,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF1976D2),
          foregroundColor: Colors.white,
        ),
      ),
    );
  }

  static ThemeData _createRondologTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFFA51B18), // Laranja RondoLog
        primary: const Color(0xFFA51B18),
        secondary: const Color(0xFF3F3F3D),
        surface: const Color(0xFFFFFFFF),
        onSurface: const Color(0xFF000000),
        error: const Color.fromRGBO(255, 0, 0, 1),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFFE65100),
        foregroundColor: Colors.white,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFE65100),
          foregroundColor: Colors.white,
        ),
      ),
    );
  }

  static ThemeData _createSpotlogTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFFBD1522),
        primary: const Color(0xFFBD1522),
        secondary: const Color(0xFF3F3F3D),
        surface: const Color(0xFFFFFFFF),
        onSurface: const Color(0xFF000000),
        error: const Color.fromRGBO(255, 0, 0, 1),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFFE65100),
        foregroundColor: Colors.white,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFE65100),
          foregroundColor: Colors.white,
        ),
      ),
    );
  }

  static ThemeData _createBoyvinyTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFFBD1522),
        primary: const Color(0xFFBD1522),
        secondary: const Color(0xFF3F3F3D),
        surface: const Color(0xFFFFFFFF),
        onSurface: const Color(0xFF000000),
        error: const Color.fromRGBO(255, 0, 0, 1),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFFE65100),
        foregroundColor: Colors.white,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFE65100),
          foregroundColor: Colors.white,
        ),
      ),
    );
  }

  // Assets específicos por flavor
  static Map<String, String> _getOctalogAssets() {
    return {
      'logo200.png': 'assets/images/octalog/logo200.png',
      'foto_acareacao.gif': 'assets/images/octalog/foto_acareacao.gif',
      'foto_receita.gif': 'assets/images/octalog/foto_receita.gif',
      'foto_fachada.png': 'assets/images/octalog/foto_fachada.png',
      'foto_canhoto.gif': 'assets/images/octalog/foto_canhoto.gif',
      'image_fmc.png': 'assets/images/octalog/image_fmc.png',
      'localization.png': 'assets/images/octalog/localization.png',
    };
  }

  static Map<String, String> _getUp360Assets() {
    return {
      'logo200.png': 'assets/images/up360/logo200.png',
      'foto_acareacao.gif': 'assets/images/up360/foto_acareacao.gif',
      'foto_receita.gif': 'assets/images/up360/foto_receita.gif',
      'foto_fachada.png': 'assets/images/up360/foto_fachada.png',
      'foto_canhoto.gif': 'assets/images/up360/foto_canhoto.gif',
      'image_fmc.png': 'assets/images/up360/image_fmc.png',
      'localization.png': 'assets/images/up360/localization.png',
    };
  }

  static Map<String, String> _getConnectAssets() {
    return {
      'logo200.png': 'assets/images/connect/logo200.png',
      'foto_acareacao.gif': 'assets/images/connect/foto_acareacao.gif',
      'foto_receita.gif': 'assets/images/connect/foto_receita.gif',
      'foto_fachada.png': 'assets/images/connect/foto_fachada.png',
      'foto_canhoto.gif': 'assets/images/connect/foto_canhoto.gif',
      'image_fmc.png': 'assets/images/connect/image_fmc.png',
      'localization.png': 'assets/images/connect/localization.png',
    };
  }

  static Map<String, String> _getRondologAssets() {
    return {
      'logo200.png': 'assets/images/rondolog/logo200.png',
      'foto_acareacao.gif': 'assets/images/rondolog/foto_acareacao.gif',
      'foto_receita.gif': 'assets/images/rondolog/foto_receita.gif',
      'foto_fachada.png': 'assets/images/rondolog/foto_fachada.png',
      'foto_canhoto.gif': 'assets/images/rondolog/foto_canhoto.gif',
      'image_fmc.png': 'assets/images/rondolog/image_fmc.png',
      'localization.png': 'assets/images/rondolog/localization.png',
    };
  }
  
  static Map<String, String> _getSpotlogAssets() {
    return {
      'logo200.png': 'assets/images/spotlog/logo200.png',
      'foto_acareacao.gif': 'assets/images/spotlog/foto_acareacao.gif',
      'foto_receita.gif': 'assets/images/spotlog/foto_receita.gif',
      'foto_fachada.png': 'assets/images/spotlog/foto_fachada.png',
      'foto_canhoto.gif': 'assets/images/spotlog/foto_canhoto.gif',
      'image_fmc.png': 'assets/images/spotlog/image_fmc.png',
      'localization.png': 'assets/images/spotlog/localization.png',
    };
  }

  static Map<String, String> _getBoyvinyAssets() {
    return {
      'logo200.png': 'assets/images/boyviny/logo200.png',
      'foto_acareacao.gif': 'assets/images/boyviny/foto_acareacao.gif',
      'foto_receita.gif': 'assets/images/boyviny/foto_receita.gif',
      'foto_fachada.png': 'assets/images/boyviny/foto_fachada.png',
      'foto_canhoto.gif': 'assets/images/boyviny/foto_canhoto.gif',
      'image_fmc.png': 'assets/images/boyviny/image_fmc.png',
      'localization.png': 'assets/images/boyviny/localization.png',
    };
  }
}
