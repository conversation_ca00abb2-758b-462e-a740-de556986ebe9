import 'dart:io';

import 'package:flutter/services.dart';
// import 'package:flutter_android_developer_mode/flutter_android_developer_mode.dart';
// import 'package:flutter_jailbreak_detection/flutter_jailbreak_detection.dart';

abstract class ConnectivityChecker {
  static final instance = ConnectivityCheckerImpl();

  Future<bool?> checkDadosMoveis();
  Future<bool?> checkModoAviao();
  Future<bool?> checkWifi();
  Future<bool?> checkDeveloperMode();
}

class ConnectivityCheckerImpl implements ConnectivityChecker {
  static const MethodChannel _channel =
      MethodChannel('com.example.dadosMoveis');

  @override
  Future<bool?> checkDadosMoveis() async {
    if (Platform.isIOS) {
      return null;
    }
    try {
      final bool? isAirplaneMode = await checkModoAviao();
      if (isAirplaneMode!) {
        return false;
      }

      final bool isConnected = await _channel.invokeMethod('checkDadosMoveis');
      return isConnected;
    } catch (_) {
      return null;
    }
  }

  @override
  Future<bool?> checkModoAviao() async {
    if (Platform.isIOS) {
      return null;
    }
    try {
      final bool isAirplaneMode =
          await _channel.invokeMethod('checkAirplaneMode');
      return isAirplaneMode;
    } catch (_) {
      return null;
    }
  }

  @override
  Future<bool?> checkWifi() async {
    if (Platform.isIOS) {
      return null;
    }
    try {
      final bool isWifiOn = await _channel.invokeMethod('checkWifi');
      return isWifiOn;
    } catch (_) {
      return null;
    }
  }

  @override
  Future<bool?> checkDeveloperMode() async {
    if (Platform.isIOS) {
      try {
        // final bool isJainBrokenOn = await FlutterJailbreakDetection.jailbroken;
        // return isJainBrokenOn;
        return false;
      } catch (_) {
        return null;
      }
    }
    try {
      // final bool isDeveloperModeOn =
      //     await FlutterAndroidDeveloperMode.isAndroidDeveloperModeEnabled;
      // return isDeveloperModeOn;
        return false;
    } catch (_) {
      return null;
    }
  }
}
