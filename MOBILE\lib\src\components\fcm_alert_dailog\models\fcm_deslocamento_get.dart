import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import 'package:map_fields/map_fields.dart';

import '../../../models_new/position_data_location.dart';
import '../fcm_alert_dialog_state.dart';
import './fcm_alert_dados.dart';

class FcmDeslocamentoGet {
  final int id;
  final String titulo;
  final String mensagem;
  final String endereco;
  final double latitude;
  final double longitude;
  final DateTime horario;
  final List<FcmPedido> pedidos;
  final String? logo;
  final int idLocal;
  final DateTime? dataHoraDeslocamento;
  final DateTime? dataHoraChegada;
  final DateTime? dataHoraFinalizada;
  final bool reverso;
  final bool uberizado;
  final int tipoModalidadeColeta;
  final bool receita;
  final bool coletarPedidoSemIntegracao;

  FcmDeslocamentoGet({
    required this.id,
    required this.titulo,
    required this.mensagem,
    required this.endereco,
    required this.latitude,
    required this.longitude,
    required this.horario,
    required this.pedidos,
    required this.logo,
    required this.idLocal,
    required this.dataHoraChegada,
    required this.dataHoraDeslocamento,
    required this.dataHoraFinalizada,
    required this.reverso,
    required this.uberizado,
    required this.tipoModalidadeColeta,
    required this.receita,
    required this.coletarPedidoSemIntegracao,
  });

  FcmAlertPageStep get step {
    if (dataHoraChegada != null) return FcmAlertPageStep.coleta;
    if (dataHoraDeslocamento != null) return FcmAlertPageStep.chegada;
    return FcmAlertPageStep.deslocamento;
  }

  String get time =>
      "${horario.hour.toString().padLeft(2, '0')}:${horario.minute.toString().padLeft(2, '0')}";

  factory FcmDeslocamentoGet.fromMap(Map<String, dynamic> map) {
    final f = MapFields.load(map);

    return FcmDeslocamentoGet(
      id: f.getInt('id', 0),
      titulo: f.getString('titulo', ''),
      mensagem: f.getString('mensagem', ''),
      endereco: f.getString('endereco', ''),
      latitude: f.getDouble('latitude', 0),
      longitude: f.getDouble('longitude', 0),
      horario: f.getDateTime('horario', DateTime.now()),
      pedidos: f
          .getList<Map<String, dynamic>>('pedidos', [])
          .map((e) => FcmPedido.fromMap(e))
          .toList(),
      logo: f.getStringNullable('logo'),
      idLocal: f.getInt('idLocal', 0),
      dataHoraDeslocamento: f.getDateTimeNullable('dataHoraDeslocamento'),
      dataHoraChegada: f.getDateTimeNullable('dataHoraChegada'),
      dataHoraFinalizada: f.getDateTimeNullable('dataHoraFinalizada'),
      reverso: f.getBool('reverso', false),
      uberizado: f.getBool('uberizado', false),
      tipoModalidadeColeta: f.getInt('tipoModalidadeColeta', 1),
      receita: f.getBool('receita', false),
      coletarPedidoSemIntegracao:
          f.getBool('coletarPedidoSemIntegracao', false),
    );
  }

  factory FcmDeslocamentoGet.fromMapKlev(Map<String, dynamic> map) {
    final f = MapFields.load(map);
    final local = MapFields.load(f.getMap<String, dynamic>('Local', {}));
    return FcmDeslocamentoGet(
      id: f.getInt('IDDeslocamento', 0),
      titulo: local.getString('Nome', ''),
      mensagem: f.getString('Mensagem', ''),
      endereco: local.getString('Endereco', ''),
      latitude: local.getDouble('Latitude', 0),
      longitude: local.getDouble('Longitude', 0),
      horario: f.getDateTime('DataHoraAgendada', DateTime.now()),
      logo: local.getStringNullable('Logo'),
      pedidos: [],
      idLocal: local.getInt('IDLocal', 0),
      dataHoraDeslocamento: f.getDateTimeNullable('DataHoraDeslocamento'),
      dataHoraChegada: f.getDateTimeNullable('DataHoraChegada'),
      dataHoraFinalizada: f.getDateTimeNullable('DataHoraFinalizada'),
      reverso: f.getBool('Reverso', false),
      uberizado: f.getBool('Uberizado', false),
      tipoModalidadeColeta: f.getInt('TipoModalidadeColeta', 1),
      receita: f.getBool('Receita', false),
      coletarPedidoSemIntegracao:
          f.getBool('ColetarPedidoSemIntegracao', false),
    );
  }

  Map<String, dynamic> toMapKlev() {
    return {
      "IDDeslocamento": id,
      "Mensagem": mensagem,
      "DataInclusao": DateTime.now().toIso8601String(),
      "DataHoraAgendada": horario.toIso8601String(),
      "DataHoraDeslocamento": dataHoraDeslocamento?.toIso8601String(),
      "DataHoraChegada": dataHoraChegada?.toIso8601String(),
      "DataHoraFinalizado": dataHoraFinalizada?.toIso8601String(),
      "Local": {
        "IDLocal": idLocal,
        "Nome": titulo,
        "Logo": logo,
        "Endereco": endereco,
        "Latitude": latitude,
        "Longitude": longitude,
      },
      "Reverso": reverso,
      "Uberizado": uberizado,
      "TipoModalidadeColeta": tipoModalidadeColeta,
      "Receita": receita,
      "ColetarPedidoSemIntegracao": coletarPedidoSemIntegracao,
    };
  }

  LatLng get destino => LatLng(latitude, longitude);

  double calcularDistancia(PositionDataLocation position) {
    final distancia = GeolocatorPlatform.instance.distanceBetween(
      position.latitude,
      position.longitude,
      latitude,
      longitude,
    );
    return distancia / 1000;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'titulo': titulo,
      'mensagem': mensagem,
      'endereco': endereco,
      'latitude': latitude,
      'longitude': longitude,
      'horario': horario.toIso8601String(),
      'pedidos': pedidos.map((e) => e.toMap()).toList(),
      'logo': logo,
      'idLocal': idLocal,
      'dataHoraDeslocamento': dataHoraDeslocamento?.toIso8601String(),
      'dataHoraChegada': dataHoraChegada?.toIso8601String(),
      'dataHoraFinalizada': dataHoraFinalizada?.toIso8601String(),
      'reverso': reverso,
      'uberizado': uberizado,
      'tipoModalidadeColeta': tipoModalidadeColeta,
      'receita': receita,
      'coletarPedidoSemIntegracao': coletarPedidoSemIntegracao,
    };
  }

  FcmDeslocamentoGet copyWith({
    int? id,
    String? titulo,
    String? mensagem,
    String? endereco,
    double? latitude,
    double? longitude,
    DateTime? horario,
    List<FcmPedido>? pedidos,
    String? logo,
    int? idLocal,
    DateTime? dataHoraDeslocamento,
    DateTime? dataHoraChegada,
    DateTime? dataHoraFinalizada,
    bool? reverso,
    bool? uberizado,
    int? tipoModalidadeColeta,
    bool? receita,
    bool? coletarPedidoSemIntegracao,
  }) {
    return FcmDeslocamentoGet(
      id: id ?? this.id,
      titulo: titulo ?? this.titulo,
      mensagem: mensagem ?? this.mensagem,
      endereco: endereco ?? this.endereco,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      horario: horario ?? this.horario,
      pedidos: pedidos ?? this.pedidos,
      logo: logo ?? this.logo,
      idLocal: idLocal ?? this.idLocal,
      dataHoraDeslocamento: dataHoraDeslocamento ?? this.dataHoraDeslocamento,
      dataHoraChegada: dataHoraChegada ?? this.dataHoraChegada,
      dataHoraFinalizada: dataHoraFinalizada ?? this.dataHoraFinalizada,
      reverso: reverso ?? this.reverso,
      uberizado: uberizado ?? this.uberizado,
      tipoModalidadeColeta: tipoModalidadeColeta ?? this.tipoModalidadeColeta,
      receita: receita ?? this.receita,
      coletarPedidoSemIntegracao:
          coletarPedidoSemIntegracao ?? this.coletarPedidoSemIntegracao,
    );
  }

  FcmDeslocamentoGet copyWithDeslocamento(FcmDeslocamentoGet deslocamento) {
    return FcmDeslocamentoGet(
      id: deslocamento.id,
      titulo: deslocamento.titulo,
      mensagem: deslocamento.mensagem,
      endereco: deslocamento.endereco,
      latitude: deslocamento.latitude,
      longitude: deslocamento.longitude,
      horario: deslocamento.horario,
      pedidos: deslocamento.pedidos.isEmpty ? pedidos : deslocamento.pedidos,
      logo: deslocamento.logo ?? logo,
      idLocal: deslocamento.idLocal,
      dataHoraDeslocamento:
          deslocamento.dataHoraDeslocamento ?? dataHoraDeslocamento,
      dataHoraChegada: deslocamento.dataHoraChegada ?? dataHoraChegada,
      dataHoraFinalizada: deslocamento.dataHoraFinalizada ?? dataHoraFinalizada,
      reverso: deslocamento.reverso,
      uberizado: deslocamento.uberizado,
      tipoModalidadeColeta: deslocamento.tipoModalidadeColeta,
      receita: deslocamento.receita,
      coletarPedidoSemIntegracao: deslocamento.coletarPedidoSemIntegracao,
    );
  }

  FcmDeslocamentoGet semDatas() {
    return FcmDeslocamentoGet(
      id: id,
      titulo: titulo,
      mensagem: mensagem,
      endereco: endereco,
      latitude: latitude,
      longitude: longitude,
      horario: horario,
      pedidos: pedidos,
      logo: logo,
      idLocal: idLocal,
      dataHoraDeslocamento: null,
      dataHoraChegada: null,
      dataHoraFinalizada: null,
      reverso: reverso,
      uberizado: uberizado,
      tipoModalidadeColeta: tipoModalidadeColeta,
      receita: receita,
      coletarPedidoSemIntegracao: coletarPedidoSemIntegracao,
    );
  }

  double distance(PositionDataLocation position) {

    if (latitude == 0) return 0;

    final distancia = GeolocatorPlatform.instance.distanceBetween(
      position.latitude,
      position.longitude,
      latitude,
      longitude,
    );

    return distancia;
  }
}
