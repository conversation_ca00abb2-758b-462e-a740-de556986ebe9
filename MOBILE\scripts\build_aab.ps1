# Script PowerShell para build AAB com flavors
# Uso: .\scripts\build_aab.ps1 [flavor] [build_type]
# Exemplo: .\scripts\build_aab.ps1 connect release

param(
    [string]$Flavor = "octalog",
    [string]$BuildType = "release",
    [switch]$Help
)

# Função para exibir ajuda
function Show-Help {
    Write-Host "Build AAB com Flavors" -ForegroundColor Blue
    Write-Host ""
    Write-Host "Uso: .\scripts\build_aab.ps1 [flavor] [build_type]"
    Write-Host ""
    Write-Host "Flavors disponíveis:"
    Write-Host "  - octalog   (padrão)"
    Write-Host "  - arcargo"
    Write-Host "  - connect"
    Write-Host "  - rondolog"
    Write-Host "  - spotlog"
    Write-Host ""
    Write-Host "Build types:"
    Write-Host "  - release   (padrão)"
    Write-Host "  - debug"
    Write-Host ""
    Write-Host "Exemplos:"
    Write-Host "  .\scripts\build_aab.ps1 connect release"
    Write-Host "  .\scripts\build_aab.ps1 arcargo debug"
    Write-Host "  .\scripts\build_aab.ps1 octalog"
    Write-Host ""
}

# Verificar se foi solicitada ajuda
if ($Help) {
    Show-Help
    exit 0
}

# Validar flavor
$ValidFlavors = @("octalog", "arcargo", "connect", "rondolog", "spotlog")
if ($ValidFlavors -notcontains $Flavor) {
    Write-Host "✗ Flavor inválido: $Flavor" -ForegroundColor Red
    Write-Host "Flavors válidos: $($ValidFlavors -join ', ')" -ForegroundColor Yellow
    exit 1
}

# Validar build type
$ValidBuildTypes = @("release", "debug")
if ($ValidBuildTypes -notcontains $BuildType) {
    Write-Host "✗ Build type inválido: $BuildType" -ForegroundColor Red
    Write-Host "Build types válidos: $($ValidBuildTypes -join ', ')" -ForegroundColor Yellow
    exit 1
}

Write-Host "✓ Flavor válido: $Flavor" -ForegroundColor Green
Write-Host "✓ Build type válido: $BuildType" -ForegroundColor Green
Write-Host ""

Write-Host "🚀 Iniciando build AAB..." -ForegroundColor Blue
Write-Host "Flavor: $Flavor" -ForegroundColor Yellow
Write-Host "Build Type: $BuildType" -ForegroundColor Yellow
Write-Host ""

try {
    # Limpar build anterior
    Write-Host "🧹 Limpando build anterior..." -ForegroundColor Blue
    flutter clean
    if ($LASTEXITCODE -ne 0) { throw "Falha ao limpar build anterior" }

    # Obter dependências
    Write-Host "📦 Obtendo dependências..." -ForegroundColor Blue
    flutter pub get
    if ($LASTEXITCODE -ne 0) { throw "Falha ao obter dependências" }

    # Executar build
    Write-Host "🔨 Executando build AAB..." -ForegroundColor Blue
    $BuildCommand = "flutter build appbundle --$BuildType --flavor $Flavor --dart-define=FLAVOR=$Flavor --target-platform android-arm,android-arm64,android-x64"
    
    Write-Host "Comando: $BuildCommand" -ForegroundColor Cyan
    Invoke-Expression $BuildCommand
    
    if ($LASTEXITCODE -ne 0) { throw "Falha no build AAB" }

    # Verificar se o build foi bem-sucedido
    Write-Host ""
    Write-Host "✅ Build AAB concluído com sucesso!" -ForegroundColor Green
    Write-Host ""
    
    # Localizar o arquivo AAB
    $AabPath = "build\app\outputs\bundle\${Flavor}Release\app-${Flavor}-release.aab"
    if ($BuildType -eq "debug") {
        $AabPath = "build\app\outputs\bundle\${Flavor}Debug\app-${Flavor}-debug.aab"
    }
    
    if (Test-Path $AabPath) {
        Write-Host "📱 Arquivo AAB gerado:" -ForegroundColor Green
        Write-Host $AabPath -ForegroundColor Blue
        Write-Host ""
        
        # Exibir informações do arquivo
        Write-Host "📊 Informações do arquivo:" -ForegroundColor Yellow
        Get-ChildItem $AabPath | Format-Table Name, Length, LastWriteTime -AutoSize
        Write-Host ""
        
        # Sugerir próximos passos
        Write-Host "🎯 Próximos passos:" -ForegroundColor Yellow
        Write-Host "1. Teste o AAB em um dispositivo"
        Write-Host "2. Faça upload para o Google Play Console"
        Write-Host "3. Configure o release no Play Console"
    } else {
        Write-Host "❌ Arquivo AAB não encontrado em: $AabPath" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host ""
    Write-Host "❌ Falha no build AAB: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
