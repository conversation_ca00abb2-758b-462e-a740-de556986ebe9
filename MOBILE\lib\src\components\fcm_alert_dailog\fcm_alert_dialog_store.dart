import 'dart:convert';

import 'package:asuka/asuka.dart' as asuka;
import 'package:flutter/material.dart';
// import 'package:flutter_beep/flutter_beep.dart';
import 'package:octalog/src/components/fcm_alert_dailog/fcm_alert_dialog_state.dart';
import 'package:octalog/src/helpers/web_connector.dart';
import 'package:octalog/src/models_new/position_data_location.dart';
import 'package:octalog/src/utils/extesion.dart';
import 'package:octalog/src/utils/theme_colors.dart';
import 'package:octalog/src/utils/verify_gps.dart';
import '../../database/config_blob/config_database.dart';
import '../../database/deslocamento_database/deslocamento_database.dart';
import '../../helpers/gps/gps_contract.dart';
import '../scan_page/scan_page.dart';
import './models/fcm_alert_dados.dart';
import 'fcm_alert_dialog_external.dart';
import 'models/fcm_coleta_finalizacao_body.dart';
import 'models/fcm_deslocamento_get.dart';
import 'models/fcm_deslocamento_info.dart';

class FcmAlertDialogStore {
  FcmAlertDialogStore(FcmDeslocamentoGet dados) {
    state = ValueNotifier<FcmAlertState>(FcmAlertState.initial(dados));
  }

  late final ValueNotifier<FcmAlertState> state;

  void setLoading(bool value) {
    state.value = state.value.copyWith(isLoading: value);
  }

  void setLiberadoPor(String value) {
    state.value = state.value.copyWith(liberadoPor: value);
  }

  void setFiltro(String value) {
    state.value = state.value.copyWith(filtro: value);
  }

  void setLoadingButton(bool value) {
    state.value = state.value.copyWith(isLoadingButton: value);
  }

  Future<void> inicio() async {
    state.value = state.value.copyWith(
      fcmAlertPageStep: FcmAlertPageStep.chegada,
      fcmAlertDados: state.value.fcmAlertDados.copyWith(dataHoraDeslocamento: DateTime.now().dataHoraServidorFomart),
    );
    final position = await GpsHelperContract.instance.updateAndGetLastPosition();
    final dados = state.value.fcmAlertDados;
    final info = FcmDeslocamentoInfo(
      idDeslocamento: dados.id,
      latitude: position?.latitude,
      longitude: position?.longitude,
      dataHora: DateTime.now().dataHoraServidorFomart,
      status: FcmAlertPageStep.chegada.title,
    );
    await FcmExternalFirebase.instance.deslocamentoInicio(info.toFcmDeslocamentoModel());
    await DeslocamentoDatabase.instance.save(state.value.fcmAlertDados);
  }

  Future<void> chegada() async {
    state.value = state.value.copyWith(
      fcmAlertPageStep: FcmAlertPageStep.coleta,
      fcmAlertDados: state.value.fcmAlertDados.copyWith(dataHoraChegada: DateTime.now().dataHoraServidorFomart),
    );

    final position = await GpsHelperContract.instance.updateAndGetLastPosition();
    final info = FcmDeslocamentoInfo(
      idDeslocamento: state.value.fcmAlertDados.id,
      latitude: position?.latitude,
      longitude: position?.longitude,
      dataHora: DateTime.now().dataHoraServidorFomart,
      status: FcmAlertPageStep.coleta.title,
    );
    await FcmExternalFirebase.instance.deslocamentoChegada(info.toFcmDeslocamentoModel());

    await DeslocamentoDatabase.instance.save(state.value.fcmAlertDados);
  }

  Future<void> finalizeColeta(BuildContext context, {bool finalizarTodosPedidos = false}) async {
    try {
      state.value = state.value.copyWith(fcmAlertDados: state.value.fcmAlertDados.copyWith(dataHoraFinalizada: DateTime.now().dataHoraServidorFomart));

      final conn = WebConnector();
      FcmColetaFinalizacaoBody body = FcmColetaFinalizacaoBody(
        nomeLiberacao: state.value.liberadoPor,
        idLocal: state.value.fcmAlertDados.idLocal,
        idDeslocamento: state.value.fcmAlertDados.id,
        coletados: finalizarTodosPedidos ? state.value.fcmAlertDados.pedidos : state.value.coletados,
        naoColetados: finalizarTodosPedidos ? [] : state.value.naoColetados,
        devolucao: state.value.fcmAlertDados.reverso ? state.value.fcmAlertDados.pedidos : [],
      );
      final bodyKlev = jsonEncode(body.toMapKlevSend());
      await conn.put('/deslocamento/pedidoscoletados', body: bodyKlev);
      await DeslocamentoDatabase.instance.save(state.value.fcmAlertDados);
    } catch (e) {
      setLoadingButton(false);
      await showDialog(
        context: context,
        builder:
            (ctx) => AlertDialog(
              title: const Text('Atenção'),
              content: const Text('Erro ao finalizar coleta, verifique sua internet!'),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(ctx);
                  },
                  child: Text('Ok', style: TextStyle(color: ThemeColors.customOrange(context))),
                ),
              ],
            ),
      );
      rethrow;
    }
  }

  Future<bool> verificarDistancia() async {
    bool pausar = false;
    final config = await ConfigDatabase.instance.getConfig();
    if (config.bloqueioDeslocamentoForaDoLocal) {
      PositionDataLocation? position = await GpsHelperContract.instance.updateAndGetLastPosition();
      position ??= await GpsHelperContract.instance.updateLoc(timeoutSeconds: 10);
      if (position == null) return false;

      if (state.value.fcmAlertDados.distance(position) > config.distanciaMetrosChegadaDeslocamento) {
        pausar = true;
        await asuka.Asuka.showDialog(
          builder:
              (ctx) => AlertDialog(
                title: const Text('Atenção'),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    state.value.fcmAlertDados.reverso
                        ? const Text("Você está finalizando a devolução longe do endereço da loja.")
                        : const Text("Você está finalizando a coleta longe do endereço da loja."),
                    const Text("Você não poderá prosseguir."),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(ctx).pop();
                    },
                    child: const Text('CANCELAR', style: TextStyle(color: Colors.red)),
                  ),
                ],
              ),
        );
      }
    }

    return pausar;
  }

  Future<void> scanBarcode(BuildContext context, [String texto = 'Aponte a câmera para o código de barras']) async {
    final response = await Navigator.push<String>(context, MaterialPageRoute(builder: (_) => ScanPage(title: texto)));
    final codigosOriginais = state.value.fcmAlertDados.pedidos.map((p) => p.os).toList();
    if (response != null && response != '') {
      //FlutterBeep.beep();
      state.value = state.value.addCodigo(response).copyWith();
      if (codigosOriginais.contains(response)) {
        Future.delayed(const Duration(seconds: 1), () {
          scanBarcode(context, 'Código $response íncluido, colete o próximo pedido');
        });
      }
    }
  }

  void deletarPedido(String os) {
    state.value = state.value.removeCodigo(os).copyWith();
  }

  void deletarPedidoCodigo(FcmPedido pedido) {
    state.value = state.value.removePedido(pedido).copyWith();
  }

  Future selecionarTodosPedidos(String nome) async {
    final todosPedidos = state.value.pedidosTotaisCliente(nome);
    for (final pedido in todosPedidos) {
      if (!state.value.codigos.contains(pedido.idOs)) {
        state.value = state.value.addIDOS(pedido.idOs).copyWith();
      }
    }
    await Future.delayed(const Duration(milliseconds: 600), () {});
  }

  void removerTodosPedidos(String nome) {
    final todosPedidos = state.value.pedidosTotaisCliente(nome);
    for (final pedido in todosPedidos) {
      removeIDOS(pedido.idOs);
    }
  }

  void deletarTodosPedidos(String nome) {
    final todosPedidos = state.value.pedidosTotaisCliente(nome);
    for (final pedido in todosPedidos) {
      deletarPedidoCodigo(pedido);
    }
  }

  bool scannOnChanged(String barcodeScanRes) {
    state.value = state.value.addCodigo(barcodeScanRes).copyWith();
    return true;
  }

  bool addIDOS(int idos) {
    state.value = state.value.addIDOS(idos).copyWith();
    return true;
  }

  bool removeIDOS(int idos) {
    state.value = state.value.removeIDOS(idos).copyWith();
    return true;
  }

  Future<void> getDadosPedidos(BuildContext context) async {
    setLoading(true);
    final conn = WebConnector();
    try {
      final response = await conn.get(
        '/deslocamento/pedidos',
        queryParameters: {"iddeslocamento": state.value.fcmAlertDados.id, "reverso": state.value.fcmAlertDados.reverso},
      );
      final responseDataJson = (response.data is String ? jsonDecode(response.data) : response.data) as List;
      final pedidos = responseDataJson.map<FcmPedido>((e) => FcmPedido.fromMapKlev(e)).toList();
      final pedidosNovos = state.value.fcmAlertDados.pedidos.where((element) => element.idOs == 0).toList();
      state.value = state.value.copyWith(pedidos: [...pedidos, ...pedidosNovos]);
      await DeslocamentoDatabase.instance.save(state.value.fcmAlertDados);
      setLoading(false);
    } catch (e) {
      await showDialog(
        context: context,
        builder:
            (ctx) => AlertDialog(
              title: const Text('Atenção'),
              content: const Text('Erro ao buscar pedidos, verifique sua internet!'),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(ctx);
                  },
                  child: const Text('Voltar', style: TextStyle(color: Colors.red)),
                ),
                TextButton(
                  onPressed: () {
                    getDadosPedidos(context);
                  },
                  child: Text('Tentar novamente', style: TextStyle(color: ThemeColors.customOrange(context))),
                ),
              ],
            ),
      );

      setLoading(false);
    }
  }

  Future verificarGPS() async {
    final gpsEnable = await GpsHelperContract.instance.checaGpsLigado();

    if (!gpsEnable) {
      await asuka.Asuka.showDialog(builder: (ctx) => const VerifyGps());
      verificarGPS();
      return;
    }
  }
}
