import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/utils/theme_colors.dart';
// import 'package:url_launcher/url_launcher.dart';

import '../../../components/buttom_ls/button_ls_custom.dart';
// import '../../../database/config_blob/config_database.dart';
//
import '../model/local_coleta_despachar_model.dart';

Future<int?> showBottomSheetLocalColetaDespachar(context, List<LocalColetaDespachar> locais, Function(int, int) onConfirm) async {
  return await showModalBottomSheet<int?>(
    context: context,
    shape: ShapeBorder.lerp(
      RoundedRectangleBorder(borderRadius: BorderRadius.circular(17)),
      RoundedRectangleBorder(borderRadius: BorderRadius.circular(17)),
      1,
    ),
    isScrollControlled: true,
    builder: (context) {
      return FractionallySizedBox(heightFactor: 0.6, child: ModalButtonLocalColetaEscolha(locais: locais, onConfirm: onConfirm));
    },
  );
}

class ModalButtonLocalColetaEscolha extends StatefulWidget {
  final List<LocalColetaDespachar> locais;
  final Function(int, int) onConfirm;
  const ModalButtonLocalColetaEscolha({super.key, required this.locais, required this.onConfirm});

  @override
  State<ModalButtonLocalColetaEscolha> createState() => _ModalButtonLocalColetaEscolhaState();
}

class _ModalButtonLocalColetaEscolhaState extends State<ModalButtonLocalColetaEscolha> {
  final TextEditingController _controller = TextEditingController(text: '0');
  int? indexEscolhido;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 10),
        Text('Volume', style: GoogleFonts.roboto(fontSize: 15, color: ThemeColors.customOrange(context), fontWeight: FontWeight.w500)),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 45,
              height: 45,
              child: AspectRatio(
                aspectRatio: 1,
                child: FloatingActionButton(
                  elevation: 0,
                  backgroundColor: ThemeColors.customOrange(context).withOpacity(0.7),
                  onPressed: () {
                    final qtd = int.tryParse(_controller.text);
                    if (qtd != 0) {
                      final value = int.tryParse(_controller.text);
                      if (value != null) {
                        setState(() {
                          _controller.text = (value - 1).toString();
                        });
                      }
                    }
                  },
                  child: const Icon(Icons.remove),
                ),
              ),
            ),
            const SizedBox(width: 20),
            SizedBox(
              height: 55,
              width: 55,
              child: TextFormField(
                textAlign: TextAlign.center,
                controller: _controller,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(hintText: 'Volume', border: OutlineInputBorder(borderRadius: BorderRadius.circular(10))),
              ),
            ),
            const SizedBox(width: 20),
            SizedBox(
              width: 45,
              height: 45,
              child: AspectRatio(
                aspectRatio: 1,
                child: FloatingActionButton(
                  elevation: 0,
                  backgroundColor: ThemeColors.customOrange(context).withOpacity(0.7),
                  onPressed: () {
                    final qtd = int.tryParse(_controller.text);
                    if (qtd != 99) {
                      final value = int.tryParse(_controller.text);
                      if (value != null) {
                        setState(() {
                          _controller.text = (value + 1).toString();
                        });
                      }
                    }
                  },
                  child: const Icon(Icons.add),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 10),
        Divider(color: ThemeColors.customOrange(context), thickness: 2, indent: 5, endIndent: 5),
        Row(
          children: [
            const SizedBox(width: 25),
            Text('Qual a loja/cliente', style: GoogleFonts.roboto(fontSize: 15, color: ThemeColors.customOrange(context), fontWeight: FontWeight.w500)),
          ],
        ),
        Expanded(
          child: ListView.builder(
            itemCount: widget.locais.length,
            itemBuilder: (_, index) {
              final local = widget.locais[index];
              final escolhido = index == indexEscolhido;
              return Padding(
                padding: const EdgeInsets.only(left: 10, right: 10),
                child: ListTile(
                  onTap: () {
                    setState(() {
                      indexEscolhido = index;
                    });
                  },
                  title: Padding(
                    padding: const EdgeInsets.only(bottom: 5),
                    child: Row(
                      children: [
                        Icon(
                          escolhido ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                          color: escolhido ? ThemeColors.customGreen(context) : ThemeColors.customGreyLight(context),
                          size: escolhido ? 20 : 14,
                        ),
                        const SizedBox(width: 15),
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              child: Text(local.nome, style: GoogleFonts.roboto(fontSize: 15, color: ThemeColors.customBlack(context), fontWeight: FontWeight.w500)),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            children: [
              // Expanded(
              //   child: ButtonLsCustom(
              //     onPressed: () async {
              //       final responseConfig =
              //           await ConfigDatabase.instance.getConfig();
              //       final fone = responseConfig.foneSac;
              //       launchUrl(Uri.parse('tel://$fone'));
              //     },
              //     text: 'SAC',
              //     colorBackground: Colors.blue,
              //   ),
              // ),
              // const SizedBox(width: 10),
              Expanded(
                flex: 2,
                child: ButtonLsCustom(
                  message: indexEscolhido == null || int.tryParse(_controller.text) == 0 ? 'Selecione o local e o volume' : null,
                  onPressed:
                      indexEscolhido != null && int.tryParse(_controller.text) != 0
                          ? () async {
                            final qtd = int.tryParse(_controller.text);
                            if (qtd != null && qtd <= 0) {
                              _controller.text = '1';
                            }
                            if (indexEscolhido != null) {
                              widget.onConfirm(widget.locais[indexEscolhido!].id, int.tryParse(_controller.text) ?? 1);
                              Navigator.pop(context, widget.locais[indexEscolhido!].id);
                            }
                          }
                          : null,
                  text: 'CONFIRMAR',
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 10),
      ],
    );
  }
}
