#!/bin/bash

# Script para build AAB com flavors
# Uso: ./scripts/build_aab.sh [flavor] [build_type]
# Exemplo: ./scripts/build_aab.sh connect release

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para exibir ajuda
show_help() {
    echo -e "${BLUE}Build AAB com Flavors${NC}"
    echo ""
    echo "Uso: $0 [flavor] [build_type]"
    echo ""
    echo "Flavors disponíveis:"
    echo "  - octalog   (padrão)"
    echo "  - arcargo"
    echo "  - connect"
    echo "  - rondolog"
    echo ""
    echo "Build types:"
    echo "  - release   (padrão)"
    echo "  - debug"
    echo ""
    echo "Exemplos:"
    echo "  $0 connect release"
    echo "  $0 arcargo debug"
    echo "  $0 octalog"
    echo ""
}

# Verificar argumentos
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# Definir flavor (padrão: octalog)
FLAVOR=${1:-octalog}

# Definir build type (padrão: release)
BUILD_TYPE=${2:-release}

# Validar flavor
case $FLAVOR in
    octalog|arcargo|connect|rondolog)
        echo -e "${GREEN}✓ Flavor válido: $FLAVOR${NC}"
        ;;
    *)
        echo -e "${RED}✗ Flavor inválido: $FLAVOR${NC}"
        echo -e "${YELLOW}Flavors válidos: octalog, arcargo, connect, rondolog${NC}"
        exit 1
        ;;
esac

# Validar build type
case $BUILD_TYPE in
    release|debug)
        echo -e "${GREEN}✓ Build type válido: $BUILD_TYPE${NC}"
        ;;
    *)
        echo -e "${RED}✗ Build type inválido: $BUILD_TYPE${NC}"
        echo -e "${YELLOW}Build types válidos: release, debug${NC}"
        exit 1
        ;;
esac

echo ""
echo -e "${BLUE}🚀 Iniciando build AAB...${NC}"
echo -e "${YELLOW}Flavor: $FLAVOR${NC}"
echo -e "${YELLOW}Build Type: $BUILD_TYPE${NC}"
echo ""

# Limpar build anterior
echo -e "${BLUE}🧹 Limpando build anterior...${NC}"
flutter clean

# Obter dependências
echo -e "${BLUE}📦 Obtendo dependências...${NC}"
flutter pub get

# Executar build
echo -e "${BLUE}🔨 Executando build AAB...${NC}"
flutter build appbundle \
    --$BUILD_TYPE \
    --flavor $FLAVOR \
    --dart-define=FLAVOR=$FLAVOR \
    --target-platform android-arm,android-arm64,android-x64

# Verificar se o build foi bem-sucedido
if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}✅ Build AAB concluído com sucesso!${NC}"
    echo ""
    
    # Localizar o arquivo AAB
    AAB_PATH="build/app/outputs/bundle/${FLAVOR}Release/app-${FLAVOR}-release.aab"
    if [ "$BUILD_TYPE" = "debug" ]; then
        AAB_PATH="build/app/outputs/bundle/${FLAVOR}Debug/app-${FLAVOR}-debug.aab"
    fi
    
    if [ -f "$AAB_PATH" ]; then
        echo -e "${GREEN}📱 Arquivo AAB gerado:${NC}"
        echo -e "${BLUE}$AAB_PATH${NC}"
        echo ""
        
        # Exibir informações do arquivo
        echo -e "${YELLOW}📊 Informações do arquivo:${NC}"
        ls -lh "$AAB_PATH"
        echo ""
        
        # Sugerir próximos passos
        echo -e "${YELLOW}🎯 Próximos passos:${NC}"
        echo "1. Teste o AAB em um dispositivo"
        echo "2. Faça upload para o Google Play Console"
        echo "3. Configure o release no Play Console"
    else
        echo -e "${RED}❌ Arquivo AAB não encontrado em: $AAB_PATH${NC}"
        exit 1
    fi
else
    echo ""
    echo -e "${RED}❌ Falha no build AAB${NC}"
    exit 1
fi
