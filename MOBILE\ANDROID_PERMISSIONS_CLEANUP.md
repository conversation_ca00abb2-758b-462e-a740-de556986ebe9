# 🔒 Limpeza de Permissões Android - AndroidManifest.xml

## 🎯 **Objetivo**
<PERSON><PERSON><PERSON><PERSON> as permissões do app removendo permissões desnecessárias para:
- ✅ Melhorar privacidade do usuário
- ✅ Facilitar aprovação na Google Play Store
- ✅ Reduzir alertas de segurança
- ✅ Seguir princípio do menor privilégio

---

## 📊 **Resumo das Mudanças**

| Status | Quantidade | Descrição |
|--------|------------|-----------|
| ❌ **Removidas** | 7 permissões | Permissões desnecessárias comentadas |
| ⚠️ **Para Revisar** | 4 permissões | Permissões questionáveis marcadas |
| ✅ **Mantidas** | 8 permissões | Permissões essenciais preservadas |

---

## ❌ **Permissões REMOVIDAS (Comentadas)**

### 1. **`AD_ID` - Publicidade**
```xml
<!-- ❌ REMOVIDO: Permissão para publicidade - app não usa ads -->
<!-- <uses-permission android:name="com.google.android.gms.permission.AD_ID"/> -->
```
**Motivo:** App de logística não usa publicidade

### 2. **`WAKE_LOCK` - Manter Tela Ligada**
```xml
<!-- ❌ REMOVIDO: Wake lock não é usado explicitamente no código -->
<!-- <uses-permission android:name="android.permission.WAKE_LOCK" /> -->
```
**Motivo:** Não encontrado uso explícito no código

### 3. **`FOREGROUND_SERVICE` - Serviços em Primeiro Plano**
```xml
<!-- ❌ REMOVIDO: Foreground service não é usado -->
<!-- <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> -->
```
**Motivo:** App não implementa foreground services

### 4. **`RECEIVE_BOOT_COMPLETED` - Inicialização do Sistema**
```xml
<!-- ❌ REMOVIDO: Boot completed não é usado -->
<!-- <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> -->
```
**Motivo:** App não precisa iniciar automaticamente

### 5. **`SCHEDULE_EXACT_ALARM` - Alarmes Exatos**
```xml
<!-- ❌ REMOVIDO: Alarmes exatos não são usados -->
<!-- <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" /> -->
```
**Motivo:** App não usa alarmes específicos

### 6. **`CHANGE_NETWORK_STATE` - Alterar Estado da Rede**
```xml
<!-- ❌ REMOVIDO: Mudança de estado da rede não é usada -->
<!-- <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" /> -->
```
**Motivo:** App apenas verifica, não altera estado da rede

### 7. **Armazenamento Legado (3 permissões)**
```xml
<!-- ❌ REMOVIDO: Armazenamento legado - substituído por READ_MEDIA_* -->
<!-- <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> -->
<!-- <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> -->
<!-- <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" tools:ignore="ScopedStorage" /> -->
```
**Motivo:** Android 13+ usa `READ_MEDIA_IMAGES/VIDEO/AUDIO` em vez dessas permissões legadas

### 8. **`ACCESS_NETWORK_STATE` Duplicada**
```xml
<!-- ❌ REMOVIDO: Duplicata da linha anterior -->
<!-- <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> -->
```
**Motivo:** Permissão estava duplicada

---

## ⚠️ **Permissões PARA REVISAR**

### 1. **`READ_PHONE_STATE` - Estado do Telefone**
```xml
<!-- ⚠️ REVISAR: Usado para obter IMEI - considerar usar Android ID -->
<uses-permission android:name="android.permission.READ_PHONE_STATE" />
```
**Uso Atual:** Obtenção de IMEI para identificação do dispositivo
**Recomendação:** Considerar migrar para Android ID (mais privado)

### 2. **Permissões de Áudio (3 permissões)**
```xml
<!-- ⚠️ REVISAR: Áudio para SAC - manter apenas se necessário -->
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
<uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
```
**Uso Atual:** Sistema de chamadas WebRTC no SAC
**Recomendação:** Manter apenas se funcionalidade de áudio no SAC for essencial

---

## ✅ **Permissões MANTIDAS (Essenciais)**

### 🌍 **Localização**
```xml
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
```
**Justificativa:** App de logística precisa de GPS para rastreamento

### 🌐 **Rede**
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```
**Justificativa:** Comunicação com servidor e APIs

### 📷 **Câmera**
```xml
<uses-permission android:name="android.permission.CAMERA" />
<uses-feature android:name="android.hardware.camera" />
<uses-feature android:name="android.hardware.camera.autofocus" />
```
**Justificativa:** Fotos de entrega, documentos, QR codes

### 📱 **Notificações**
```xml
<uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
<uses-permission android:name="android.permission.VIBRATE" />
```
**Justificativa:** Notificações de entregas e alertas

### 💾 **Armazenamento Moderno**
```xml
<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
<uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
```
**Justificativa:** Acesso a fotos e vídeos (Android 13+)

---

## 🔄 **Próximos Passos Recomendados**

### 1. **Testar Funcionalidades**
```bash
# Testar build e funcionalidades principais
flutter build appbundle --release --flavor up360 --dart-define=FLAVOR=up360
```

### 2. **Revisar SAC com Áudio**
- Verificar se funcionalidade de áudio no SAC é realmente necessária
- Se não for essencial, remover permissões de áudio

### 3. **Migrar IMEI para Android ID**
- Substituir uso de IMEI por Android ID
- Remover `READ_PHONE_STATE` após migração

### 4. **Validar com Equipe**
- Confirmar que funcionalidades removidas não são necessárias
- Testar em dispositivos reais

---

## 📈 **Benefícios Alcançados**

✅ **Privacidade:** Redução de 37% nas permissões (19 → 12)
✅ **Segurança:** Remoção de permissões sensíveis desnecessárias
✅ **Play Store:** Menor chance de rejeição por permissões excessivas
✅ **Usuário:** Menos alertas de permissão durante instalação
✅ **Compliance:** Melhor aderência às práticas de privacidade

---

## ⚠️ **Importante**

- **Teste todas as funcionalidades** após as mudanças
- **As permissões comentadas** podem ser facilmente restauradas se necessário
- **Monitore logs** para identificar possíveis problemas
- **Documente** qualquer funcionalidade que pare de funcionar

**🎯 Resultado: App mais seguro, privado e com melhor aprovação na Play Store!**
