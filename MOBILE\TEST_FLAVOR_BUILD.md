# 🧪 Teste de Build com Flavors

## 🎯 **Como Testar a Solução**

### 📋 **1. <PERSON><PERSON> (Recomendado)**

```bash
# Windows
.\scripts\build_aab.ps1 connect release

# Linux/Mac
./scripts/build_aab.sh connect release
```

### 📋 **2. Teste Manual Completo**

```bash
# 1. Limpar projeto
flutter clean

# 2. Obter dependências
flutter pub get

# 3. Build AAB com flavor connect
flutter build appbundle \
    --release \
    --flavor connect \
    --dart-define=FLAVOR=connect \
    --target-platform android-arm,android-arm64,android-x64
```

### 📋 **3. Verificar Resultado**

O arquivo AAB deve ser gerado em:
```
build/app/outputs/bundle/connectRelease/app-connect-release.aab
```

---

## ✅ **Verificações de Sucesso**

### 🔍 **1. Arquivo AAB Gerado**
- ✅ Arquivo existe no caminho correto
- ✅ Tamanho do arquivo > 10MB (aproximadamente)
- ✅ Nome contém o flavor: `app-connect-release.aab`

### 🔍 **2. Application ID Correto**
```bash
# Verificar com aapt (Android Asset Packaging Tool)
aapt dump badging build/app/outputs/bundle/connectRelease/app-connect-release.aab
```

Deve mostrar: `package: name='com.octalog.connect'`

### 🔍 **3. Assets do Flavor Correto**
- ✅ Imagens carregadas de `assets/images/connect/`
- ✅ Logo específico do Connect
- ✅ Cores e temas do Connect

---

## 🐛 **Possíveis Problemas e Soluções**

### ❌ **Erro: "No flavor named 'connect'"**

**Causa:** Flavor não configurado no build.gradle.kts

**Solução:**
```bash
# Verificar se o flavor existe
grep -n "connect" android/app/build.gradle.kts
```

### ❌ **Erro: "FLAVOR_NAME not found"**

**Causa:** BuildConfig não configurado

**Solução:**
Verificar se existe no build.gradle.kts:
```kotlin
buildConfigField("String", "FLAVOR_NAME", "\"connect\"")
```

### ❌ **Assets não carregam**

**Causa:** Pasta de assets não existe

**Solução:**
```bash
# Verificar se a pasta existe
ls -la assets/images/connect/
```

### ❌ **Build falha com erro de signing**

**Causa:** Configuração de assinatura

**Solução:**
```bash
# Build debug primeiro para testar
flutter build appbundle --debug --flavor connect --dart-define=FLAVOR=connect
```

---

## 📊 **Comandos de Diagnóstico**

### 🔍 **Verificar Flavors Disponíveis**
```bash
flutter build appbundle --help | grep -A 10 "flavor"
```

### 🔍 **Verificar Configuração Android**
```bash
cat android/app/build.gradle.kts | grep -A 5 -B 5 "connect"
```

### 🔍 **Verificar Assets**
```bash
find assets/images -name "*connect*" -type d
ls -la assets/images/connect/
```

### 🔍 **Verificar Dart Defines**
```bash
flutter build appbundle --release --flavor connect --dart-define=FLAVOR=connect --verbose
```

---

## 🎯 **Teste de Todos os Flavors**

### 📱 **Script de Teste Completo**

```bash
# Testar todos os flavors
for flavor in octalog arcargo connect rondolog; do
    echo "🧪 Testando flavor: $flavor"
    flutter build appbundle \
        --release \
        --flavor $flavor \
        --dart-define=FLAVOR=$flavor \
        --target-platform android-arm,android-arm64,android-x64
    
    if [ $? -eq 0 ]; then
        echo "✅ $flavor: SUCCESS"
    else
        echo "❌ $flavor: FAILED"
    fi
    echo ""
done
```

---

## 📈 **Resultados Esperados**

### ✅ **Build Bem-sucedido**
```
✅ Build AAB concluído com sucesso!
📱 Arquivo AAB gerado: build/app/outputs/bundle/connectRelease/app-connect-release.aab
📊 Tamanho: ~15-25MB
```

### ✅ **Logs de Sucesso**
```
🎯 Flavor atual: FlavorType.connect
📱 App Name: Connect
🖼️ Assets path: assets/images/connect/
📦 Application ID: com.octalog.connect
```

---

## 🚀 **Próximos Passos Após Build**

1. **Teste Local:**
   ```bash
   # Instalar AAB em dispositivo de teste
   bundletool build-apks --bundle=app-connect-release.aab --output=connect.apks
   bundletool install-apks --apks=connect.apks
   ```

2. **Upload para Play Console:**
   - Fazer upload do AAB
   - Configurar release
   - Testar em internal testing

3. **Verificação Final:**
   - Testar todas as funcionalidades
   - Verificar assets corretos
   - Confirmar branding específico do flavor
