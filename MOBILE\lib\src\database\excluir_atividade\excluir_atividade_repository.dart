import 'package:flutter/cupertino.dart';
import 'package:octalog/src/models_new/endereco_new.dart';

import '../../helpers/web_connector.dart';

class DeleteAtividade {
  Future<EnderecoNew> delete(int idEnderecoCliente) async {
    final responseStatus = await WebConnector().delete(
      '/entrega/excluir?IDOS=$idEnderecoCliente',
    );
    if (responseStatus.statusCode == 200) {
      final deleteMeuId = EnderecoNew.fromJson(responseStatus.data);
      debugPrint('deleteMeuId: $deleteMeuId');
      return deleteMeuId;
    } else {
      throw Exception('Failed to load delete');
    }
  }
}
