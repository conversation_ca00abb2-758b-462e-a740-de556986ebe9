// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/models_new/endereco_new.dart';
import 'package:octalog/src/pages/entrega_newpages/controller/entrega_new_store.dart';
import 'package:octalog/src/pages/home/<USER>/show_modal_home.dart';
import 'package:octalog/src/pages/home/<USER>';
import 'package:octalog/src/utils/extesion.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../../components/mensagem_widget/mensagem_widget.dart';
import '../../../components/reentrega_widget/reentrega_widget.dart';
import '../../../models/id_status_atividade_enum.dart';
import '../../../utils/theme_colors.dart';
import '../../entrega_newpages/entrega_conclusao/entrega_conclusao_new.dart';
import '../../entrega_newpages/entrega_new_page.dart';

class CustomListviewHome extends StatelessWidget {
  final HomeController controller;
  final EnderecoNew? atividadeOffline;
  final Position? position;

  const CustomListviewHome(
      {super.key,
      required this.controller,
      required this.atividadeOffline,
      this.position});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: controller.state,
      builder: (_, state, __) {
        bool pesquisa = state.search.isNotEmpty;
        final data = state.atividadesFiltradas;

        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: (pesquisa ? data.length + 1 : data.length) + 1,
          itemBuilder: (_, index) {
            final l = (pesquisa ? data.length + 1 : data.length);
            if (index == l) {
              return const SizedBox(
                height: 100,
              );
            }
            if (pesquisa && index == 0) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        'Resultado da pesquisa: ${state.search}',
                        style: GoogleFonts.roboto(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: ThemeColors.customGrey(context),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        controller.setSearch('');
                      },
                      child: Text(
                        'Limpar pesquisa',
                        style: GoogleFonts.roboto(
                          color: Colors.red,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }
            EnderecoNew atividade =
                data.elementAt(pesquisa ? index - 1 : index);

            EntregaNewStore entregaNewStore = EntregaNewStore(atividade);
            if ([
              IdStatusAtividadeEnum.entregue,
              IdStatusAtividadeEnum.cancelada,
            ].contains(
              state.statusAtividade,
            )) {
              atividade = atividade.whereStatus(state.statusAtividade);
            } else {
              atividade = atividade.apenasEntrega;
            }
            final entregaOrd = atividade.entrega;

            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GestureDetector(
                  onLongPress: () {
                    _mostrarEndereco(
                        context, atividade, controller, entregaNewStore);
                  },
                  child: InkWell(
                    onTap: () async {
                      if ([
                        IdStatusAtividadeEnum.entregue,
                        IdStatusAtividadeEnum.cancelada,
                      ].contains(
                        state.statusAtividade,
                      )) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (_) => EntregasConclusaoNew(
                              store: entregaNewStore,
                              idStatusAtividadeEnum: state.statusAtividade,
                              position: position,
                            ),
                          ),
                        );
                      } else {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => EntregaNewPage(
                              atividade: atividade,
                            ),
                          ),
                        );
                      }
                    },
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              if (entregaOrd != null)
                                SizedBox(
                                    child: data.length > 100
                                        ? Text(
                                            entregaOrd.padLeft(3) == '000'
                                                ? ''
                                                : entregaOrd.padLeft(3),
                                            style: GoogleFonts.roboto(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w600,
                                                color:
                                                    ThemeColors.customOrange(context)),
                                            overflow: TextOverflow.ellipsis,
                                            maxLines: 3,
                                          )
                                        : Text(
                                            entregaOrd.padLeft(2) == '00'
                                                ? ''
                                                : entregaOrd.padLeft(2),
                                            style: GoogleFonts.roboto(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w600,
                                                color:
                                                    ThemeColors.customOrange(context)),
                                            overflow: TextOverflow.ellipsis,
                                            maxLines: 3,
                                          )),
                              if (entregaOrd != null &&
                                  entregaOrd.padLeft(3) != '000' &&
                                  entregaOrd.padLeft(2) != '00')
                                const VerticalDivider(),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      atividade.enderecoFormatado,
                                      style: GoogleFonts.roboto(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w600,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 3,
                                    ),
                                    const SizedBox(
                                      height: 5,
                                    ),
                                    if (atividade.status.contains(
                                            IdStatusAtividadeEnum.negativa) ||
                                        atividade.status.contains(
                                            IdStatusAtividadeEnum.cancelada) ||
                                        atividade.status.contains(
                                            IdStatusAtividadeEnum.entregue))
                                      Text(
                                        atividade.statusString.join(', '),
                                        style: GoogleFonts.roboto(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                          color: ThemeColors.customOrange(context),
                                        ),
                                      ),
                                    if (atividade.clientes.length > 1)
                                      Text(
                                        '${atividade.clientes.length} Pedidos',
                                        style: GoogleFonts.roboto(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w500,
                                          color: const Color.fromRGBO(
                                              150, 150, 150, 1),
                                        ),
                                      ),
                                    atividade.codRastreioList.length > 3
                                        ? Column(
                                            children: [
                                              Text(
                                                atividade.codRastreioList
                                                    .join(', '),
                                                style: GoogleFonts.roboto(
                                                  fontSize: 13,
                                                  fontWeight: FontWeight.w500,
                                                  color: const Color.fromRGBO(
                                                      150, 150, 150, 1),
                                                ),
                                              ),
                                            ],
                                          )
                                        : Text(
                                            atividade.codRastreioList
                                                .join(', '),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                            style: GoogleFonts.roboto(
                                              fontSize: 13,
                                              fontWeight: FontWeight.w500,
                                              color: const Color.fromRGBO(
                                                  150, 150, 150, 1),
                                            ),
                                          ),
                                    const SizedBox(
                                      height: 5,
                                    ),
                                    MensagemWidget(
                                      mensagens: atividade.mensagens,
                                    ),
                                    ReentregaWidget(
                                      tags: atividade.tags,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 5),
                        ],
                      ),
                    ),
                  ),
                ),
                if ((pesquisa ? data.length : data.length - 1) != index)
                   Divider(
                    color: ThemeColors.customGrey(context),
                    thickness: 1,
                  ),
              ],
            );
          },
        );
      },
    );
  }

  _mostrarEndereco(
    context,
    EnderecoNew atividade,
    HomeController controller,
    EntregaNewStore entregaNewStore,
  ) async {
    try {
      showModalBottomSheet(
          backgroundColor: Colors.white,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(30),
              topRight: Radius.circular(30),
            ),
          ),
          isDismissible: true,
          isScrollControlled: true,
          context: context,
          builder: (context) => ShowModalHome(
                atividade: atividade,
                controller: controller,
                storeEntrega: entregaNewStore,
              ));
    } catch (_) {}
  }
}
