import 'package:flutter/cupertino.dart';
import 'package:octalog/src/helpers/login/login.dart';

import 'offline_helper_state.dart';

class OfflineStore {
  final ValueNotifier<OfflineState> offlineState =
      ValueNotifier(OfflineState());

  void setOffline(bool value) {
    if (Login.instance.usuarioLogado?.loginMocked == true) {
      offlineState.value = OfflineState(isOffline: false);
      return;
    }
    offlineState.value = OfflineState(isOffline: value);
  }
}
