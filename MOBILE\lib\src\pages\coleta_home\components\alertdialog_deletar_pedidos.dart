import 'package:flutter/material.dart';

import '../model/pedido_coleta_despachar_model.dart';

class Deletarpedidos extends StatefulWidget {
  final List<PedidoColetaDespachar> pedidosCliente;

  const Deletarpedidos({super.key, required this.pedidosCliente});

  @override
  State<Deletarpedidos> createState() => _DeletarpedidosState();
}

class _DeletarpedidosState extends State<Deletarpedidos> {
  List<PedidoColetaDespachar> pedidosdeletados = [];
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Center(child: Text('Pedidos')),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.5,
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: widget.pedidosCliente.length,
          itemBuilder: (context, index) {
            return Row(
              children: [
                Checkbox(
                  activeColor: Colors.green,
                  value:
                      pedidosdeletados.contains(widget.pedidosCliente[index]),
                  onChanged: (value) {
                    setState(() {
                      if (value == true) {
                        pedidosdeletados.add(widget.pedidosCliente[index]);
                      } else {
                        pedidosdeletados.remove(widget.pedidosCliente[index]);
                      }
                    });
                  },
                ),
                Text(widget.pedidosCliente[index].os),
              ],
            );
          },
        ),
      ),
      actions: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          mainAxisSize: MainAxisSize.max,
          children: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text('CANCELAR',
                  style: TextStyle(color: Colors.grey.shade800)),
            ),
            TextButton(
              onPressed: pedidosdeletados.isEmpty
                  ? null
                  : () {
                      Navigator.pop(context, pedidosdeletados);
                    },
              child:
                  const Text('DELETAR', style: TextStyle(color: Colors.orange)),
            ),
          ],
        ),
      ],
    );
  }
}
