import 'package:flutter/material.dart';

import '../components/buttom_ls/button_ls_custom.dart';
import '../helpers/gps/gps_contract.dart';

class VerifyGps extends StatefulWidget {
  const VerifyGps({super.key});

  @override
  State<VerifyGps> createState() => _VerifyGpsState();
}

class _VerifyGpsState extends State<VerifyGps> {
  bool isLoad = false;
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          title: const Text("GPS desativado"),
          centerTitle: true,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Feche o aplicativo, ative o GPS do aparelho e tente novamente!',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        floatingActionButton: Padding(
          padding: const EdgeInsets.only(
            left: 42,
            right: 20,
          ),
          child: ButtonLsCustom(
            text: "JÁ CORRIGI",
            isLoading: isLoad,
            onPressed: () async {
              setState(() => isLoad = true);
              final gps = await GpsHelperContract.instance.checaGpsLigado();
              if (gps) {
                if (Navigator.of(context).canPop()) {
                  Navigator.of(context).pop();
                }
              } else {
                await Future.delayed(
                  const Duration(seconds: 1),
                  () => setState(
                    () => isLoad = false,
                  ),
                );
              }
            },
          ),
        ),
      ),
    );
  }
}
