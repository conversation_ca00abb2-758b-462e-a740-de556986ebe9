import 'package:dotted_decoration/dotted_decoration.dart';
import 'package:flutter/material.dart';
import 'package:octalog/src/utils/colors.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import './../../../components/fcm_alert_dailog/fcm_alert_dialog_state.dart';
import './../../../components/fcm_alert_dailog/fcm_alert_dialog_store.dart';

class CardPedidoCustom extends StatefulWidget {
  final FcmAlertDialogStore store;
  final String nomeCliente;
  final String endereco;
  const CardPedidoCustom({
    super.key,
    required this.store,
    required this.nomeCliente,
    required this.endereco,
  });

  @override
  State<CardPedidoCustom> createState() => _CardPedidoCustomState();
}

class _CardPedidoCustomState extends State<CardPedidoCustom> {
  bool _isExpanded = true;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<FcmAlertState>(
      valueListenable: widget.store.state,
      builder: (context, state, child) {
        int total = state.pedidosTotaisCliente(widget.nomeCliente).length;
        int coletados = state.coletados
            .where((element) => element.cliente == widget.nomeCliente)
            .length;
        bool iscompleto = coletados == total;
        bool isparcial = coletados > 0 && coletados < total;

        bool checkgeral = state.pedidosTotaisCliente(widget.nomeCliente).every(
              (element) => state.coletados
                  .any((element2) => element2.idOs == element.idOs),
            );

        return Container(
          decoration: DottedDecoration(
            shape: Shape.box,
            color: isparcial
                ? const Color.fromARGB(0, 29, 29, 29)
                : iscompleto
                    ? const Color.fromARGB(0, 255, 209, 186)
                    : const Color.fromARGB(242, 201, 201, 201),
            borderRadius: BorderRadius.circular(5),
            strokeWidth: 2,
          ),
          child: Container(
            decoration: BoxDecoration(
              color: isparcial
                  ? const Color.fromARGB(255, 228, 228, 228)
                  : iscompleto
                      ? ThemeColors.customBlue(context)
                      : const Color.fromARGB(117, 240, 240, 240),
              borderRadius: BorderRadius.circular(5),
              border: Border.all(
                color: isparcial
                    ? const Color.fromARGB(255, 197, 197, 197)
                    : iscompleto
                        ? ThemeColors.customOrange(context)
                        : const Color.fromARGB(117, 240, 240, 240),
                width: 2,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.only(
                top: 10,
                left: 12,
                right: 14,
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () async {
                            if (widget.nomeCliente == 'Sem informações') {
                              widget.store
                                  .deletarTodosPedidos(widget.nomeCliente);
                            }
                            if (checkgeral) {
                              widget.store
                                  .removerTodosPedidos(widget.nomeCliente);
                            } else {
                              await widget.store
                                  .selecionarTodosPedidos(widget.nomeCliente);
                              setState(() {
                                _isExpanded = false;
                              });
                            }
                          },
                          child: Text(
                            widget.nomeCliente,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style:  TextStyle(
                              fontSize: 16.0,
                              fontWeight: FontWeight.w700,
                              color: ThemeColors.customOrange(context),
                            ),
                          ),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 1,
                        ),
                        height: 20,
                        decoration: BoxDecoration(
                          color: isparcial
                              ? const Color.fromARGB(255, 207, 207, 207)
                              : iscompleto
                                  ? const Color.fromARGB(255, 255, 206, 182)
                                  : const Color.fromARGB(255, 226, 226, 226),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Center(
                          child: Text(
                            '$coletados de $total pedidos',
                            style: const TextStyle(
                              fontSize: 11,
                              color: Color(0xFF757474),
                              fontWeight: FontWeight.w800,
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: SizedBox(
                          height: 30,
                          child: GestureDetector(
                            onTap: () async {
                              if (widget.nomeCliente == 'Sem informações') {
                                widget.store
                                    .deletarTodosPedidos(widget.nomeCliente);
                              }
                              if (checkgeral) {
                                widget.store
                                    .removerTodosPedidos(widget.nomeCliente);
                              } else {
                                await widget.store
                                    .selecionarTodosPedidos(widget.nomeCliente);
                                setState(() {
                                  _isExpanded = false;
                                });
                              }
                            },
                            child: Text(
                              widget.endereco,
                              maxLines: 2,
                              overflow: TextOverflow.visible,
                              style: const TextStyle(
                                fontSize: 14,
                                letterSpacing: 0.5,
                                color: Color(0xFF534845),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                _isExpanded = !_isExpanded;
                              });
                            },
                            child: Container(
                              alignment: Alignment.topCenter,
                              width: MediaQuery.of(context).size.width * 0.23,
                              height: 30,
                              child: Icon(
                                _isExpanded
                                    ? Icons.keyboard_arrow_up
                                    : Icons.keyboard_arrow_down,
                                color: _isExpanded
                                    ? ThemeColors.customGrey(context)
                                    : ThemeColors.customOrange(context),
                                size: 30,
                              ),
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                  if (_isExpanded) ...[
                    const SizedBox(
                      width: double.infinity,
                      child: Divider(
                        color: ThemeColors.customGrey(context),
                        thickness: 0.5,
                      ),
                    ),
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount:
                          state.pedidosTotaisCliente(widget.nomeCliente).length,
                      itemBuilder: (context, index) {
                        final pedido = state
                            .pedidosTotaisCliente(widget.nomeCliente)[index];

                        bool coletadoCheck = state.coletados
                            .any((element) => element.idOs == pedido.idOs);

                        return Column(
                          children: [
                            if (index == 0 &&
                                state
                                        .pedidosTotaisCliente(
                                            widget.nomeCliente)
                                        .length >
                                    1) ...[
                              GestureDetector(
                                onTap: () async {
                                  if (widget.nomeCliente == 'Sem informações') {
                                    widget.store.deletarTodosPedidos(
                                        widget.nomeCliente);
                                  }
                                  if (checkgeral) {
                                    widget.store.removerTodosPedidos(
                                        widget.nomeCliente);
                                  } else {
                                    await widget.store.selecionarTodosPedidos(
                                        widget.nomeCliente);
                                    setState(() {
                                      _isExpanded = false;
                                    });
                                  }
                                },
                                child: SizedBox(
                                  height: 30,
                                  child: Row(
                                    children: [
                                      SizedBox(
                                        width: 30,
                                        child: widget.nomeCliente ==
                                                'Sem informações'
                                            ? const Icon(
                                                Icons.delete,
                                                color: Color.fromARGB(
                                                    255, 247, 91, 125),
                                                size: 23,
                                              )
                                            : Icon(
                                                checkgeral
                                                    ? Icons.check_box
                                                    : Icons
                                                        .check_box_outline_blank,
                                                color: checkgeral
                                                    ? ThemeColors.customOrange(context)
                                                    : const Color.fromARGB(
                                                        255, 172, 172, 172),
                                                size: 23,
                                              ),
                                      ),
                                      GestureDetector(
                                        onTap: () async {
                                          if (widget.nomeCliente ==
                                              'Sem informações') {
                                            widget.store.deletarTodosPedidos(
                                                widget.nomeCliente);
                                          }
                                          if (checkgeral) {
                                            widget.store.removerTodosPedidos(
                                                widget.nomeCliente);
                                          } else {
                                            await widget.store
                                                .selecionarTodosPedidos(
                                                    widget.nomeCliente);
                                            setState(() {
                                              _isExpanded = false;
                                            });
                                          }
                                        },
                                        child: Text(
                                          widget.nomeCliente ==
                                                  'Sem informações'
                                              ? 'Excluir todos'
                                              : 'Selecionar todos',
                                          style: const TextStyle(
                                            fontSize: 15,
                                            letterSpacing: 0.5,
                                            color: Color(0xFF534845),
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                            Container(
                              alignment: Alignment.center,
                              height: 30,
                              child: GestureDetector(
                                onTap: () {
                                  if (widget.nomeCliente == 'Sem informações') {
                                    widget.store.deletarPedidoCodigo(
                                        state.pedidosTotaisCliente(
                                            widget.nomeCliente)[index]);
                                  } else if (coletadoCheck) {
                                    widget.store.removeIDOS(pedido.idOs);
                                  } else {
                                    widget.store.addIDOS(pedido.idOs);
                                  }
                                },
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Container(
                                      alignment: Alignment.center,
                                      width: 30,
                                      height: 30,
                                      child: widget.nomeCliente ==
                                              'Sem informações'
                                          ? GestureDetector(
                                              onTap: () {
                                                widget.store.deletarPedidoCodigo(
                                                    state.pedidosTotaisCliente(
                                                            widget.nomeCliente)[
                                                        index]);
                                              },
                                              child: const Icon(
                                                Icons.delete,
                                                color: Color.fromARGB(
                                                    255, 247, 91, 125),
                                                size: 23,
                                              ),
                                            )
                                          : Icon(
                                              coletadoCheck
                                                  ? Icons.check_box
                                                  : Icons
                                                      .check_box_outline_blank,
                                              color: coletadoCheck
                                                  ? ThemeColors.customOrange(context)
                                                  : const Color.fromARGB(
                                                      255, 172, 172, 172),
                                              size: 23,
                                            ),
                                    ),
                                    Text(
                                      pedido.os,
                                      style: const TextStyle(
                                        fontSize: 15,
                                        letterSpacing: 0.5,
                                        color: Color(0xFF534845),
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    )
                  ],
                  const SizedBox(
                    height: 5,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
