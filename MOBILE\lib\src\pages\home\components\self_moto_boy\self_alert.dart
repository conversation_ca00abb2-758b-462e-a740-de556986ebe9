import 'package:flutter/material.dart';
import 'package:octalog/src/components/buttom_ls/button_ls_custom.dart';
import 'package:octalog/src/models/selfie_agente.dart';
import 'package:octalog/src/pages/home/<USER>/self_moto_boy/self_widget.dart';

class SelfAlert extends StatelessWidget {
  final SelfieAgente selfie;
  const SelfAlert({super.key, required this.selfie});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Selfie'),
          centerTitle: true,
          automaticallyImplyLeading: false,
        ),
        body: SizedBox(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          child: const Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: Center(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20),
                    child: Text(
                      'Por motivos de segurança, gostaríamos de solicitar que você tire uma selfie antes de continuar trabalhando.',
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 80,
              ),
            ],
          ),
        ),
        floatingActionButton: Padding(
          padding: const EdgeInsets.only(left: 30),
          child: ButtonLsCustom(
            text: 'Tirar Selfie',
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => SelfWidget(selfie: selfie),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
