import 'package:octalog/src/models_new/endereco_new.dart';

import '../../helpers/login/login.dart';
import '../../helpers/web_connector.dart';

Future<List<EnderecoNew>> getAtividadeEndereco() async {
  var response = await WebConnector().get(
    '/atividades/buscar',
    headers: {
      'token': Login.instance.usuarioLogado?.token ?? '',
    },
  );
  if (response.statusCode == 200) {
    return (response.data as List)
        .map((meuEndereco) => EnderecoNew.fromJson(meuEndereco))
        .toList();
  } else {
    throw Exception('Failed to load get');
  }
}
