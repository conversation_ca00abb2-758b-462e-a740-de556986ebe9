import 'package:flutter/material.dart';

class WidgetExtratoCard extends StatelessWidget {
  const WidgetExtratoCard({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 100,
      width: MediaQuery.of(context).size.width,
      child: const Column(
        children: [
          CircleAvatar(
            backgroundColor: Colors.red,
            child: Text('A'),
          )
        ],
      ),
    );
  }
}
