import 'dart:async';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/components/buttom_ls/button_ls_custom.dart';
import 'package:octalog/src/helpers/web_connector.dart';
import 'package:speech_to_text/speech_recognition_result.dart';
import 'package:speech_to_text/speech_to_text.dart';

import '../../../components/icon_return.dart';
import '../../../components/loading_ls/loading_ls.dart';
import '../sac_page.state.dart';
import '../sac_page_store.dart';
import '../../../utils/theme_colors.dart';

class MensagemSacPage extends StatefulWidget {
  final SacPageStore store;

  const MensagemSacPage({
    super.key,
    required this.store,
  });

  @override
  State<MensagemSacPage> createState() => _MensagemSacPageState();
}

class _MensagemSacPageState extends State<MensagemSacPage> {
  final controller = TextEditingController();
  final SpeechToText _speechToText = SpeechToText();
  bool isListening = false;
  bool _buttonVisible = false;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _initSpeech();
  }

  void _initSpeech() async {
    isListening = await _speechToText.initialize();
    setState(() {});
  }

  void _toggleListening() {
    if (_speechToText.isNotListening) {
      _startListening();
    } else {
      _stopListening();
    }
  }

  void _startListening() async {
    await _speechToText.listen(
      onResult: _onSpeechResult,
      localeId: 'pt_BR',
    );
    setState(() {
      isListening = true;
      _startBlinking();
    });
  }

  void _stopListening() async {
    await _speechToText.stop();
    setState(() {
      isListening = false;
      _stopBlinking();
    });
  }

  void _onSpeechResult(SpeechRecognitionResult result) {
    setState(() {
      controller.text = result.recognizedWords;
      controller.selection = TextSelection.fromPosition(
        TextPosition(offset: controller.text.length),
      );
    });
  }

  void _startBlinking() {
    _timer ??= Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (_speechToText.isNotListening) {
        _stopBlinking();
      }

      setState(() {
        _buttonVisible = !_buttonVisible;
      });
    });
  }

  void _stopBlinking() {
    _timer?.cancel();
    _timer = null;
    setState(() {
      _buttonVisible = true;
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<SacPageState>(
      valueListenable: widget.store.state,
      builder: (_, SacPageState value, __) {
        if (value.isLoadingLocalizacao) {
          return const Center(
            child: LoadingLs(),
          );
        }
        if (value.isLoadingSacEmAberto) {
          return const Center(
            child: LoadingLs(),
          );
        }
        return Scaffold(
          body: SingleChildScrollView(
            reverse: true,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(width: 10),
                      GestureDetector(
                        child: const IconPadrao(
                          isBack: true,
                        ),
                        onTap: () {
                          Navigator.pop(context, null);
                        },
                      ),
                      Expanded(
                        child: Text(
                          "Sac Atendimento",
                          textAlign: TextAlign.center,
                          softWrap: true,
                          style: GoogleFonts.roboto(
                            fontSize: 20,
                            fontWeight: FontWeight.w500,
                            color: const Color.fromARGB(255, 0, 0, 0),
                          ),
                        ),
                      ),
                      const SizedBox(width: 60),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 15,
                  ),
                  child: Stack(
                    children: [
                      Image.asset(
                        'assets/sac/atendete.png',
                        height: MediaQuery.of(context).size.height * 0.3,
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                  ),
                  child: Text(
                    'Por favor, deixe uma breve mensagem para agilizar seu atendimento. Você será encaminhado para a fila de espera.',
                    textAlign: TextAlign.center,
                    style: GoogleFonts.roboto(
                        fontSize: 20,
                        fontWeight: FontWeight.w500,
                        color: const Color.fromARGB(255, 0, 0, 0)),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 15,
                    vertical: 15,
                  ),
                  child: Visibility(
                    visible: value.exibirBotaoEsperarSAC,
                    child: GestureDetector(
                      onTap: () async {
                        final foto = await WebConnector().tirarFoto(context);
                        if (foto == null) {
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: const Text('Atenção'),
                              content: const Text(
                                  'Não e possivel desativar a opção de aguardar o atendimento sem tirar uma foto.'),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  child: const Text('OK'),
                                ),
                              ],
                            ),
                          );
                          return;
                        }
                        widget.store.upLoadFotoSac(foto);
                        widget.store.setAgenteDesejaAguardarSAC();
                      },
                      child: Row(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: value.agenteDesejaAguardarSAC
                                  ? ThemeColors.customOrange(context)
                                  : Colors.transparent,
                              border: Border.all(
                                color: ThemeColors.customOrange(context),
                              ),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            width: 24,
                            height: 24,
                            child: value.agenteDesejaAguardarSAC
                                ? Icon(
                                    Icons.check,
                                    color: ThemeColors.customWhite(context),
                                    size: 20,
                                  )
                                : null,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Abrir o chamado e seguir com as entregas.',
                            textAlign: TextAlign.center,
                            style: GoogleFonts.roboto(
                              fontSize: 17,
                              fontWeight: FontWeight.w500,
                              color: const Color.fromARGB(255, 0, 0, 0),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Container(
                  width: MediaQuery.of(context).size.width,
                  padding: const EdgeInsets.all(10),
                  child: TextField(
                    controller: controller,
                    maxLength: 500,
                    maxLines: 2,
                    onChanged: (value) {
                      widget.store.setMensagem(value);
                    },
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey),
                        borderRadius: BorderRadius.all(
                          Radius.circular(10),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide:
                            BorderSide(color: ThemeColors.customOrange(context)),
                        borderRadius: BorderRadius.all(
                          Radius.circular(10),
                        ),
                      ),
                      hintText: 'Digite sua mensagem',
                      hintStyle: TextStyle(
                        color: ThemeColors.customOrange(context),
                      ),
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: _toggleListening,
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 500),
                    height: 60,
                    width: 60,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(50),
                      color: _buttonVisible
                          ? ThemeColors.customOrange(context).withOpacity(0.1)
                          : ThemeColors.customOrange(context),
                    ),
                    child: Icon(
                      _speechToText.isNotListening ? Icons.mic_off : Icons.mic,
                      color: _speechToText.isNotListening
                          ? ThemeColors.customWhite(context)
                          : ThemeColors.customBlack(context),
                    ),
                  ),
                ),
                const SizedBox(height: 80),
              ],
            ),
          ),
          floatingActionButton: ValueListenableBuilder<SacPageState>(
            valueListenable: widget.store.state,
            builder: (_, SacPageState value, __) {
              String? mensagem;
              if (controller.text.isEmpty) {
                mensagem = 'Por favor, digite uma mensagem para continuar';
              }
              return Padding(
                padding: EdgeInsets.only(
                  left: MediaQuery.of(context).size.width * 0.08,
                ),
                child: SizedBox(
                  width: MediaQuery.of(context).size.width,
                  height: 50,
                  child: ButtonLsCustom(
                    text: 'CONTINUAR',
                    message: mensagem,
                    isLoading: value.isBotaoMensagem,
                    onPressed: () {
                      widget.store.setMensagem(controller.text);
                      widget.store.abrirSacAtendimento();
                    },
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}
