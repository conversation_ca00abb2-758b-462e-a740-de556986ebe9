import 'package:flutter/material.dart';
import 'package:octalog/src/components/loading_ls/loading_ls.dart';
import 'package:octalog/src/pages/cadastro/cadastro_store.dart';

import '../../../components/text_field_ls/text_field_ls_custom.dart';
import '../cadastro_state.dart';

class DadosBancariosPage extends StatefulWidget {
  final CadastroStore store;
  const DadosBancariosPage({super.key, required this.store});

  @override
  State<DadosBancariosPage> createState() => _DadosBancariosPageState();
}

class _DadosBancariosPageState extends State<DadosBancariosPage> {
  final TextEditingController banco = TextEditingController();
  final TextEditingController agencia = TextEditingController();
  final TextEditingController conta = TextEditingController();
  final TextEditingController titularConta = TextEditingController();
  final TextEditingController titularPix = TextEditingController();
  final TextEditingController pixtext = TextEditingController();

  @override
  void initState() {
    super.initState();

    init();
  }

  void init() {
    final state = widget.store.state.value;
    banco.text = state.contratoModel?.banco ?? '';
    agencia.text = state.contratoModel?.agencia ?? '';
    conta.text = state.contratoModel?.conta ?? '';
    titularConta.text = state.contratoModel?.titularConta ?? '';
    titularPix.text = state.contratoModel?.titularPix ?? '';
    pixtext.text = state.contratoModel?.pix ?? '';

    if (state.contratoModel?.conta != null &&
        state.contratoModel?.conta != '') {
      widget.store.setContaCorrente(true);
    } else {
      widget.store.setContaCorrente(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<CadastroState>(
      valueListenable: widget.store.state,
      builder: (BuildContext context, state, _) {
        final store = widget.store;
        return state.isLoadingBuscaCep
            ? const Center(
                child: LoadingLs(),
              )
            : SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              GestureDetector(
                                onTap: () {
                                  store.setContaCorrente(true);
                                },
                                child: state.contaCorrente
                                    ? const Icon(Icons.check_box,
                                        color: Colors.orange)
                                    : const Icon(Icons.check_box_outline_blank),
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              const Text('Conta Bancária'),
                            ],
                          ),
                          Row(
                            children: [
                              GestureDetector(
                                onTap: () {
                                  store.setContaCorrente(false);
                                },
                                child: !state.contaCorrente
                                    ? const Icon(Icons.check_box,
                                        color: Colors.orange)
                                    : const Icon(Icons.check_box_outline_blank),
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              const Text('PIX'),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Visibility(
                      visible: state.contaCorrente,
                      replacement: Column(
                        children: [
                          space(),
                          space(),
                          TextFieldLsCustom(
                            labelText: 'Titular do pix ',
                            isError: false,
                            textInputAction: TextInputAction.next,
                            controller: titularPix,
                            maxCaracteres: 50,
                            onChanged: (value) {
                              store.setContratoModelParte(titularPix: value);
                            },
                          ),
                          space(),
                          TextFieldLsCustom(
                            labelText: 'PIX',
                            isError: false,
                            textInputAction: TextInputAction.next,
                            controller: pixtext,
                            maxCaracteres: 50,
                            onChanged: (value) {
                              store.setContratoModelParte(pix: value);
                            },
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          space(),
                          TextFieldLsCustom(
                            labelText: 'Banco',
                            isError: false,
                            textInputAction: TextInputAction.next,
                            controller: banco,
                            maxCaracteres: 50,
                            onChanged: (value) {
                              store.setContratoModelParte(banco: value);
                            },
                          ),
                          space(),
                          TextFieldLsCustom(
                            labelText: 'Agência',
                            keyboardType: TextInputType.number,
                            isError: false,
                            textInputAction: TextInputAction.next,
                            controller: agencia,
                            maxCaracteres: 10,
                            onChanged: (value) {
                              store.setContratoModelParte(agencia: value);
                            },
                          ),
                          space(),
                          TextFieldLsCustom(
                            labelText: 'Conta Bancária',
                            keyboardType: TextInputType.number,
                            isError: false,
                            textInputAction: TextInputAction.next,
                            controller: conta,
                            maxCaracteres: 20,
                            onChanged: (value) {
                              store.setContratoModelParte(conta: value);
                            },
                          ),
                          space(),
                          TextFieldLsCustom(
                            labelText: 'Titular da Conta',
                            isError: false,
                            textInputAction: TextInputAction.next,
                            controller: titularConta,
                            maxCaracteres: 100,
                            onChanged: (value) {
                              store.setContratoModelParte(titularConta: value);
                            },
                          ),
                          space(),
                          space(),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: MediaQuery.of(context).size.height * 0.3,
                    )
                  ],
                ),
              );
      },
    );
  }

  Widget space() {
    return const SizedBox(
      height: 10,
    );
  }
}
