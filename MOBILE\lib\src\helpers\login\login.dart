import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:octalog/one_signal_wrapper.dart';
import 'package:octalog/src/components/image_perfil/image_perfil.dart';
import 'package:octalog/src/config/flavor_helper.dart';
import 'package:octalog/src/database/status_atividade/status_atividade_database.dart';
import 'package:octalog/src/helpers/gps/gps_position.dart';
import 'package:octalog/src/helpers/login/login_hive.dart';
import 'package:octalog/src/models/user.dart';
import 'package:map_fields/map_fields.dart';
import 'package:octalog/src/utils/offline_helper.dart';

import '../../../errors.dart';
import '../../components/fcm_alert_dailog/fcm_external/fcm_external_login.dart';
import '../../database/config_blob/config_database.dart';
import '../../database/log_database/log_database.dart';
import '../../pages/cadastro/data/hive_contrato.dart';
import '../../pages/cadastro/model/contrato_model.dart';
import '../../pages/home/<USER>';
import '../../utils/info_device_global.dart';
import '../../utils/versao.dart';
import '../web_connector.dart';

class Login {
  static final instance = Login();
  bool usuarioInativo = false;
  String mensagem = '';
  UserData? _usuarioLogado;
  UserData? get usuarioLogado => _usuarioLogado;

  void setMensagem(String msg) {
    mensagem = msg;
  }

  String get _iniciais {
    if (_usuarioLogado == null) return 'SN';
    final nomes = usuarioLogado!.nomeCompleto..toUpperCase().replaceAll('  ', ' ').trim().split(' ').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
    if (nomes.length == 1) return nomes[0][0];

    return '${nomes[0][0]}${nomes[1][0]}';
  }

  ImagePerfil fotoUsuarioLogado(double fontSize, [bool offlineEnable = false]) =>
      ImagePerfil(iniciais: _iniciais, url: usuarioLogado?.foto ?? '', fontSize: fontSize, offlineEnable: offlineEnable);

  void setUsuarioLogado(UserData newUser) {
    _usuarioLogado = newUser;
  }

  Future<UserData?> login(String usuario, String senha) async {
    try {
      final fcmKey = FmcExternalLogin.fcmKey;
      WebConnector connector = WebConnector();
      usuarioInativo = false;
      mensagem = '';
      final gpsHelper = GpsHelper.instance;
      final latlog = await gpsHelper.updateAndGetLastPosition();
      final responseLogin = await connector.post(
        '/agente/login',
        body: {
          'login': usuario,
          'senha': senha,
          'nome': InfoDeviceData.instance.getDeviceInfoStatic().name,
          'modelo': InfoDeviceData.instance.getDeviceInfoStatic().modelo,
          'nomeSistema': InfoDeviceData.instance.getDeviceInfoStatic().systemName,
          'versaoSistema': InfoDeviceData.instance.getDeviceInfoStatic().systemVersion,
          'tela': InfoDeviceData.instance.getDeviceInfoStatic().tela,
          'fcmKey': fcmKey,
          'imei': await WebConnector().getImei(),
          'APPVersao': VersaoApp.string,
          'APP': FlavorHelper.getCurrentFlavor().name,
          'plataforma': Platform.isIOS ? 'IOS' : 'ANDROID',
          'latitude': latlog?.latitude,
          'longitude': latlog?.longitude,
        },
      );
      final data = MapFields.load(responseLogin.data);
      final status = data.getString('status', '');
      if (status == 'inative') {
        usuarioInativo = true;
        return null;
      }
      mensagem = data.getString('Mensagem', '');
      if (mensagem.isNotEmpty) {
        return null;
      }
      final token = data.getStringNullable('Token');
      _usuarioLogado = null;
      if (token == null) {
        await LogDatabase.instance.logError('Login', 'Token não encontrado ($token)', '', {'token': token});
        return null;
      } else {
        await LogDatabase.instance.logInfo('Login', 'Token ($token)', '', {'token': token});
      }
      final userLogin = UserLogin(usuario: usuario, senha: senha, token: token, atualizarCadastro: responseLogin.data['AtualizarCadastro'] ?? true);

      final responseUser = await connector.get('/agente/buscar', headers: {'Token': token});

      final userF = MapFields.load(responseUser.data);

      _usuarioLogado = UserData(
        nomeCompleto: userF.getString('Nome', ''),
        telefone: userF.getString('Telefone', ''),
        email: userF.getString('Email', ''),
        uf: userF.getString('Uf', ''),
        foto: userF.getString('Foto', ''),
        cpf: userF.getString('CPF', ''),
        userLogin: userLogin,
        idTipoAgenteUberizado: userF.getInt('IDTipoAgenteUberizado', 0),
        permiteTransferenciaMobile: userF.getBool('PermiteTransferenciaMobile', false),
      );

      //final tagsNotificacao = userF.getList<String, dynamic>('TagsNotificacao', []);
      final tags = userF.getMap<String, dynamic>('TagsNotificacao');

      OneSignalService().salveTags(tags);

      await Future.wait([
        HomeController.instance.setIsOnline(userF.getBool('Disponivel', false)),
        LoginHive.instance.save(_usuarioLogado!),
        ConfigDatabase.instance.fetchConfig(),
        StatusAtividadeDatabase.instance.fetchStatus(),
        carregarContrato(connector),
      ]);

      return _usuarioLogado;
    } catch (e) {
      if (e is ConnectionError) {
        try {
          final f = MapFields.load(e.response);
          final mensagemJson = f.getString('Mensagem', 'Erro ao logar');
          mensagem = mensagemJson.replaceAll('\n\n', '\n');
          if (e.status == 400) {
            usuarioInativo = true;
          }
        } catch (_) {
          mensagem = '''Verifique sua conexão com a internet
              e tente novamente''';
          return null;
        }
      }
      if (e is DioException) {
        debugPrint('Erro ao fazer login: ${e.message}');
        _usuarioLogado = null;
        final data = e.response?.data as Map<String, dynamic>?;
        if (data != null && data['status'] == 'inative') {
          usuarioInativo = true;
        }
      }
      _usuarioLogado = null;
      return null;
    }
  }

  Future carregarContrato(connector) async {
    try {
      final responsecontrato = await connector.get('/agente/contrato-completo');
      final contrato = ContratoModel.fromJson(responsecontrato.data);
      await ContratoPrefs.instance.clean();
      await ContratoPrefs.instance.save(contrato);
    } on ConnectionError catch (erro) {
      await LogDatabase.instance.logError('Busca Contrato', erro.toString(), '', {'/agente/contrato': erro.toString()});
    }
  }

  Future<void> logout() async {
    _usuarioLogado = null;
    await LoginHive.instance.clear();
    await ContratoPrefs.instance.clean();

    offlineStore.setForceFakeOnline(false);
    
  }

  Future<bool> esqueceuSenha(String telefone) async {
    WebConnector connector = WebConnector();
    try {
      final response = await connector.post('/agente/esquecisenha', body: {'celular': telefone});
      return response.data['SMS'] ?? false;
    } catch (e) {
      return false;
    }
  }
}
