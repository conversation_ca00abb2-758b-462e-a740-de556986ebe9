import 'package:camera_camera/camera_camera.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/utils/theme_colors.dart';

class WidgetAvisoReceber extends StatefulWidget {
  const WidgetAvisoReceber({
    super.key,
  });

  @override
  State<WidgetAvisoReceber> createState() => _WidgetAvisoReceberState();
}

class _WidgetAvisoReceberState extends State<WidgetAvisoReceber> {
  late XFile? fotopath;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 10),
          Text(
            'Atenção essa OS se trata de  uma coleta de receita, ',
            style: GoogleFonts.roboto(
              fontSize: 15,
              color: ThemeColors.customBlack(context),
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            'não está autorizado a receber valores ',
            style: GoogleFonts.roboto(
              fontSize: 17,
              color: Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            'em espécie ou produtos, coletar a receita e ',
            style: GoogleFonts.roboto(
              fontSize: 15,
              color: ThemeColors.customBlack(context),
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            'não lacrar o envelope.',
            style: GoogleFonts.roboto(
              fontSize: 15,
              color: ThemeColors.customBlack(context),
              fontWeight: FontWeight.bold,
            ),
          )
        ],
      ),
    );
  }
}
