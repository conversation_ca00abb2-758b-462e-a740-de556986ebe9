<dependencies>
  <compile
      roots="__local_aars__:C:\projetos\octa.log\MOBILE\build\app\intermediates\flutter\octalogRelease\libs.jar:unspecified@jar,:@@:airplane_mode_checker::release,:@@:android_id::release,:@@:flutter_activity_recognition::release,:@@:in_app_review::release,:@@:mobile_scanner::release,:@@:shared_preferences_android::release,:@@:speech_to_text::release,:@@:webview_flutter_android::release,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar,:@@:camera_android::release,:@@:connectivity_plus::release,:@@:device_info_plus::release,:@@:file_picker::release,:@@:firebase_messaging::release,:@@:firebase_remote_config::release,:@@:firebase_core::release,:@@:flutter_keyboard_visibility::release,:@@:flutter_pdfview::release,:@@:flutter_plugin_android_lifecycle::release,:@@:flutter_ringtone_player::release,:@@:flutter_webrtc::release,:@@:geolocator_android::release,:@@:google_mlkit_commons::release,:@@:google_mlkit_face_detection::release,:@@:image_picker_android::release,:@@:onesignal_flutter::release,:@@:open_file_android::release,:@@:package_info_plus::release,:@@:path_provider_android::release,:@@:pdfrx::release,:@@:permission_handler_android::release,:@@:rive_common::release,:@@:sqflite_android::release,:@@:sqlite3_flutter_libs::release,:@@:system_info_plus::release,:@@:url_launcher_android::release,io.flutter:flutter_embedding_release:1.0.0-18b71d647a292a980abb405ac7d16fe1f0b20434@jar,androidx.fragment:fragment:1.7.1@aar,androidx.activity:activity:1.9.3@aar,androidx.loader:loader:1.0.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar,androidx.core:core-ktx:1.13.1@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.lifecycle:lifecycle-runtime:2.7.0@aar,androidx.lifecycle:lifecycle-process:2.7.0@aar,androidx.lifecycle:lifecycle-common-java8:2.7.0@jar,androidx.lifecycle:lifecycle-common:2.7.0@jar,androidx.window:window:1.2.0@aar,androidx.window:window-java:1.2.0@aar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.annotation:annotation-jvm:1.9.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,io.flutter:arm64_v8a_release:1.0.0-18b71d647a292a980abb405ac7d16fe1f0b20434@jar,org.jetbrains:annotations:23.0.0@jar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,com.getkeepsafe.relinker:relinker:1.4.5@aar">
    <dependency
        name="__local_aars__:C:\projetos\octa.log\MOBILE\build\app\intermediates\flutter\octalogRelease\libs.jar:unspecified@jar"
        simpleName="__local_aars__:C:\projetos\octa.log\MOBILE\build\app\intermediates\flutter\octalogRelease\libs.jar"/>
    <dependency
        name=":@@:airplane_mode_checker::release"
        simpleName="artifacts::airplane_mode_checker"/>
    <dependency
        name=":@@:android_id::release"
        simpleName="artifacts::android_id"/>
    <dependency
        name=":@@:flutter_activity_recognition::release"
        simpleName="artifacts::flutter_activity_recognition"/>
    <dependency
        name=":@@:in_app_review::release"
        simpleName="artifacts::in_app_review"/>
    <dependency
        name=":@@:mobile_scanner::release"
        simpleName="artifacts::mobile_scanner"/>
    <dependency
        name=":@@:shared_preferences_android::release"
        simpleName="artifacts::shared_preferences_android"/>
    <dependency
        name=":@@:speech_to_text::release"
        simpleName="artifacts::speech_to_text"/>
    <dependency
        name=":@@:webview_flutter_android::release"
        simpleName="artifacts::webview_flutter_android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name=":@@:camera_android::release"
        simpleName="artifacts::camera_android"/>
    <dependency
        name=":@@:connectivity_plus::release"
        simpleName="artifacts::connectivity_plus"/>
    <dependency
        name=":@@:device_info_plus::release"
        simpleName="artifacts::device_info_plus"/>
    <dependency
        name=":@@:file_picker::release"
        simpleName="artifacts::file_picker"/>
    <dependency
        name=":@@:firebase_messaging::release"
        simpleName="artifacts::firebase_messaging"/>
    <dependency
        name=":@@:firebase_remote_config::release"
        simpleName="artifacts::firebase_remote_config"/>
    <dependency
        name=":@@:firebase_core::release"
        simpleName="artifacts::firebase_core"/>
    <dependency
        name=":@@:flutter_keyboard_visibility::release"
        simpleName="artifacts::flutter_keyboard_visibility"/>
    <dependency
        name=":@@:flutter_pdfview::release"
        simpleName="artifacts::flutter_pdfview"/>
    <dependency
        name=":@@:flutter_plugin_android_lifecycle::release"
        simpleName="artifacts::flutter_plugin_android_lifecycle"/>
    <dependency
        name=":@@:flutter_ringtone_player::release"
        simpleName="artifacts::flutter_ringtone_player"/>
    <dependency
        name=":@@:flutter_webrtc::release"
        simpleName="artifacts::flutter_webrtc"/>
    <dependency
        name=":@@:geolocator_android::release"
        simpleName="artifacts::geolocator_android"/>
    <dependency
        name=":@@:google_mlkit_commons::release"
        simpleName="artifacts::google_mlkit_commons"/>
    <dependency
        name=":@@:google_mlkit_face_detection::release"
        simpleName="artifacts::google_mlkit_face_detection"/>
    <dependency
        name=":@@:image_picker_android::release"
        simpleName="artifacts::image_picker_android"/>
    <dependency
        name=":@@:onesignal_flutter::release"
        simpleName="artifacts::onesignal_flutter"/>
    <dependency
        name=":@@:open_file_android::release"
        simpleName="artifacts::open_file_android"/>
    <dependency
        name=":@@:package_info_plus::release"
        simpleName="artifacts::package_info_plus"/>
    <dependency
        name=":@@:path_provider_android::release"
        simpleName="artifacts::path_provider_android"/>
    <dependency
        name=":@@:pdfrx::release"
        simpleName="artifacts::pdfrx"/>
    <dependency
        name=":@@:permission_handler_android::release"
        simpleName="artifacts::permission_handler_android"/>
    <dependency
        name=":@@:rive_common::release"
        simpleName="artifacts::rive_common"/>
    <dependency
        name=":@@:sqflite_android::release"
        simpleName="artifacts::sqflite_android"/>
    <dependency
        name=":@@:sqlite3_flutter_libs::release"
        simpleName="artifacts::sqlite3_flutter_libs"/>
    <dependency
        name=":@@:system_info_plus::release"
        simpleName="artifacts::system_info_plus"/>
    <dependency
        name=":@@:url_launcher_android::release"
        simpleName="artifacts::url_launcher_android"/>
    <dependency
        name="io.flutter:flutter_embedding_release:1.0.0-18b71d647a292a980abb405ac7d16fe1f0b20434@jar"
        simpleName="io.flutter:flutter_embedding_release"/>
    <dependency
        name="androidx.fragment:fragment:1.7.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.9.3@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.window:window:1.2.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="androidx.window:window-java:1.2.0@aar"
        simpleName="androidx.window:window-java"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="io.flutter:arm64_v8a_release:1.0.0-18b71d647a292a980abb405ac7d16fe1f0b20434@jar"
        simpleName="io.flutter:arm64_v8a_release"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
        simpleName="com.getkeepsafe.relinker:relinker"/>
  </compile>
  <package
      roots="__local_aars__:C:\projetos\octa.log\MOBILE\build\app\intermediates\flutter\octalogRelease\libs.jar:unspecified@jar,:@@:airplane_mode_checker::release,:@@:android_id::release,:@@:flutter_activity_recognition::release,:@@:in_app_review::release,:@@:mobile_scanner::release,:@@:shared_preferences_android::release,:@@:speech_to_text::release,:@@:webview_flutter_android::release,:@@:geolocator_android::release,com.google.android.gms:play-services-location:21.2.0@aar,:@@:camera_android::release,:@@:connectivity_plus::release,:@@:device_info_plus::release,:@@:file_picker::release,:@@:firebase_messaging::release,:@@:firebase_remote_config::release,:@@:firebase_core::release,:@@:flutter_keyboard_visibility::release,:@@:flutter_pdfview::release,:@@:image_picker_android::release,:@@:flutter_plugin_android_lifecycle::release,:@@:flutter_ringtone_player::release,:@@:flutter_webrtc::release,:@@:google_mlkit_face_detection::release,:@@:google_mlkit_commons::release,:@@:onesignal_flutter::release,:@@:open_file_android::release,:@@:package_info_plus::release,:@@:path_provider_android::release,:@@:pdfrx::release,:@@:permission_handler_android::release,:@@:rive_common::release,:@@:sqflite_android::release,:@@:sqlite3_flutter_libs::release,:@@:system_info_plus::release,:@@:url_launcher_android::release,io.flutter:flutter_embedding_release:1.0.0-18b71d647a292a980abb405ac7d16fe1f0b20434@jar,androidx.camera:camera-core:1.3.4@aar,androidx.camera:camera-camera2:1.3.4@aar,androidx.camera:camera-lifecycle:1.3.4@aar,com.github.mhiew:android-pdf-viewer:3.2.0-beta.3@aar,androidx.preference:preference:1.2.1@aar,com.google.mlkit:barcode-scanning:17.3.0@aar,com.google.mlkit:face-detection:16.1.7@aar,com.google.android.gms:play-services-mlkit-face-detection:17.1.0@aar,com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1@aar,com.google.mlkit:barcode-scanning-common:17.0.0@aar,com.google.mlkit:vision-common:17.3.0@aar,com.google.mlkit:common:18.11.0@aar,com.onesignal:OneSignal:5.1.29@aar,com.onesignal:in-app-messages:5.1.29@aar,com.onesignal:notifications:5.1.29@aar,com.onesignal:location:5.1.29@aar,com.onesignal:core:5.1.29@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,androidx.appcompat:appcompat:1.6.1@aar,androidx.appcompat:appcompat:1.6.1@aar,com.google.firebase:firebase-messaging:24.1.1@aar,com.google.android.gms:play-services-base:18.5.0@aar,com.google.android.play:review:2.0.2@aar,com.google.firebase:firebase-config:22.1.0@aar,com.google.firebase:firebase-installations:18.0.0@aar,com.google.firebase:firebase-common-ktx:21.0.0@aar,com.google.firebase:firebase-common:21.0.0@aar,com.google.firebase:firebase-iid-interop:17.1.0@aar,com.google.android.gms:play-services-cloud-messaging:17.2.0@aar,com.google.mlkit:vision-interfaces:16.3.0@aar,androidx.recyclerview:recyclerview:1.0.0@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.window:window:1.2.0@aar,androidx.window:window-java:1.2.0@aar,androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar,androidx.datastore:datastore-preferences-proto:1.1.3@jar,androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar,androidx.datastore:datastore-core-okio-jvm:1.1.3@jar,androidx.datastore:datastore-core-android:1.1.3@aar,androidx.datastore:datastore-preferences-android:1.1.3@aar,androidx.datastore:datastore-android:1.1.3@aar,androidx.fragment:fragment-ktx:1.7.1@aar,androidx.activity:activity-ktx:1.9.3@aar,androidx.work:work-runtime-ktx:2.8.1@aar,androidx.work:work-runtime:2.8.1@aar,androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-service:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar,com.google.android.gms:play-services-stats:17.0.2@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-common-java8:2.7.0@jar,androidx.emoji2:emoji2-views-helper:1.2.0@aar,androidx.emoji2:emoji2:1.2.0@aar,androidx.lifecycle:lifecycle-process:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar,com.google.firebase:firebase-installations-interop:17.1.1@aar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.firebase:firebase-abt:21.1.1@aar,com.google.firebase:firebase-measurement-connector:19.0.0@aar,com.google.android.gms:play-services-basement:18.4.0@aar,androidx.fragment:fragment:1.7.1@aar,androidx.fragment:fragment:1.7.1@aar,androidx.activity:activity:1.9.3@aar,androidx.browser:browser:1.8.0@aar,androidx.webkit:webkit:1.12.1@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.core:core-ktx:1.13.1@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.transition:transition:1.4.1@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.lifecycle:lifecycle-runtime:2.7.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.lifecycle:lifecycle-common:2.7.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar,com.github.davidliu:audioswitch:89582c47c9a04c62f90aa5e57251af4800a62c9a@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.exifinterface:exifinterface:1.3.7@aar,androidx.exifinterface:exifinterface:1.3.7@aar,androidx.profileinstaller:profileinstaller:1.3.1@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,com.google.firebase:firebase-config-interop:16.0.1@aar,com.google.firebase:firebase-datatransport:18.2.0@aar,com.google.android.datatransport:transport-backend-cct:3.1.9@aar,com.google.android.datatransport:transport-runtime:3.1.9@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.firebase:firebase-encoders-json:18.0.1@aar,com.google.firebase:firebase-encoders:17.0.0@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.collection:collection-ktx:1.1.0@jar,androidx.collection:collection:1.1.0@jar,androidx.room:room-runtime:2.5.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,com.google.firebase:firebase-components:18.0.0@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,com.google.android.datatransport:transport-api:3.1.0@aar,androidx.cardview:cardview:1.0.0@aar,androidx.window.extensions.core:core:1.0.0@aar,androidx.sqlite:sqlite-framework:2.3.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.room:room-common:2.5.0@jar,androidx.sqlite:sqlite:2.3.0@aar,androidx.annotation:annotation-jvm:1.9.1@jar,androidx.annotation:annotation-experimental:1.4.1@aar,org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar,com.squareup.okio:okio-jvm:3.4.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,io.flutter:arm64_v8a_release:1.0.0-18b71d647a292a980abb405ac7d16fe1f0b20434@jar,com.google.code.gson:gson:2.10.1@jar,io.github.webrtc-sdk:android:125.6422.03@aar,eu.simonbinder:sqlite3-native-library:3.49.1+1@aar,org.jetbrains:annotations:23.0.0@jar,com.getkeepsafe.relinker:relinker:1.4.5@aar,com.github.mhiew:pdfium-android:1.9.2@aar,com.google.android.odml:image:1.0.0-beta1@aar,com.google.android.play:core-common:2.0.4@aar,com.google.auto.value:auto-value-annotations:1.6.3@jar,com.google.guava:listenablefuture:1.0@jar,com.google.firebase:firebase-annotations:16.2.0@jar,com.google.errorprone:error_prone_annotations:2.26.0@jar,javax.inject:javax.inject:1@jar">
    <dependency
        name="__local_aars__:C:\projetos\octa.log\MOBILE\build\app\intermediates\flutter\octalogRelease\libs.jar:unspecified@jar"
        simpleName="__local_aars__:C:\projetos\octa.log\MOBILE\build\app\intermediates\flutter\octalogRelease\libs.jar"/>
    <dependency
        name=":@@:airplane_mode_checker::release"
        simpleName="artifacts::airplane_mode_checker"/>
    <dependency
        name=":@@:android_id::release"
        simpleName="artifacts::android_id"/>
    <dependency
        name=":@@:flutter_activity_recognition::release"
        simpleName="artifacts::flutter_activity_recognition"/>
    <dependency
        name=":@@:in_app_review::release"
        simpleName="artifacts::in_app_review"/>
    <dependency
        name=":@@:mobile_scanner::release"
        simpleName="artifacts::mobile_scanner"/>
    <dependency
        name=":@@:shared_preferences_android::release"
        simpleName="artifacts::shared_preferences_android"/>
    <dependency
        name=":@@:speech_to_text::release"
        simpleName="artifacts::speech_to_text"/>
    <dependency
        name=":@@:webview_flutter_android::release"
        simpleName="artifacts::webview_flutter_android"/>
    <dependency
        name=":@@:geolocator_android::release"
        simpleName="artifacts::geolocator_android"/>
    <dependency
        name="com.google.android.gms:play-services-location:21.2.0@aar"
        simpleName="com.google.android.gms:play-services-location"/>
    <dependency
        name=":@@:camera_android::release"
        simpleName="artifacts::camera_android"/>
    <dependency
        name=":@@:connectivity_plus::release"
        simpleName="artifacts::connectivity_plus"/>
    <dependency
        name=":@@:device_info_plus::release"
        simpleName="artifacts::device_info_plus"/>
    <dependency
        name=":@@:file_picker::release"
        simpleName="artifacts::file_picker"/>
    <dependency
        name=":@@:firebase_messaging::release"
        simpleName="artifacts::firebase_messaging"/>
    <dependency
        name=":@@:firebase_remote_config::release"
        simpleName="artifacts::firebase_remote_config"/>
    <dependency
        name=":@@:firebase_core::release"
        simpleName="artifacts::firebase_core"/>
    <dependency
        name=":@@:flutter_keyboard_visibility::release"
        simpleName="artifacts::flutter_keyboard_visibility"/>
    <dependency
        name=":@@:flutter_pdfview::release"
        simpleName="artifacts::flutter_pdfview"/>
    <dependency
        name=":@@:image_picker_android::release"
        simpleName="artifacts::image_picker_android"/>
    <dependency
        name=":@@:flutter_plugin_android_lifecycle::release"
        simpleName="artifacts::flutter_plugin_android_lifecycle"/>
    <dependency
        name=":@@:flutter_ringtone_player::release"
        simpleName="artifacts::flutter_ringtone_player"/>
    <dependency
        name=":@@:flutter_webrtc::release"
        simpleName="artifacts::flutter_webrtc"/>
    <dependency
        name=":@@:google_mlkit_face_detection::release"
        simpleName="artifacts::google_mlkit_face_detection"/>
    <dependency
        name=":@@:google_mlkit_commons::release"
        simpleName="artifacts::google_mlkit_commons"/>
    <dependency
        name=":@@:onesignal_flutter::release"
        simpleName="artifacts::onesignal_flutter"/>
    <dependency
        name=":@@:open_file_android::release"
        simpleName="artifacts::open_file_android"/>
    <dependency
        name=":@@:package_info_plus::release"
        simpleName="artifacts::package_info_plus"/>
    <dependency
        name=":@@:path_provider_android::release"
        simpleName="artifacts::path_provider_android"/>
    <dependency
        name=":@@:pdfrx::release"
        simpleName="artifacts::pdfrx"/>
    <dependency
        name=":@@:permission_handler_android::release"
        simpleName="artifacts::permission_handler_android"/>
    <dependency
        name=":@@:rive_common::release"
        simpleName="artifacts::rive_common"/>
    <dependency
        name=":@@:sqflite_android::release"
        simpleName="artifacts::sqflite_android"/>
    <dependency
        name=":@@:sqlite3_flutter_libs::release"
        simpleName="artifacts::sqlite3_flutter_libs"/>
    <dependency
        name=":@@:system_info_plus::release"
        simpleName="artifacts::system_info_plus"/>
    <dependency
        name=":@@:url_launcher_android::release"
        simpleName="artifacts::url_launcher_android"/>
    <dependency
        name="io.flutter:flutter_embedding_release:1.0.0-18b71d647a292a980abb405ac7d16fe1f0b20434@jar"
        simpleName="io.flutter:flutter_embedding_release"/>
    <dependency
        name="androidx.camera:camera-core:1.3.4@aar"
        simpleName="androidx.camera:camera-core"/>
    <dependency
        name="androidx.camera:camera-camera2:1.3.4@aar"
        simpleName="androidx.camera:camera-camera2"/>
    <dependency
        name="androidx.camera:camera-lifecycle:1.3.4@aar"
        simpleName="androidx.camera:camera-lifecycle"/>
    <dependency
        name="com.github.mhiew:android-pdf-viewer:3.2.0-beta.3@aar"
        simpleName="com.github.mhiew:android-pdf-viewer"/>
    <dependency
        name="androidx.preference:preference:1.2.1@aar"
        simpleName="androidx.preference:preference"/>
    <dependency
        name="com.google.mlkit:barcode-scanning:17.3.0@aar"
        simpleName="com.google.mlkit:barcode-scanning"/>
    <dependency
        name="com.google.mlkit:face-detection:16.1.7@aar"
        simpleName="com.google.mlkit:face-detection"/>
    <dependency
        name="com.google.android.gms:play-services-mlkit-face-detection:17.1.0@aar"
        simpleName="com.google.android.gms:play-services-mlkit-face-detection"/>
    <dependency
        name="com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1@aar"
        simpleName="com.google.android.gms:play-services-mlkit-barcode-scanning"/>
    <dependency
        name="com.google.mlkit:barcode-scanning-common:17.0.0@aar"
        simpleName="com.google.mlkit:barcode-scanning-common"/>
    <dependency
        name="com.google.mlkit:vision-common:17.3.0@aar"
        simpleName="com.google.mlkit:vision-common"/>
    <dependency
        name="com.google.mlkit:common:18.11.0@aar"
        simpleName="com.google.mlkit:common"/>
    <dependency
        name="com.onesignal:OneSignal:5.1.29@aar"
        simpleName="com.onesignal:OneSignal"/>
    <dependency
        name="com.onesignal:in-app-messages:5.1.29@aar"
        simpleName="com.onesignal:in-app-messages"/>
    <dependency
        name="com.onesignal:notifications:5.1.29@aar"
        simpleName="com.onesignal:notifications"/>
    <dependency
        name="com.onesignal:location:5.1.29@aar"
        simpleName="com.onesignal:location"/>
    <dependency
        name="com.onesignal:core:5.1.29@aar"
        simpleName="com.onesignal:core"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.google.firebase:firebase-messaging:24.1.1@aar"
        simpleName="com.google.firebase:firebase-messaging"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.android.play:review:2.0.2@aar"
        simpleName="com.google.android.play:review"/>
    <dependency
        name="com.google.firebase:firebase-config:22.1.0@aar"
        simpleName="com.google.firebase:firebase-config"/>
    <dependency
        name="com.google.firebase:firebase-installations:18.0.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-common:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-iid-interop"/>
    <dependency
        name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
        simpleName="com.google.android.gms:play-services-cloud-messaging"/>
    <dependency
        name="com.google.mlkit:vision-interfaces:16.3.0@aar"
        simpleName="com.google.mlkit:vision-interfaces"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.0.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.window:window:1.2.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="androidx.window:window-java:1.2.0@aar"
        simpleName="androidx.window:window-java"/>
    <dependency
        name="androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar"
        simpleName="androidx.datastore:datastore-preferences-external-protobuf"/>
    <dependency
        name="androidx.datastore:datastore-preferences-proto:1.1.3@jar"
        simpleName="androidx.datastore:datastore-preferences-proto"/>
    <dependency
        name="androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar"
        simpleName="androidx.datastore:datastore-preferences-core-jvm"/>
    <dependency
        name="androidx.datastore:datastore-core-okio-jvm:1.1.3@jar"
        simpleName="androidx.datastore:datastore-core-okio-jvm"/>
    <dependency
        name="androidx.datastore:datastore-core-android:1.1.3@aar"
        simpleName="androidx.datastore:datastore-core-android"/>
    <dependency
        name="androidx.datastore:datastore-preferences-android:1.1.3@aar"
        simpleName="androidx.datastore:datastore-preferences-android"/>
    <dependency
        name="androidx.datastore:datastore-android:1.1.3@aar"
        simpleName="androidx.datastore:datastore-android"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.7.1@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.activity:activity-ktx:1.9.3@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.work:work-runtime-ktx:2.8.1@aar"
        simpleName="androidx.work:work-runtime-ktx"/>
    <dependency
        name="androidx.work:work-runtime:2.8.1@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.2.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.2.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.firebase:firebase-abt:21.1.1@aar"
        simpleName="com.google.firebase:firebase-abt"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.4.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.7.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.9.3@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.browser:browser:1.8.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.webkit:webkit:1.12.1@aar"
        simpleName="androidx.webkit:webkit"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.transition:transition:1.4.1@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="com.github.davidliu:audioswitch:89582c47c9a04c62f90aa5e57251af4800a62c9a@aar"
        simpleName="com.github.davidliu:audioswitch"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.7@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="com.google.firebase:firebase-config-interop:16.0.1@aar"
        simpleName="com.google.firebase:firebase-config-interop"/>
    <dependency
        name="com.google.firebase:firebase-datatransport:18.2.0@aar"
        simpleName="com.google.firebase:firebase-datatransport"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.1@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.collection:collection-ktx:1.1.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.room:room-runtime:2.5.0@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="com.google.firebase:firebase-components:18.0.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.1.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.window.extensions.core:core:1.0.0@aar"
        simpleName="androidx.window.extensions.core:core"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.3.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.room:room-common:2.5.0@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.sqlite:sqlite:2.3.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.4.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="io.flutter:arm64_v8a_release:1.0.0-18b71d647a292a980abb405ac7d16fe1f0b20434@jar"
        simpleName="io.flutter:arm64_v8a_release"/>
    <dependency
        name="com.google.code.gson:gson:2.10.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="io.github.webrtc-sdk:android:125.6422.03@aar"
        simpleName="io.github.webrtc-sdk:android"/>
    <dependency
        name="eu.simonbinder:sqlite3-native-library:3.49.1+1@aar"
        simpleName="eu.simonbinder:sqlite3-native-library"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
        simpleName="com.getkeepsafe.relinker:relinker"/>
    <dependency
        name="com.github.mhiew:pdfium-android:1.9.2@aar"
        simpleName="com.github.mhiew:pdfium-android"/>
    <dependency
        name="com.google.android.odml:image:1.0.0-beta1@aar"
        simpleName="com.google.android.odml:image"/>
    <dependency
        name="com.google.android.play:core-common:2.0.4@aar"
        simpleName="com.google.android.play:core-common"/>
    <dependency
        name="com.google.auto.value:auto-value-annotations:1.6.3@jar"
        simpleName="com.google.auto.value:auto-value-annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.26.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
  </package>
</dependencies>
