// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
//import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/components/buttom_custom/button_custom.dart';
import 'package:octalog/src/components/error_widget_custom/error_widget_custom.dart';
import 'package:octalog/src/components/flavor_image/flavor_image.dart';
import 'package:octalog/src/components/text_field/text_field_custom.dart';
//import 'package:octalog/src/components/title_widget/title_widget.dart';
import 'package:octalog/src/helpers/login/login_hive.dart';
import 'package:octalog/src/helpers/smtp_email_envio.dart';
import 'package:octalog/src/pages/home/<USER>';
import 'package:octalog/src/pages/login/login_state.dart';
import 'package:octalog/src/pages/login/login_store.dart';
import 'package:octalog/src/utils/theme_colors.dart';
//import 'package:url_launcher/url_launcher_string.dart';

import '../../helpers/login/login.dart';
import '../../models/user.dart';
import '../../utils/offline_helper.dart';
import '../cadastro/cadastro_page.dart';
//import '../esqueceu_senha/esqueceu_senha_page.dart';
import '../home/<USER>';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _idController = TextEditingController();
  final _passwordController = TextEditingController();

  final store = LoginStore();

  Future<void> _init() async {
    try {
      final response = await LoginHive.instance.read();
      if (response != null) {
        _idController.text = response.usuario;
        _passwordController.text = response.senha;
        final resp = await _login();
        if (!resp) {
          if (Login.instance.usuarioInativo) return;
          await _login(usuario: response);
        }
      }
    } catch (_) {
      store.setLoading(false);
    }
  }

  Future<bool> _login({UserData? usuario}) async {
    try {
      FocusScope.of(context).unfocus();
      if (usuario == null) {
        final user = await store.login(_idController.text, _passwordController.text);
        if (user == null) {
          return false;
        }
        offlineStore.setOffline(false);

        Login.instance.setUsuarioLogado(user);

        Navigator.push(context, MaterialPageRoute(builder: (context) => user.atualizarCadastro ? const CadastroAgente() : const Home()));

        HomeController.instance.setCarregouInicio(false);
        return true;
      } else {
        Login.instance.setUsuarioLogado(usuario);
        offlineStore.setOffline(true);
        Navigator.push(context, MaterialPageRoute(builder: (context) => const Home()));
        HomeController.instance.setCarregouInicio(false);
        return true;
      }
    } catch (e, s) {
      if (Platform.isIOS) {
        await SmtpEmailEnvio.instance.enviarEmail(
          emailsDestino: ['<EMAIL>'],
          assunto: 'Falha no login botao',
          mensagem: e.toString(),
          anexos: [AnexoEmail(nome: 'erro_login_botao.txt', dados: 'Erro:\n${e.toString()}\n\nStackTrace: ${s.toString()}')],
        );
      }
      store.setLoading(false);
      return false;
    }
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: ValueListenableBuilder<LoginState>(
        valueListenable: store.state,
        builder: (BuildContext context, LoginState value, Widget? child) {
          return Center(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 31),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Center(child: FlavorImage(assetName: 'logo200.png')),
                    Container(
                      constraints: const BoxConstraints(minHeight: 100.0),
                      child:
                          value.error
                              ? Center(child: ErrorWidgetCustom(errorMessage: value.message.isEmpty ? 'Usuário ou senha inválida' : value.message))
                              : Container(),
                    ),
                    TextFieldLsCustom(
                      isError: value.error,
                      labelText: 'Login',
                      controller: _idController,
                      suffixIcon: value.error ? null : Icon(Icons.check_box_sharp, color: ThemeColors.customGrey(context), size: 20),
                    ),
                    Builder(
                      builder: (context) {
                        final visiblePassword = value.visiblePassword;
                        return TextFieldLsCustom(
                          labelText: 'Senha',
                          controller: _passwordController,
                          obscureText: !visiblePassword,
                          suffixIcon: Transform(
                            alignment: Alignment.center,
                            transform: Matrix4.rotationY(pi),
                            child: IconButton(
                              onPressed: () {
                                store.setVisiblePassword(!visiblePassword);
                              },
                              icon: Icon(visiblePassword ? Icons.visibility : Icons.visibility_off, color: ThemeColors.customGrey(context), size: 20),
                            ),
                          ),
                          isError: value.error,
                          obscuringCharacter: '*',
                        );
                      },
                    ),
                    const SizedBox(height: 8),
                    ButtonLsCustom(text: 'ENTRAR', isLoading: value.loading, onPressed: _login),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
