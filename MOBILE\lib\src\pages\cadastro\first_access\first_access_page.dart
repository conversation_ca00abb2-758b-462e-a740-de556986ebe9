// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:octalog/src/pages/cadastro/cadastro_state.dart';

import '../../../components/text_field_ls/text_field_ls_custom.dart';
import '../../../utils/colors-dart';
import '../cadastro_store.dart';
import '../model/contrato_model.dart';

class FirstAccess extends StatefulWidget {
  final CadastroStore store;
  const FirstAccess({super.key, required this.store});

  @override
  State<FirstAccess> createState() => _FirstAccessState();
}

class _FirstAccessState extends State<FirstAccess> {
  final nomeController = TextEditingController();
  final celularController = TextEditingController();
  final emailController = TextEditingController();
  final cpfController = TextEditingController();

  ContratoModel? contratoModel;
  bool loadingPage = true;

  @override
  void initState() {
    init();
    super.initState();
  }

  init() {
    nomeController.text = widget.store.state.value.contratoModel?.nomeAgente ?? '';
    celularController.text = widget.store.state.value.contratoModel?.telefone ?? '';
    emailController.text = widget.store.state.value.contratoModel?.email ?? '';
    cpfController.text = widget.store.state.value.contratoModel?.cpf ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<CadastroState>(
      valueListenable: widget.store.state,
      builder: (BuildContext context, state, _) {
        final store = widget.store;
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFieldLsCustom(
              labelText: 'Nome',
              enabled: state.contratoModel?.dataConfirmouCadastro == null,
              isError: false,
              textInputAction: TextInputAction.next,
              controller: nomeController,
              onChanged: (value) {
                store.setContratoModelParte(nomeAgente: value);
              },
              maxCaracteres: 255,
            ),
            const SizedBox(height: 16),
            TextFieldLsCustom(
              labelText: 'CPF',
              maxCaracteres: 14,
              enabled: state.contratoModel?.dataConfirmouCadastro == null,
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.number,
              isError: false,
              controller: cpfController,
              isCpf: true,
              onChanged: (value) {
                store.setContratoModelParte(cpf: value);
              },
            ),
            const SizedBox(height: 16),
            TextFieldLsCustom(
              labelText: 'Número de celular',
              isError: false,
              maxCaracteres: 15,
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.number,
              controller: celularController,
              isTelefone: true,
              onChanged: (value) {
                store.setContratoModelParte(telefone: value);
              },
            ),
            const SizedBox(height: 16),
            TextFieldLsCustom(
              labelText: 'E-mail',
              isError: false,
              textInputAction: TextInputAction.done,
              controller: emailController,
              maxCaracteres: 300,
              suffixIcon: const Icon(Icons.check_circle, color: ThemeColors.customGreen(context), size: 20),
              onChanged: (value) {
                store.setContratoModelParte(email: value);
              },
            ),
          ],
        );
      },
    );
  }
}
