import 'package:flutter/material.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../../utils/colors-dart';

class FcmPorcent extends StatefulWidget {
  const FcmPorcent({super.key});

  @override
  State<FcmPorcent> createState() => _FcmPorcentState();
}

class _FcmPorcentState extends State<FcmPorcent> {
  late final double _progress = 0.3;

  @override
  void initState() {
    // Timer.periodic(
    //   const Duration(seconds: 3),
    //   (Timer timer) => setState(
    //     () {
    //       if (_progress == 1) {
    //         _progress = 0;
    //       } else {
    //         _progress += 0.2;
    //       }
    //     },
    //   ),
    // );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 5,
      child: LinearProgressIndicator(
        backgroundColor: ThemeColors.customGreyLight(context),
        valueColor:
             AlwaysStoppedAnimation<Color>(ThemeColors.customOrange(context)),
        value: _progress,
      ),
    );
  }
}
