{"version": "0.2.0", "configurations": [{"name": "Flutter Debug (Octalog)", "request": "launch", "type": "dart", "flutterMode": "debug", "args": ["--flavor", "octalog"], "env": {"JAVA_HOME": "/Library/Java/JavaVirtualMachines/jdk-21.jdk/Contents/Home"}}, {"name": "Flutter Debug (Arcargo)", "request": "launch", "type": "dart", "flutterMode": "debug", "args": ["--flavor", "<PERSON>ar<PERSON>"], "env": {"JAVA_HOME": "/Library/Java/JavaVirtualMachines/jdk-21.jdk/Contents/Home"}}, {"name": "Flutter Debug (Connect)", "request": "launch", "type": "dart", "flutterMode": "debug", "args": ["--flavor", "connect"], "env": {"JAVA_HOME": "/Library/Java/JavaVirtualMachines/jdk-21.jdk/Contents/Home"}}, {"name": "Flutter Debug (Rondolog)", "request": "launch", "type": "dart", "flutterMode": "debug", "args": ["--flavor", "rondolog"], "env": {"JAVA_HOME": "/Library/Java/JavaVirtualMachines/jdk-21.jdk/Contents/Home"}}]}