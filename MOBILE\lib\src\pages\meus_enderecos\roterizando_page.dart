// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:octalog/src/components/loading_ls/loading_ls.dart';

class RoterizandoPage extends StatefulWidget {
  const RoterizandoPage({
    super.key,
  });

  @override
  State<RoterizandoPage> createState() => _RoterizandoPageState();
}

class _RoterizandoPageState extends State<RoterizandoPage> {
  @override
  initState() {
    super.initState();
    _roterizar();
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: SizedBox(width: 150, height: 150, child: LoadingLs()),
      ),
    );
  }

  Future _roterizar() async {
    Navigator.of(context).pop();
  }
}
