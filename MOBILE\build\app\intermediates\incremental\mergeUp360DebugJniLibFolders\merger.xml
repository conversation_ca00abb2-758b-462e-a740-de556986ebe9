<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\android\app\src\main\jniLibs"/><source path="C:\projetos\octa.log\MOBILE\build\native_assets\android\jniLibs\lib"/></dataSet><dataSet config="up360" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\android\app\src\up360\jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\android\app\src\debug\jniLibs"/></dataSet><dataSet config="variant" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\projetos\octa.log\MOBILE\android\app\src\up360Debug\jniLibs"/></dataSet></merger>