import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/pages/romaneio/model/romaneio_model.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../../utils/colors.dart';

class WidgetRomaneioCard extends StatefulWidget {
  final RomaneioModel romaneio;
  const WidgetRomaneioCard({
    super.key,
    required this.romaneio,
  });

  @override
  State<WidgetRomaneioCard> createState() => _WidgetRomaneioCardState();
}

class _WidgetRomaneioCardState extends State<WidgetRomaneioCard> {
  bool isExpanded = true;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        setState(() {
          isExpanded = !isExpanded;
        });
      },
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                 Expanded(
                  flex: 2,
                  child: Divider(
                    thickness: 2,
                    endIndent: 25,
                    color: ThemeColors.customOrange(context),
                  ),
                ),
                Expanded(
                  flex: 4,
                  child: Text(
                    "${widget.romaneio.total} ${widget.romaneio.tipo}",
                    textAlign: TextAlign.center,
                    style: GoogleFonts.roboto(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: ThemeColors.customOrange(context).withOpacity(0.9),
                    ),
                  ),
                ),
                 Expanded(
                  flex: 2,
                  child: Divider(
                    thickness: 2,
                    indent: 25,
                    color: ThemeColors.customOrange(context),
                  ),
                ),
              ],
            ),
          ),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: const Color.fromARGB(193, 255, 221, 205),
              borderRadius: BorderRadius.circular(5),
              border: Border.all(
                color: ThemeColors.customOrange(context),
                width: 2,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.only(
                left: 10,
                top: 10,
                bottom: 10,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ListView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemCount: widget.romaneio.resumo.length,
                    itemBuilder: (context, index) {
                      final grupoCliente = widget.romaneio.resumo[index];
                      return SizedBox(
                        width: double.infinity,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Builder(builder: (context) {
                              //se o qtde for menor que 10 exibe um 0 antes do número
                              final qtde = grupoCliente.qtde < 10
                                  ? "0${grupoCliente.qtde}"
                                  : grupoCliente.qtde;
                              return Text(
                                "$qtde",
                                textAlign: TextAlign.end,
                                style: GoogleFonts.roboto(
                                  fontSize: 15,
                                  fontWeight: FontWeight.bold,
                                  color:
                                      ColorsCustom.customBlack.withOpacity(0.7),
                                ),
                              );
                            }),
                            Expanded(
                              child: Text(
                                " - ${grupoCliente.local}",
                                style: GoogleFonts.roboto(
                                  fontSize: 15,
                                  fontWeight: FontWeight.bold,
                                  color:
                                      ColorsCustom.customBlack.withOpacity(0.7),
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                  if (widget.romaneio.tipo == "ENTREGA") ...[
                    if (isExpanded) ...[
                      const SizedBox(
                        height: 10,
                      ),
                      SizedBox(
                        width: double.infinity,
                        child: Wrap(alignment: WrapAlignment.center, children: [
                          Builder(
                            builder: (context) {
                              // se romaneio for maior que 1, exibe o texto "Romaneios: "~
                              final plural =
                                  widget.romaneio.romaneios.length > 1;

                              return Text(
                                plural ? "Romaneios: " : "Romaneio: ",
                                style: GoogleFonts.roboto(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color:
                                      ColorsCustom.customBlack.withOpacity(0.7),
                                ),
                              );
                            },
                          ),
                          ...widget.romaneio.romaneios.map(
                            (e) => Padding(
                              padding: const EdgeInsets.only(right: 5),
                              child: Text(
                                //colocar uma virgula depois de cada romanio, menos na final
                                "$e${widget.romaneio.romaneios.last != e ? "," : ""}",
                                style: GoogleFonts.roboto(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color:
                                      ColorsCustom.customBlack.withOpacity(0.6),
                                ),
                              ),
                            ),
                          ),
                        ]),
                      ),
                    ]
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
