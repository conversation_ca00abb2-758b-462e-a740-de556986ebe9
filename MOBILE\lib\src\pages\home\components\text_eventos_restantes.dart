import 'package:flutter/material.dart';

// import 'package:octalog/src/utils/theme_colors.dart';

import '../../../database/offline_request/offline_request_database.dart';

class TextEventosRestantes extends StatefulWidget {
  const TextEventosRestantes({super.key});

  @override
  State<TextEventosRestantes> createState() => _TextEventosRestantesState();
}

class _TextEventosRestantesState extends State<TextEventosRestantes> {
  int qtd = 0;

  bool get isVisible => qtd > 0;

  void loadQtd() async {
    while (true) {
      if (!mounted) return;
      qtd = await OfflineRequestDatabase.instance.getQtdEnventosRestantes();
      if (!mounted) return;

      setState(() {});

      // final stateApp =
      //     WidgetsBinding.instance.lifecycleState == AppLifecycleState.resumed;
      // if (stateApp) {
      //   await FcmDataBase.instance.shownotification();
      // }
      await Future.delayed(const Duration(seconds: 2));
    }
  }

  @override
  void initState() {
    super.initState();
    loadQtd();
  }

  @override
  void dispose() {
    super.dispose();
    loadQtd();
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: isVisible,
      child: Text(
        '$qtd ${(qtd == 1 ? " item" : " itens")} para sincronizar, mantenha aberto.',
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
        style: const TextStyle(
          fontSize: 12,
          color: Colors.amber,
          //fontStyle: FontStyle.italic,
        ),
      ),
    );
  }
}
