1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.octalog"
4    android:versionCode="30"
5    android:versionName="1.2.4" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <!-- Add permissions for location access -->
12    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
12-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:6:5-78
12-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
13-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:7:5-80
13-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:7:22-78
14    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
14-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:9:2-75
14-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:9:19-73
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:10:2-64
15-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:10:19-61
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:11:2-76
16-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:11:19-73
17    <uses-permission android:name="android.permission.WAKE_LOCK" />
17-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:12:2-65
17-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:12:19-62
18    <uses-permission android:name="android.permission.CAMERA" />
18-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:13:2-62
18-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:13:19-59
19    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
19-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:14:2-72
19-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:14:19-69
20    <uses-permission android:name="android.permission.VIBRATE" />
20-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:15:2-63
20-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:15:19-60
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
21-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:16:2-74
21-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:16:19-71
22    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
22-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:17:2-78
22-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:17:19-75
23    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
23-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:18:2-76
23-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:18:19-73
24    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
24-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:19:2-73
24-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:19:19-71
25    <uses-permission android:name="android.permission.RECORD_AUDIO" />
25-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:20:2-68
25-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:20:19-65
26    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
26-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:11:2-76
26-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:11:19-73
27    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
27-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:22:2-76
27-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:22:19-73
28    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
28-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:23:2-77
28-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:23:19-74
29    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
29-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:24:2-78
29-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:24:19-75
30    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
30-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:25:2-77
30-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:25:19-74
31    <!--
32    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
33		tools:ignore="ScopedStorage" /> 
34	<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
35	<uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
36	<uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
37    -->
38    <uses-feature android:name="android.hardware.camera" />
38-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:31:2-57
38-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:31:16-54
39    <uses-feature android:name="android.hardware.camera.autofocus" />
39-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:32:2-67
39-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:32:16-64
40    <!--
41         Required to query activities that can process text, see:
42         https://developer.android.com/training/package-visibility and
43         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
44
45         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
46    -->
47    <queries>
47-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:71:5-76:15
48        <intent>
48-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:72:9-75:18
49            <action android:name="android.intent.action.PROCESS_TEXT" />
49-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:73:13-72
49-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:73:21-70
50
51            <data android:mimeType="text/plain" />
51-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:74:13-50
51-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:74:19-48
52        </intent>
53        <intent>
53-->[:file_picker] C:\projetos\octa.log\MOBILE\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:18
54            <action android:name="android.intent.action.GET_CONTENT" />
54-->[:file_picker] C:\projetos\octa.log\MOBILE\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-72
54-->[:file_picker] C:\projetos\octa.log\MOBILE\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:21-69
55
56            <data android:mimeType="*/*" />
56-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:74:13-50
56-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:74:19-48
57        </intent>
58    </queries>
59    <!-- Create a unique permission for your app and use it so only your app can receive your OneSignal messages. -->
60    <permission
60-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:10:5-12:47
61        android:name="com.octalog.permission.C2D_MESSAGE"
61-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:11:9-63
62        android:protectionLevel="signature" />
62-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:12:9-44
63
64    <uses-permission android:name="com.octalog.permission.C2D_MESSAGE" /> <!-- c2dm RECEIVE are basic requirements for push messages through Google's FCM -->
64-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:14:5-79
64-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:14:22-76
65    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" /> <!-- START: ShortcutBadger -->
65-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:19:5-82
65-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:19:22-79
66    <!-- Samsung -->
67    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
67-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:31:5-86
67-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:31:22-83
68    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
68-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:32:5-87
68-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:32:22-84
69    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
69-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:33:5-81
69-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:33:22-78
70    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
70-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:34:5-83
70-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:34:22-80
71    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
71-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:35:5-88
71-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:35:22-85
72    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
72-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:36:5-92
72-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:36:22-89
73    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
73-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:37:5-84
73-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:37:22-81
74    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
74-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:38:5-83
74-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:38:22-80
75    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
75-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:39:5-91
75-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:39:22-88
76    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
76-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:40:5-92
76-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:40:22-89
77    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- ZUK -->
77-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:41:5-93
77-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:41:22-90
78    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- OPPO -->
78-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:42:5-73
78-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:42:22-70
79    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
79-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:43:5-82
79-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:43:22-79
80    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- EvMe -->
80-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:44:5-83
80-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:44:22-80
81    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
81-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:45:5-88
81-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:45:22-85
82    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
82-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:46:5-89
82-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:46:22-86
83
84    <permission
84-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
85        android:name="com.octalog.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
85-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
86        android:protectionLevel="signature" />
86-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
87
88    <uses-permission android:name="com.octalog.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
88-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
88-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
89    <uses-permission
89-->[com.github.davidliu:audioswitch:89582c47c9a04c62f90aa5e57251af4800a62c9a] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\160f0e0b2f19cfe5268e794069515506\transformed\jetified-audioswitch-89582c47c9a04c62f90aa5e57251af4800a62c9a\AndroidManifest.xml:9:5-11:38
90        android:name="android.permission.BLUETOOTH"
90-->[com.github.davidliu:audioswitch:89582c47c9a04c62f90aa5e57251af4800a62c9a] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\160f0e0b2f19cfe5268e794069515506\transformed\jetified-audioswitch-89582c47c9a04c62f90aa5e57251af4800a62c9a\AndroidManifest.xml:10:9-52
91        android:maxSdkVersion="30" />
91-->[com.github.davidliu:audioswitch:89582c47c9a04c62f90aa5e57251af4800a62c9a] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\160f0e0b2f19cfe5268e794069515506\transformed\jetified-audioswitch-89582c47c9a04c62f90aa5e57251af4800a62c9a\AndroidManifest.xml:11:9-35
92
93    <application
94        android:name="android.app.Application"
94-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:36:9-42
95        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
95-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
96        android:extractNativeLibs="true"
97        android:icon="@mipmap/launcher_icon"
97-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:37:9-45
98        android:label="@string/app_name" >
98-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:35:9-41
99        <activity
99-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:38:9-59:20
100            android:name="com.octalog.MainActivity"
100-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:39:13-41
101            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
101-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:44:13-163
102            android:exported="true"
102-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:40:13-36
103            android:hardwareAccelerated="true"
103-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:45:13-47
104            android:launchMode="singleTop"
104-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:41:13-43
105            android:taskAffinity=""
105-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:42:13-36
106            android:theme="@style/LaunchTheme"
106-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:43:13-47
107            android:windowSoftInputMode="adjustResize" >
107-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:46:13-55
108
109            <!--
110                 Specifies an Android theme to apply to this Activity as soon as
111                 the Android process has started. This theme is visible to the user
112                 while the Flutter UI initializes. After that, this theme continues
113                 to determine the Window background behind the Flutter UI.
114            -->
115            <meta-data
115-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:51:13-54:17
116                android:name="io.flutter.embedding.android.NormalTheme"
116-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:52:15-70
117                android:resource="@style/NormalTheme" />
117-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:53:15-52
118
119            <intent-filter>
119-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:55:13-58:29
120                <action android:name="android.intent.action.MAIN" />
120-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:56:17-68
120-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:56:25-66
121
122                <category android:name="android.intent.category.LAUNCHER" />
122-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:57:17-76
122-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:57:27-74
123            </intent-filter>
124        </activity>
125        <!--
126             Don't delete the meta-data below.
127             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
128        -->
129        <meta-data
129-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:62:9-64:33
130            android:name="flutterEmbedding"
130-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:63:13-44
131            android:value="2" />
131-->C:\projetos\octa.log\MOBILE\android\app\src\main\AndroidManifest.xml:64:13-30
132
133        <receiver android:name="com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentReceiver" />
133-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-119
133-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:19-116
134
135        <service
135-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:9-14:72
136            android:name="com.pravera.flutter_activity_recognition.service.ActivityRecognitionIntentService"
136-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-109
137            android:enabled="true"
137-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-35
138            android:exported="false"
138-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-37
139            android:permission="android.permission.BIND_JOB_SERVICE" />
139-->[:flutter_activity_recognition] C:\projetos\octa.log\MOBILE\build\flutter_activity_recognition\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-69
140        <service
140-->[:geolocator_android] C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:56
141            android:name="com.baseflow.geolocator.GeolocatorLocationService"
141-->[:geolocator_android] C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-77
142            android:enabled="true"
142-->[:geolocator_android] C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-35
143            android:exported="false"
143-->[:geolocator_android] C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-37
144            android:foregroundServiceType="location" />
144-->[:geolocator_android] C:\projetos\octa.log\MOBILE\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-53
145        <service
145-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:9-17:72
146            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
146-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-107
147            android:exported="false"
147-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-37
148            android:permission="android.permission.BIND_JOB_SERVICE" />
148-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-69
149        <service
149-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:9-24:19
150            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
150-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:13-97
151            android:exported="false" >
151-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-37
152            <intent-filter>
152-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-23:29
153                <action android:name="com.google.firebase.MESSAGING_EVENT" />
153-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:17-78
153-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:25-75
154            </intent-filter>
155        </service>
156
157        <receiver
157-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:9-33:20
158            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
158-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-98
159            android:exported="true"
159-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-36
160            android:permission="com.google.android.c2dm.permission.SEND" >
160-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-73
161            <intent-filter>
161-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-32:29
162                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
162-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:17-81
162-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:25-78
163            </intent-filter>
164        </receiver>
165
166        <service
166-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:35:9-39:19
167            android:name="com.google.firebase.components.ComponentDiscoveryService"
167-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:35:18-89
168            android:directBootAware="true"
168-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
169            android:exported="false" >
169-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
170            <meta-data
170-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:13-38:85
171                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
171-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:17-128
172                android:value="com.google.firebase.components.ComponentRegistrar" />
172-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:17-82
173            <meta-data
173-->[:firebase_remote_config] C:\projetos\octa.log\MOBILE\build\firebase_remote_config\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
174                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firebaseremoteconfig.FlutterFirebaseAppRegistrar"
174-->[:firebase_remote_config] C:\projetos\octa.log\MOBILE\build\firebase_remote_config\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-139
175                android:value="com.google.firebase.components.ComponentRegistrar" />
175-->[:firebase_remote_config] C:\projetos\octa.log\MOBILE\build\firebase_remote_config\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
176            <meta-data
176-->[:firebase_core] C:\projetos\octa.log\MOBILE\build\firebase_core\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
177                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
177-->[:firebase_core] C:\projetos\octa.log\MOBILE\build\firebase_core\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-124
178                android:value="com.google.firebase.components.ComponentRegistrar" />
178-->[:firebase_core] C:\projetos\octa.log\MOBILE\build\firebase_core\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
179            <meta-data
179-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
180                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
180-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
181                android:value="com.google.firebase.components.ComponentRegistrar" />
181-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
182            <meta-data
182-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
183                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
183-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
184                android:value="com.google.firebase.components.ComponentRegistrar" />
184-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
185            <meta-data
185-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:29:13-31:85
186                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar"
186-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:30:17-128
187                android:value="com.google.firebase.components.ComponentRegistrar" />
187-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:31:17-82
188            <meta-data
188-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:32:13-34:85
189                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
189-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:33:17-117
190                android:value="com.google.firebase.components.ComponentRegistrar" />
190-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5136e727b68f569bb48cae627b8d5a4b\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:34:17-82
191            <meta-data
191-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
192                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
192-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
193                android:value="com.google.firebase.components.ComponentRegistrar" />
193-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
194            <meta-data
194-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
195                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
195-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
196                android:value="com.google.firebase.components.ComponentRegistrar" />
196-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f383d60f6cbf7d58847eaa68fefd0674\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
197            <meta-data
197-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcc8d2985ec1ab09d1e21c43fe50ab02\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
198                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
198-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcc8d2985ec1ab09d1e21c43fe50ab02\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
199                android:value="com.google.firebase.components.ComponentRegistrar" />
199-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcc8d2985ec1ab09d1e21c43fe50ab02\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
200            <meta-data
200-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
201                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
201-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
202                android:value="com.google.firebase.components.ComponentRegistrar" />
202-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
203            <meta-data
203-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf6a27b1025e5f977c0f155cbd2c4f7c\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
204                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
204-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf6a27b1025e5f977c0f155cbd2c4f7c\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
205                android:value="com.google.firebase.components.ComponentRegistrar" />
205-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf6a27b1025e5f977c0f155cbd2c4f7c\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
206            <meta-data
206-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e32c06b9e94e0afe422db7a8cc6919d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
207                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
207-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e32c06b9e94e0afe422db7a8cc6919d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
208                android:value="com.google.firebase.components.ComponentRegistrar" />
208-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e32c06b9e94e0afe422db7a8cc6919d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
209        </service>
210
211        <provider
211-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:41:9-45:38
212            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
212-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:13-102
213            android:authorities="com.octalog.flutterfirebasemessaginginitprovider"
213-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:13-88
214            android:exported="false"
214-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-37
215            android:initOrder="99" />
215-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:45:13-35
216        <provider
216-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-17:20
217            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
217-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-82
218            android:authorities="com.octalog.flutter.image_provider"
218-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
219            android:exported="false"
219-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
220            android:grantUriPermissions="true" >
220-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-47
221            <meta-data
221-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:75
222                android:name="android.support.FILE_PROVIDER_PATHS"
222-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-67
223                android:resource="@xml/flutter_image_picker_file_paths" />
223-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:17-72
224        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
225        <service
225-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:19
226            android:name="com.google.android.gms.metadata.ModuleDependencies"
226-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-78
227            android:enabled="false"
227-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-36
228            android:exported="false" >
228-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
229            <intent-filter>
229-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-26:29
230                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
230-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-94
230-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-91
231            </intent-filter>
232
233            <meta-data
233-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-30:36
234                android:name="photopicker_activity:0:required"
234-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-63
235                android:value="" />
235-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-33
236        </service>
237
238        <provider
238-->[:open_file_android] C:\projetos\octa.log\MOBILE\build\open_file_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-19:20
239            android:name="com.crazecoder.openfile.FileProvider"
239-->[:open_file_android] C:\projetos\octa.log\MOBILE\build\open_file_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-64
240            android:authorities="com.octalog.fileProvider.com.crazecoder.openfile"
240-->[:open_file_android] C:\projetos\octa.log\MOBILE\build\open_file_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-88
241            android:exported="false"
241-->[:open_file_android] C:\projetos\octa.log\MOBILE\build\open_file_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
242            android:grantUriPermissions="true"
242-->[:open_file_android] C:\projetos\octa.log\MOBILE\build\open_file_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-47
243            android:requestLegacyExternalStorage="true" >
243-->[:open_file_android] C:\projetos\octa.log\MOBILE\build\open_file_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-56
244            <meta-data
244-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:75
245                android:name="android.support.FILE_PROVIDER_PATHS"
245-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-67
246                android:resource="@xml/filepaths" />
246-->[:image_picker_android] C:\projetos\octa.log\MOBILE\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:17-72
247        </provider>
248
249        <activity
249-->[:url_launcher_android] C:\projetos\octa.log\MOBILE\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
250            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
250-->[:url_launcher_android] C:\projetos\octa.log\MOBILE\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
251            android:exported="false"
251-->[:url_launcher_android] C:\projetos\octa.log\MOBILE\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
252            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
252-->[:url_launcher_android] C:\projetos\octa.log\MOBILE\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
253        <!--
254        Service for holding metadata. Cannot be instantiated.
255        Metadata will be merged from other manifests.
256        -->
257        <service
257-->[androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41ce894b414f4e410f366a4a6ad7589\transformed\jetified-camera-core-1.3.4\AndroidManifest.xml:29:9-33:78
258            android:name="androidx.camera.core.impl.MetadataHolderService"
258-->[androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41ce894b414f4e410f366a4a6ad7589\transformed\jetified-camera-core-1.3.4\AndroidManifest.xml:30:13-75
259            android:enabled="false"
259-->[androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41ce894b414f4e410f366a4a6ad7589\transformed\jetified-camera-core-1.3.4\AndroidManifest.xml:31:13-36
260            android:exported="false" >
260-->[androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41ce894b414f4e410f366a4a6ad7589\transformed\jetified-camera-core-1.3.4\AndroidManifest.xml:32:13-37
261            <meta-data
261-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a600f391fc985fb6fe77353ae0360c6c\transformed\jetified-camera-camera2-1.3.4\AndroidManifest.xml:30:13-32:89
262                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
262-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a600f391fc985fb6fe77353ae0360c6c\transformed\jetified-camera-camera2-1.3.4\AndroidManifest.xml:31:17-103
263                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
263-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a600f391fc985fb6fe77353ae0360c6c\transformed\jetified-camera-camera2-1.3.4\AndroidManifest.xml:32:17-86
264        </service>
265        <service
265-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:9:9-15:19
266            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
266-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:10:13-91
267            android:directBootAware="true"
267-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:17:13-43
268            android:exported="false" >
268-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:11:13-37
269            <meta-data
269-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:12:13-14:85
270                android:name="com.google.firebase.components:com.google.mlkit.vision.face.internal.FaceRegistrar"
270-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:13:17-114
271                android:value="com.google.firebase.components.ComponentRegistrar" />
271-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d7c6518f3a5e07ad02d7ba3ce0d5e\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:14:17-82
272            <meta-data
272-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\087b3f5fae32c8257c73d14fa1c12986\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
273                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
273-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\087b3f5fae32c8257c73d14fa1c12986\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
274                android:value="com.google.firebase.components.ComponentRegistrar" />
274-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\087b3f5fae32c8257c73d14fa1c12986\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
275            <meta-data
275-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70809b849f79bdfe99712e94237d768f\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
276                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
276-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70809b849f79bdfe99712e94237d768f\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
277                android:value="com.google.firebase.components.ComponentRegistrar" />
277-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70809b849f79bdfe99712e94237d768f\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
278            <meta-data
278-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:20:13-22:85
279                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
279-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:21:17-120
280                android:value="com.google.firebase.components.ComponentRegistrar" />
280-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:22:17-82
281        </service>
282
283        <provider
283-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:9:9-13:38
284            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
284-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:10:13-78
285            android:authorities="com.octalog.mlkitinitprovider"
285-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:11:13-69
286            android:exported="false"
286-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:12:13-37
287            android:initOrder="99" />
287-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0da87d36be692cd5086aca476c9fe1\transformed\jetified-common-18.11.0\AndroidManifest.xml:13:13-35
288
289        <receiver
289-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:49:9-60:20
290            android:name="com.onesignal.notifications.receivers.FCMBroadcastReceiver"
290-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:50:13-86
291            android:exported="true"
291-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:51:13-36
292            android:permission="com.google.android.c2dm.permission.SEND" >
292-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:52:13-73
293
294            <!-- High priority so OneSignal payloads can be filtered from other FCM receivers -->
295            <intent-filter android:priority="999" >
295-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:55:13-59:29
295-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:55:28-50
296                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
296-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:17-81
296-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:25-78
297
298                <category android:name="com.octalog" />
298-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:58:17-61
298-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:58:27-58
299            </intent-filter>
300        </receiver>
301
302        <service
302-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:62:9-68:19
303            android:name="com.onesignal.notifications.services.HmsMessageServiceOneSignal"
303-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:63:13-91
304            android:exported="false" >
304-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:64:13-37
305            <intent-filter>
305-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:65:13-67:29
306                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
306-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:66:17-81
306-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:66:25-78
307            </intent-filter>
308        </service> <!-- CAUTION: OneSignal backend includes the activity name in the payload, modifying the name without sync may result in notification click not firing -->
309        <activity
309-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:69:9-77:20
310            android:name="com.onesignal.NotificationOpenedActivityHMS"
310-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:70:13-71
311            android:exported="true"
311-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:71:13-36
312            android:noHistory="true"
312-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:72:13-37
313            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
313-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:73:13-72
314            <intent-filter>
314-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:74:13-76:29
315                <action android:name="android.intent.action.VIEW" />
315-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:75:17-69
315-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:75:25-66
316            </intent-filter>
317        </activity>
318
319        <receiver
319-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:79:9-81:39
320            android:name="com.onesignal.notifications.receivers.NotificationDismissReceiver"
320-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:80:13-93
321            android:exported="true" />
321-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:81:13-36
322        <receiver
322-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:82:9-89:20
323            android:name="com.onesignal.notifications.receivers.BootUpReceiver"
323-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:83:13-80
324            android:exported="true" >
324-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:84:13-36
325            <intent-filter>
325-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:85:13-88:29
326                <action android:name="android.intent.action.BOOT_COMPLETED" />
326-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:86:17-79
326-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:86:25-76
327                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
327-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:87:17-82
327-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:87:25-79
328            </intent-filter>
329        </receiver>
330        <receiver
330-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:90:9-96:20
331            android:name="com.onesignal.notifications.receivers.UpgradeReceiver"
331-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:91:13-81
332            android:exported="true" >
332-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:92:13-36
333            <intent-filter>
333-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:93:13-95:29
334                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
334-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:94:17-84
334-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:94:25-81
335            </intent-filter>
336        </receiver>
337
338        <activity
338-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:98:9-104:75
339            android:name="com.onesignal.notifications.activities.NotificationOpenedActivity"
339-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:99:13-93
340            android:excludeFromRecents="true"
340-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:100:13-46
341            android:exported="true"
341-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:101:13-36
342            android:noHistory="true"
342-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:102:13-37
343            android:taskAffinity=""
343-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:103:13-36
344            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
344-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:104:13-72
345        <activity
345-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:105:9-110:75
346            android:name="com.onesignal.notifications.activities.NotificationOpenedActivityAndroid22AndOlder"
346-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:106:13-110
347            android:excludeFromRecents="true"
347-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:107:13-46
348            android:exported="true"
348-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:108:13-36
349            android:noHistory="true"
349-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:109:13-37
350            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
350-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:110:13-72
351
352        <service
352-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:13:9-16:72
353            android:name="com.onesignal.core.services.SyncJobService"
353-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:14:13-70
354            android:exported="false"
354-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:15:13-37
355            android:permission="android.permission.BIND_JOB_SERVICE" />
355-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:16:13-69
356
357        <activity
357-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:18:9-21:75
358            android:name="com.onesignal.core.activities.PermissionsActivity"
358-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:19:13-77
359            android:exported="false"
359-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:20:13-37
360            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
360-->[com.onesignal:core:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952d4bb05156a6bae798ebc3d4f6b56b\transformed\jetified-core-5.1.29\AndroidManifest.xml:21:13-72
361
362        <receiver
362-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
363            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
363-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
364            android:exported="true"
364-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
365            android:permission="com.google.android.c2dm.permission.SEND" >
365-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
366            <intent-filter>
366-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-32:29
367                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
367-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:17-81
367-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:25-78
368            </intent-filter>
369
370            <meta-data
370-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
371                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
371-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
372                android:value="true" />
372-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
373        </receiver>
374        <!--
375             FirebaseMessagingService performs security checks at runtime,
376             but set to not exported to explicitly avoid allowing another app to call it.
377        -->
378        <service
378-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
379            android:name="com.google.firebase.messaging.FirebaseMessagingService"
379-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
380            android:directBootAware="true"
380-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
381            android:exported="false" >
381-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f80248970efbd3bafd1721bfcda96536\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
382            <intent-filter android:priority="-500" >
382-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-23:29
383                <action android:name="com.google.firebase.MESSAGING_EVENT" />
383-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:17-78
383-->[:firebase_messaging] C:\projetos\octa.log\MOBILE\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:25-75
384            </intent-filter>
385        </service>
386
387        <activity
387-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07660f349a2fe95c07a6bc7026689012\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
388            android:name="com.google.android.gms.common.api.GoogleApiActivity"
388-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07660f349a2fe95c07a6bc7026689012\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
389            android:exported="false"
389-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07660f349a2fe95c07a6bc7026689012\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
390            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
390-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07660f349a2fe95c07a6bc7026689012\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
391
392        <provider
392-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
393            android:name="com.google.firebase.provider.FirebaseInitProvider"
393-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
394            android:authorities="com.octalog.firebaseinitprovider"
394-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
395            android:directBootAware="true"
395-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
396            android:exported="false"
396-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
397            android:initOrder="100" />
397-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
398
399        <uses-library
399-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
400            android:name="androidx.window.extensions"
400-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
401            android:required="false" />
401-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
402        <uses-library
402-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
403            android:name="androidx.window.sidecar"
403-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
404            android:required="false" />
404-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
405
406        <provider
406-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
407            android:name="androidx.startup.InitializationProvider"
407-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
408            android:authorities="com.octalog.androidx-startup"
408-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
409            android:exported="false" >
409-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
410            <meta-data
410-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
411                android:name="androidx.work.WorkManagerInitializer"
411-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
412                android:value="androidx.startup" />
412-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
413            <meta-data
413-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95a194f62dbd62c14638e0c38a7881a4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
414                android:name="androidx.emoji2.text.EmojiCompatInitializer"
414-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95a194f62dbd62c14638e0c38a7881a4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
415                android:value="androidx.startup" />
415-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95a194f62dbd62c14638e0c38a7881a4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
416            <meta-data
416-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
417                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
417-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
418                android:value="androidx.startup" />
418-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
419            <meta-data
419-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
420                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
420-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
421                android:value="androidx.startup" />
421-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
422        </provider>
423
424        <service
424-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
425            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
425-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
426            android:directBootAware="false"
426-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
427            android:enabled="@bool/enable_system_alarm_service_default"
427-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
428            android:exported="false" />
428-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
429        <service
429-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
430            android:name="androidx.work.impl.background.systemjob.SystemJobService"
430-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
431            android:directBootAware="false"
431-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
432            android:enabled="@bool/enable_system_job_service_default"
432-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
433            android:exported="true"
433-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
434            android:permission="android.permission.BIND_JOB_SERVICE" />
434-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
435        <service
435-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
436            android:name="androidx.work.impl.foreground.SystemForegroundService"
436-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
437            android:directBootAware="false"
437-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
438            android:enabled="@bool/enable_system_foreground_service_default"
438-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
439            android:exported="false" />
439-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
440
441        <receiver
441-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
442            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
442-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
443            android:directBootAware="false"
443-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
444            android:enabled="true"
444-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
445            android:exported="false" />
445-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
446        <receiver
446-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
447            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
447-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
448            android:directBootAware="false"
448-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
449            android:enabled="false"
449-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
450            android:exported="false" >
450-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
451            <intent-filter>
451-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
452                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
452-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
452-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
453                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
453-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
453-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
454            </intent-filter>
455        </receiver>
456        <receiver
456-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
457            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
457-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
458            android:directBootAware="false"
458-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
459            android:enabled="false"
459-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
460            android:exported="false" >
460-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
461            <intent-filter>
461-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
462                <action android:name="android.intent.action.BATTERY_OKAY" />
462-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
462-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
463                <action android:name="android.intent.action.BATTERY_LOW" />
463-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
463-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
464            </intent-filter>
465        </receiver>
466        <receiver
466-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
467            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
467-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
468            android:directBootAware="false"
468-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
469            android:enabled="false"
469-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
470            android:exported="false" >
470-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
471            <intent-filter>
471-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
472                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
472-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
472-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
473                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
473-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
473-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
474            </intent-filter>
475        </receiver>
476        <receiver
476-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
477            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
477-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
478            android:directBootAware="false"
478-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
479            android:enabled="false"
479-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
480            android:exported="false" >
480-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
481            <intent-filter>
481-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
482                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
482-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
482-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
483            </intent-filter>
484        </receiver>
485        <receiver
485-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
486            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
486-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
487            android:directBootAware="false"
487-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
488            android:enabled="false"
488-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
489            android:exported="false" >
489-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
490            <intent-filter>
490-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
491                <action android:name="android.intent.action.BOOT_COMPLETED" />
491-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:86:17-79
491-->[com.onesignal:notifications:5.1.29] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b951ec29728b45cedd679ddbec605b\transformed\jetified-notifications-5.1.29\AndroidManifest.xml:86:25-76
492                <action android:name="android.intent.action.TIME_SET" />
492-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
492-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
493                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
493-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
493-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
494            </intent-filter>
495        </receiver>
496        <receiver
496-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
497            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
497-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
498            android:directBootAware="false"
498-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
499            android:enabled="@bool/enable_system_alarm_service_default"
499-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
500            android:exported="false" >
500-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
501            <intent-filter>
501-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
502                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
502-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
502-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
503            </intent-filter>
504        </receiver>
505        <receiver
505-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
506            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
506-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
507            android:directBootAware="false"
507-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
508            android:enabled="true"
508-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
509            android:exported="true"
509-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
510            android:permission="android.permission.DUMP" >
510-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
511            <intent-filter>
511-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
512                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
512-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
512-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23aabf00f3a34e0b600bc8ec6a919265\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
513            </intent-filter>
514        </receiver>
515
516        <meta-data
516-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf636ef2f0738047fe8129f320ef339e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
517            android:name="com.google.android.gms.version"
517-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf636ef2f0738047fe8129f320ef339e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
518            android:value="@integer/google_play_services_version" />
518-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf636ef2f0738047fe8129f320ef339e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
519
520        <receiver
520-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
521            android:name="androidx.profileinstaller.ProfileInstallReceiver"
521-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
522            android:directBootAware="false"
522-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
523            android:enabled="true"
523-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
524            android:exported="true"
524-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
525            android:permission="android.permission.DUMP" >
525-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
526            <intent-filter>
526-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
527                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
527-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
527-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
528            </intent-filter>
529            <intent-filter>
529-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
530                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
530-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
530-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
531            </intent-filter>
532            <intent-filter>
532-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
533                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
533-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
533-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
534            </intent-filter>
535            <intent-filter>
535-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
536                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
536-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
536-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
537            </intent-filter>
538        </receiver>
539
540        <service
540-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
541            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
541-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
542            android:exported="false" >
542-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
543            <meta-data
543-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
544                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
544-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
545                android:value="cct" />
545-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3680aa234ac342a007dbd86447bc8f64\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
546        </service>
547        <service
547-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
548            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
548-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
549            android:exported="false"
549-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
550            android:permission="android.permission.BIND_JOB_SERVICE" >
550-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
551        </service>
552
553        <receiver
553-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
554            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
554-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
555            android:exported="false" />
555-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\642b350cb6e62dd2a2606cba48493bbb\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
556
557        <service
557-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca09e878cbb074545c6b4079b0555c5\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
558            android:name="androidx.room.MultiInstanceInvalidationService"
558-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca09e878cbb074545c6b4079b0555c5\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
559            android:directBootAware="true"
559-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca09e878cbb074545c6b4079b0555c5\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
560            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
560-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dca09e878cbb074545c6b4079b0555c5\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
561        <activity
561-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2748b27b9a4960040fddad0a76748cc0\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
562            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
562-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2748b27b9a4960040fddad0a76748cc0\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
563            android:exported="false"
563-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2748b27b9a4960040fddad0a76748cc0\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
564            android:stateNotNeeded="true"
564-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2748b27b9a4960040fddad0a76748cc0\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
565            android:theme="@style/Theme.PlayCore.Transparent" />
565-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2748b27b9a4960040fddad0a76748cc0\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
566    </application>
567
568</manifest>
