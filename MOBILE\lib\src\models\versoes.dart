import 'dart:io';

import 'package:map_fields/map_fields.dart';
import 'package:pub_semver/pub_semver.dart';

class Versoes {
  final String versaoAndroid;
  final String versaoIphone;

  Versoes({required this.versaoAndroid, required this.versaoIphone});

  String get versaoLoja => Platform.isIOS ? versaoIphone : versaoAndroid;

  factory Versoes.fromMap(Map<String, dynamic> map) {
    final f = MapFields.load(map);
    return Versoes(versaoAndroid: f.getString('VersaoAndroid'), versaoIphone: f.getString('VersaoIphone'));
  }
  
  bool updateNecessary(String versaoLocal) {
    final versaoLoja = Platform.isIOS ? versaoIphone : versaoAndroid;
    final versaoLocalParsed = Version.parse(versaoLocal);
    final versaoLojaParsed = Version.parse(versaoLoja);
    if (versaoLocalParsed < versaoLojaParsed) {
      return true;
    }
    return false;
  }
}
