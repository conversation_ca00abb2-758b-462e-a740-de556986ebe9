import 'dart:math' as math;

import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:octalog/src/models_new/endereco_new.dart';
import 'package:octalog/src/pages/home/<USER>';

import '../helpers/gps/gps_contract.dart';
import '../helpers/ls_maps.dart';

List<LatLng> _getRoute(List<EnderecoNew> atividades) {
  final route = atividades
      .map((e) => LatLng(e.latitude, e.longitude))
      .where((e) => e.latitude != 0 && e.longitude != 0)
      .toList();
  return route;
}

Future<PolyLinesMap> getPolylines(List<EnderecoNew> atividades) async {
  try {
    final position = await GpsHelperContract.instance.receberLocalizacao();
    final origin = LatLng(position.latitude, position.longitude);
    final destination = HomeController.instance.state.value.fimRota;
    final waypoints = _getRoute(atividades);
    final result = await LsMaps.instance.getRouteWaypoints(
      origin,
      destination,
      waypoints,
    );
    final polisResponse = decodePolylines(result.geometry);
    return PolyLinesMap(
      distance: result.distance,
      polyLines: polisResponse,
    );
  } catch (e) {
    return PolyLinesMap(
      distance: 0,
      polyLines: [],
    );
  }
}

class PolyLinesMap {
  final double distance;
  final List<LatLng> polyLines;
  PolyLinesMap({
    required this.distance,
    required this.polyLines,
  });

  LatLngBounds get bounds => _bounds(polyLines);
}

LatLngBounds _bounds(List<LatLng> polyLines) {
  if (polyLines.isEmpty) {
    return LatLngBounds(
      LatLng(0, 0),
      LatLng(0, 0),
    );
  }
  double menorLat = 0;
  double menorLng = 0;
  double maiorLat = 0;
  double maiorLng = 0;
  if (polyLines.isNotEmpty) {
    for (final poly in polyLines) {
      if (menorLat == 0 || poly.latitude < menorLat) {
        menorLat = poly.latitude;
      }
      if (maiorLat == 0 || poly.latitude > maiorLat) {
        maiorLat = poly.latitude;
      }
      if (menorLng == 0 || poly.longitude < menorLng) {
        menorLng = poly.longitude;
      }
      if (maiorLng == 0 || poly.longitude > maiorLng) {
        maiorLng = poly.longitude;
      }
    }
  } else {
    for (final poly in polyLines) {
      if (menorLat == 0 || poly.latitude < menorLat) {
        menorLat = poly.latitude;
      }
      if (maiorLat == 0 || poly.latitude > maiorLat) {
        maiorLat = poly.latitude;
      }
      if (menorLng == 0 || poly.longitude < menorLng) {
        menorLng = poly.longitude;
      }
      if (maiorLng == 0 || poly.longitude > maiorLng) {
        maiorLng = poly.longitude;
      }
    }
  }
  final difLat = math.sqrt(
        (maiorLat - menorLat) * (maiorLat - menorLat),
      ) *
      0.05;
  return LatLngBounds(
    LatLng(menorLat - difLat, menorLng - (difLat * 10)),
    LatLng(maiorLat + difLat, maiorLng + difLat),
  );
}

List<LatLng> decodePolylines(String encoded) {
  List<LatLng> poly = [];
  int index = 0, len = encoded.length;
  int lat = 0, lng = 0;

  while (index < len) {
    int b, shift = 0, result = 0;
    do {
      b = encoded.codeUnitAt(index++) - 63;
      result |= (b & 0x1f) << shift;
      shift += 5;
    } while (b >= 0x20);
    int dlat = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
    lat += dlat;

    shift = 0;
    result = 0;
    do {
      b = encoded.codeUnitAt(index++) - 63;
      result |= (b & 0x1f) << shift;
      shift += 5;
    } while (b >= 0x20);
    int dlng = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
    lng += dlng;
    LatLng p = LatLng((lat / 1E5).toDouble(), (lng / 1E5).toDouble());
    poly.add(p);
  }
  return poly;
}
