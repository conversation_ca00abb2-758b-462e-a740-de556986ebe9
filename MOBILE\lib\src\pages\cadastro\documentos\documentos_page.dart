import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/pages/cadastro/cadastro_store.dart';
import 'package:octalog/src/utils/extesion.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../../components/text_field_ls/text_field_ls_custom.dart';
import '../../../utils/colors.dart';
import '../cadastro_state.dart';
import '../model/model_veiculos.dart';

class DocumenoPage extends StatefulWidget {
  final CadastroStore store;
  const DocumenoPage({super.key, required this.store});

  @override
  State<DocumenoPage> createState() => _DocumenoPageState();
}

class _DocumenoPageState extends State<DocumenoPage> {
  final controller = TextEditingController();

  @override
  void initState() {
    init();
    super.initState();
  }

  init() {
    if (widget.store.state.value.contratoModel?.dataValidadeCNH != null) {
      controller.text =
          widget.store.state.value.contratoModel?.dataValidadeCNH?.dataPtBr ??
              '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<CadastroState>(
      valueListenable: widget.store.state,
      builder: (BuildContext context, state, _) {
        final store = widget.store;
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _space(),
            IgnorePointer(
              ignoring: state.contratoModel!.dataConfirmouCadastro != null,
              child: SizedBox(
                width: MediaQuery.of(context).size.width,
                child: DropdownButtonFormField<Veiculo>(
                  decoration: InputDecoration(
                    labelText: 'Meu veículo é',
                    labelStyle: GoogleFonts.roboto(
                      fontSize: 22,
                      color: ColorsCustom.customGrey,
                    ),
                  ),
                  iconSize: 24,
                  icon: const Icon(
                    Icons.keyboard_arrow_down_outlined,
                    size: 20,
                  ),
                  onChanged: (Veiculo? newValue) {
                    store.setContratoModelParte(idTipoVeiculo: newValue?.id);
                    setState(() {});
                  },
                  value: state.veiculos.firstWhere(
                    (element) =>
                        element.id == state.contratoModel?.iDTipoVeiculo,
                    orElse: () => state.veiculos.first,
                  ),
                  items: state.veiculos.map(
                    (Veiculo value) {
                      return DropdownMenuItem<Veiculo>(
                        value: value,
                        child: Text(
                          value.nome,
                          style: GoogleFonts.roboto(
                            fontSize: 18,
                            color: ColorsCustom.customBlack,
                          ),
                        ),
                      );
                    },
                  ).toList(),
                ),
              ),
            ),
            Stack(
              children: [
                IgnorePointer(
                  child: TextFieldLsCustom(
                    labelText: 'CNH vence dia',
                    controller: controller,
                    isError: false,
                    textInputAction: TextInputAction.next,
                    maxCaracteres: 255,
                    onChanged: (value) {},
                  ),
                ),
                GestureDetector(
                  onTap: () async {
                    final DateTime? picked = await showDatePicker(
                      // tema
                      builder: (BuildContext context, Widget? child) {
                        return Theme(
                          data: ThemeData.light().copyWith(
                            colorScheme: const ColorScheme.light().copyWith(
                              primary: ThemeColors.customOrange(context),
                            ),
                          ),
                          child: child!,
                        );
                      },
                      context: context,
                      initialDate: DateTime.now().add(const Duration(days: 40)),
                      firstDate: state.contratoModel?.dataValidadeCNH ??
                          DateTime.now(),
                      lastDate: DateTime(2101),
                    );
                    if (picked != null) {
                      store.setContratoModelParte(dataValidadeCNH: picked);
                      controller.text = picked.dataPtBr.toString();
                    }
                  },
                  child: Container(
                    width: MediaQuery.of(context).size.width * 0.54,
                    height: 60,
                    color: ColorsCustom.customTransparent,
                  ),
                ),
              ],
            ),
            SizedBox(
              height: MediaQuery.of(context).size.height * 0.2,
            )
          ],
        );
      },
    );
  }

  Widget _space() {
    return const SizedBox(
      height: 10,
    );
  }
}
