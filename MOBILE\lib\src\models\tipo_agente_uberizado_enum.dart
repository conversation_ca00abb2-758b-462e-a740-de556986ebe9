enum TipoAgenteUberizado {
  nenhum(
    id: 0,
    isBotaoEnabled: false,
    isBannerEnabled: false,
    isButtonColetaEnabled: true,
  ),
  dedicado(
    id: 1,
    isBotaoEnabled: true,
    isBannerEnabled: true,
    isButtonColetaEnabled: false,
  ),
  esporadico(
    id: 2,
    isBotaoEnabled: true,
    isBannerEnabled: false,
    isButtonColetaEnabled: true,
  );

  final int id;
  final bool isBotaoEnabled;
  final bool isBannerEnabled;
  final bool isButtonColetaEnabled;

  const TipoAgenteUberizado({
    required this.id,
    required this.isBotaoEnabled,
    required this.isBannerEnabled,
    required this.isButtonColetaEnabled,
  });
}
