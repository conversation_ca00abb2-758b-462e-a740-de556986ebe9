import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/components/loading_ls/loading_ls.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../helpers/login/login.dart';
import '../../pages/home/<USER>';

class ImagePerfil extends StatelessWidget {
  final String url;
  final String iniciais;
  final double? fontSize;
  final bool offlineEnable;
  const ImagePerfil({
    super.key,
    required this.url,
    required this.iniciais,
    this.fontSize,
    this.offlineEnable = false,
  });

  @override
  Widget build(BuildContext context) {
    final child = url.isEmpty
        ? CircleAvatar(
            backgroundColor: ThemeColors.customOrange(context),
            child: Center(
              child: Text(
                iniciais,
                style: GoogleFonts.roboto(
                  textStyle: TextStyle(
                    color: Colors.white,
                    fontSize: fontSize ?? 40,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ),
          )
        : CachedNetworkImage(
            imageUrl: url,
            cacheKey: url,
            imageBuilder: (context, imageProvider) => CircleAvatar(
              backgroundImage: imageProvider,
            ),
            placeholder: (context, url) => const LoadingLs(),
            errorWidget: (context, url, error) => const Icon(Icons.error),
          );
    final usuario = Login.instance.usuarioLogado;
    if ((usuario?.tipoAgenteUberizado.isBotaoEnabled ?? false) &&
        offlineEnable) {
      return Stack(
        children: [
          AspectRatio(
            aspectRatio: 1,
            child: child,
          ),
          Positioned(
            right: 0,
            top: 0,
            child: ValueListenableBuilder(
              valueListenable: HomeController.instance.state,
              builder: (_, state, __) {
                return Container(
                  decoration: BoxDecoration(
                    color: state.isOnline
                        ? ThemeColors.customGreen(context)
                        : ThemeColors.customOrange(context),
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: Colors.white,
                    ),
                  ),
                  height: 15,
                  width: 15,
                );
              },
            ),
          ),
        ],
      );
    }
    return child;
  }
}
