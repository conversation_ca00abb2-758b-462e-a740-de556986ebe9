import 'package:flutter/material.dart';
import 'package:octalog/src/utils/theme_colors.dart';


class TextFiledCustom extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final TextInputType keyboardType;
  final MouseCursor? mouseCursor;
  final bool? readOnly;
  final FocusNode? focusNode;
  final bool? enabled;
  final String? Function(String? value)? validator;
  const TextFiledCustom({
    super.key,
    required this.controller,
    required this.label,
    required this.keyboardType,
    this.mouseCursor,
    this.readOnly,
    this.focusNode,
    this.enabled,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      children: [
        Text("  $label"),
        Container(
          height: 45,
          width: MediaQuery.of(context).size.width * 0.9,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: ThemeColors.customBlue(context),
            borderRadius: BorderRadius.circular(0),
          ),
          child: Text<PERSON><PERSON><PERSON><PERSON>(
            controller: controller,
            keyboardType: keyboardType,
            autofocus: false,
            autocorrect: false,
            decoration: const InputDecoration(
              contentPadding: EdgeInsets.fromLTRB(16, 16, 8, 16),
              border: InputBorder.none,
            ),
            validator: validator,
          ),
        ),
      ],
    );
  }
}
