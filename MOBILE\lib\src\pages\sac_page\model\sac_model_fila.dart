import 'package:map_fields/map_fields.dart';

class SacModelFila {
  final int fila;
  final SacChamadoModel chamado;
  final String icone;
  final String? mensagem;
  final String? tituloMensagemSac;

  SacModelFila({
    required this.fila,
    required this.chamado,
    required this.icone,
    this.mensagem,
    this.tituloMensagemSac,
  });

  factory SacModelFila.fromMap(Map<String, dynamic> map) {
    final MapFields f = MapFields.load(map);
    return SacModelFila(
      fila: f.getInt('Fila', 0),
      chamado: SacChamadoModel.fromMap(f.getMap('Chamado', {})),
      icone: f.getString('Icone', ''),
      mensagem: f.getStringNullable('Mensagem'),
      tituloMensagemSac: f.getStringNullable('TituloMensagemSac'),
    );
  }
}

class SacChamadoModel {
  final int idSacAtendimento;
  final int filaInicial;
  final String? nomeAtendente;
  final String? ocorrencia;
  final String os;
  final String? mensagemSac;
  final String? tituloMensagemSac;

  SacChamadoModel({
    required this.idSacAtendimento,
    required this.filaInicial,
    required this.nomeAtendente,
    required this.ocorrencia,
    required this.os,
    required this.mensagemSac,
    required this.tituloMensagemSac,
  });

  factory SacChamadoModel.fromMap(Map<String, dynamic> map) {
    final MapFields f = MapFields.load(map);
    return SacChamadoModel(
      idSacAtendimento: f.getInt('IDSacAtendimento', 0),
      filaInicial: f.getInt('FilaInicial', 1),
      nomeAtendente: f.getStringNullable('NomeAtendente'),
      ocorrencia: f.getStringNullable('Ocorrencia'),
      os: f.getString('OS', ''),
      mensagemSac: f.getStringNullable('MensagemSac'),
      tituloMensagemSac: f.getStringNullable('TituloMensagemSac'),
    );
  }
}
