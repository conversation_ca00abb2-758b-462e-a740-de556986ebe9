import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/components/romaneio_expedicao/romaneio_de_expedicao.dart';
import 'package:octalog/src/helpers/login/login.dart';
import 'package:octalog/src/utils/colors-dart';
import 'package:octalog/src/utils/theme_colors.dart';
import '../../pages/home/<USER>';

import '../../utils/offline_helper.dart';
import '../icon_return.dart';

class CustomScaffold extends StatefulWidget {
  final Widget child;
  final Function()? onPop;
  final Function()? cameraTela;
  final IconData? iconsCamera;
  final Widget? appBar;
  final Color? backgroundColor;
  final String? title;
  final Widget? drawer;
  final GlobalKey<ScaffoldState>? scaffoldKey;
  final Widget? titleWidget;
  final Widget? floatingActionButton;
  final bool centerFloatingActionButton;
  final bool showFloatingActionButtonOnOffLine;
  final Widget? bottomNavigationBar;
  final bool canPop;
  final bool isColorIcon;
  final bool isOffLineStack;
  final bool isExpedicao;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final Future<bool> Function()? onPopClose;

  const CustomScaffold({
    super.key,
    required this.child,
    this.onPop,
    this.cameraTela,
    this.iconsCamera,
    this.appBar,
    this.backgroundColor,
    this.title,
    this.drawer,
    this.scaffoldKey,
    this.titleWidget,
    this.floatingActionButton,
    this.centerFloatingActionButton = false,
    this.showFloatingActionButtonOnOffLine = true,
    this.bottomNavigationBar,
    this.canPop = true,
    this.isColorIcon = true,
    this.isOffLineStack = true,
    this.floatingActionButtonLocation,
    this.onPopClose,
    this.isExpedicao = false,
  });

  @override
  State<CustomScaffold> createState() => _CustomScaffoldState();
}

class _CustomScaffoldState extends State<CustomScaffold> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    const size = 68;
    final double height = MediaQuery.of(context).size.height;
    final double width = MediaQuery.of(context).size.width;
    final int numOfRows = (height / size).ceil() + 2;
    final int numOfColumns = (width / size).ceil() + 2;

    // final connect = _connectionStatus;

    return WillPopScope(
      onWillPop: () async {
        if (widget.onPopClose != null) {
          final retorno = await widget.onPopClose!();
          if (retorno) {
            return true;
          } else {
            return false;
          }
        } else {
          return true;
        }
      },
      child: Stack(
        children: [
          ValueListenableBuilder(
            valueListenable: offlineStore.offline,
            builder: (_, offline, __) {
              return SafeArea(
                child: Column(
                  children: [
                    if (widget.isOffLineStack) ...[
                      Visibility(
                        visible: offline,
                        child: Container(
                          height: 30,
                          color: ColorsCustom.customRed,
                          child: Center(
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.wifi_off,
                                  color: Colors.white,
                                  size: 20,
                                ),
                                const SizedBox(width: 5),
                                Text(
                                  'sem internet',
                                  style: GoogleFonts.roboto(
                                    fontSize: 16,
                                    color: Colors.white,
                                    fontStyle: FontStyle.italic,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],

                    if (widget.isExpedicao) ...[
                      GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const RomaneioExpedicao(),
                            ),
                          );
                        },
                        child: Container(
                          height: 50,
                          width: MediaQuery.of(context).size.width,
                          color: ThemeColors.customGrey(context)Bold,
                          child: Center(
                            child: FittedBox(
                              fit: BoxFit.scaleDown,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12.0),
                                child: Text(
                                  'EXISTEM NOVOS PEDIDOS - CLIQUE PARA PROSSEGUIR',
                                  softWrap: false, // Desativa quebra de linha
                                  overflow: TextOverflow.visible,
                                  style: GoogleFonts.roboto(
                                    color: ColorsCustom.customYellow,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                    ValueListenableBuilder(
                      valueListenable: HomeController.instance.state,
                      builder: (context, state, _) {
                        final usuario = Login.instance.usuarioLogado;
                        final isOnline = state.isOnline;

                        if (usuario == null) return Container();

                        return Visibility(
                          visible: !offline &&
                              usuario.tipoAgenteUberizado.isBannerEnabled &&
                              !isOnline,
                          child: Container(
                            height: 30,
                            color: ThemeColors.customOrange(context),
                            child: Center(
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Icon(
                                    Icons.grid_off_outlined,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 5),
                                  Text(
                                    'INDISPONÍVEL PARA COLETA',
                                    style: GoogleFonts.roboto(
                                      fontSize: 16,
                                      color: Colors.white,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    Expanded(
                      child: Scaffold(
                        resizeToAvoidBottomInset: false,
                        floatingActionButton:
                            !widget.showFloatingActionButtonOnOffLine && offline
                                ? null
                                : widget.floatingActionButton,
                        floatingActionButtonLocation:
                            widget.floatingActionButtonLocation,
                        key: widget.scaffoldKey,
                        drawer: widget.drawer,
                        backgroundColor: widget.backgroundColor,
                        body: Stack(
                          children: [
                            if (widget.isOffLineStack)
                              if (offline)
                                ListView(
                                  children: List.generate(
                                    numOfRows,
                                    (coluna) {
                                      bool colunaPar = (coluna + 2) % 2 == 0;
                                      final offset = colunaPar ? (size / 2) : 0;
                                      final controller = ScrollController(
                                        initialScrollOffset: offset.toDouble(),
                                      );
                                      return SizedBox(
                                        height: size.toDouble(),
                                        width: width,
                                        child: ListView(
                                          scrollDirection: Axis.horizontal,
                                          controller: controller,
                                          children: List.generate(
                                            numOfColumns,
                                            (linha) => Icon(
                                              Icons.wifi_off_rounded,
                                              color: ThemeColors.customGrey(context)
                                                  .withOpacity(0.1),
                                              size: size.toDouble(),
                                            ),
                                          ).toList(),
                                        ),
                                      );
                                    },
                                  ).toList(),
                                ),
                            if (offline)
                              Container(
                                height: height,
                                width: width,
                                color: Colors.transparent,
                              ),
                            if (widget.onPop == null) widget.child,
                            if (widget.onPop != null)
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 10,
                                ),
                                child: Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        CircleAvatar(
                                            radius: 20,
                                            backgroundColor: Colors.grey[200],
                                            child: const IconPadrao(
                                              isBack: false,
                                            )),
                                        const SizedBox(width: 30),
                                        if (widget.titleWidget != null)
                                          Expanded(
                                            child: widget.titleWidget!,
                                          ),
                                        if (widget.titleWidget == null)
                                          Text(
                                            widget.title ?? '',
                                            style: GoogleFonts.poppins(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        const SizedBox(width: 20),
                                        if (widget.cameraTela != null)
                                          CircleAvatar(
                                            radius: 20,
                                            backgroundColor: Colors.grey[200],
                                            child: IconButton(
                                              icon: Icon(
                                                widget.iconsCamera ??
                                                    Icons.camera_alt,
                                                color: widget.isColorIcon
                                                    ? Colors.grey
                                                    : ThemeColors.customOrange(context),
                                              ),
                                              onPressed: widget.cameraTela,
                                            ),
                                          ),
                                        if (widget.cameraTela == null)
                                          const SizedBox(
                                            width: 60,
                                          )
                                      ],
                                    ),
                                    Expanded(
                                      child: widget.child,
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                        extendBody: true,
                        bottomNavigationBar: widget.bottomNavigationBar,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
