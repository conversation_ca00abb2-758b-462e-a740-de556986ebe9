@echo off
setlocal enabledelayedexpansion

REM Script para build dos flavors do projeto Octalog
REM Uso: build_flavors.bat [flavor] [build_type]
REM Exemplo: build_flavors.bat octalog apk
REM Exemplo: build_flavors.bat arcargo appbundle

set FLAVOR=%1
set BUILD_TYPE=%2

if "%FLAVOR%"=="" set FLAVOR=octalog
if "%BUILD_TYPE%"=="" set BUILD_TYPE=apk

echo 🚀 Iniciando build do flavor: %FLAVOR%
echo 📦 Tipo de build: %BUILD_TYPE%

REM Validar flavor
if "%FLAVOR%"=="octalog" goto flavor_ok
if "%FLAVOR%"=="arcargo" goto flavor_ok
if "%FLAVOR%"=="connect" goto flavor_ok
if "%FLAVOR%"=="rondolog" goto flavor_ok

echo ❌ Flavor inválido: %FLAVOR%
echo Flavors disponíveis: octalog, arcargo, connect, rondolog
exit /b 1

:flavor_ok
echo ✅ Flavor válido: %FLAVOR%

REM Gerar ícones para o flavor
echo 🎨 Gerando ícones para o flavor: %FLAVOR%
if exist "scripts\generate_icons.bat" (
    call scripts\generate_icons.bat %FLAVOR%
    if errorlevel 1 (
        echo ❌ Erro ao gerar ícones para o flavor: %FLAVOR%
        exit /b 1
    )
) else (
    echo ⚠️  Script de geração de ícones não encontrado, continuando sem gerar ícones...
)

REM Validar tipo de build
if "%BUILD_TYPE%"=="apk" goto build_type_ok
if "%BUILD_TYPE%"=="appbundle" goto build_type_ok

echo ❌ Tipo de build inválido: %BUILD_TYPE%
echo Tipos disponíveis: apk, appbundle
exit /b 1

:build_type_ok
echo ✅ Tipo de build válido: %BUILD_TYPE%

REM Limpar build anterior
echo 🧹 Limpando build anterior...
flutter clean
flutter pub get

REM Definir comando de build baseado no tipo
if "%BUILD_TYPE%"=="apk" (
    set BUILD_CMD=flutter build apk --release --flavor %FLAVOR% --dart-define=FLAVOR=%FLAVOR%
) else (
    set BUILD_CMD=flutter build appbundle --release --flavor %FLAVOR% --dart-define=FLAVOR=%FLAVOR%
)

echo 🔨 Executando: !BUILD_CMD!
!BUILD_CMD!

if %errorlevel% equ 0 (
    echo ✅ Build concluído com sucesso!
    
    REM Mostrar localização do arquivo gerado
    if "%BUILD_TYPE%"=="apk" (
        echo 📱 APK gerado em: build\app\outputs\flutter-apk\app-%FLAVOR%-release.apk
    ) else (
        echo 📱 AAB gerado em: build\app\outputs\bundle\%FLAVOR%Release\app-%FLAVOR%-release.aab
    )
    
    echo.
    echo 🎯 Informações do build:
    echo    Flavor: %FLAVOR%
    echo    Tipo: %BUILD_TYPE%
    echo    Modo: release
    echo.
    echo 📋 Para instalar o APK:
    echo    adb install build\app\outputs\flutter-apk\app-%FLAVOR%-release.apk
    
) else (
    echo ❌ Erro durante o build!
    exit /b 1
)

pause
