import 'package:flutter/material.dart';
import 'package:octalog/src/helpers/web_connector.dart';
import 'package:octalog/src/pages/entrega_newpages/componentes/elevated_button.dart';
import 'package:octalog/src/utils/extesion.dart';

Future<void> verifyDateTime(BuildContext context) async {
  final now = DateTime.now();
  final response = await errorInHour(now);
  if (response.error) {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => DateTimeErrorWidget(response: response),
      ),
    );
  }
}

class DateTimeErrorWidget extends StatelessWidget {
  final DateTimeResponse response;
  const DateTimeErrorWidget({
    super.key,
    required this.response,
  });

  Widget textoHora(String prefix, DateTime date) {
    final data = date.toLocal();
    final texto = '${data.dataPtBr} ${data.hour}:${data.minute}';
    return Text(
      '$prefix: $texto',
      style: const TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          title: const Text("Data e Hora incorretas"),
          centerTitle: true,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'Ajuste o horário e a data do seu telefone!',
                overflow: TextOverflow.visible,
                softWrap: true,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              textoHora('Hora telefone', response.now),
              textoHora('Hora correta', response.time),
              const SizedBox(height: 10),
              Text(
                '${response.difference} de diferença',
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 10),
              const Text(
                'Caso o erro persista, reinicie o celular.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
            ],
          ),
        ),
        floatingActionButton: Padding(
          padding: const EdgeInsets.only(right: 20),
          child: ElevatedLsButton(
            text: "JÁ CORRIGI",
            isLoading: false,
            onPressed: () async {
              final now = DateTime.now();
              final response = await errorInHour(now);
              if (!response.error) {
                Navigator.pop(context);
              }
            },
          ),
        ),
      ),
    );
  }
}

class DateTimeResponse {
  final DateTime now;
  final DateTime time;

  DateTimeResponse({
    required this.now,
    required this.time,
  });

  String get difference {
    final dif = now.difference(time);
    final difMin = dif.inMinutes;
    final difHoras = dif.inHours;
    final difDias = dif.inDays;

    if (difDias != 0) {
      return '${difDias.abs()} ${difDias.abs() == 1 ? "dia" : "dias"}';
    } else if (difHoras != 0) {
      return '${difHoras.abs()} ${difHoras.abs() == 1 ? "hora" : "horas"}';
    } else {
      return '${difMin.abs()} ${difMin.abs() == 1 ? "minuto" : "minutos"}';
    }
  }

  bool get error {
    final dif = now.difference(time);
    final difMin = dif.inMinutes;
    return difMin.abs() > 5;
  }
}

Future<DateTimeResponse> errorInHour(DateTime now) async {
  final another = await WebConnector.getDateTimeGlobalFromWeb(now);
  return another.fold(
    (success) => DateTimeResponse(now: now, time: success),
    (error) => DateTimeResponse(now: now, time: now),
  );
}
