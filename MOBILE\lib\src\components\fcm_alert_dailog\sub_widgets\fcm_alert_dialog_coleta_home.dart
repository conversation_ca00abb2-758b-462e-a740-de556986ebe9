// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/components/buttom_ls/button_ls_custom.dart';
import 'package:octalog/src/pages/home/<USER>';

import 'package:octalog/src/utils/extesion.dart';
import 'package:octalog/src/utils/theme_colors.dart';

import '../../../database/log_database/log_database.dart';
import '../../../pages/home/<USER>';
import '../../uberizado_single/uberizado_loja_card.dart';
import '../components/card_pedido_delet.dart';
import '../components/fcm_appbar_custom.dart';
import '../fcm_alert_dialog_store.dart';

class FcmAlertDialogColetaHome extends StatefulWidget {
  final FcmAlertDialogStore store;

  const FcmAlertDialogColetaHome({super.key, required this.store});

  @override
  State<FcmAlertDialogColetaHome> createState() =>
      _FcmAlertDialogColetaHomeState();
}

class _FcmAlertDialogColetaHomeState extends State<FcmAlertDialogColetaHome> {
  final TextEditingController _controllerBarr = TextEditingController();
  bool visibleButton = false;
  bool isAnimeted = true;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: widget.store.state,
      builder: (context, state, child) {
        return WillPopScope(
          onWillPop: () async => false,
          child:
              KeyboardVisibilityBuilder(builder: (context, isKeyboardVisible) {
            return SafeArea(
              child: Scaffold(
                backgroundColor: Colors.transparent,
                body: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: GestureDetector(
                          onTap: () {
                            widget.store.getDadosPedidos(context);
                          },
                          child: const FcmAppBarCustom(
                            title: 'Coleta de Pedidos',
                            isBack: true,
                            isLoadgin: false,
                          ),
                        ),
                      ),
                      UberizadoLojaCard(
                        endereco: state.fcmAlertDados.endereco,
                        foto: state.fcmAlertDados.logo ?? "",
                        iniciais: state.fcmAlertDados.titulo.iniciais,
                        titulo: state.fcmAlertDados.titulo,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        child: SizedBox(
                          height: 54,
                          child: Row(
                            children: [
                              Expanded(
                                child: TextField(
                                  style: const TextStyle(
                                    fontSize: 20,
                                    height: 1,
                                  ),
                                  controller: _controllerBarr,
                                  onChanged: ((value) {
                                    widget.store.setFiltro(value);
                                  }),
                                  textInputAction: TextInputAction.search,
                                  decoration: InputDecoration(
                                    hintText:
                                        'Digite pedido, cliente ou endereço',
                                    hintStyle: const TextStyle(
                                      color: Colors.grey,
                                      fontSize: 15,
                                    ),
                                    prefixIcon: Icon(
                                      Icons.search,
                                      color: Colors.grey.shade600,
                                    ),
                                    suffixIcon: SizedBox(
                                      width: 80,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        children: [
                                          IconButton(
                                            onPressed: () {
                                              widget.store.scanBarcode(context);
                                            },
                                            icon: const Icon(
                                              Icons.qr_code_scanner,
                                              color: Colors.black54,
                                            ),
                                          ),
                                          if (isKeyboardVisible) ...[
                                            Container(
                                              width: 27,
                                              height: 27,
                                              alignment: Alignment.bottomCenter,
                                              child: GestureDetector(
                                                onTap: () {
                                                  FocusScope.of(context)
                                                      .requestFocus(
                                                          FocusNode());
                                                },
                                                child: Image.asset(
                                                  'assets/images/keyboard_close.png',
                                                  width: 25,
                                                  color: Colors.black54,
                                                ),
                                              ),
                                            ),
                                            const SizedBox(width: 5),
                                          ]
                                        ],
                                      ),
                                    ),
                                    filled: true,
                                    fillColor:
                                        const Color.fromRGBO(230, 230, 230, 1),
                                    border: const OutlineInputBorder(
                                      borderSide: BorderSide.none,
                                      borderRadius: BorderRadius.all(
                                        Radius.circular(8),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              if (state.filtro.isNotEmpty) ...[
                                const SizedBox(width: 5),
                                SizedBox(
                                  width: 90,
                                  child: ButtonLsCustom(
                                    text: 'Coletar',
                                    onPressed: _controllerBarr.text.isEmpty
                                        ? null
                                        : () {
                                            final codigo = _controllerBarr.text;
                                            _controllerBarr.clear();
                                            widget.store.scannOnChanged(codigo);
                                            widget.store.setFiltro('');
                                            setState(
                                              () {
                                                visibleButton = _controllerBarr
                                                    .text.isNotEmpty;
                                              },
                                            );
                                          },
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      if (state.clientesUnicos.isEmpty) ...[
                        SizedBox(
                          height: MediaQuery.of(context).size.height / 5,
                        ),
                        Center(
                          child: Text(
                            'Para coletar os pedidos da loja, digite ou \nleia o código de barra',
                            style: GoogleFonts.roboto(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            overflow: TextOverflow.clip,
                            maxLines: 3,
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Text(
                          'Após a leitura os pedidos estarão disponível\r\npara entrega na lista de pedidos.',
                          style: GoogleFonts.roboto(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                          ),
                          overflow: TextOverflow.fade,
                          maxLines: 3,
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: MediaQuery.of(context).size.height / 5)
                      ],
                      if (state.clientesUnicos.isNotEmpty) ...[
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                             Expanded(
                              child: Divider(
                                color: ThemeColors.customOrange(context),
                                thickness: 2,
                                indent: 20,
                                endIndent: 20,
                              ),
                            ),
                            Text(
                              '${state.coletados.toSet().length} ${state.coletados.toSet().length == 1 ? ' pedido coletado' : ' pedidos coletados'}',
                              style:  TextStyle(
                                color: ThemeColors.customOrange(context),
                                fontWeight: FontWeight.w500,
                                fontSize: 17,
                              ),
                            ),
                             Expanded(
                              child: Divider(
                                color: ThemeColors.customOrange(context),
                                thickness: 2,
                                indent: 20,
                                endIndent: 20,
                              ),
                            ),
                          ],
                        ),
                        Container(
                          constraints: BoxConstraints(
                            minHeight: MediaQuery.of(context).size.height / 2,
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            child: ListView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: state.clientesColetadosUnicos.length,
                              itemBuilder: (context, index) {
                                final cliente =
                                    state.clientesColetadosUnicos[index];
                                final pedidosCliente = state.clientesColetados
                                    .where((element) =>
                                        element.cliente == cliente.cliente)
                                    .toList();

                                return Padding(
                                  padding: const EdgeInsets.all(6.0),
                                  child: CardPedidoDeletCustom(
                                    store: widget.store,
                                    coletados: pedidosCliente.length,
                                    pedidos: pedidosCliente,
                                    nomeCliente: cliente.cliente,
                                    endereco: pedidosCliente.first.title,
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ],
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SizedBox(
                              height: 54,
                              width: double.infinity,
                              child: TextField(
                                style: const TextStyle(
                                  fontSize: 20,
                                  height: 1,
                                ),
                                onChanged: widget.store.setLiberadoPor,
                                textInputAction: TextInputAction.search,
                                decoration: InputDecoration(
                                  hintText: 'Liberado na loja por',
                                  hintStyle: const TextStyle(
                                    color: Colors.grey,
                                    fontSize: 18,
                                  ),
                                  prefixIcon: Icon(
                                    Icons.supervisor_account_sharp,
                                    color: Colors.grey.shade600,
                                  ),
                                  filled: true,
                                  fillColor:
                                      const Color.fromARGB(255, 238, 238, 238),
                                  border: const OutlineInputBorder(
                                    borderSide: BorderSide.none,
                                    borderRadius: BorderRadius.all(
                                      Radius.circular(8),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            ButtonLsCustom(
                              isLoading: state.isLoadingButton,
                              onPressed: () async {
                                if (widget
                                        .store.state.value.liberadoPor.length >
                                    4) {
                                  widget.store.setLoadingButton(true);

                                  if (await widget.store.verificarDistancia()) {
                                    widget.store.setLoadingButton(false);
                                    return;
                                  }

                                  try {
                                    await widget.store.finalizeColeta(context);
                                    HomeController.instance.fetchAtividades();
                                    Navigator.pushAndRemoveUntil(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => const Home(
                                          enterAtividade: false,
                                        ),
                                      ),
                                      (route) => false,
                                    );
                                  } catch (e) {
                                    await LogDatabase.instance.logError(
                                      '/deslocamento/pedidoscoletados',
                                      '',
                                      'finalizeColeta',
                                      {
                                        'error': e.toString(),
                                        'error_type': e.runtimeType.toString(),
                                      },
                                    );
                                  }
                                } else {
                                  widget.store.setLoadingButton(false);
                                  showDialog(
                                    context: context,
                                    builder: (ctx) {
                                      return AlertDialog(
                                        title: const Text('Atenção'),
                                        content: const Text(
                                            'Informe o nome de quem liberou a mercadoria na loja.'),
                                        actions: [
                                          TextButton(
                                            onPressed: () {
                                              Navigator.pop(ctx);
                                            },
                                            child: const Text(
                                              'Ok',
                                              style: TextStyle(
                                                  color: Colors.orange),
                                            ),
                                          )
                                        ],
                                      );
                                    },
                                  );
                                }
                              },
                              text: 'FINALIZAR COLETA',
                              colorBackground: ThemeColors.customOrange(context),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }),
        );
      },
    );
  }
}
