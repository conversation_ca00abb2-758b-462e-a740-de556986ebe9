import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';



class TitleWidget extends StatelessWidget {
  final String title;
  final String subtitle;
  const TitleWidget({
    super.key,
    required this.title,
    required this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.roboto(
            fontSize: 30,
            fontWeight: FontWeight.bold,
            color: ThemeColors.customBlack(context),
          ),
        ),
        if (subtitle.isNotEmpty)
          Text(
            subtitle,
            style: GoogleFonts.roboto(
              fontSize: 15,
              fontWeight: FontWeight.w500,
              color: ThemeColors.customGrey(context),
            ),
          ),
      ],
    );
  }
}
