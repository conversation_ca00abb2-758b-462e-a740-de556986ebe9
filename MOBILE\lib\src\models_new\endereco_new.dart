// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:math';

import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import 'package:octalog/src/models_new/mensagens_new.dart';
import 'package:octalog/src/models_new/position_data_location.dart';
import 'package:octalog/src/models_new/volume_new.dart';
import 'package:map_fields/map_fields.dart';

import '../models/id_status_atividade_enum.dart';
import 'cliente_new.dart';
import 'tag_new.dart';

class EnderecoNew {
  final int idEnderecoCliente;
  final int? ordem;
  final int? entrega;
  final String enderecoCompleto;
  final double latitude;
  final double longitude;
  final bool foto;
  final bool excluir;
  final List<MensagensNew> mensagens;
  final List<TagNew> tags;
  final List<ClienteNew> clientes;
  final int distanciaPermitidaParaChegada;
  final DateTime? dataFisico;
  EnderecoNew({
    required this.idEnderecoCliente,
    this.ordem,
    this.entrega,
    required this.enderecoCompleto,
    required this.latitude,
    required this.longitude,
    required this.foto,
    required this.excluir,
    required this.mensagens,
    required this.tags,
    required this.clientes,
    required this.distanciaPermitidaParaChegada,
    required this.dataFisico,
  });

  bool get contemInfo =>
      clientes.where((c) => c.info != null).toList().isNotEmpty;

  EnderecoNew copyWith({
    int? idEnderecoCliente,
    int? ordem,
    int? entrega,
    String? textoSecundario,
    String? enderecoCompleto,
    double? latitude,
    double? longitude,
    bool? foto,
    bool? excluir,
    List<MensagensNew>? mensagens,
    List<TagNew>? tags,
    List<ClienteNew>? clientes,
    int? distanciaPermitidaParaChegada,
    DateTime? dataFisico,
  }) {
    return EnderecoNew(
      idEnderecoCliente: idEnderecoCliente ?? this.idEnderecoCliente,
      ordem: ordem ?? this.ordem,
      entrega: entrega ?? this.entrega,
      enderecoCompleto: enderecoCompleto ?? this.enderecoCompleto,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      foto: foto ?? this.foto,
      excluir: excluir ?? this.excluir,
      mensagens: mensagens ?? this.mensagens,
      tags: tags ?? this.tags,
      clientes: clientes ?? this.clientes,
      distanciaPermitidaParaChegada:
          distanciaPermitidaParaChegada ?? this.distanciaPermitidaParaChegada,
      dataFisico: dataFisico ?? this.dataFisico,
    );
  }

  EnderecoNew get apenasEntrega {
    return copyWith(
      clientes: clientes
          .where((e) =>
              e.status.contains(IdStatusAtividadeEnum.entrega) ||
              e.status.contains(IdStatusAtividadeEnum.negativa))
          .toList(),
    );
  }

  LatLng get latLng => LatLng(latitude, longitude);

  double get latLngEnd {
    return sqrt(pow(latitude, 0) + pow(longitude, 0));
  }

  Map<String, dynamic> toHiveMap() {
    return {
      'idEnderecoCliente': idEnderecoCliente,
      'ordem': ordem,
      'entrega': entrega,
      'enderecoCompleto': enderecoCompleto,
      'latitude': latitude,
      'longitude': longitude,
      'foto': foto,
      'excluir': excluir,
      'mensagens': mensagens.map((e) => e.toHiveMap()).toList(),
      'tags': tags.map((e) => e.toHiveMap()).toList(),
      'clientes': clientes.map((e) => e.toHiveMap()).toList(),
      'distanciaPermitidaParaChegada': distanciaPermitidaParaChegada,
      'dataFisico': dataFisico?.toIso8601String(),
    };
  }

  factory EnderecoNew.fromHiveMap(Map<String, dynamic> map) {
    final e = MapFields.load(map);
    return EnderecoNew(
      idEnderecoCliente: e.getInt('idEnderecoCliente', 0),
      ordem: e.getIntNullable('ordem'),
      entrega: e.getIntNullable('entrega'),
      enderecoCompleto: e.getString('enderecoCompleto', ''),
      latitude: e.getDouble('latitude', 0.0),
      longitude: e.getDouble('longitude', 0.0),
      foto: e.getBool('foto', true),
      excluir: e.getBool('excluir', false),
      mensagens: e
          .getList<Map<String, dynamic>>('mensagens', [])
          .map<MensagensNew>(
              (Map<String, dynamic> tag) => MensagensNew.fromHiveMap(tag))
          .toList(),
      tags: e
          .getList<Map<String, dynamic>>('tags', [])
          .map<TagNew>((Map<String, dynamic> tag) => TagNew.fromHiveMap(tag))
          .toList(),
      clientes: e
          .getList<Map<String, dynamic>>('clientes', [])
          .map<ClienteNew>(
              (Map<String, dynamic> cliente) => ClienteNew.fromHiveMap(cliente))
          .toList(),
      distanciaPermitidaParaChegada:
          e.getInt('distanciaPermitidaParaChegada', 0),
      dataFisico: e.getDateTimeNullable('dataFisico'),
    );
  }

  factory EnderecoNew.fromJson(Map<String, dynamic> map) {
    final e = MapFields.load(map);
    return EnderecoNew(
      idEnderecoCliente: e.getInt('IDEnderecoCliente'),
      ordem: e.getIntNullable('Ordem'),
      entrega: e.getInt('Entrega', 0),
      enderecoCompleto: e.getString('EnderecoCompleto'),
      latitude: e.getDouble('Latitude', 0.0),
      longitude: e.getDouble('Longitude', 0.0),
      foto: e.getBool('Foto'),
      excluir: e.getBool('Excluir'),
      mensagens: e
          .getList<Map<String, dynamic>>('Mensagens', [])
          .map<MensagensNew>(
              (Map<String, dynamic> tag) => MensagensNew.fromJson(tag))
          .toList(),
      tags: e
          .getList<Map<String, dynamic>>('Tags', [])
          .map<TagNew>((Map<String, dynamic> tag) => TagNew.fromJson(tag))
          .toList(),
      clientes: e
          .getList<Map<String, dynamic>>('Clientes', [])
          .map<ClienteNew>(
              (Map<String, dynamic> cliente) => ClienteNew.fromJson(cliente))
          .toList(),
      distanciaPermitidaParaChegada:
          e.getInt('DistanciaPermitidaParaChegada', 0),
      dataFisico: e.getDateTimeNullable('DataFisico'),
    );
  }

  EnderecoNew whereStatus(IdStatusAtividadeEnum value) {
    return copyWith(
      clientes: clientes.where((e) => e.status.contains(value)).toList(),
    );
  }

  EnderecoNew withStatus(int value, String newSituacao,
      [List<ClienteNew>? ignore]) {
    return copyWith(
        clientes: clientes.map(
      (e) {
        final index = ignore?.indexWhere((e) => e.hashCode == e.hashCode) ?? -1;
        if (index == -1) return e;
        return e.withStatus(value, newSituacao);
      },
    ).toList());
  }

  int get volumesLength => clientes.fold<int>(
        0,
        (previousValue, element) => previousValue + element.volumes.length,
      );

  List<IdStatusAtividadeEnum> get status {
    return clientes
        .map<List<IdStatusAtividadeEnum>>((e) => e.status)
        .toList()
        .expand((e) => e)
        .toList();
    // final List<IdStatusAtividadeEnum> statusi = [];
    // final c1 = clientes.map((e) => e.status).toList();
    // for (final c in c1) {
    //   statusi.addAll(c);
    // }
    // return statusi;
  }

  List<String> get statusString =>
      clientes.map((e) => e.statusAtividade).toSet().toList();

  Map<int, int> get statusCount {
    final Map<int, int> statusCount = {
      0: 0,
      1: 0,
      2: 0,
      3: 0,
    };
    for (var cliente in clientes) {
      for (var status in cliente.status) {
        statusCount[status.index] = (statusCount[status.index] ?? 0) + 1;
      }
    }
    return statusCount;
  }

  bool find(String search) {
    return enderecoCompleto
            .toLowerCase()
            .trim()
            .contains(search.toLowerCase().trim()) ||
        clientes.where((cliente) => cliente.find(search)).isNotEmpty;
  }

  List<int> get idosList {
    List<int> idos = [];
    for (var cliente in clientes) {
      idos.addAll(cliente.idosList);
    }
    return idos;
  }

  List<String> get codRastreioList {
    final List<String> codRastreioList = [];
    for (var cliente in clientes) {
      for (var volume in cliente.volumes) {
        codRastreioList.add(volume.os);
      }
    }
    return codRastreioList;
  }

  List<ClienteNew> get clienteList {
    final List<ClienteNew> clienteList = [];
    for (var cliente in clientes) {
      clienteList.add(cliente);
    }
    return clienteList;
  }

  List<String> get statusConclusaoList {
    final List<String> statusConclusaoList = [];
    for (var cliente in clientes) {
      statusConclusaoList.add(cliente.statusAtividade);
    }
    return statusConclusaoList;
  }

  List<String> get fotoConclusao {
    return clientes
        .map((c) => c.links)
        .toList()
        .expand((e) => e)
        .toList()
        .toSet()
        .toList();
    // final List<String> fotoConclusao = [];
    // for (var cliente in clientes) {
    //   for (var foto in cliente.fotos) {
    //     fotoConclusao.add(foto.link);
    //   }
    // }
    // return fotoConclusao;
  }

  double distance(PositionDataLocation position) {
    // final lat1 = position.latitude;
    // final lon1 = position.longitude;
    // final lat2 = latitude;
    // final lon2 = longitude;
    // if (lat1 == 0.0 || lon1 == 0.0 || lat2 == 0.0 || lon2 == 0.0) {
    //   return 0.0;
    // }
    // final dx = lat2 - lat1;
    // final dy = lon2 - lon1;
    // final d = sqrt(dx * dx + dy * dy) * 111.045;
    // return d;
    //to
    // final distancia = GeolocatorPlatform.instance.distanceBetween(
    //   position.latitude,
    //   position.longitude,
    //   latitude,
    //   longitude,
    // );
    // return distancia;

    final distancia = Geolocator.distanceBetween(
      position.latitude,
      position.longitude,
      latitude,
      longitude,
    );
    return distancia / 1000;
  }

  final String _consumidor = 'Consumidor Final';

  String get enderecoFormatado {
    if (enderecoCompleto.isEmpty) {
      return _consumidor;
    }
    return enderecoCompleto;
  }

  List<ClienteSituacao> get clienteSituacao {
    List<ClienteSituacao> listC = [];
    for (final sit in IdStatusAtividadeEnum.values) {
      final clistSit = clientes
          .where((e) => e.statusAtividade.contains(sit.toString()))
          .toList();
      if (clistSit.isNotEmpty) {
        ClienteSituacao cls =
            ClienteSituacao(idStatusAtividade: sit, clientesSit: clistSit);
        listC.add(cls);
      }
    }
    return listC;
  }

  EnderecoNew setSituacao(
      IdStatusAtividadeEnum idStatusAtividadeEnum, ClienteNew cliente) {
    try {
      final indexClienteNew =
          clientes.indexWhere((e) => e.telefone == cliente.telefone);
      final value = idStatusAtividadeEnum.id;
      final nome = idStatusAtividadeEnum.title;
      return _setSituacao(indexClienteNew, cliente, value, nome);
    } catch (e, s) {
      String text = 'ErroCustomSetSituacao: $e';
      text = '$text\n\nCliente: $cliente';
      text = '$text\n\nClientes: ${clientes.join('\n')}';
      text = '$text\n\n$s';
      throw text;
    }
  }

  EnderecoNew _setSituacao(
    int indexClienteNew,
    ClienteNew clienteNew,
    int idStatus,
    String statusNome,
  ) {
    if (indexClienteNew == -1) return this;
    clientes[indexClienteNew] ==
        clienteNew.copyWith(
          volumes: clienteNew.volumes
              .map<VolumeNew>((v) => v.copyWith(
                    idos: idStatus,
                    os: statusNome,
                  ))
              .toList(),
        );
    return this;
  }
}

class ClienteSituacao {
  final IdStatusAtividadeEnum idStatusAtividade;
  final List<ClienteNew> clientesSit;

  ClienteSituacao({
    required this.idStatusAtividade,
    required this.clientesSit,
  });
}
