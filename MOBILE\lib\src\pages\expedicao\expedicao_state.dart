import 'model/expedicao_model.dart';

class ExpedicaoState {
  final ExpedicaoPageModel expedicaoPageModel;
  final bool loading;
  final DateTime dataInicio;
  final DateTime dataFim;
  ExpedicaoState({
    required this.expedicaoPageModel,
    required this.loading,
    required this.dataInicio,
    required this.dataFim,
  });

  ExpedicaoState copyWith({
    ExpedicaoPageModel? expedicaoPageModel,
    bool? loading,
    DateTime? dataInicio,
    DateTime? dataFim,
  }) {
    return ExpedicaoState(
      expedicaoPageModel: expedicaoPageModel ?? this.expedicaoPageModel,
      loading: loading ?? this.loading,
      dataInicio: dataInicio ?? this.dataInicio,
      dataFim: dataFim ?? this.dataFim,
    );
  }
}
