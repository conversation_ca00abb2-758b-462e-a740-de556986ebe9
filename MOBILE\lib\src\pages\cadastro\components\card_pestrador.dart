import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:octalog/src/utils/theme_colors.dart';

class CardPrestador extends StatelessWidget {
  final int idTipoAgente;
  final String nomePrestador;
  const CardPrestador(
      {super.key, required this.idTipoAgente, required this.nomePrestador});

  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) {
        final prestador = idTipoAgente == 2;

        if (prestador) {
          return Container(
            width: MediaQuery.of(context).size.width,
            margin: const EdgeInsets.symmetric(
              vertical: 10,
            ),
            decoration: BoxDecoration(
              color: ThemeColors.customGreyLight(context),
              borderRadius: BorderRadius.circular(0),
              border: Border.all(
                color: ThemeColors.customGrey(context),
                width: 2,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 5,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (prestador)
                    Text(
                      "Empresa:",
                      textAlign: TextAlign.start,
                      style: GoogleFonts.roboto(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: ThemeColors.customBlack(context),
                      ),
                    ),
                  const SizedBox(
                    height: 5,
                  ),
                  prestador
                      ? Text(
                          nomePrestador,
                          style: GoogleFonts.roboto(
                            fontWeight: FontWeight.w500,
                            color: ThemeColors.customOrange(context),
                          ),
                        )
                      : Center(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                              vertical: 10,
                            ),
                            child: Text(
                              "Serviço Temporário",
                              style: GoogleFonts.roboto(
                                fontWeight: FontWeight.w500,
                                color: ThemeColors.customOrange(context),
                              ),
                            ),
                          ),
                        ),
                ],
              ),
            ),
          );
        }
        return Container(
          height: MediaQuery.of(context).size.height * 0.05,
        );
      },
    );
  }
}
