import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_map/plugin_api.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:iconsax/iconsax.dart';
import 'package:latlong2/latlong.dart';
import 'package:octalog/src/components/flavor_image/flavor_image.dart';
import 'package:octalog/src/models_new/endereco_new.dart';
import 'package:octalog/src/utils/theme_colors.dart';

//import '../../helpers/login/login.dart';

import '../../utils/polylines.dart';
import '../entrega_newpages/entrega_new_page.dart';
import '../home/<USER>';
import 'mapa_page_state.dart';
import 'mapa_page_store.dart';

class MapaPage extends StatefulWidget {
  final List<EnderecoNew> entregas;
  final List<EnderecoNew> reentregas;

  final PolyLinesMap polyLinesMap;
  final controller = HomeController.instance;
  MapaPage({super.key, required this.entregas, required this.reentregas, required this.polyLinesMap});

  @override
  State<MapaPage> createState() => _MapaPageState();
}

class _MapaPageState extends State<MapaPage> with TickerProviderStateMixin {
  List<EnderecoNew> atividades = <EnderecoNew>[];
  final mapaPageStorei = MapaPageStore();

  final controller = HomeController.instance;
  final pageController = PageController();
  int? selectedIndex;
  LatLng? currentLocation;
  int count = 0;

  @override
  void initState() {
    super.initState();
    atividades = [...widget.entregas];
    final fimRota = controller.state.value.fimRota;
    if (fimRota != null) {
      atividades.add(
        EnderecoNew(
          idEnderecoCliente: -1,
          enderecoCompleto: '',
          latitude: fimRota.latitude,
          longitude: fimRota.longitude,
          foto: false,
          excluir: false,
          mensagens: [],
          tags: [],
          clientes: [],
          distanciaPermitidaParaChegada: 0,
          dataFisico: null,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<MapaPageState>(
      valueListenable: mapaPageStorei.state,
      builder: (BuildContext context, MapaPageState value, Widget? child) {
        final loc = value.latLng;
        final mapaPageController = value.mapaPageController;
        return Stack(
          children: [
            FlutterMap(
              mapController: mapaPageController,
              options: MapOptions(
                // onMapCreated: (mapController) {
                //  .state.value mapaPageController = mapController;
                // },
                rotation: 0,
                center: loc,
                zoom: 16.2,
              ),
              nonRotatedChildren: const [Positioned(bottom: 5, right: 5, child: Text("octalog"))],
              children: [
                TileLayer(urlTemplate: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", subdomains: const ['a', 'b', 'c']),
                PolylineLayer(polylines: [Polyline(points: widget.polyLinesMap.polyLines, color: Colors.blue, strokeWidth: 3)]),
                MarkerLayer(
                  markers: [
                    Marker(
                      width: 20.0,
                      height: 20.0,
                      point: loc,
                      builder: (ctx) {
                        return FlavorImage(assetName: 'map_marker.png', color: ThemeColors.customOrange(context));
                      },
                    ),
                    ...List.generate(widget.entregas.length, (index) => transformAtividadeToMarker(widget.entregas[index], index)),
                    // ...widget.entregas.map(transformAtividadeToMarker).toList(),
                    // ...widget.reentregas
                    //     .map(transformAtividadeToMarker)
                    //     .toList(),
                  ],
                ),
              ],
            ),
            Positioned(
              right: 10,
              bottom: 100,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  FloatingActionButton(
                    heroTag: 'asd',
                    key: const ValueKey('asd'),
                    onPressed: () {
                      final centerZoom = mapaPageController.centerZoomFitBounds(widget.polyLinesMap.bounds);
                      mapaPageController.move(centerZoom.center, centerZoom.zoom);
                      mapaPageController.move(loc, (mapaPageController.zoom));
                    },
                    child: const Icon(Iconsax.routing, size: 25),
                  ),
                  const SizedBox(height: 10),
                  FloatingActionButton(
                    heroTag: 'gps',
                    key: const ValueKey('gps'),
                    onPressed: () {
                      mapaPageStorei.setIsTracking(!value.isTracking);
                    },
                    child: Builder(
                      builder: (context) {
                        final isTracking = value.isTracking;
                        return Icon(isTracking ? Iconsax.gps : Iconsax.gps_slash, size: 25);
                      },
                    ),
                  ),
                  const SizedBox(height: 10),
                  Row(
                    children: [
                      FloatingActionButton(
                        key: const ValueKey('menos'),
                        heroTag: 'menos',
                        onPressed: () {
                          mapaPageController.move(mapaPageController.center, (mapaPageController.zoom) - 0.5);
                        },
                        child: const Icon(Iconsax.search_zoom_out, size: 25),
                      ),
                      const SizedBox(width: 10),
                      FloatingActionButton(
                        heroTag: 'mais',
                        key: const ValueKey('mais'),
                        onPressed: () {
                          mapaPageController.move(mapaPageController.center, (mapaPageController.zoom) + 0.5);
                        },
                        child: const Icon(Iconsax.search_zoom_in, size: 25),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Visibility(
              visible: selectedIndex != null,
              child: Positioned(
                left: 0,
                right: 0,
                bottom: 100,
                height: MediaQuery.of(context).size.height * 0.3,
                child: Stack(
                  children: [
                    PageView.builder(
                      controller: pageController,
                      onPageChanged: (value) {
                        selectedIndex = value;
                        currentLocation = widget.entregas[value].latLng;
                        _animatedMapMove(currentLocation!, 16.5);
                        setState(() {});
                        mapaPageController.move(currentLocation!, (mapaPageController.zoom));
                      },
                      itemCount:
                          widget
                              .entregas
                              // .map(transformAtividadeToMarker)
                              // .toList()
                              .length,
                      itemBuilder: (_, index) {
                        final item = widget.entregas[index];
                        return Padding(
                          padding: const EdgeInsets.all(15.0),
                          child: Card(
                            elevation: 5,
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                            color: ThemeColors.customWhite(context),
                            child: Row(
                              children: [
                                const SizedBox(width: 10),
                                Expanded(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Expanded(
                                        child: ListView.builder(
                                          padding: EdgeInsets.zero,
                                          scrollDirection: Axis.horizontal,
                                          itemCount: 1,
                                          itemBuilder: (BuildContext context, int index) {
                                            return FlavorImage(assetName: 'logo_ls_white.png', width: 40, height: 40);
                                          },
                                        ),
                                      ),
                                      Expanded(
                                        flex: 3,
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(item.enderecoCompleto, style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold)),
                                            const SizedBox(height: 10),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 10),
                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.all(4.0),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(10),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            crossAxisAlignment: CrossAxisAlignment.center,
                                            children: [
                                              Column(
                                                children: [
                                                  FlavorImage(assetName: 'packageLs.png', width: 40, height: 40),
                                                  Text(
                                                    item.ordem == null ? '' : item.ordem.toString(),
                                                    style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                                                  ),
                                                ],
                                              ),
                                              Column(
                                                children: [
                                                  Image.asset(
                                                    'assets/images/map_marker.png',
                                                    //fit: BoxFit.cover,
                                                    color: ThemeColors.customOrange(context),
                                                    width: 40,
                                                    height: 40,
                                                  ),
                                                  Text(
                                                    item.entrega == null ? '' : item.entrega.toString(),
                                                    style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 10),
                                          FloatingActionButton.extended(
                                            backgroundColor: ThemeColors.customOrange(context),
                                            onPressed: () {
                                              Navigator.push(
                                                context,
                                                MaterialPageRoute(builder: (context) => EntregaNewPage(atividade: item, withInitAuto: true)),
                                              );
                                            },
                                            label: Text('Iniciar rota', style: GoogleFonts.roboto(color: ThemeColors.customWhite(context))),
                                            icon:  CircleAvatar(
                                              backgroundColor: Colors.white,
                                              child: Icon(Icons.play_arrow, color: ThemeColors.customOrange(context)),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 10),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                    Positioned(
                      right: 18,
                      top: 18,
                      child: IconButton(
                        onPressed: () {
                          selectedIndex = null;
                          setState(() {});
                        },
                        icon: const Icon(Icons.close),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Container(
            //   padding: const EdgeInsets.only(top: 0, left: 0, right: 0),
            //   height: 100,
            //   color: ThemeColors.customOrange(context),
            //   child: Card(
            //     shadowColor: Colors.transparent,
            //     shape: RoundedRectangleBorder(
            //       borderRadius: BorderRadius.circular(15.0), // Cantos arredondados
            //     ),
            //     color: ThemeColors.customGrey(context),
            //     child: Padding(
            //       padding: const EdgeInsets.all(10.0), // Adiciona padding ao redor do Card
            //       child: Row(
            //         children: [
            //           const SizedBox(width: 10),
            //           SizedBox(width: 60, height: 60, child: Login.instance.fotoUsuarioLogado(25)),
            //           Padding(
            //             padding: const EdgeInsets.only(left: 10),
            //             child: Column(
            //               crossAxisAlignment: CrossAxisAlignment.start,
            //               children: [
            //                 const SizedBox(height: 10),
            //                 Text(
            //                   Login.instance.usuarioLogado?.nomeCompleto ?? '',
            //                   style: GoogleFonts.roboto(fontSize: 16, color: Colors.white, fontWeight: FontWeight.bold),
            //                 ),
            //                 Text(
            //                   '${widget.entregas.length} entregas.',
            //                   style: GoogleFonts.roboto(fontSize: 12, color: Colors.white, fontWeight: FontWeight.w300),
            //                 ),
            //               ],
            //             ),
            //           ),
            //         ],
            //       ),
            //     ),
            //   ),
            // ),
          ],
        );
      },
    );
  }

  Marker transformAtividadeToMarker(EnderecoNew a, [int? i]) {
    final item = widget.entregas[i ?? 0];

    return Marker(
      width: 30.0,
      height: 30.0,
      point: a.latLng,
      builder: (ctx) {
        return GestureDetector(
          onTap: () {
            if (i == null) return;
            selectedIndex = i;
            currentLocation = a.latLng;
            if (selectedIndex != null) {
              try {
                pageController.animateToPage(i, duration: const Duration(milliseconds: 500), curve: Curves.easeInOut);
              } catch (e) {
                log('Teste: $e');
              }
            }
            setState(() {});
            _animatedMapMove(a.latLng, 16.5);
            mapaPageStorei.state.value.mapaPageController.move(a.latLng, (mapaPageStorei.state.value.mapaPageController.zoom));
          },
          child: AnimatedScale(
            duration: const Duration(milliseconds: 500),
            scale: selectedIndex == null || selectedIndex == i ? 1 : 0.7,
            child: AnimatedOpacity(
              duration: const Duration(milliseconds: 500),
              opacity: selectedIndex == null || selectedIndex == i ? 1 : 0.5,
              child: CircleAvatar(
                backgroundColor: ThemeColors.customOrange(context),
                child: Text(item.ordem == null ? '' : item.ordem.toString(), style: TextStyle(fontSize: 15, color: ThemeColors.customWhite(context))),
              ),
            ),
          ),
        );
      },
    );
  }

  void _animatedMapMove(LatLng destLocation, double destZoom) {
    final latTween = Tween<double>(begin: mapaPageStorei.state.value.mapaPageController.center.latitude, end: destLocation.latitude);
    final lngTween = Tween<double>(begin: mapaPageStorei.state.value.mapaPageController.center.longitude, end: destLocation.longitude);
    final zoomTween = Tween<double>(begin: mapaPageStorei.state.value.mapaPageController.zoom, end: destZoom);

    var controller = AnimationController(duration: const Duration(milliseconds: 1000), vsync: this);

    Animation<double> animation = CurvedAnimation(parent: controller, curve: Curves.fastOutSlowIn);

    controller.addListener(() {
      mapaPageStorei.state.value.mapaPageController.move(LatLng(latTween.evaluate(animation), lngTween.evaluate(animation)), zoomTween.evaluate(animation));
    });

    animation.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        controller.dispose();
      } else if (status == AnimationStatus.dismissed) {
        controller.dispose();
      }
    });
    controller.forward();
  }
}
